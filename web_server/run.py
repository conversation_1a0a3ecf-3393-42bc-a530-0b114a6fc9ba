#!/usr/bin/env python3
"""
简化的应用启动脚本
"""

import sys
import os
import argparse
import subprocess

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Financial Strategy Dashboard 启动脚本')
    
    parser.add_argument('--host', default='0.0.0.0', help='主机地址')
    parser.add_argument('--port', type=int, default=8051, help='端口号')
    parser.add_argument('--test', action='store_true', help='运行测试')
    parser.add_argument('--health', action='store_true', help='健康检查')
    
    args = parser.parse_args()
    
    if args.test:
        print("🧪 运行测试...")
        result = subprocess.run([sys.executable, 'test_simple.py'], 
                              cwd=os.path.dirname(__file__))
        sys.exit(result.returncode)
    
    if args.health:
        print("🔍 运行健康检查...")
        try:
            from deploy_config import health_check
            result = health_check()
            print(f"状态: {result['status']}")
            for check, status in result['checks'].items():
                status_icon = "✅" if status else "❌"
                print(f"  {status_icon} {check}")
            sys.exit(0 if result['status'] == 'healthy' else 1)
        except Exception as e:
            print(f"健康检查失败: {e}")
            sys.exit(1)
    
    print(f"🚀 启动 Financial Strategy Dashboard v2.0")
    print(f"🌐 地址: http://{args.host}:{args.port}")
    print("-" * 50)
    
    try:
        # 直接启动应用
        result = subprocess.run([sys.executable, 'app_new.py'], 
                              cwd=os.path.dirname(__file__))
        sys.exit(result.returncode)
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
