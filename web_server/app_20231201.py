"""
uplog:
在修改查看周期时，显示的start_date/end_date也会同步发生变化
"""
# 引入所需库和组件
import dash
import dash_core_components as dcc
import dash_html_components as html
from dash.dependencies import Input, Output
from dash import dash_table
import dash_bootstrap_components as dbc
from urllib.parse import parse_qs
import os
import plotly.express as px
import pandas as pd
from plotly.subplots import make_subplots
import plotly.graph_objects as go
from matplotlib import colors
import json
import itertools
from dateutil.relativedelta import relativedelta
import numpy as np

# 创建 Dash 应用实例
app = dash.Dash(__name__, external_stylesheets=[dbc.themes.BOOTSTRAP], suppress_callback_exceptions=True)


app.layout = html.Div(
    children=[
        dcc.Store(id="store"),
        dcc.Store(id="tempdata"),
        dcc.Location(id='url', refresh=False),
        # 标题
        html.H1(children='策略回测', style={'textAlign': 'center'}),
        # 用户名下拉框和数据目录文本输入框
        html.Div(
            children=[
                html.Label('数据目录:\t'),
                html.Div(style={'width': '1%'}),
                html.Div([
                dbc.Input(id='data-pth', type='text', placeholder='请输入回测数据所在位置...'),
                ],style={"width": "30%"},),

                html.Div(style={'width': '3%'}),
                
                html.Label('策略:\t'),
                html.Div(style={'width': '1%'}),
                html.Div([
                dcc.Dropdown(id='strage-dropdown'),
                ],style={"width": "35%"},),
            ],
            style={'display': 'flex', 'justifyContent': 'center', 'alignItems': 'center', 'marginBottom': '10px'}  # 添加底部边距
        ),
        # 账户ID下拉框
        html.Div(
            children=[
                html.Label('账户ID:\t'),
                html.Div(style={'width': '1%'}),
                html.Div([
                dcc.Dropdown(id='account-id-dropdown', value='全部账户汇总'),
                ],style={"width": "15%"},),

                html.Div(style={'width': '3%'}),
                html.Label('业绩评价周期:\t'),
                html.Div(style={'width': '1%'}),
                html.Div([
                dcc.Dropdown(id='period-dropdown', value='全部'),
                ],style={"width": "15%"},),
            ],
            style={'display': 'flex', 'justifyContent': 'center', 'alignItems': 'center', 'marginBottom': '10px'}
        ),
        # 复选框
        html.Div(
            children=[
                dcc.Checklist(
                    id='metrics-checklist',
                    style={'display': 'flex', 'flexDirection': 'row'},
                    labelStyle={'marginRight': '20px'}
                )
            ],
            style={'width': '60%', 'margin': 'auto'}
        ),
        html.H2(id='dynamic-title', style={'textAlign': 'center', 'marginBottom': '10px'})
    ]
)
# 图表占位
app.layout.children.append(
    html.Div([
        # dynamic-graphs在左侧
        html.Div(id='dynamic-graphs',
                 style={'width': '70%', 'display': 'inline-block', 'vertical-align': 'top', 'marginTop': '20px'}),

        # 右侧的Div，包含run-info和table
        html.Div([
            # run-info DataTable
            html.Div(dash_table.DataTable(id='run-info'),
                     style={'width': '100%', 'display': 'block', 'vertical-align': 'top', 'marginTop': '20px'}),

            # table DataTable
            html.Div(dash_table.DataTable(id='table'),
                     style={'width': '100%', 'display': 'block', 'vertical-align': 'top', 'marginTop': '20px'}),
        ], style={'width': '28%', 'display': 'inline-block', 'vertical-align': 'top', 'marginLeft': '2%',
                  'marginTop': '20px'})
    ], style={'width': '80%', 'marginRight': 'auto', 'marginLeft': 'auto'})
)

# 获取真实路径
def get_real_path(user, path, strage):
    if user == 'admin':
        pass
    else:
        path = path.replace('\\', '/').split('upyter/')[-1]
        path = f'/jupyter/jupyter_docker/{user}/jupyter/{path}'

    if strage[-1] == '/':
        strage = strage[:-1]
    if strage != "":
        path = f"{path}/bt_data/{strage}"
    return path.replace('//', '/')

# 回调解析url
@app.callback(
    [Output('store', 'data'),
     Output('data-pth', 'value'),
     Output('strage-dropdown', 'options'),
     Output('strage-dropdown', 'value')],
    [Input('url', 'search')]
)
def update_components(search):
    print(search)
    # 解析URL参数
    if search:
        params = parse_qs(search[1:])  # 去掉'?'字符
        user = params.get('user', [''])
        if len(user) > 0:
            user = user[0]  # 获取'user'参数
        path = params.get('path', [''])[0]  # 获取'path'参数
        if path == 'nv_tracking':
            user = "admin"
            path = '/mnt/Data2/auto_task/nv_tracking_v2/'
        if path == 'stock':
            user = "admin"
            path = '/mnt/Data2/auto_task/nv_tracking_v3/gs_strage'
        if path == 'fut':
            user = "admin"
            path = '/mnt/Data2/auto_task/nv_tracking_v3/fut_strage'
        strage_name = ""
        f_path = path
        strage_options = []
        directory_path = f_path
        if 'bt_data' in path:
            f_path = path.split('bt_data')[0]
            strage_name = path.split('/')[-1]
        if user == 'admin':
            real_path = f_path
        else:
            real_path = f_path.replace('\\', '/').split('upyter/')[-1]
            real_path = f'/jupyter/jupyter_docker/{user}/jupyter/{real_path}'
        directory_path  = f'{real_path}/bt_data'.replace('//', '/')
        print(directory_path)
        # 获取目录下所有文件夹列表
        folders = [f for f in os.listdir(directory_path) if os.path.isdir(os.path.join(directory_path, f))]
        # 根据每个文件夹的最新修改时间进行排序
        sorted_folders = sorted(folders, key=lambda x: os.path.getmtime(os.path.join(directory_path, x)), reverse=True)

        strage_options = [{'label': strage, 'value': strage} for strage in sorted_folders if strage[0]!='.']
        if strage_name == "":
            strage_name = sorted_folders[0]
        print(user)
        return {"user": user}, f_path, strage_options, strage_name  # 更新组件值
    return '', '', [], ''  # 如果没有URL参数则返回空字符串


# 添加回调来更新账户ID下拉框的选项
@app.callback(
    Output('account-id-dropdown', 'options'),
    [Input('store', 'data'),
     Input('data-pth', 'value'),
     Input('strage-dropdown', 'value'),
     ]
)
def update_account_id_dropdown(store, input_value, strage):
    if not input_value:  # 如果输入框为空，则返回空选项
        return []
    user = store['user']
    input_value = get_real_path(user, input_value, strage)
    # 拼接路径
    performance_dir = os.path.join(input_value, 'show_data')

    # 获取performance目录下的所有文件
    try:
        files = os.listdir(performance_dir)
    except FileNotFoundError:
        return []  # 如果目录不存在，则返回空选项

    # 提取并去重账户ID
    account_ids = []
    for file in files:
        if file.endswith('.fea'):
            account_id = file.split('.fea')[0]
            account_id  = account_id
            account_ids.append(account_id)
    account_ids.sort()
    # 创建下拉框选项
    options = [{'label': account_id, 'value': account_id} for account_id in account_ids]
    return options

# 添加回调来更新周期选择下拉框的选项
@app.callback(
    Output('period-dropdown', 'options'),
    [Input('store', 'data'),
     Input('data-pth', 'value'),
     Input('strage-dropdown', 'value'),
     Input('account-id-dropdown', 'value'),
     ]
)
def update_period(store, path, strage, account_id):
    user = store['user']
    if not path or not account_id or not user:
        return []
    path = get_real_path(user, path, strage)
    print(path)
    if account_id == '全部账户汇总':
        fn = f"{path}/performance.xlsx"
    elif '汇总' in account_id:
        fn = f"{path}/performance/{account_id}.xlsx"
    else:
        account_id = account_id.split('_')[-1]
        fn = f"{path}/performance/account_{account_id}.xlsx"
    # 用Pandas打开Excel文件
    excel_file = pd.ExcelFile(fn)
    # 获取Excel文件中的所有表名
    sheet_names = excel_file.sheet_names
    return sheet_names


@app.callback(
    Output('metrics-checklist', 'options'),
    [Input('account-id-dropdown', 'value')]
)
def update_checklist(account_id):
    if '股票' in account_id:
        return [
            {'label': '持仓标的数量', 'value': '持仓标的数量'},
            {'label': '成交金额', 'value': '成交金额'},
            {'label': '手续费', 'value': '手续费'},
            {'label': '持仓市值', 'value': '持仓市值'},
            {'label': '仓位占比', 'value': '仓位占比'},
            {'label': '交易次数', 'value': '交易次数'},
            {'label': '累计换手率', 'value': '累计换手率'},
        ]
    elif '期货' in account_id:
        return [
            {'label': '持仓标的数量', 'value': '持仓标的数量'},
            {'label': '成交金额', 'value': '成交金额'},
            {'label': '手续费', 'value': '手续费'},
            {'label': '保证金占用', 'value': '保证金占用'},
            {'label': '合约市值', 'value': '合约市值'},
            {'label': '仓位占比', 'value': '仓位占比'},
            {'label': '交易次数', 'value': '交易次数'},
        ]
    elif account_id == '全部账户汇总':
        return [
            {'label': '持仓标的数量', 'value': '持仓标的数量'},
            {'label': '成交金额', 'value': '成交金额'},
            {'label': '手续费', 'value': '手续费'},
            {'label': '保证金占用', 'value': '保证金占用'},
            {'label': '持仓市值', 'value': '持仓市值'},
            {'label': '仓位占比', 'value': '仓位占比'},
            {'label': '交易次数', 'value': '交易次数'},
        ]
    else:
        return []


def period_deal(period, data, cal_fields=[], date_field='date'):
    if period == '全部':
        return data
    else:
        last_date = data.iloc[-1][date_field]
        last_1year = (pd.to_datetime(last_date) - relativedelta(years=1)).strftime("%Y-%m-%d")
        last_6m = (pd.to_datetime(last_date) - relativedelta(months=6)).strftime("%Y-%m-%d")
        last_3m = (pd.to_datetime(last_date) - relativedelta(months=3)).strftime("%Y-%m-%d")
        last_1m = (pd.to_datetime(last_date) - relativedelta(months=1)).strftime("%Y-%m-%d")
        near_1y = last_date[:4]
        near_2y = str(int(last_date[:4]) - 1)
        near_3y = str(int(last_date[:4]) - 2)
        if '近' in period:
            if '1月' in period:
                data = data[data[date_field]>=last_1m]
            elif '3月' in period:
                data = data[data[date_field]>=last_3m]
            elif '6月' in period:
                data = data[data[date_field]>=last_6m]
            elif '1年' in period:
                data = data[data[date_field]>=last_1year]
        else:
            if near_1y in period:
                data = data[(data[date_field]>=f"{near_1y}-01-01")&(data[date_field]<=f"{near_1y}-12-31")]
            elif near_2y in period:
                data = data[(data[date_field]>=f"{near_2y}-01-01")&(data[date_field]<=f"{near_2y}-12-31")]
            elif near_3y in period:
                data = data[(data[date_field]>=f"{near_3y}-01-01")&(data[date_field]<=f"{near_3y}-12-31")]

        for f in cal_fields:
            data[f] = data[f].pct_change().fillna(0)
            data[f] = (data[f] + 1).cumprod()
        return data


# 补充数据
def additional_data(data, strage_path, strage, account_id, selected_metrics):
    deal = False
    for metric in ['仓位占比', '交易次数', '累计换手率']:
        if metric in selected_metrics:
            deal = True
            break
    if not deal:
        return data
    real_data_path = f"{strage_path}/data".replace('//', '/')
    if account_id == '全部账户汇总':
        all_fns = os.listdir(real_data_path)
        all_ids = [x.split('_')[0] for x in all_fns if '_order' in x]
        count_asset_df = pd.DataFrame()
        count_order_list = []
        for real_id in all_ids:
            asset_df = pd.read_feather(f'{real_data_path}/{real_id}_asset.fea').set_index('date')
            if count_asset_df.empty:
                count_asset_df['balance'] = asset_df['balance']
                count_asset_df['available'] = asset_df['available']
            else:
                count_asset_df['balance'] += asset_df['balance']
                count_asset_df['available'] += asset_df['available']
            if os.path.isfile(f'{real_data_path}/{real_id}_order.fea'):
                order_df = pd.read_feather(f'{real_data_path}/{real_id}_order.fea')
                count_order_list.append(order_df)
        if len(count_order_list) > 0:
            count_order_df = pd.concat(count_order_list)
        else:
            count_order_df = None
        data = data.set_index('date')
        # 仓位占比
        if '仓位占比' in selected_metrics:
            data['仓位占比'] = round((1 - count_asset_df['available'] / count_asset_df['balance'])*100, 2)
        if '交易次数' in selected_metrics:
            if count_order_df is None:
                data['交易次数'] = 0
            else:
                count_order_df['交易次数'] = 1
                count_order_df = count_order_df.reset_index(drop=True)
                data['交易次数'] = count_order_df.groupby('date')['交易次数'].sum()
                data['交易次数'] = data['交易次数'].fillna(0).cumsum()
        return data.reset_index()

    elif '汇总' in account_id:
        all_fns = os.listdir(real_data_path)
        all_ids = [x.split('_')[0] for x in all_fns if '_order' in x]
        if '期货' in '汇总':
            account_type = 'fut_opt'
        else:
            account_type = 'stock_fund'
        count_asset_df = pd.DataFrame()
        count_order_list = []
        for real_id in all_ids:
            asset_df = pd.read_feather(f'{real_data_path}/{real_id}_asset.fea').set_index('date')
            if account_type != asset_df.iloc[0].account_type:
                continue
            if count_asset_df.empty:
                count_asset_df['balance'] = asset_df['balance']
                count_asset_df['available'] = asset_df['available']
            else:
                count_asset_df['balance'] += asset_df['balance']
                count_asset_df['available'] += asset_df['available']
            if os.path.isfile(f'{real_data_path}/{real_id}_order.fea'):
                order_df = pd.read_feather(f'{real_data_path}/{real_id}_order.fea')
                count_order_list.append(order_df)
        if len(count_order_list) > 0:
            count_order_df = pd.concat(count_order_list)
        else:
            count_order_df = None
        data = data.set_index('date')
        # 仓位占比
        if '仓位占比' in selected_metrics:
            data['仓位占比'] = round((1 - count_asset_df['available'] / count_asset_df['balance'])*100, 2)
        if '交易次数' in selected_metrics:
            if count_order_df is None:
                data['交易次数'] = 0
            else:
                count_order_df['交易次数'] = 1
                count_order_df = count_order_df.reset_index(drop=True)
                data['交易次数'] = count_order_df.groupby('date')['交易次数'].sum()
                data['交易次数'] = data['交易次数'].fillna(0).cumsum()
        if '累计换手率' in selected_metrics:
            if count_order_df is None:
                data['累计换手率'] = 0
            else:
                tu_rate = count_order_df.groupby('date')['order_values'].sum() / count_asset_df['balance']
                data['累计换手率'] = tu_rate
                data['累计换手率'] = data['累计换手率'].fillna(0).cumsum()
        return data.reset_index()
            
    else:
        real_id = account_id.split('_')[-1]
        asset_df = pd.read_feather(f'{real_data_path}/{real_id}_asset.fea').set_index('date')
        if len(asset_df) == 0:
            return data
        if os.path.isfile(f'{real_data_path}/{real_id}_order.fea'):
            order_df = pd.read_feather(f'{real_data_path}/{real_id}_order.fea')
        else:
            order_df = None

        account_type = asset_df.iloc[0].account_type
        if account_type == 'bank':
            return data
        data = data.set_index('date')
        # 仓位占比
        if '仓位占比' in selected_metrics:
            data['仓位占比'] = round((1 - asset_df['available'] / asset_df['balance'])*100, 2)
        if '交易次数' in selected_metrics:
            if order_df is None:
                data['交易次数'] = 0
            else:
                order_df['交易次数'] = 1
                data['交易次数'] = count_order_df.groupby('date')['交易次数'].sum()
                data['交易次数'] = data['交易次数'].fillna(0).cumsum()
        if '累计换手率' in selected_metrics:
            if order_df is None:
                data['累计换手率'] = 0
            else:
                tu_rate = order_df.groupby('date')['order_values'].sum() / asset_df['balance']
                data['累计换手率'] = tu_rate
                data['累计换手率'] = data['累计换手率'].fillna(0).cumsum()
        return data.reset_index()
                
            
@app.callback(
    [Output('dynamic-graphs', 'children'),
     Output('tempdata', 'data')],
    [Input('store', 'data'),
     Input('data-pth', 'value'),
     Input('strage-dropdown', 'value'),
     Input('account-id-dropdown', 'value'),
     Input('metrics-checklist', 'value'),
     Input('period-dropdown', 'value')]
)
def update_dynamic_graphs(store, data_path, strage, account_id, selected_metrics, period):
    user = store['user']

    children = []
    if not data_path or not account_id:
        return children
    strage_path = get_real_path(user, data_path, strage)
    # 使用选择的指标从数据文件中获取数据
    # 注意: 您需要根据您的数据格式和文件结构调整下面的代码
    daily_return_df_fn = f"{strage_path}/show_data/{account_id}.fea"
    if not strage_path or not account_id:
        return children

    file_path = strage_path.replace('\\', '/')
    # 检查文件是否存在
    if not os.path.exists(daily_return_df_fn):
        fig = make_subplots(rows=1, cols=1, shared_xaxes=True)
        # 如果不存在 _daily_return.fea 文件，尝试加载 balance 文件
        balance_file_path = f"{file_path}/data/{account_id}_asset.fea"
        if not os.path.exists(balance_file_path):
            return children  # 如果都不存在，返回空的折线图
        # 读取 balance 文件，并绘制 balance 折线图
        balance_df = pd.read_feather(balance_file_path)
        balance_df = period_deal(period, balance_df, ['balance'])
        trace = go.Scatter(x=balance_df['date'], y=balance_df['balance'], mode='lines', name=f'账户：{account_id} 资产走势图')
        fig.add_trace(trace, row=1, col=1)
        sum_hign = 400
    else:
        if selected_metrics is None:
            selected_metrics = []
        # 计算子图的总数
        num_subplots = len(selected_metrics) + 1
        # 创建 specs 列表
        # 创建高度列表
        heights = [400] + [200] * (num_subplots - 1)
        sum_hign = sum(heights)
        titles = ['净值走势'] + selected_metrics

        fig = make_subplots(rows=len(selected_metrics)+1, cols=1, shared_xaxes=True, subplot_titles=tuple(titles),
                            row_heights=heights, vertical_spacing=0.1)
        # 如果存在 _daily_return.fea 文件，读取并绘制策略收益、基准收益和超额收益
        daily_return_df = pd.read_feather(daily_return_df_fn)
        daily_return_df = additional_data(daily_return_df, strage_path, strage, account_id, selected_metrics)
        all_columns = daily_return_df.columns.tolist()
        son_account_list = [x for x in all_columns if '_净值' in x]
        if account_id == '全部账户汇总':
            color_dict = {'策略净值': dict(color='red'), '基准指数': dict(color='#93a5cf'),
                          '超额收益': dict(color='#7046aa', dash='dash')}
            if len(son_account_list) > 1:
                if '股票账户_净值' in all_columns:
                    color_dict['股票账户_净值'] = dict(color='#fcc5e4')
                if '期货账户_净值' in all_columns:
                    color_dict['期货账户_净值'] = dict(color='#ff7882')
        else:
            color_dict = {'策略净值': dict(color='red'), '基准指数': dict(color='#93a5cf'), '超额收益': dict(color='#7046aa', dash='dash')}
        daily_return_df = period_deal(period, daily_return_df, list(color_dict.keys()))
        for name, line_conf in color_dict.items():
            # 添加策略净值、基准指数和超额收益的轨迹，并设置线的颜色和样式
            trace = go.Scatter(x=daily_return_df['date'], y=daily_return_df[name], mode='lines', name=name,
                                line=line_conf)
            fig.add_trace(trace, row=1, col=1)
        print(account_id)
        # 绘制最大回撤区域
        performance_df = get_performance_df(user, data_path, strage, account_id, sheet_name=period)
        color_dict = {'策略': 'red', '基准': '#93a5cf',
                      '超额': '#7046aa'}
        for name, color in color_dict.items():
            # 获取最大回撤的开始和结束日期
            start_date = str(performance_df.loc[7, name])
            end_date = str(performance_df.loc[8, name])
            # 找到这些日期对应的y值
            mask = (daily_return_df['date'] >= start_date) & (daily_return_df['date'] <= end_date)
            name2name = {'策略': '策略净值', '基准': '基准指数', '超额': '超额收益'}
            y_values = daily_return_df[mask][name2name[name]]
            # 创建一个填充的Scatter trace
            fill_trace = go.Scatter(
                x=daily_return_df['date'][mask],
                y=y_values,
                fill='toself',
                fillcolor=hex_or_name_to_rgba(color, 0.5),  # red with 50% opacity
                line=dict(color=hex_or_name_to_rgba(color, 1)),  # line color invisible
                name=f'{name}_最大回撤',  # customize the name as you want
                showlegend=True,
                hoverinfo = 'none'
            )
            fig.add_trace(fill_trace, row=1, col=1)
        fig.update_xaxes(row=1, col=1, showticklabels=True, tickformat='%Y-%m-%d', ticklabelmode="period")

    showlegend = (account_id == '全部账户汇总')

    metric_colors = ['#FF0000', '#000000', '#FE00FF']
    
    # 为每个选择的 metric 添加一行子图
    for i, metric in enumerate(selected_metrics, start=1):
        ii = 0
        
        if metric not in ['成交金额']:
            type_list = [x for x in all_columns if metric in x]
            for type_ in type_list:
                trace = go.Scatter(x=daily_return_df['date'], y=daily_return_df[type_], mode='lines', name=type_, showlegend=showlegend, line=dict(color=metric_colors[ii]))
                fig.add_trace(trace, row=i + 1, col=1)
                ii += 1
                
        else:
            type_list = [x for x in all_columns if metric in x]
            for type_ in type_list:
                trace = go.Bar(x=daily_return_df['date'], y=daily_return_df[type_], name=type_, showlegend=showlegend, marker_color=metric_colors[ii])
                fig.add_trace(trace, row=i+1, col=1)
                ii += 1
                
        fig.update_xaxes(row=i+1, col=1, showticklabels=True, tickformat='%Y-%m-%d', ticklabelmode="period")
    fig.update_layout(bargap=0)
    # 设置光标样式为长十字线（crosshair）
    fig.update_layout(hovermode='x')
    # 计算刻度间隔，确保在 x 轴上只显示30个数据点
    num_ticks = 10
    # 确保第一个和最后一个数据点被包含在刻度值中
    tick_values = [0, len(daily_return_df) - 1]

    # 如果需要的刻度数量大于2（因为已经包含了首尾两点），则添加中间的刻度
    if num_ticks > 2:
        # 使用numpy的linspace来计算中间的刻度
        middle_ticks = np.linspace(0, len(daily_return_df) - 1, num_ticks - 2).astype(int)[1:-1]  # 去除首尾
        tick_values = np.concatenate(([0], middle_ticks, [len(daily_return_df) - 1]))

    # 设置 x 轴的刻度值
    fig.update_xaxes(type='category', tickvals=tick_values)
    children.append(dcc.Graph(figure=fig, style={'height': f'{sum_hign}px'}))
    return children, daily_return_df.to_dict('records')


# 获取业绩评价指标
def get_performance_df(user, data_path, strage, account_id, sheet_name='全部'):
    data_path = get_real_path(user, data_path, strage)
    if account_id in ['全部账户汇总', '总账户']:
        fn = f"{data_path}/performance.xlsx"
        df = pd.read_excel(fn, sheet_name=sheet_name).rename({'Unnamed: 0': ''}, axis=1)
        # 将 DataFrame 转换为字典列表以更新 DataTable
        return df
    elif '汇总' in account_id:
        fn = f"{data_path}/performance/{account_id}.xlsx"
        df = pd.read_excel(fn, sheet_name=sheet_name).rename({'Unnamed: 0': ''}, axis=1)
        # 将 DataFrame 转换为字典列表以更新 DataTable
        return df
    else:
        account_id = account_id.split('期货账户_')[-1].split('股票账户_')[-1]
        fn = f"{data_path}/performance/account_{account_id}.xlsx"
        df = pd.read_excel(fn, sheet_name=sheet_name).rename({'Unnamed: 0': ''}, axis=1)
        # 将 DataFrame 转换为字典列表以更新 DataTable
        return df


def hex_or_name_to_rgba(color, alpha=1.0):
    if color.startswith('#'):
        # Convert hex to RGB, and then to RGBA
        color = colors.hex2color(color)
        return f'rgba({int(color[0] * 255)}, {int(color[1] * 255)}, {int(color[2] * 255)}, {alpha})'
    elif color in colors.CSS4_COLORS:
        # Convert named color to hex, and then to RGB, and then to RGBA
        color = colors.hex2color(colors.CSS4_COLORS[color])
        return f'rgba({int(color[0] * 255)}, {int(color[1] * 255)}, {int(color[2] * 255)}, {alpha})'
    else:
        raise ValueError(f"Invalid color: {color}")

@app.callback(
    Output('table', 'data'),
    [Input('store', 'data'),
    Input('data-pth', 'value'),
    Input('strage-dropdown', 'value'),
     Input('account-id-dropdown', 'value'),
     Input('period-dropdown', 'value')]  # 假设你想根据用户选中的账户ID来更新表格
)
def update_table(store, data_path, strage, account_id, sheet_name):
    user = store['user']
    if not data_path or not account_id or not user:
        return {}
    return get_performance_df(user, data_path, strage, account_id, sheet_name).to_dict('records')

@app.callback(
    Output('run-info', 'data'),
    [Input('store', 'data'),
     Input('data-pth', 'value'),
     Input('strage-dropdown', 'value'),
     Input('tempdata', 'data'),
     Input('period-dropdown', 'value')
     ]
)
def update_config_display(store, data_path, strage, temp_dict, period):
    user = store['user']
    if not data_path or not user:
        return {}
    config_file_path = get_real_path(user, data_path, strage)
    try:
        with open(f"{config_file_path}/run_info/config.json", 'r') as file:
            config_data = json.load(file)
    except Exception as e:
        return {}

    rows = {}
    for key in ['start_date', 'end_date', 'base_index', 'freq', 'price_mode', 'super_trader']:
        rows[key] = {'values': config_data[key]}
    if period != '全部':
        temp_df = pd.DataFrame(temp_dict)
        dates = temp_df['date'].sort_values().to_list()
        rows['start_date'] = {'values': dates[0]}
        rows['end_date'] = {'values': dates[-1]}
    df = pd.DataFrame(rows)
    return df.to_dict('records')

@app.callback(
    Output('dynamic-title', 'children'),
    Input('data-pth', 'value')
)
def update_title(pth_value):
    bt_name = pth_value.split('/')[-1]
    return bt_name

# 启动应用
if __name__ == '__main__':
    app.run_server(host='0.0.0.0',debug=False)
    # app.run_server(host='0.0.0.0',debug=True, port="555")
