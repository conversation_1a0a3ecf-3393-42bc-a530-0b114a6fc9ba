# README

This directory contains a collection of example apps that demonstrate usage of `dash-bootstrap-components`. Many of the apps have dependencies that are not dependencies of `dash-bootstrap-components` itself, each app has its own `requirements.txt` file containing these dependencies. You can easily install them with `pip install -r requirements.txt`.

Apps are written for Python 3.6+, they may happen to work with older versions of Python, but this is not tested or guaranteed.
