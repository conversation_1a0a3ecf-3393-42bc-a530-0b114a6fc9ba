/*! For license information please see dash_bootstrap_components.min.js.LICENSE.txt */
(()=>{var e=[e=>{"use strict";e.exports=window.React},(e,t)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function s(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=a(e,o(n)))}return e}function o(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return s.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)r.call(e,n)&&e[n]&&(t=a(t,n));return t}function a(e,t){return t?e?e+" "+t:e+t:e}e.exports?(s.default=s,e.exports=s):void 0===(n=function(){return s}.apply(t,[]))||(e.exports=n)}()},e=>{"use strict";e.exports=function(){}},(e,t,n)=>{var r=n(4).default;function s(){"use strict";e.exports=s=function(){return n},e.exports.__esModule=!0,e.exports.default=e.exports;var t,n={},o=Object.prototype,a=o.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},l="function"==typeof Symbol?Symbol:{},c=l.iterator||"@@iterator",u=l.asyncIterator||"@@asyncIterator",d=l.toStringTag||"@@toStringTag";function f(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{f({},"")}catch(t){f=function(e,t,n){return e[t]=n}}function p(e,t,n,r){var s=t&&t.prototype instanceof x?t:x,o=Object.create(s.prototype),a=new D(r||[]);return i(o,"_invoke",{value:C(e,n,a)}),o}function m(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}n.wrap=p;var b="suspendedStart",y="suspendedYield",g="executing",v="completed",h={};function x(){}function O(){}function _(){}var w={};f(w,c,(function(){return this}));var N=Object.getPrototypeOf,j=N&&N(N(L([])));j&&j!==o&&a.call(j,c)&&(w=j);var E=_.prototype=x.prototype=Object.create(w);function k(e){["next","throw","return"].forEach((function(t){f(e,t,(function(e){return this._invoke(t,e)}))}))}function P(e,t){function n(s,o,i,l){var c=m(e[s],e,o);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==r(d)&&a.call(d,"__await")?t.resolve(d.__await).then((function(e){n("next",e,i,l)}),(function(e){n("throw",e,i,l)})):t.resolve(d).then((function(e){u.value=e,i(u)}),(function(e){return n("throw",e,i,l)}))}l(c.arg)}var s;i(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,s){n(e,r,t,s)}))}return s=s?s.then(o,o):o()}})}function C(e,n,r){var s=b;return function(o,a){if(s===g)throw Error("Generator is already running");if(s===v){if("throw"===o)throw a;return{value:t,done:!0}}for(r.method=o,r.arg=a;;){var i=r.delegate;if(i){var l=T(i,r);if(l){if(l===h)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(s===b)throw s=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);s=g;var c=m(e,n,r);if("normal"===c.type){if(s=r.done?v:y,c.arg===h)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(s=v,r.method="throw",r.arg=c.arg)}}}function T(e,n){var r=n.method,s=e.iterator[r];if(s===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,T(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),h;var o=m(s,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,h;var a=o.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,h):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,h)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function R(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function D(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function L(e){if(e||""===e){var n=e[c];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var s=-1,o=function n(){for(;++s<e.length;)if(a.call(e,s))return n.value=e[s],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(r(e)+" is not iterable")}return O.prototype=_,i(E,"constructor",{value:_,configurable:!0}),i(_,"constructor",{value:O,configurable:!0}),O.displayName=f(_,d,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===O||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,f(e,d,"GeneratorFunction")),e.prototype=Object.create(E),e},n.awrap=function(e){return{__await:e}},k(P.prototype),f(P.prototype,u,(function(){return this})),n.AsyncIterator=P,n.async=function(e,t,r,s,o){void 0===o&&(o=Promise);var a=new P(p(e,t,r,s),o);return n.isGeneratorFunction(t)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},k(E),f(E,d,"Generator"),f(E,c,(function(){return this})),f(E,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},n.values=L,D.prototype={constructor:D,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(R),!e)for(var n in this)"t"===n.charAt(0)&&a.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function r(r,s){return i.type="throw",i.arg=e,n.next=r,s&&(n.method="next",n.arg=t),!!s}for(var s=this.tryEntries.length-1;s>=0;--s){var o=this.tryEntries[s],i=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var l=a.call(o,"catchLoc"),c=a.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&a.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var s=r;break}}s&&("break"===e||"continue"===e)&&s.tryLoc<=t&&t<=s.finallyLoc&&(s=null);var o=s?s.completion:{};return o.type=e,o.arg=t,s?(this.method="next",this.next=s.finallyLoc,h):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),R(n),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var s=r.arg;R(n)}return s}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:L(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),h}},n}e.exports=s,e.exports.__esModule=!0,e.exports.default=e.exports},e=>{function t(n){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},e=>{"use strict";e.exports=function(e,t,n,r,s,o,a,i){if(!e){var l;if(void 0===t)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[n,r,s,o,a,i],u=0;(l=new Error(t.replace(/%s/g,(function(){return c[u++]})))).name="Invariant Violation"}throw l.framesToPop=1,l}}},(e,t,n)=>{"use strict";var r=n(7);function s(){}function o(){}o.resetWarningCache=s,e.exports=function(){function e(e,t,n,s,o,a){if(a!==r){var i=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:s};return n.PropTypes=n,n}},e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},(e,t,n)=>{"use strict";var r=n(9);e.exports=function(e){var t=typeof e;if("string"===t){var n=e;if(0==(e=+e)&&r(n))return!1}else if("number"!==t)return!1;return e-e<1}},e=>{"use strict";e.exports=function(e){for(var t,n=e.length,r=0;r<n;r++)if(((t=e.charCodeAt(r))<9||t>13)&&32!==t&&133!==t&&160!==t&&5760!==t&&6158!==t&&(t<8192||t>8205)&&8232!==t&&8233!==t&&8239!==t&&8287!==t&&8288!==t&&12288!==t&&65279!==t)return!1;return!0}},(e,t,n)=>{var r;window,e.exports=(r=n(0),function(e){var t={};function n(r){if(t[r])return t[r].exports;var s=t[r]={i:r,l:!1,exports:{}};return e[r].call(s.exports,s,s.exports,n),s.l=!0,s.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var s in e)n.d(r,s,function(t){return e[t]}.bind(null,s));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=1)}([function(e,t){e.exports=r},function(e,t,n){"use strict";n.r(t),n.d(t,"asyncDecorator",(function(){return a})),n.d(t,"inheritAsyncDecorator",(function(){return i})),n.d(t,"isReady",(function(){return l})),n.d(t,"History",(function(){return d}));var r=n(0);function s(e,t,n,r,s,o,a){try{var i=e[o](a),l=i.value}catch(e){return void n(e)}i.done?t(l):Promise.resolve(l).then(r,s)}function o(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){s(a,r,o,i,l,"next",e)}function l(e){s(a,r,o,i,l,"throw",e)}i(void 0)}))}}var a=function(e,t){var n,s={isReady:new Promise((function(e){n=e})),get:Object(r.lazy)((function(){return Promise.resolve(t()).then((function(e){return setTimeout(o(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n(!0);case 2:s.isReady=!0;case 3:case"end":return e.stop()}}),e)}))),0),e}))}))};return Object.defineProperty(e,"_dashprivate_isLazyComponentReady",{get:function(){return s.isReady}}),s.get},i=function(e,t){Object.defineProperty(e,"_dashprivate_isLazyComponentReady",{get:function(){return l(t)}})},l=function(e){return e&&e._dashprivate_isLazyComponentReady};function c(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var u="_dashprivate_historychange",d=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n;return t=e,n=[{key:"dispatchChangeEvent",value:function(){window.dispatchEvent(new CustomEvent(u))}},{key:"onChange",value:function(e){return window.addEventListener(u,e),function(){return window.removeEventListener(u,e)}}}],null&&c(t.prototype,null),n&&c(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}()}]))},(e,t,n)=>{var r=n(3)();e.exports=r;try{regeneratorRuntime=r}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=r:Function("r","regeneratorRuntime = r")(r)}},(e,t,n)=>{e.exports=n(6)()},(e,t,n)=>{"use strict";e.exports=n(15)},,(e,t,n)=>{"use strict";var r=n(0),s=Symbol.for("react.element"),o=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,i=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,n){var r,o={},c=null,u=null;for(r in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)a.call(t,r)&&!l.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===o[r]&&(o[r]=t[r]);return{$$typeof:s,type:e,key:c,ref:u,props:o,_owner:i.current}}t.Fragment=o,t.jsx=c,t.jsxs=c}],t={};function n(r){var s=t[r];if(void 0!==s)return s.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};(()=>{"use strict";function e(){return e=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.apply(null,arguments)}function t(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function s(e,n){if(e){if("string"==typeof e)return t(e,n);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?t(e,n):void 0}}function o(e){return function(e){if(Array.isArray(e))return t(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||s(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}function i(e,t){if(null==e)return{};var n,r,s=a(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(s[n]=e[n])}return s}n.r(r),n.d(r,{Accordion:()=>Pt,AccordionItem:()=>St,Alert:()=>bn,Badge:()=>kn,Breadcrumb:()=>Ln,Button:()=>Fn,ButtonGroup:()=>Un,Card:()=>hr,CardBody:()=>_r,CardFooter:()=>jr,CardGroup:()=>Tr,CardHeader:()=>Dr,CardImg:()=>Ar,CardImgOverlay:()=>Fr,CardLink:()=>Hr,Carousel:()=>ns,Checkbox:()=>ss,Checklist:()=>fs,Col:()=>Os,Collapse:()=>Ns,Container:()=>Cs,DropdownMenu:()=>zi,DropdownMenuItem:()=>Ui,Fade:()=>Yi,Form:()=>Cl,FormFeedback:()=>Rl,FormFloating:()=>Il,FormText:()=>Bl,Input:()=>Xl,InputGroup:()=>rc,InputGroupText:()=>ac,Label:()=>fc,ListGroup:()=>hc,ListGroupItem:()=>Nc,Modal:()=>fu,ModalBody:()=>gu,ModalFooter:()=>_u,ModalHeader:()=>Cu,ModalTitle:()=>Iu,Nav:()=>Bu,NavItem:()=>Id,NavLink:()=>Fd,Navbar:()=>Od,NavbarBrand:()=>Nd,NavbarSimple:()=>Rd,NavbarToggler:()=>kd,Offcanvas:()=>Hd,Pagination:()=>tf,Placeholder:()=>df,Popover:()=>Uf,PopoverBody:()=>Vf,PopoverHeader:()=>Xf,Progress:()=>lp,RadioButton:()=>mp,RadioItems:()=>fp,Row:()=>Op,Select:()=>Np,Spinner:()=>Sp,Stack:()=>$p,Switch:()=>Fp,Tab:()=>zp,Table:()=>ym,Tabs:()=>dm,Textarea:()=>hm,Toast:()=>Rm,Tooltip:()=>Im});var l=n(0),c=n.n(l),u=n(12),d=n.n(u);function f(e){return null!=e&&"object"==typeof e&&!0===e["@@functional/placeholder"]}function p(e){return function t(n){return 0===arguments.length||f(n)?t:e.apply(this,arguments)}}function m(e){return function t(n,r){switch(arguments.length){case 0:return t;case 1:return f(n)?t:p((function(t){return e(n,t)}));default:return f(n)&&f(r)?t:f(n)?p((function(t){return e(t,r)})):f(r)?p((function(t){return e(n,t)})):e(n,r)}}}const b=m((function(e,t){for(var n={},r={},s=0,o=e.length;s<o;)r[e[s]]=1,s+=1;for(var a in t)r.hasOwnProperty(a)||(n[a]=t[a]);return n}));var y=n(1),g=n.n(y);function v(e){return"default"+e.charAt(0).toUpperCase()+e.substr(1)}function h(e){var t=function(e){if("object"!=typeof e||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}function x(t,n){return Object.keys(n).reduce((function(r,s){var o,i=r,c=i[v(s)],u=i[s],d=a(i,[v(s),s].map(h)),f=n[s],p=function(e,t,n){var r=(0,l.useRef)(void 0!==e),s=(0,l.useState)(t),o=s[0],a=s[1],i=void 0!==e,c=r.current;return r.current=i,!i&&c&&o!==t&&a(t),[i?e:o,(0,l.useCallback)((function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),s=1;s<t;s++)r[s-1]=arguments[s];n&&n.apply(void 0,[e].concat(r)),a(e)}),[n])]}(u,c,t[f]),m=p[0],b=p[1];return e({},d,((o={})[s]=m,o[f]=b,o))}),t)}n(5);var O=n(13);const _=["xxl","xl","lg","md","sm","xs"],w=l.createContext({prefixes:{},breakpoints:_,minBreakpoint:"xs"}),{Consumer:N,Provider:j}=w;function E(e,t){const{prefixes:n}=(0,l.useContext)(w);return e||n[t]||t}function k(){const{breakpoints:e}=(0,l.useContext)(w);return e}function P(){const{minBreakpoint:e}=(0,l.useContext)(w);return e}function C(){const{dir:e}=(0,l.useContext)(w);return"rtl"===e}function T(e){return e&&e.ownerDocument||document}var S=/([A-Z])/g,R=/^ms-/;function D(e){return function(e){return e.replace(S,"-$1").toLowerCase()}(e).replace(R,"-ms-")}var L=/^((translate|rotate|scale)(X|Y|Z|3d)?|matrix(3d)?|perspective|skew(X|Y)?)$/i;const I=function(e,t){var n="",r="";if("string"==typeof t)return e.style.getPropertyValue(D(t))||function(e,t){return function(e){var t=T(e);return t&&t.defaultView||window}(e).getComputedStyle(e,t)}(e).getPropertyValue(D(t));Object.keys(t).forEach((function(s){var o=t[s];o||0===o?function(e){return!(!e||!L.test(e))}(s)?r+=s+"("+o+") ":n+=D(s)+": "+o+";":e.style.removeProperty(D(s))})),r&&(n+="transform: "+r+";"),e.style.cssText+=";"+n};function A(e,t){return A=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},A(e,t)}const $=window.ReactDOM;var M=n.n($);const F=c().createContext(null);var B="unmounted",z="exited",H="entering",K="entered",U="exiting",W=function(e){var t,n;function r(t,n){var r;r=e.call(this,t,n)||this;var s,o=n&&!n.isMounting?t.enter:t.appear;return r.appearStatus=null,t.in?o?(s=z,r.appearStatus=H):s=K:s=t.unmountOnExit||t.mountOnEnter?B:z,r.state={status:s},r.nextCallback=null,r}n=e,(t=r).prototype=Object.create(n.prototype),t.prototype.constructor=t,A(t,n),r.getDerivedStateFromProps=function(e,t){return e.in&&t.status===B?{status:z}:null};var s=r.prototype;return s.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},s.componentDidUpdate=function(e){var t=null;if(e!==this.props){var n=this.state.status;this.props.in?n!==H&&n!==K&&(t=H):n!==H&&n!==K||(t=U)}this.updateStatus(!1,t)},s.componentWillUnmount=function(){this.cancelNextCallback()},s.getTimeouts=function(){var e,t,n,r=this.props.timeout;return e=t=n=r,null!=r&&"number"!=typeof r&&(e=r.exit,t=r.enter,n=void 0!==r.appear?r.appear:t),{exit:e,enter:t,appear:n}},s.updateStatus=function(e,t){if(void 0===e&&(e=!1),null!==t)if(this.cancelNextCallback(),t===H){if(this.props.unmountOnExit||this.props.mountOnEnter){var n=this.props.nodeRef?this.props.nodeRef.current:M().findDOMNode(this);n&&function(e){e.scrollTop}(n)}this.performEnter(e)}else this.performExit();else this.props.unmountOnExit&&this.state.status===z&&this.setState({status:B})},s.performEnter=function(e){var t=this,n=this.props.enter,r=this.context?this.context.isMounting:e,s=this.props.nodeRef?[r]:[M().findDOMNode(this),r],o=s[0],a=s[1],i=this.getTimeouts(),l=r?i.appear:i.enter;e||n?(this.props.onEnter(o,a),this.safeSetState({status:H},(function(){t.props.onEntering(o,a),t.onTransitionEnd(l,(function(){t.safeSetState({status:K},(function(){t.props.onEntered(o,a)}))}))}))):this.safeSetState({status:K},(function(){t.props.onEntered(o)}))},s.performExit=function(){var e=this,t=this.props.exit,n=this.getTimeouts(),r=this.props.nodeRef?void 0:M().findDOMNode(this);t?(this.props.onExit(r),this.safeSetState({status:U},(function(){e.props.onExiting(r),e.onTransitionEnd(n.exit,(function(){e.safeSetState({status:z},(function(){e.props.onExited(r)}))}))}))):this.safeSetState({status:z},(function(){e.props.onExited(r)}))},s.cancelNextCallback=function(){null!==this.nextCallback&&(this.nextCallback.cancel(),this.nextCallback=null)},s.safeSetState=function(e,t){t=this.setNextCallback(t),this.setState(e,t)},s.setNextCallback=function(e){var t=this,n=!0;return this.nextCallback=function(r){n&&(n=!1,t.nextCallback=null,e(r))},this.nextCallback.cancel=function(){n=!1},this.nextCallback},s.onTransitionEnd=function(e,t){this.setNextCallback(t);var n=this.props.nodeRef?this.props.nodeRef.current:M().findDOMNode(this),r=null==e&&!this.props.addEndListener;if(n&&!r){if(this.props.addEndListener){var s=this.props.nodeRef?[this.nextCallback]:[n,this.nextCallback],o=s[0],a=s[1];this.props.addEndListener(o,a)}null!=e&&setTimeout(this.nextCallback,e)}else setTimeout(this.nextCallback,0)},s.render=function(){var e=this.state.status;if(e===B)return null;var t=this.props,n=t.children,r=(t.in,t.mountOnEnter,t.unmountOnExit,t.appear,t.enter,t.exit,t.timeout,t.addEndListener,t.onEnter,t.onEntering,t.onEntered,t.onExit,t.onExiting,t.onExited,t.nodeRef,a(t,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]));return c().createElement(F.Provider,{value:null},"function"==typeof n?n(e,r):c().cloneElement(c().Children.only(n),r))},r}(c().Component);function q(){}W.contextType=F,W.propTypes={},W.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:q,onEntering:q,onEntered:q,onExit:q,onExiting:q,onExited:q},W.UNMOUNTED=B,W.EXITED=z,W.ENTERING=H,W.ENTERED=K,W.EXITING=U;const V=W;function G(e){return"Escape"===e.code||27===e.keyCode}function Y(e){if(!e||"function"==typeof e)return null;const{major:t}=function(){const e=l.version.split(".");return{major:+e[0],minor:+e[1],patch:+e[2]}}();return t>=19?e.props.ref:e.ref}const X=!("undefined"==typeof window||!window.document||!window.document.createElement);var J=!1,Z=!1;try{var Q={get passive(){return J=!0},get once(){return Z=J=!0}};X&&(window.addEventListener("test",Q,Q),window.removeEventListener("test",Q,!0))}catch(e){}const ee=function(e,t,n,r){if(r&&"boolean"!=typeof r&&!Z){var s=r.once,o=r.capture,a=n;!Z&&s&&(a=n.__once||function e(r){this.removeEventListener(t,e,o),n.call(this,r)},n.__once=a),e.addEventListener(t,a,J?r:o)}e.addEventListener(t,n,r)},te=function(e,t,n,r){var s=r&&"boolean"!=typeof r?r.capture:r;e.removeEventListener(t,n,s),n.__once&&e.removeEventListener(t,n.__once,s)},ne=function(e,t,n,r){return ee(e,t,n,r),function(){te(e,t,n,r)}};function re(e,t,n,r){var s,o;null==n&&(o=-1===(s=I(e,"transitionDuration")||"").indexOf("ms")?1e3:1,n=parseFloat(s)*o||0);var a=function(e,t,n){void 0===n&&(n=5);var r=!1,s=setTimeout((function(){r||function(e,t,n,r){if(void 0===n&&(n=!1),void 0===r&&(r=!0),e){var s=document.createEvent("HTMLEvents");s.initEvent("transitionend",n,r),e.dispatchEvent(s)}}(e,0,!0)}),t+n),o=ne(e,"transitionend",(function(){r=!0}),{once:!0});return function(){clearTimeout(s),o()}}(e,n,r),i=ne(e,"transitionend",t);return function(){a(),i()}}function se(e,t){const n=I(e,t)||"",r=-1===n.indexOf("ms")?1e3:1;return parseFloat(n)*r}function oe(e,t){const n=se(e,"transitionDuration"),r=se(e,"transitionDelay"),s=re(e,(n=>{n.target===e&&(s(),t(n))}),n+r)}const ae=function(...e){return e.filter((e=>null!=e)).reduce(((e,t)=>{if("function"!=typeof t)throw new Error("Invalid Argument Type, must only provide functions, undefined, or null.");return null===e?t:function(...n){e.apply(this,n),t.apply(this,n)}}),null)};function ie(e){e.offsetHeight}const le=e=>e&&"function"!=typeof e?t=>{e.current=t}:e,ce=function(e,t){return(0,l.useMemo)((()=>function(e,t){const n=le(e),r=le(t);return e=>{n&&n(e),r&&r(e)}}(e,t)),[e,t])};function ue(e){return e&&"setState"in e?M().findDOMNode(e):null!=e?e:null}const de=c().forwardRef((({onEnter:e,onEntering:t,onEntered:n,onExit:r,onExiting:s,onExited:o,addEndListener:a,children:i,childRef:u,...d},f)=>{const p=(0,l.useRef)(null),m=ce(p,u),b=e=>{m(ue(e))},y=e=>t=>{e&&p.current&&e(p.current,t)},g=(0,l.useCallback)(y(e),[e]),v=(0,l.useCallback)(y(t),[t]),h=(0,l.useCallback)(y(n),[n]),x=(0,l.useCallback)(y(r),[r]),_=(0,l.useCallback)(y(s),[s]),w=(0,l.useCallback)(y(o),[o]),N=(0,l.useCallback)(y(a),[a]);return(0,O.jsx)(V,{ref:f,...d,onEnter:g,onEntered:h,onEntering:v,onExit:x,onExited:w,onExiting:_,addEndListener:N,nodeRef:p,children:"function"==typeof i?(e,t)=>i(e,{...t,ref:b}):c().cloneElement(i,{ref:b})})})),fe={height:["marginTop","marginBottom"],width:["marginLeft","marginRight"]};function pe(e,t){const n=t[`offset${e[0].toUpperCase()}${e.slice(1)}`],r=fe[e];return n+parseInt(I(t,r[0]),10)+parseInt(I(t,r[1]),10)}const me={[z]:"collapse",[U]:"collapsing",[H]:"collapsing",[K]:"collapse show"},be=c().forwardRef((({onEnter:e,onEntering:t,onEntered:n,onExit:r,onExiting:s,className:o,children:a,dimension:i="height",in:u=!1,timeout:d=300,mountOnEnter:f=!1,unmountOnExit:p=!1,appear:m=!1,getDimensionValue:b=pe,...y},v)=>{const h="function"==typeof i?i():i,x=(0,l.useMemo)((()=>ae((e=>{e.style[h]="0"}),e)),[h,e]),_=(0,l.useMemo)((()=>ae((e=>{const t=`scroll${h[0].toUpperCase()}${h.slice(1)}`;e.style[h]=`${e[t]}px`}),t)),[h,t]),w=(0,l.useMemo)((()=>ae((e=>{e.style[h]=null}),n)),[h,n]),N=(0,l.useMemo)((()=>ae((e=>{e.style[h]=`${b(h,e)}px`,ie(e)}),r)),[r,b,h]),j=(0,l.useMemo)((()=>ae((e=>{e.style[h]=null}),s)),[h,s]);return(0,O.jsx)(de,{ref:v,addEndListener:oe,...y,"aria-expanded":y.role?u:null,onEnter:x,onEntering:_,onEntered:w,onExit:N,onExiting:j,childRef:Y(a),in:u,timeout:d,mountOnEnter:f,unmountOnExit:p,appear:m,children:(e,t)=>c().cloneElement(a,{...t,className:g()(o,a.props.className,me[e],"width"===h&&"collapse-horizontal")})})}));function ye(e,t){return Array.isArray(e)?e.includes(t):e===t}const ge=l.createContext({});ge.displayName="AccordionContext";const ve=ge,he=l.forwardRef((({as:e="div",bsPrefix:t,className:n,children:r,eventKey:s,...o},a)=>{const{activeEventKey:i}=(0,l.useContext)(ve);return t=E(t,"accordion-collapse"),(0,O.jsx)(be,{ref:a,in:ye(i,s),...o,className:g()(n,t),children:(0,O.jsx)(e,{children:l.Children.only(r)})})}));he.displayName="AccordionCollapse";const xe=he,Oe=l.createContext({eventKey:""});Oe.displayName="AccordionItemContext";const _e=Oe,we=l.forwardRef((({as:e="div",bsPrefix:t,className:n,onEnter:r,onEntering:s,onEntered:o,onExit:a,onExiting:i,onExited:c,...u},d)=>{t=E(t,"accordion-body");const{eventKey:f}=(0,l.useContext)(_e);return(0,O.jsx)(xe,{eventKey:f,onEnter:r,onEntering:s,onEntered:o,onExit:a,onExiting:i,onExited:c,children:(0,O.jsx)(e,{ref:d,...u,className:g()(n,t)})})}));we.displayName="AccordionBody";const Ne=we,je=l.forwardRef((({as:e="button",bsPrefix:t,className:n,onClick:r,...s},o)=>{t=E(t,"accordion-button");const{eventKey:a}=(0,l.useContext)(_e),i=function(e,t){const{activeEventKey:n,onSelect:r,alwaysOpen:s}=(0,l.useContext)(ve);return o=>{let a=e===n?null:e;s&&(a=Array.isArray(n)?n.includes(e)?n.filter((t=>t!==e)):[...n,e]:[e]),null==r||r(a,o),null==t||t(o)}}(a,r),{activeEventKey:c}=(0,l.useContext)(ve);return"button"===e&&(s.type="button"),(0,O.jsx)(e,{ref:o,onClick:i,...s,"aria-expanded":Array.isArray(c)?c.includes(a):a===c,className:g()(n,t,!ye(c,a)&&"collapsed")})}));je.displayName="AccordionButton";const Ee=je,ke=l.forwardRef((({as:e="h2","aria-controls":t,bsPrefix:n,className:r,children:s,onClick:o,...a},i)=>(n=E(n,"accordion-header"),(0,O.jsx)(e,{ref:i,...a,className:g()(r,n),children:(0,O.jsx)(Ee,{onClick:o,"aria-controls":t,children:s})}))));ke.displayName="AccordionHeader";const Pe=ke,Ce=l.forwardRef((({as:e="div",bsPrefix:t,className:n,eventKey:r,...s},o)=>{t=E(t,"accordion-item");const a=(0,l.useMemo)((()=>({eventKey:r})),[r]);return(0,O.jsx)(_e.Provider,{value:a,children:(0,O.jsx)(e,{ref:o,...s,className:g()(n,t)})})}));Ce.displayName="AccordionItem";const Te=Ce,Se=l.forwardRef(((e,t)=>{const{as:n="div",activeKey:r,bsPrefix:s,className:o,onSelect:a,flush:i,alwaysOpen:c,...u}=x(e,{activeKey:"onSelect"}),d=E(s,"accordion"),f=(0,l.useMemo)((()=>({activeEventKey:r,onSelect:a,alwaysOpen:c})),[r,a,c]);return(0,O.jsx)(ve.Provider,{value:f,children:(0,O.jsx)(n,{ref:t,...u,className:g()(o,d,i&&`${d}-flush`)})})}));Se.displayName="Accordion";const Re=Object.assign(Se,{Button:Ee,Collapse:xe,Item:Te,Header:Pe,Body:Ne});var De=c().createContext({});function Le(e){return Le="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Le(e)}function Ie(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,s,o,a,i=[],l=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);l=!0);}catch(e){c=!0,s=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw s}}return i}}(e,t)||s(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}const Ae=p((function(e){return null===e?"Null":void 0===e?"Undefined":Object.prototype.toString.call(e).slice(8,-1)}));function $e(e,t){return Object.prototype.hasOwnProperty.call(t,e)}const Me=p((function(e){var t=[];for(var n in e)$e(n,e)&&(t[t.length]=[n,e[n]]);return t})),Fe=Array.isArray||function(e){return null!=e&&e.length>=0&&"[object Array]"===Object.prototype.toString.call(e)};function Be(e,t,n){return function(){if(0===arguments.length)return n();var r=arguments[arguments.length-1];if(!Fe(r)){for(var s=0;s<e.length;){if("function"==typeof r[e[s]])return r[e[s]].apply(r,Array.prototype.slice.call(arguments,0,-1));s+=1}if(function(e){return null!=e&&"function"==typeof e["@@transducer/step"]}(r))return t.apply(null,Array.prototype.slice.call(arguments,0,-1))(r)}return n.apply(this,arguments)}}const ze=function(){return this.xf["@@transducer/init"]()},He=function(e){return this.xf["@@transducer/result"](e)};var Ke=function(){function e(e,t){this.xf=t,this.f=e,this.any=!1}return e.prototype["@@transducer/init"]=ze,e.prototype["@@transducer/result"]=function(e){return this.any||(e=this.xf["@@transducer/step"](e,!1)),this.xf["@@transducer/result"](e)},e.prototype["@@transducer/step"]=function(e,t){var n;return this.f(t)&&(this.any=!0,e=(n=this.xf["@@transducer/step"](e,!0))&&n["@@transducer/reduced"]?n:{"@@transducer/value":n,"@@transducer/reduced":!0}),e},e}();function Ue(e){return function(t){return new Ke(e,t)}}const We=m(Be(["any"],Ue,(function(e,t){for(var n=0;n<t.length;){if(e(t[n]))return!0;n+=1}return!1})));function qe(e){for(var t,n=[];!(t=e.next()).done;)n.push(t.value);return n}function Ve(e,t,n){for(var r=0,s=n.length;r<s;){if(e(t,n[r]))return!0;r+=1}return!1}const Ge="function"==typeof Object.is?Object.is:function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t};var Ye=Object.prototype.toString;const Xe=function(){return"[object Arguments]"===Ye.call(arguments)?function(e){return"[object Arguments]"===Ye.call(e)}:function(e){return $e("callee",e)}}();var Je=!{toString:null}.propertyIsEnumerable("toString"),Ze=["constructor","valueOf","isPrototypeOf","toString","propertyIsEnumerable","hasOwnProperty","toLocaleString"],Qe=function(){return arguments.propertyIsEnumerable("length")}(),et=function(e,t){for(var n=0;n<e.length;){if(e[n]===t)return!0;n+=1}return!1};const tt="function"!=typeof Object.keys||Qe?p((function(e){if(Object(e)!==e)return[];var t,n,r=[],s=Qe&&Xe(e);for(t in e)!$e(t,e)||s&&"length"===t||(r[r.length]=t);if(Je)for(n=Ze.length-1;n>=0;)$e(t=Ze[n],e)&&!et(r,t)&&(r[r.length]=t),n-=1;return r})):p((function(e){return Object(e)!==e?[]:Object.keys(e)}));function nt(e,t,n,r){var s=qe(e);function o(e,t){return rt(e,t,n.slice(),r.slice())}return!Ve((function(e,t){return!Ve(o,t,e)}),qe(t),s)}function rt(e,t,n,r){if(Ge(e,t))return!0;var s,o,a=Ae(e);if(a!==Ae(t))return!1;if("function"==typeof e["fantasy-land/equals"]||"function"==typeof t["fantasy-land/equals"])return"function"==typeof e["fantasy-land/equals"]&&e["fantasy-land/equals"](t)&&"function"==typeof t["fantasy-land/equals"]&&t["fantasy-land/equals"](e);if("function"==typeof e.equals||"function"==typeof t.equals)return"function"==typeof e.equals&&e.equals(t)&&"function"==typeof t.equals&&t.equals(e);switch(a){case"Arguments":case"Array":case"Object":if("function"==typeof e.constructor&&"Promise"===(s=e.constructor,null==(o=String(s).match(/^function (\w*)/))?"":o[1]))return e===t;break;case"Boolean":case"Number":case"String":if(typeof e!=typeof t||!Ge(e.valueOf(),t.valueOf()))return!1;break;case"Date":if(!Ge(e.valueOf(),t.valueOf()))return!1;break;case"Error":return e.name===t.name&&e.message===t.message;case"RegExp":if(e.source!==t.source||e.global!==t.global||e.ignoreCase!==t.ignoreCase||e.multiline!==t.multiline||e.sticky!==t.sticky||e.unicode!==t.unicode)return!1}for(var i=n.length-1;i>=0;){if(n[i]===e)return r[i]===t;i-=1}switch(a){case"Map":return e.size===t.size&&nt(e.entries(),t.entries(),n.concat([e]),r.concat([t]));case"Set":return e.size===t.size&&nt(e.values(),t.values(),n.concat([e]),r.concat([t]));case"Arguments":case"Array":case"Object":case"Boolean":case"Number":case"String":case"Date":case"Error":case"RegExp":case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"ArrayBuffer":break;default:return!1}var l=tt(e);if(l.length!==tt(t).length)return!1;var c=n.concat([e]),u=r.concat([t]);for(i=l.length-1;i>=0;){var d=l[i];if(!$e(d,t)||!rt(t[d],e[d],c,u))return!1;i-=1}return!0}const st=m((function(e,t){return rt(e,t,[],[])}));function ot(e,t){return function(e,t,n){var r,s;if("function"==typeof e.indexOf)switch(typeof t){case"number":if(0===t){for(r=1/t;n<e.length;){if(0===(s=e[n])&&1/s===r)return n;n+=1}return-1}if(t!=t){for(;n<e.length;){if("number"==typeof(s=e[n])&&s!=s)return n;n+=1}return-1}return e.indexOf(t,n);case"string":case"boolean":case"function":case"undefined":return e.indexOf(t,n);case"object":if(null===t)return e.indexOf(t,n)}for(;n<e.length;){if(st(e[n],t))return n;n+=1}return-1}(t,e,0)>=0}const at=m(ot);function it(e){var t=Object.prototype.toString.call(e);return"[object Function]"===t||"[object AsyncFunction]"===t||"[object GeneratorFunction]"===t||"[object AsyncGeneratorFunction]"===t}function lt(e){return"[object String]"===Object.prototype.toString.call(e)}function ct(e,t){for(var n=0,r=t.length,s=Array(r);n<r;)s[n]=e(t[n]),n+=1;return s}function ut(e){return'"'+e.replace(/\\/g,"\\\\").replace(/[\b]/g,"\\b").replace(/\f/g,"\\f").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\t/g,"\\t").replace(/\v/g,"\\v").replace(/\0/g,"\\0").replace(/"/g,'\\"')+'"'}var dt=function(e){return(e<10?"0":"")+e};const ft="function"==typeof Date.prototype.toISOString?function(e){return e.toISOString()}:function(e){return e.getUTCFullYear()+"-"+dt(e.getUTCMonth()+1)+"-"+dt(e.getUTCDate())+"T"+dt(e.getUTCHours())+":"+dt(e.getUTCMinutes())+":"+dt(e.getUTCSeconds())+"."+(e.getUTCMilliseconds()/1e3).toFixed(3).slice(2,5)+"Z"};var pt=function(){function e(e,t){this.xf=t,this.f=e}return e.prototype["@@transducer/init"]=ze,e.prototype["@@transducer/result"]=He,e.prototype["@@transducer/step"]=function(e,t){return this.f(t)?this.xf["@@transducer/step"](e,t):e},e}();function mt(e){return function(t){return new pt(e,t)}}const bt=m(Be(["fantasy-land/filter","filter"],mt,(function(e,t){return n=t,"[object Object]"===Object.prototype.toString.call(n)?function(e,t,n){for(var r=0,s=n.length;r<s;)t=e(t,n[r]),r+=1;return t}((function(n,r){return e(t[r])&&(n[r]=t[r]),n}),{},tt(t)):function(e,t){for(var n=0,r=t.length,s=[];n<r;)e(t[n])&&(s[s.length]=t[n]),n+=1;return s}(e,t);var n}))),yt=m((function(e,t){return bt((n=e,function(){return!n.apply(this,arguments)}),t);var n}));function gt(e,t){var n=function(n){var r=t.concat([e]);return ot(n,r)?"<Circular>":gt(n,r)},r=function(e,t){return ct((function(t){return ut(t)+": "+n(e[t])}),t.slice().sort())};switch(Object.prototype.toString.call(e)){case"[object Arguments]":return"(function() { return arguments; }("+ct(n,e).join(", ")+"))";case"[object Array]":return"["+ct(n,e).concat(r(e,yt((function(e){return/^\d+$/.test(e)}),tt(e)))).join(", ")+"]";case"[object Boolean]":return"object"==typeof e?"new Boolean("+n(e.valueOf())+")":e.toString();case"[object Date]":return"new Date("+(isNaN(e.valueOf())?n(NaN):ut(ft(e)))+")";case"[object Map]":return"new Map("+n(Array.from(e))+")";case"[object Null]":return"null";case"[object Number]":return"object"==typeof e?"new Number("+n(e.valueOf())+")":1/e==-1/0?"-0":e.toString(10);case"[object Set]":return"new Set("+n(Array.from(e).sort())+")";case"[object String]":return"object"==typeof e?"new String("+n(e.valueOf())+")":ut(e);case"[object Undefined]":return"undefined";default:if("function"==typeof e.toString){var s=e.toString();if("[object Object]"!==s)return s}return"{"+r(e,tt(e)).join(", ")+"}"}}const vt=p((function(e){return gt(e,[])})),ht=m((function(e,t){if(Fe(e)){if(Fe(t))return e.concat(t);throw new TypeError(vt(t)+" is not an array")}if(lt(e)){if(lt(t))return e+t;throw new TypeError(vt(t)+" is not a string")}if(null!=e&&it(e["fantasy-land/concat"]))return e["fantasy-land/concat"](t);if(null!=e&&it(e.concat))return e.concat(t);throw new TypeError(vt(e)+' does not have a method named "concat" or "fantasy-land/concat"')}));var xt=function(e){return e&&!Array.isArray(e)?[e]:e},Ot=function(e){return e.props.componentPath?window.dash_component_api.getLayout([].concat(o(e.props.componentPath),["props"])):e.props},_t=function(e){return"Object"===Ae(e)?Object.entries(e).map((function(e){var t=Ie(e,2),n=t[0],r=t[1];return{label:String(r),value:n}})):"Array"===Ae(e)&&e.length>0&&["String","Number"].includes(Ae(e[0]))?e.map((function(e){return{label:String(e),value:e}})):e},wt=function(e){return"object"!==Le(e)?e:"{"+Object.keys(e).sort().map((function(t){return JSON.stringify(t)+":"+((n=e[t])&&n.wild||JSON.stringify(n));var n})).join(",")+"}"},Nt=function(){var e,t=null===(e=window.dash_component_api)||void 0===e?void 0:e.useDashContext();return!!t&&t.useLoading()},jt=function(e,t){return function(n){var r=JSON.stringify(e);r=r.substring(0,r.length-1);var s=Me(n.loading).reduce((function(e,n){var s=Ie(n,2),o=s[0],a=s[1];return o.startsWith(r)&&a.length?t&&!We((function(e){var n=t[e.id];return!!n&&(Array.isArray(n)?at(e.property,n):e.property===n)}),a)?e:ht(e,a):e}),[]);return s.length?s:null}},Et=["children","active_item","always_open","start_collapsed","class_name","className","key","setProps"];function kt(t){var n=t.children,r=t.active_item,s=t.always_open,a=void 0!==s&&s,u=t.start_collapsed,d=void 0!==u&&u,f=t.class_name,p=t.className,m=t.key,y=t.setProps,g=i(t,Et);n=xt(n),(0,l.useEffect)((function(){if(y&&void 0===r&&!d){var e=n&&(Ot(n[0]).item_id||"item-0");y({active_item:a?[e]:e})}}),[]);var v=function(e){var t;y&&(t=a?Array.isArray(r)?r.includes(e)?r.filter((function(t){return t!==e})):[e].concat(o(r)):[e]:r!==e?e:null,y({active_item:t}))},h=n&&n.map((function(e,t){var n=Ot(e).item_id||"item-".concat(t);return c().createElement(De.Provider,{key:n,value:{toggle:v,idx:t}},e)}));return c().createElement(Re,e({key:m,"data-dash-is-loading":Nt()||void 0,activeKey:r,defaultActiveKey:d?null:r,alwaysOpen:a,className:f||p},b(["setProps","persistence","persistence_type","persisted_props"],g)),h)}kt.dashPersistence={persisted_props:["active_item"],persistence_type:"local"},kt.propTypes={children:d().node,id:d().string,active_item:d().oneOfType([d().string,d().arrayOf(d().string)]),always_open:d().bool,start_collapsed:d().bool,flush:d().bool,style:d().object,class_name:d().string,persistence:d().oneOfType([d().bool,d().string,d().number]),persisted_props:d().arrayOf(d().oneOf(["active_item"])),persistence_type:d().oneOf(["local","session","memory"]),key:d().string,className:d().string,setProps:d().func};const Pt=kt;var Ct=["children","id","title","item_id","class_name","className"];function Tt(t){var n=t.children,r=t.id,s=t.title,o=t.item_id,a=t.class_name,u=t.className,d=i(t,Ct),f=(0,l.useContext)(De),p=f.toggle,m=f.idx,y=o||"item-".concat(m);return c().createElement(Re.Item,e({id:wt(r),key:y,eventKey:y,className:a||u},b(["setProps","persistence","persistence_type","persisted_props"],d),{"data-dash-is-loading":Nt()||void 0}),c().createElement(Re.Header,{onClick:function(){return p(y)}},s),c().createElement(Re.Body,null,n))}Tt.propTypes={id:d().string,children:d().node,title:d().node,item_id:d().string,style:d().object,class_name:d().string,className:d().string,setProps:d().func};const St=Tt;function Rt(e,t,n){return(t=function(e){var t=function(e){if("object"!=Le(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=Le(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Le(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const Dt=function(e){const t=(0,l.useRef)(e);return(0,l.useEffect)((()=>{t.current=e}),[e]),t};function Lt(e){const t=Dt(e);return(0,l.useCallback)((function(...e){return t.current&&t.current(...e)}),[t])}const It=e=>l.forwardRef(((t,n)=>(0,O.jsx)("div",{...t,ref:n,className:g()(t.className,e)}))),At=It("h4");At.displayName="DivStyledAsH4";const $t=l.forwardRef((({className:e,bsPrefix:t,as:n=At,...r},s)=>(t=E(t,"alert-heading"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));$t.displayName="AlertHeading";const Mt=$t;function Ft(){return(0,l.useState)(null)}function Bt(e){const t=function(e){const t=(0,l.useRef)(e);return(0,l.useEffect)((()=>{t.current=e}),[e]),t}(e);return(0,l.useCallback)((function(...e){return t.current&&t.current(...e)}),[t])}function zt(){const e=(0,l.useRef)(!0),t=(0,l.useRef)((()=>e.current));return(0,l.useEffect)((()=>(e.current=!0,()=>{e.current=!1})),[]),t.current}function Ht(e){const t=(0,l.useRef)(null);return(0,l.useEffect)((()=>{t.current=e})),t.current}const Kt=void 0!==n.g&&n.g.navigator&&"ReactNative"===n.g.navigator.product,Ut="undefined"!=typeof document||Kt?l.useLayoutEffect:l.useEffect;new WeakMap;const Wt=["as","disabled"];function qt({tagName:e,disabled:t,href:n,target:r,rel:s,role:o,onClick:a,tabIndex:i=0,type:l}){e||(e=null!=n||null!=r||null!=s?"a":"button");const c={tagName:e};if("button"===e)return[{type:l||"button",disabled:t},c];const u=r=>{(t||"a"===e&&function(e){return!e||"#"===e.trim()}(n))&&r.preventDefault(),t?r.stopPropagation():null==a||a(r)};return"a"===e&&(n||(n="#"),t&&(n=void 0)),[{role:null!=o?o:"button",disabled:void 0,tabIndex:t?void 0:i,href:n,target:"a"===e?r:void 0,"aria-disabled":t||void 0,rel:"a"===e?s:void 0,onClick:u,onKeyDown:e=>{" "===e.key&&(e.preventDefault(),u(e))}},c]}const Vt=l.forwardRef(((e,t)=>{let{as:n,disabled:r}=e,s=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,Wt);const[o,{tagName:a}]=qt(Object.assign({tagName:n,disabled:r},s));return(0,O.jsx)(a,Object.assign({},s,o,{ref:t}))}));Vt.displayName="Button";const Gt=Vt,Yt=["onKeyDown"],Xt=l.forwardRef(((e,t)=>{let{onKeyDown:n}=e,r=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,Yt);const[s]=qt(Object.assign({tagName:"a"},r)),o=Bt((e=>{s.onKeyDown(e),null==n||n(e)}));return(a=r.href)&&"#"!==a.trim()&&"button"!==r.role?(0,O.jsx)("a",Object.assign({ref:t},r,{onKeyDown:n})):(0,O.jsx)("a",Object.assign({ref:t},r,s,{onKeyDown:o}));var a}));Xt.displayName="Anchor";const Jt=Xt,Zt=l.forwardRef((({className:e,bsPrefix:t,as:n=Jt,...r},s)=>(t=E(t,"alert-link"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));Zt.displayName="AlertLink";const Qt=Zt,en={[H]:"show",[K]:"show"},tn=l.forwardRef((({className:e,children:t,transitionClasses:n={},onEnter:r,...s},o)=>{const a={in:!1,timeout:300,mountOnEnter:!1,unmountOnExit:!1,appear:!1,...s},i=(0,l.useCallback)(((e,t)=>{ie(e),null==r||r(e,t)}),[r]);return(0,O.jsx)(de,{ref:o,addEndListener:oe,...a,onEnter:i,childRef:Y(t),children:(r,s)=>l.cloneElement(t,{...s,className:g()("fade",e,t.props.className,en[r],n[r])})})}));tn.displayName="Fade";const nn=tn,rn={"aria-label":d().string,onClick:d().func,variant:d().oneOf(["white"])},sn=l.forwardRef((({className:e,variant:t,"aria-label":n="Close",...r},s)=>(0,O.jsx)("button",{ref:s,type:"button",className:g()("btn-close",t&&`btn-close-${t}`,e),"aria-label":n,...r})));sn.displayName="CloseButton",sn.propTypes=rn;const on=sn,an=l.forwardRef(((e,t)=>{const{bsPrefix:n,show:r=!0,closeLabel:s="Close alert",closeVariant:o,className:a,children:i,variant:l="primary",onClose:c,dismissible:u,transition:d=nn,...f}=x(e,{show:"onClose"}),p=E(n,"alert"),m=Lt((e=>{c&&c(!1,e)})),b=!0===d?nn:d,y=(0,O.jsxs)("div",{role:"alert",...b?void 0:f,ref:t,className:g()(a,p,l&&`${p}-${l}`,u&&`${p}-dismissible`),children:[u&&(0,O.jsx)(on,{onClick:m,"aria-label":s,variant:o}),i]});return b?(0,O.jsx)(b,{unmountOnExit:!0,...f,ref:void 0,in:r,children:y}):r?y:null}));an.displayName="Alert";const ln=Object.assign(an,{Link:Qt,Heading:Mt});var cn=new Set(["primary","secondary","success","danger","warning","info","light","dark","white","transparent"]),un=new Set(["primary","secondary","success","danger","warning","info","light","dark","muted","white","black-50","white-50"]),dn=["children","is_open","color","dismissable","duration","fade","style","class_name","className","setProps"];function fn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function pn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?fn(Object(n),!0).forEach((function(t){Rt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):fn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function mn(t){var n=t.children,r=t.is_open,s=void 0===r||r,o=t.color,a=void 0===o?"success":o,u=t.dismissable,d=t.duration,f=void 0===d?null:d,p=t.fade,m=t.style,y=t.class_name,g=t.className,v=t.setProps,h=i(t,dn),x=(0,l.useRef)(null),O=function(){v&&v({is_open:!1})};(0,l.useEffect)((function(){return f&&(s?x.current=setTimeout(O,f):x.current&&(clearTimeout(x.current),x.current=null)),function(){return clearTimeout(x.current)}}),[s,f]);var _=cn.has(a);return c().createElement(ln,e({show:s,dismissible:u,onClose:u?O:null,variant:_?a:null,className:y||g,transition:p,style:_?m:pn({backgroundColor:a},m)},b(["persistence","persisted_props","persistence_type"],h),{"data-dash-is-loading":Nt()||void 0}),n)}mn.dashPersistence={persisted_props:["is_open"],persistence_type:"local"},mn.propTypes={children:d().node,id:d().string,is_open:d().bool,color:d().string,dismissable:d().bool,duration:d().number,fade:d().bool,style:d().object,class_name:d().string,persistence:d().oneOfType([d().bool,d().string,d().number]),persisted_props:d().arrayOf(d().oneOf(["is_open"])),persistence_type:d().oneOf(["local","session","memory"]),key:d().string,className:d().string,setProps:d().func};const bn=mn,yn=l.forwardRef((({bsPrefix:e,bg:t="primary",pill:n=!1,text:r,className:s,as:o="span",...a},i)=>{const l=E(e,"badge");return(0,O.jsx)(o,{ref:i,...a,className:g()(s,l,n&&"rounded-pill",r&&`text-${r}`,t&&`bg-${t}`)})}));yn.displayName="Badge";const gn=yn;var vn=["children","preOnClick","target","linkTarget","href","download","external_link","disabled"],hn=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,xn=/^[a-zA-Z]:\\/;function On(e,t){return null==e?function(e){if("string"!=typeof e)throw new TypeError("Expected a `string`, got `".concat(Le(e),"`"));return!xn.test(e)&&hn.test(e)}(t):e}var _n=c().forwardRef((function(t,n){var r=t.children,s=t.preOnClick,o=t.target,a=t.linkTarget,l=t.href,u=t.download,d=t.external_link,f=t.disabled,p=i(t,vn),m=l&&On(d,l);return c().createElement("a",e({href:l,target:m?o||a:void 0,download:u&&m?u:void 0},p,{onClick:function(e){return function(e){e.metaKey||e.shiftKey||e.altKey||e.ctrlKey||(f?e.preventDefault():(s&&s(),l&&!On(d,l)&&(e.preventDefault(),window.history.pushState({},"",l),window.dispatchEvent(new CustomEvent("_dashprivate_pushstate")),window.scrollTo(0,0))))}(e)},ref:n}),r)}));_n.propTypes={id:d().string,children:d().node,style:d().object,class_name:d().string,className:d().string,href:d().string,disabled:d().bool,external_link:d().bool,preOnClick:d().func,target:d().string,linkTarget:d().string,download:d().string};var wn=["children","href","color","text_color","n_clicks","style","class_name","className","setProps"];function Nn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function jn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Nn(Object(n),!0).forEach((function(t){Rt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Nn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function En(t){var n=t.children,r=t.href,s=t.color,o=void 0===s?"secondary":s,a=t.text_color,l=t.n_clicks,u=void 0===l?0:l,d=t.style,f=t.class_name,p=t.className,m=t.setProps,y=i(t,wn),g=cn.has(o);return y[r?"preOnClick":"onClick"]=function(){m&&m({n_clicks:u+1})},c().createElement(gn,e({as:r&&_n,href:r,bg:g?o:null,text:a,className:f||p,style:g?d:jn({backgroundColor:o},d)},b(["setProps"],y),{"data-dash-is-loading":Nt()||void 0}),n)}En.propTypes={children:d().node,id:d().string,color:d().string,text_color:d().string,n_clicks:d().number,href:d().string,external_link:d().bool,pill:d().bool,style:d().object,class_name:d().string,tag:d().string,target:d().string,title:d().string,key:d().string,className:d().string,setProps:d().func};const kn=En,Pn=l.forwardRef((({bsPrefix:e,active:t=!1,children:n,className:r,as:s="li",linkAs:o=Jt,linkProps:a={},href:i,title:l,target:c,...u},d)=>{const f=E(e,"breadcrumb-item");return(0,O.jsx)(s,{ref:d,...u,className:g()(f,r,{active:t}),"aria-current":t?"page":void 0,children:t?n:(0,O.jsx)(o,{...a,href:i,title:l,target:c,children:n})})}));Pn.displayName="BreadcrumbItem";const Cn=Pn,Tn=l.forwardRef((({bsPrefix:e,className:t,listProps:n={},children:r,label:s="breadcrumb",as:o="nav",...a},i)=>{const l=E(e,"breadcrumb");return(0,O.jsx)(o,{"aria-label":s,className:t,ref:i,...a,children:(0,O.jsx)("ol",{...n,className:g()(l,null==n?void 0:n.className),children:r})})}));Tn.displayName="Breadcrumb";const Sn=Object.assign(Tn,{Item:Cn});var Rn=["items","class_name","className","item_style","item_class_name","itemClassName","tag"];function Dn(t){var n=t.items,r=t.class_name,s=t.className,o=t.item_style,a=t.item_class_name,l=t.itemClassName,u=t.tag,d=i(t,Rn);return c().createElement(Sn,e({as:u,"data-dash-is-loading":Nt()||void 0,className:r||s},d),(n||[]).map((function(e,t){return c().createElement(Sn.Item,{key:"".concat(e.value).concat(t),active:e.active,linkAs:e.href&&_n,className:a||l,href:e.href,linkProps:e.href&&{external_link:e.external_link},style:o},e.label)})))}Dn.propTypes={id:d().string,items:d().arrayOf(d().shape({label:d().string,href:d().string,active:d().bool,external_link:d().bool,target:d().string,title:d().string})),style:d().object,item_style:d().object,class_name:d().string,item_class_name:d().string,key:d().string,className:d().string,itemClassName:d().string,tag:d().object,setProps:d().func};const Ln=Dn,In=l.forwardRef((({as:e,bsPrefix:t,variant:n="primary",size:r,active:s=!1,disabled:o=!1,className:a,...i},l)=>{const c=E(t,"btn"),[u,{tagName:d}]=qt({tagName:e,disabled:o,...i}),f=d;return(0,O.jsx)(f,{...u,...i,ref:l,disabled:o,className:g()(a,c,s&&"active",n&&`${c}-${n}`,r&&`${c}-${r}`,i.href&&o&&"disabled")})}));In.displayName="Button";const An=In;var $n=["children","n_clicks","color","outline","disabled","href","target","type","download","name","value","class_name","className","rel","setProps","onClick"];function Mn(t){var n=t.children,r=t.n_clicks,s=void 0===r?0:r,o=t.color,a=t.outline,l=t.disabled,u=t.href,d=t.target,f=t.type,p=t.download,m=t.name,b=t.value,y=t.class_name,g=t.className,v=t.rel,h=t.setProps,x=t.onClick,O=i(t,$n),_=u&&!l;return O[_?"preOnClick":"onClick"]=x||function(){!l&&h&&h({n_clicks:s+1})},_&&(O.linkTarget=d),c().createElement(An,e({as:_?_n:"button",variant:a?"outline-".concat(o):o,type:_?void 0:f,href:l?void 0:u,disabled:l,download:_?p:void 0,name:_?void 0:m,value:_?void 0:b,className:y||g,rel:_?v:void 0},O,{"data-dash-is-loading":Nt()||void 0}),n)}Mn.propTypes={children:d().node,id:d().string,n_clicks:d().number,color:d().string,href:d().string,external_link:d().bool,class_name:d().string,style:d().object,active:d().bool,disabled:d().bool,size:d().string,title:d().string,outline:d().bool,target:d().string,type:d().oneOf(["button","reset","submit"]),download:d().string,name:d().string,value:d().string,rel:d().string,key:d().string,className:d().string,setProps:d().func};const Fn=Mn,Bn=l.forwardRef((({bsPrefix:e,size:t,vertical:n=!1,className:r,role:s="group",as:o="div",...a},i)=>{const l=E(e,"btn-group");let c=l;return n&&(c=`${l}-vertical`),(0,O.jsx)(o,{...a,ref:i,role:s,className:g()(r,c,t&&`${l}-${t}`)})}));Bn.displayName="ButtonGroup";const zn=Bn;var Hn=["children","class_name","className"];function Kn(t){var n=t.children,r=t.class_name,s=t.className,o=i(t,Hn);return c().createElement(zn,e({className:r||s},b(["setProps"],o),{"data-dash-is-loading":Nt()||void 0}),n)}Kn.propTypes={children:d().node,id:d().string,size:d().string,vertical:d().bool,style:d().object,class_name:d().string,key:d().string,className:d().string,setProps:d().func};const Un=Kn,Wn=l.forwardRef((({className:e,bsPrefix:t,as:n="div",...r},s)=>(t=E(t,"card-body"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));Wn.displayName="CardBody";const qn=Wn,Vn=l.forwardRef((({className:e,bsPrefix:t,as:n="div",...r},s)=>(t=E(t,"card-footer"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));Vn.displayName="CardFooter";const Gn=Vn,Yn=l.createContext(null);Yn.displayName="CardHeaderContext";const Xn=Yn,Jn=l.forwardRef((({bsPrefix:e,className:t,as:n="div",...r},s)=>{const o=E(e,"card-header"),a=(0,l.useMemo)((()=>({cardHeaderBsPrefix:o})),[o]);return(0,O.jsx)(Xn.Provider,{value:a,children:(0,O.jsx)(n,{ref:s,...r,className:g()(t,o)})})}));Jn.displayName="CardHeader";const Zn=Jn,Qn=l.forwardRef((({bsPrefix:e,className:t,variant:n,as:r="img",...s},o)=>{const a=E(e,"card-img");return(0,O.jsx)(r,{ref:o,className:g()(n?`${a}-${n}`:a,t),...s})}));Qn.displayName="CardImg";const er=Qn,tr=l.forwardRef((({className:e,bsPrefix:t,as:n="div",...r},s)=>(t=E(t,"card-img-overlay"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));tr.displayName="CardImgOverlay";const nr=tr,rr=l.forwardRef((({className:e,bsPrefix:t,as:n="a",...r},s)=>(t=E(t,"card-link"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));rr.displayName="CardLink";const sr=rr,or=It("h6"),ar=l.forwardRef((({className:e,bsPrefix:t,as:n=or,...r},s)=>(t=E(t,"card-subtitle"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));ar.displayName="CardSubtitle";const ir=ar,lr=l.forwardRef((({className:e,bsPrefix:t,as:n="p",...r},s)=>(t=E(t,"card-text"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));lr.displayName="CardText";const cr=lr,ur=It("h5"),dr=l.forwardRef((({className:e,bsPrefix:t,as:n=ur,...r},s)=>(t=E(t,"card-title"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));dr.displayName="CardTitle";const fr=dr,pr=l.forwardRef((({bsPrefix:e,className:t,bg:n,text:r,border:s,body:o=!1,children:a,as:i="div",...l},c)=>{const u=E(e,"card");return(0,O.jsx)(i,{ref:c,...l,className:g()(t,u,n&&`bg-${n}`,r&&`text-${r}`,s&&`border-${s}`),children:o?(0,O.jsx)(qn,{children:a}):a})}));pr.displayName="Card";const mr=Object.assign(pr,{Img:er,Title:fr,Subtitle:ir,Body:qn,Link:sr,Text:cr,Header:Zn,Footer:Gn,ImgOverlay:nr});var br=["children","color","inverse","outline","style","class_name","className"];function yr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function gr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?yr(Object(n),!0).forEach((function(t){Rt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):yr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function vr(t){var n=t.children,r=t.color,s=t.inverse,o=t.outline,a=t.style,l=t.class_name,u=t.className,d=i(t,br),f=cn.has(r);return c().createElement(mr,e({"data-dash-is-loading":Nt()||void 0,text:s?"white":null,bg:f&&!o?r:null,border:f&&o?r:null,style:f?a:gr({backgroundColor:r},a),className:l||u},b(["setProps"],d)),n)}vr.propTypes={children:d().node,id:d().string,color:d().string,body:d().bool,outline:d().bool,inverse:d().bool,style:d().object,class_name:d().string,key:d().string,className:d().string,setProps:d().func};const hr=vr;var xr=["children","class_name","className"];function Or(t){var n=t.children,r=t.class_name,s=t.className,o=i(t,xr);return c().createElement(mr.Body,e({"data-dash-is-loading":Nt()||void 0,className:r||s},b(["setProps"],o)),n)}Or.propTypes={children:d().node,id:d().string,style:d().object,class_name:d().string,tag:d().string,key:d().string,className:d().string,setProps:d().func};const _r=Or;var wr=["children","class_name","className"];function Nr(t){var n=t.children,r=t.class_name,s=t.className,o=i(t,wr);return c().createElement(mr.Footer,e({"data-dash-is-loading":Nt()||void 0,className:r||s},b(["setProps"],o)),n)}Nr.propTypes={children:d().node,id:d().string,style:d().object,class_name:d().string,tag:d().string,key:d().string,className:d().string,setProps:d().func};const jr=Nr,Er=l.forwardRef((({className:e,bsPrefix:t,as:n="div",...r},s)=>(t=E(t,"card-group"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));Er.displayName="CardGroup";const kr=Er;var Pr=["children","className","class_name"];function Cr(t){var n=t.children,r=t.className,s=t.class_name,o=i(t,Pr);return c().createElement(kr,e({"data-dash-is-loading":Nt()||void 0,className:s||r},b(["setProps"],o)),n)}Cr.propTypes={children:d().node,id:d().string,style:d().object,class_name:d().string,tag:d().string,key:d().string,className:d().string,setProps:d().func};const Tr=Cr;var Sr=["children","class_name","className"];function Rr(t){var n=t.children,r=t.class_name,s=t.className,o=i(t,Sr);return c().createElement(Zn,e({"data-dash-is-loading":Nt()||void 0,className:r||s},b(["setProps"],o)),n)}Rr.propTypes={children:d().node,id:d().string,style:d().object,class_name:d().string,tag:d().string,key:d().string,className:d().string,setProps:d().func};const Dr=Rr;var Lr=["children","class_name","top","bottom","tag","className"];function Ir(t){var n=t.children,r=t.class_name,s=t.top,o=t.bottom,a=t.tag,l=t.className,u=i(t,Lr);return c().createElement(er,e({"data-dash-is-loading":Nt()||void 0,className:r||l,variant:s?"top":o?"bottom":null,as:a},b(["setProps"],u)),n)}Ir.propTypes={children:d().node,id:d().string,src:d().string,style:d().object,class_name:d().string,tag:d().string,top:d().bool,bottom:d().bool,alt:d().string,title:d().string,key:d().string,className:d().string,setProps:d().func};const Ar=Ir;var $r=["children","class_name","className"];function Mr(t){var n=t.children,r=t.class_name,s=t.className,o=i(t,$r);return c().createElement(mr.ImgOverlay,e({"data-dash-is-loading":Nt()||void 0,className:r||s},b(["setProps"],o)),n)}Mr.propTypes={children:d().node,id:d().string,style:d().object,class_name:d().string,tag:d().string,key:d().string,className:d().string,setProps:d().func};const Fr=Mr;var Br=["children","disabled","className","class_name","n_clicks","setProps"];function zr(t){var n=t.children,r=t.disabled,s=t.className,o=t.class_name,a=t.n_clicks,l=void 0===a?0:a,u=t.setProps,d=i(t,Br);return c().createElement(mr.Link,e({"data-dash-is-loading":Nt()||void 0,as:_n,preOnClick:function(){!r&&u&&u({n_clicks:l+1})},disabled:r,className:o||s},d),n)}zr.propTypes={id:d().string,children:d().node,href:d().string,external_link:d().bool,n_clicks:d().number,style:d().object,class_name:d().string,target:d().string,disabled:d().bool,key:d().string,className:d().string,setProps:d().func};const Hr=zr;function Kr(e){const t=function(e){const t=(0,l.useRef)(e);return t.current=e,t}(e);(0,l.useEffect)((()=>()=>t.current()),[])}const Ur=2**31-1;function Wr(e,t,n){const r=n-Date.now();e.current=r<=Ur?setTimeout(t,r):setTimeout((()=>Wr(e,t,n)),Ur)}function qr(){const e=function(){const e=(0,l.useRef)(!0),t=(0,l.useRef)((()=>e.current));return(0,l.useEffect)((()=>(e.current=!0,()=>{e.current=!1})),[]),t.current}(),t=(0,l.useRef)();return Kr((()=>clearTimeout(t.current))),(0,l.useMemo)((()=>{const n=()=>clearTimeout(t.current);return{set:function(r,s=0){e()&&(n(),s<=Ur?t.current=setTimeout(r,s):Wr(t,r,Date.now()+s))},clear:n,handleRef:t}}),[])}const Vr=l.forwardRef((({className:e,bsPrefix:t,as:n="div",...r},s)=>(t=E(t,"carousel-caption"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));Vr.displayName="CarouselCaption";const Gr=Vr,Yr=l.forwardRef((({as:e="div",bsPrefix:t,className:n,...r},s)=>{const o=g()(n,E(t,"carousel-item"));return(0,O.jsx)(e,{ref:s,...r,className:o})}));Yr.displayName="CarouselItem";const Xr=Yr;function Jr(e,t){let n=0;return l.Children.map(e,(e=>l.isValidElement(e)?t(e,n++):e))}const Zr=l.forwardRef((({defaultActiveIndex:e=0,...t},n)=>{const{as:r="div",bsPrefix:s,slide:o=!0,fade:a=!1,controls:i=!0,indicators:c=!0,indicatorLabels:u=[],activeIndex:d,onSelect:f,onSlide:p,onSlid:m,interval:b=5e3,keyboard:y=!0,onKeyDown:v,pause:h="hover",onMouseOver:_,onMouseOut:w,wrap:N=!0,touch:j=!0,onTouchStart:k,onTouchMove:P,onTouchEnd:T,prevIcon:S=(0,O.jsx)("span",{"aria-hidden":"true",className:"carousel-control-prev-icon"}),prevLabel:R="Previous",nextIcon:D=(0,O.jsx)("span",{"aria-hidden":"true",className:"carousel-control-next-icon"}),nextLabel:L="Next",variant:I,className:A,children:$,...M}=x({defaultActiveIndex:e,...t},{activeIndex:"onSelect"}),F=E(s,"carousel"),B=C(),z=(0,l.useRef)(null),[H,K]=(0,l.useState)("next"),[U,W]=(0,l.useState)(!1),[q,V]=(0,l.useState)(!1),[G,Y]=(0,l.useState)(d||0);(0,l.useEffect)((()=>{q||d===G||(z.current?K(z.current):K((d||0)>G?"next":"prev"),o&&V(!0),Y(d||0))}),[d,q,G,o]),(0,l.useEffect)((()=>{z.current&&(z.current=null)}));let X,J=0;!function(e){let t=0;l.Children.forEach(e,(e=>{l.isValidElement(e)&&((e,t)=>{++J,t===d&&(X=e.props.interval)})(e,t++)}))}($);const Z=Dt(X),Q=(0,l.useCallback)((e=>{if(q)return;let t=G-1;if(t<0){if(!N)return;t=J-1}z.current="prev",null==f||f(t,e)}),[q,G,f,N,J]),ee=Lt((e=>{if(q)return;let t=G+1;if(t>=J){if(!N)return;t=0}z.current="next",null==f||f(t,e)})),te=(0,l.useRef)();(0,l.useImperativeHandle)(n,(()=>({element:te.current,prev:Q,next:ee})));const ne=Lt((()=>{!document.hidden&&function(e){if(!(e&&e.style&&e.parentNode&&e.parentNode.style))return!1;const t=getComputedStyle(e);return"none"!==t.display&&"hidden"!==t.visibility&&"none"!==getComputedStyle(e.parentNode).display}(te.current)&&(B?Q():ee())})),re="next"===H?"start":"end";!function(e,t){const n=(0,l.useRef)(!0);(0,l.useEffect)((()=>{if(!n.current)return e();n.current=!1}),t)}((()=>{o||(null==p||p(G,re),null==m||m(G,re))}),[G]);const se=`${F}-item-${H}`,ae=`${F}-item-${re}`,le=(0,l.useCallback)((e=>{ie(e),null==p||p(G,re)}),[p,G,re]),ce=(0,l.useCallback)((()=>{V(!1),null==m||m(G,re)}),[m,G,re]),ue=(0,l.useCallback)((e=>{if(y&&!/input|textarea/i.test(e.target.tagName))switch(e.key){case"ArrowLeft":return e.preventDefault(),void(B?ee(e):Q(e));case"ArrowRight":return e.preventDefault(),void(B?Q(e):ee(e))}null==v||v(e)}),[y,v,Q,ee,B]),fe=(0,l.useCallback)((e=>{"hover"===h&&W(!0),null==_||_(e)}),[h,_]),pe=(0,l.useCallback)((e=>{W(!1),null==w||w(e)}),[w]),me=(0,l.useRef)(0),be=(0,l.useRef)(0),ye=qr(),ge=(0,l.useCallback)((e=>{me.current=e.touches[0].clientX,be.current=0,"hover"===h&&W(!0),null==k||k(e)}),[h,k]),ve=(0,l.useCallback)((e=>{e.touches&&e.touches.length>1?be.current=0:be.current=e.touches[0].clientX-me.current,null==P||P(e)}),[P]),he=(0,l.useCallback)((e=>{if(j){const t=be.current;Math.abs(t)>40&&(t>0?Q(e):ee(e))}"hover"===h&&ye.set((()=>{W(!1)}),b||void 0),null==T||T(e)}),[j,h,Q,ee,ye,b,T]),xe=null!=b&&!U&&!q,Oe=(0,l.useRef)();(0,l.useEffect)((()=>{var e,t;if(!xe)return;const n=B?Q:ee;return Oe.current=window.setInterval(document.visibilityState?ne:n,null!=(e=null!=(t=Z.current)?t:b)?e:void 0),()=>{null!==Oe.current&&clearInterval(Oe.current)}}),[xe,Q,ee,Z,b,ne,B]);const _e=(0,l.useMemo)((()=>c&&Array.from({length:J},((e,t)=>e=>{null==f||f(t,e)}))),[c,J,f]);return(0,O.jsxs)(r,{ref:te,...M,onKeyDown:ue,onMouseOver:fe,onMouseOut:pe,onTouchStart:ge,onTouchMove:ve,onTouchEnd:he,className:g()(A,F,o&&"slide",a&&`${F}-fade`,I&&`${F}-${I}`),children:[c&&(0,O.jsx)("div",{className:`${F}-indicators`,children:Jr($,((e,t)=>(0,O.jsx)("button",{type:"button","data-bs-target":"","aria-label":null!=u&&u.length?u[t]:`Slide ${t+1}`,className:t===G?"active":void 0,onClick:_e?_e[t]:void 0,"aria-current":t===G},t)))}),(0,O.jsx)("div",{className:`${F}-inner`,children:Jr($,((e,t)=>{const n=t===G;return o?(0,O.jsx)(de,{in:n,onEnter:n?le:void 0,onEntered:n?ce:void 0,addEndListener:oe,children:(t,r)=>l.cloneElement(e,{...r,className:g()(e.props.className,n&&"entered"!==t&&se,("entered"===t||"exiting"===t)&&"active",("entering"===t||"exiting"===t)&&ae)})}):l.cloneElement(e,{className:g()(e.props.className,n&&"active")})}))}),i&&(0,O.jsxs)(O.Fragment,{children:[(N||0!==d)&&(0,O.jsxs)(Jt,{className:`${F}-control-prev`,onClick:Q,children:[S,R&&(0,O.jsx)("span",{className:"visually-hidden",children:R})]}),(N||d!==J-1)&&(0,O.jsxs)(Jt,{className:`${F}-control-next`,onClick:ee,children:[D,L&&(0,O.jsx)("span",{className:"visually-hidden",children:L})]})]})]})}));Zr.displayName="Carousel";const Qr=Object.assign(Zr,{Caption:Gr,Item:Xr});var es=["items","interval","style","class_name","active_index","controls","indicators","className","setProps"];function ts(t){var n=t.items,r=t.interval,s=t.style,o=t.class_name,a=t.active_index,l=void 0===a?0:a,u=t.controls,d=void 0===u||u,f=t.indicators,p=void 0===f||f,m=t.className,y=t.setProps,g=i(t,es),v=n.map((function(t){var n=t.href?{href:t.href,external_link:t.external_link,target:t.target||"_self"}:{};return c().createElement(Qr.Item,e({key:t.key,as:t.href?_n:"div"},n),c().createElement("img",{src:t.src,className:t.img_class_name||t.imgClassName||"d-block w-100",style:t.img_style,alt:t.alt}),c().createElement(Qr.Caption,{className:t.caption_class_name||t.captionClassName},t.header&&c().createElement("h5",null,t.header),t.caption&&c().createElement("p",null,t.caption)))}));return c().createElement("div",{style:s,className:o||m},c().createElement(Qr,e({"data-dash-is-loading":Nt()||void 0,activeIndex:l,onSelect:function(e){return y({active_index:e})},interval:r||null,controls:d,indicators:p},b(["persistence","persisted_props","persistence_type","setProps"],g)),v))}ts.dashPersistence={persisted_props:["active_index"],persistence_type:"local"},ts.propTypes={id:d().string,items:d().arrayOf(d().exact({src:d().string,alt:d().string,header:d().string,caption:d().string,img_style:d().object,img_class_name:d().string,caption_class_name:d().string,href:d().string,target:d().string,external_link:d().bool,key:d().string,imgClassName:d().string,captionClassName:d().string})).isRequired,active_index:d().number,interval:d().number,controls:d().bool,indicators:d().bool,style:d().object,class_name:d().string,slide:d().bool,variant:d().oneOf(["dark"]),persistence:d().oneOfType([d().bool,d().string,d().number]),persisted_props:d().arrayOf(d().oneOf(["active_index"])),persistence_type:d().oneOf(["local","session","memory"]),className:d().string,setProps:d().func};const ns=ts;function rs(e){var t=e.id,n=e.value,r=void 0!==n&&n,s=e.disabled,o=void 0!==s&&s,a=e.class_name,i=e.style,l=e.label,u=e.label_id,d=e.name,f=e.input_style,p=void 0===f?null:f,m=e.input_class_name,b=void 0===m?"":m,y=e.label_style,v=void 0===y?null:y,h=e.label_class_name,x=void 0===h?"":h,O=e.className,_=e.inputStyle,w=e.inputClassName,N=e.labelStyle,j=e.labelClassName,E=e.setProps;return c().createElement("div",{className:g()("form-check",a||O),style:i,"data-dash-is-loading":Nt()||void 0},c().createElement("input",{id:t,name:d,checked:r,className:g()("form-check-input",b||w),disabled:o,style:p||_,type:"checkbox",onChange:function(){o||E&&E({value:!r})}}),c().createElement("label",{id:u,style:v||N,className:g()(x||j,"form-check-label","form-label"),htmlFor:t},l))}rs.dashPersistence={persisted_props:["value"],persistence_type:"local"},rs.propTypes={id:d().string,value:d().bool,disabled:d().bool,label:d().node,class_name:d().string,style:d().object,input_style:d().object,input_class_name:d().string,label_id:d().string,label_style:d().object,label_class_name:d().string,name:d().string,persistence:d().oneOfType([d().bool,d().string,d().number]),persisted_props:d().arrayOf(d().oneOf(["value"])),persistence_type:d().oneOf(["local","session","memory"]),className:d().string,inputStyle:d().object,inputClassName:d().string,labelStyle:d().object,labelClassName:d().string,setProps:d().func};const ss=rs;function os(e,t,n){var r,s=typeof e;switch(s){case"string":case"number":return 0===e&&1/e==-1/0?!!n._items["-0"]||(t&&(n._items["-0"]=!0),!1):null!==n._nativeSet?t?(r=n._nativeSet.size,n._nativeSet.add(e),n._nativeSet.size===r):n._nativeSet.has(e):s in n._items?e in n._items[s]||(t&&(n._items[s][e]=!0),!1):(t&&(n._items[s]={},n._items[s][e]=!0),!1);case"boolean":if(s in n._items){var o=e?1:0;return!!n._items[s][o]||(t&&(n._items[s][o]=!0),!1)}return t&&(n._items[s]=e?[!1,!0]:[!0,!1]),!1;case"function":return null!==n._nativeSet?t?(r=n._nativeSet.size,n._nativeSet.add(e),n._nativeSet.size===r):n._nativeSet.has(e):s in n._items?!!ot(e,n._items[s])||(t&&n._items[s].push(e),!1):(t&&(n._items[s]=[e]),!1);case"undefined":return!!n._items[s]||(t&&(n._items[s]=!0),!1);case"object":if(null===e)return!!n._items.null||(t&&(n._items.null=!0),!1);default:return(s=Object.prototype.toString.call(e))in n._items?!!ot(e,n._items[s])||(t&&n._items[s].push(e),!1):(t&&(n._items[s]=[e]),!1)}}const as=function(){function e(){this._nativeSet="function"==typeof Set?new Set:null,this._items={}}return e.prototype.add=function(e){return!os(e,!0,this)},e.prototype.has=function(e){return os(e,!1,this)},e}(),is=m((function(e,t){for(var n=new as,r=0;r<e.length;r+=1)n.add(e[r]);return yt(n.has.bind(n),t)})),ls=m((function(e,t){return function(e,t){var n;t=t||[];var r=(e=e||[]).length,s=t.length,o=[];for(n=0;n<r;)o[o.length]=e[n],n+=1;for(n=0;n<s;)o[o.length]=t[n],n+=1;return o}(t,[e])}));function cs(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function us(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?cs(Object(n),!0).forEach((function(t){Rt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):cs(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ds(e){var t=e.options,n=void 0===t?[]:t,r=e.value,s=void 0===r?[]:r,o=e.id,a=e.switch,i=e.inline,l=e.class_name,u=e.style,d=e.input_style,f=e.input_class_name,p=e.label_style,m=e.label_class_name,b=e.input_checked_class_name,y=e.input_checked_style,v=e.label_checked_class_name,h=e.label_checked_style,x=e.name,O=e.key,_=e.className,w=e.inputStyle,N=e.inputClassName,j=e.labelStyle,E=e.labelClassName,k=e.inputCheckedClassName,P=e.inputCheckedStyle,C=e.labelCheckedClassName,T=e.labelCheckedStyle,S=e.setProps,R=_t(n).map((function(e){return function(e){var t=at(e.value,s),n=t?us(us({},d||w),y||P):d||w,r=t?us(us({},p||j),h||T):p||j,l=e.input_id||"_dbcprivate_checklist_".concat(o,"_input_").concat(e.value);return c().createElement("div",{className:g()("form-check",i&&"form-check-inline",a&&"form-switch"),key:e.value},c().createElement("input",{id:l,name:x,value:e.value,checked:t,className:g()("form-check-input",f||N,t&&(b||k)),disabled:Boolean(e.disabled),style:n,type:"checkbox",onChange:function(){var t;t=at(e.value,s)?is([e.value],s):ls(e.value,s),S({value:t})}}),c().createElement("label",{id:e.label_id,style:r,className:g()("form-check-label",m||E,t&&(v||C)),key:e.value,htmlFor:l},e.label))}(e)}));return c().createElement("div",{id:o,style:u,className:l||_,key:O,"data-dash-is-loading":Nt()||void 0},R)}ds.dashPersistence={persisted_props:["value"],persistence_type:"local"},ds.propTypes={options:d().oneOfType([d().arrayOf(d().oneOfType([d().string,d().number])),d().object,d().arrayOf(d().exact({label:d().node.isRequired,value:d().oneOfType([d().string,d().number]).isRequired,disabled:d().bool,input_id:d().string,label_id:d().string}))]),value:d().arrayOf(d().oneOfType([d().string,d().number])),id:d().string,inline:d().bool,switch:d().bool,style:d().object,class_name:d().string,input_style:d().object,input_checked_style:d().object,input_class_name:d().string,input_checked_class_name:d().string,label_style:d().object,label_class_name:d().string,label_checked_style:d().object,label_checked_class_name:d().string,name:d().string,persistence:d().oneOfType([d().bool,d().string,d().number]),persisted_props:d().arrayOf(d().oneOf(["value"])),persistence_type:d().oneOf(["local","session","memory"]),key:d().string,className:d().string,inputClassName:d().string,inputStyle:d().object,inputCheckedStyle:d().object,inputCheckedClassName:d().string,labelStyle:d().object,labelClassName:d().string,labelCheckedStyle:d().object,labelCheckedClassName:d().string,setProps:d().func};const fs=ds;function ps({as:e,bsPrefix:t,className:n,...r}){t=E(t,"col");const s=k(),o=P(),a=[],i=[];return s.forEach((e=>{const n=r[e];let s,l,c;delete r[e],"object"==typeof n&&null!=n?({span:s,offset:l,order:c}=n):s=n;const u=e!==o?`-${e}`:"";s&&a.push(!0===s?`${t}${u}`:`${t}${u}-${s}`),null!=c&&i.push(`order${u}-${c}`),null!=l&&i.push(`offset${u}-${l}`)})),[{...r,className:g()(n,...a,...i)},{as:e,bsPrefix:t,spans:a}]}const ms=l.forwardRef(((e,t)=>{const[{className:n,...r},{as:s="div",bsPrefix:o,spans:a}]=ps(e);return(0,O.jsx)(s,{...r,ref:t,className:g()(n,!a.length&&o)})}));ms.displayName="Col";const bs=ms;var ys=d().oneOfType([d().number,d().string]),gs=d().oneOfType([d().bool,d().string,d().number,d().shape({size:d().oneOfType([d().bool,d().number,d().string]),order:ys,offset:ys})]),vs=["children","align","class_name","width","xs","sm","md","lg","xl","xxl","className"],hs={start:"align-self-start",center:"align-self-center",end:"align-self-end",stretch:"align-self-stretch",baseline:"align-self-baseline"};function xs(t){var n=t.children,r=t.align,s=t.class_name,o=t.width,a=t.xs,l=t.sm,u=t.md,d=t.lg,f=t.xl,p=t.xxl,m=t.className,y=i(t,vs);[o,a,l,u,d,f,p].forEach((function(e){"object"===Le(e)&&null!==e&&(e.span=e.size)}));var v=r&&hs[r],h=g()(s||m,v);return c().createElement(bs,e({xs:a||o,sm:l,md:u,lg:d,xl:f,xxl:p,className:h},b(["setProps"],y),{"data-dash-is-loading":Nt()||void 0}),n)}xs.propTypes={children:d().node,id:d().string,align:d().oneOf(["start","center","end","stretch","baseline"]),style:d().object,class_name:d().string,width:gs,xs:gs,sm:gs,md:gs,lg:gs,xl:gs,xxl:gs,key:d().string,className:d().string,setProps:d().func};const Os=xs;var _s=["children","is_open","navbar","className","class_name","tag","dimension"],ws=c().forwardRef((function(t,n){var r=t.children,s=t.is_open,o=t.navbar,a=t.className,l=t.class_name,u=t.tag,d=t.dimension,f=void 0===d?"height":d,p=i(t,_s);return c().createElement(be,e({in:s,as:u,className:l||a,dimension:f},b(["setProps"],p),{"data-dash-is-loading":Nt()||void 0}),c().createElement("div",{ref:n,className:o&&"navbar-collapse"},r))}));ws.propTypes={children:d().node,id:d().string,is_open:d().bool,dimension:d().oneOf(["height","width"]),navbar:d().bool,style:d().object,class_name:d().string,tag:d().string,key:d().string,className:d().string,setProps:d().func};const Ns=ws,js=l.forwardRef((({bsPrefix:e,fluid:t=!1,as:n="div",className:r,...s},o)=>{const a=E(e,"container"),i="string"==typeof t?`-${t}`:"-fluid";return(0,O.jsx)(n,{ref:o,...s,className:g()(r,t?`${a}${i}`:a)})}));js.displayName="Container";const Es=js;var ks=["children","class_name","tag","className"];function Ps(t){var n=t.children,r=t.class_name,s=t.tag,o=t.className,a=i(t,ks);return c().createElement(Es,e({as:s,className:r||o},b(["setProps"],a),{"data-dash-is-loading":Nt()||void 0}),n)}Ps.propTypes={children:d().node,id:d().string,fluid:d().oneOfType([d().bool,d().string]),style:d().object,class_name:d().string,tag:d().string,key:d().string,className:d().string,setProps:d().func};const Cs=Ps;var Ts=Function.prototype.bind.call(Function.prototype.call,[].slice);function Ss(e,t){return Ts(e.querySelectorAll(t))}function Rs(e,t,n){const r=(0,l.useRef)(void 0!==e),[s,o]=(0,l.useState)(t),a=void 0!==e,i=r.current;return r.current=a,!a&&i&&s!==t&&o(t),[a?e:s,(0,l.useCallback)(((...e)=>{const[t,...r]=e;let s=null==n?void 0:n(t,...r);return o(t),s}),[n])]}function Ds(){const[,e]=(0,l.useReducer)((e=>e+1),0);return e}const Ls=l.createContext(null);var Is=Object.prototype.hasOwnProperty;function As(e,t,n){for(n of e.keys())if($s(n,t))return n}function $s(e,t){var n,r,s;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((r=e.length)===t.length)for(;r--&&$s(e[r],t[r]););return-1===r}if(n===Set){if(e.size!==t.size)return!1;for(r of e){if((s=r)&&"object"==typeof s&&!(s=As(t,s)))return!1;if(!t.has(s))return!1}return!0}if(n===Map){if(e.size!==t.size)return!1;for(r of e){if((s=r[0])&&"object"==typeof s&&!(s=As(t,s)))return!1;if(!$s(r[1],t.get(s)))return!1}return!0}if(n===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(n===DataView){if((r=e.byteLength)===t.byteLength)for(;r--&&e.getInt8(r)===t.getInt8(r););return-1===r}if(ArrayBuffer.isView(e)){if((r=e.byteLength)===t.byteLength)for(;r--&&e[r]===t[r];);return-1===r}if(!n||"object"==typeof e){for(n in r=0,e){if(Is.call(e,n)&&++r&&!Is.call(t,n))return!1;if(!(n in t)||!$s(e[n],t[n]))return!1}return Object.keys(t).length===r}}return e!=e&&t!=t}function Ms(e){return e.split("-")[0]}function Fs(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Bs(e){return e instanceof Fs(e).Element||e instanceof Element}function zs(e){return e instanceof Fs(e).HTMLElement||e instanceof HTMLElement}function Hs(e){return"undefined"!=typeof ShadowRoot&&(e instanceof Fs(e).ShadowRoot||e instanceof ShadowRoot)}var Ks=Math.max,Us=Math.min,Ws=Math.round;function qs(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function Vs(){return!/^((?!chrome|android).)*safari/i.test(qs())}function Gs(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),s=1,o=1;t&&zs(e)&&(s=e.offsetWidth>0&&Ws(r.width)/e.offsetWidth||1,o=e.offsetHeight>0&&Ws(r.height)/e.offsetHeight||1);var a=(Bs(e)?Fs(e):window).visualViewport,i=!Vs()&&n,l=(r.left+(i&&a?a.offsetLeft:0))/s,c=(r.top+(i&&a?a.offsetTop:0))/o,u=r.width/s,d=r.height/o;return{width:u,height:d,top:c,right:l+u,bottom:c+d,left:l,x:l,y:c}}function Ys(e){var t=Gs(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function Xs(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&Hs(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function Js(e){return e?(e.nodeName||"").toLowerCase():null}function Zs(e){return Fs(e).getComputedStyle(e)}function Qs(e){return["table","td","th"].indexOf(Js(e))>=0}function eo(e){return((Bs(e)?e.ownerDocument:e.document)||window.document).documentElement}function to(e){return"html"===Js(e)?e:e.assignedSlot||e.parentNode||(Hs(e)?e.host:null)||eo(e)}function no(e){return zs(e)&&"fixed"!==Zs(e).position?e.offsetParent:null}function ro(e){for(var t=Fs(e),n=no(e);n&&Qs(n)&&"static"===Zs(n).position;)n=no(n);return n&&("html"===Js(n)||"body"===Js(n)&&"static"===Zs(n).position)?t:n||function(e){var t=/firefox/i.test(qs());if(/Trident/i.test(qs())&&zs(e)&&"fixed"===Zs(e).position)return null;var n=to(e);for(Hs(n)&&(n=n.host);zs(n)&&["html","body"].indexOf(Js(n))<0;){var r=Zs(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}function so(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function oo(e,t,n){return Ks(e,Us(t,n))}function ao(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function io(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}var lo="top",co="bottom",uo="right",fo="left",po="auto",mo=[lo,co,uo,fo],bo="start",yo="end",go="viewport",vo="popper",ho=mo.reduce((function(e,t){return e.concat([t+"-"+bo,t+"-"+yo])}),[]),xo=[].concat(mo,[po]).reduce((function(e,t){return e.concat([t,t+"-"+bo,t+"-"+yo])}),[]),Oo=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];const _o={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,r=e.name,s=e.options,o=n.elements.arrow,a=n.modifiersData.popperOffsets,i=Ms(n.placement),l=so(i),c=[fo,uo].indexOf(i)>=0?"height":"width";if(o&&a){var u=function(e,t){return ao("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:io(e,mo))}(s.padding,n),d=Ys(o),f="y"===l?lo:fo,p="y"===l?co:uo,m=n.rects.reference[c]+n.rects.reference[l]-a[l]-n.rects.popper[c],b=a[l]-n.rects.reference[l],y=ro(o),g=y?"y"===l?y.clientHeight||0:y.clientWidth||0:0,v=m/2-b/2,h=u[f],x=g-d[c]-u[p],O=g/2-d[c]/2+v,_=oo(h,O,x),w=l;n.modifiersData[r]=((t={})[w]=_,t.centerOffset=_-O,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&Xs(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function wo(e){return e.split("-")[1]}var No={top:"auto",right:"auto",bottom:"auto",left:"auto"};function jo(e){var t,n=e.popper,r=e.popperRect,s=e.placement,o=e.variation,a=e.offsets,i=e.position,l=e.gpuAcceleration,c=e.adaptive,u=e.roundOffsets,d=e.isFixed,f=a.x,p=void 0===f?0:f,m=a.y,b=void 0===m?0:m,y="function"==typeof u?u({x:p,y:b}):{x:p,y:b};p=y.x,b=y.y;var g=a.hasOwnProperty("x"),v=a.hasOwnProperty("y"),h=fo,x=lo,O=window;if(c){var _=ro(n),w="clientHeight",N="clientWidth";_===Fs(n)&&"static"!==Zs(_=eo(n)).position&&"absolute"===i&&(w="scrollHeight",N="scrollWidth"),(s===lo||(s===fo||s===uo)&&o===yo)&&(x=co,b-=(d&&_===O&&O.visualViewport?O.visualViewport.height:_[w])-r.height,b*=l?1:-1),s!==fo&&(s!==lo&&s!==co||o!==yo)||(h=uo,p-=(d&&_===O&&O.visualViewport?O.visualViewport.width:_[N])-r.width,p*=l?1:-1)}var j,E=Object.assign({position:i},c&&No),k=!0===u?function(e,t){var n=e.x,r=e.y,s=t.devicePixelRatio||1;return{x:Ws(n*s)/s||0,y:Ws(r*s)/s||0}}({x:p,y:b},Fs(n)):{x:p,y:b};return p=k.x,b=k.y,l?Object.assign({},E,((j={})[x]=v?"0":"",j[h]=g?"0":"",j.transform=(O.devicePixelRatio||1)<=1?"translate("+p+"px, "+b+"px)":"translate3d("+p+"px, "+b+"px, 0)",j)):Object.assign({},E,((t={})[x]=v?b+"px":"",t[h]=g?p+"px":"",t.transform="",t))}const Eo={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,s=void 0===r||r,o=n.adaptive,a=void 0===o||o,i=n.roundOffsets,l=void 0===i||i,c={placement:Ms(t.placement),variation:wo(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:s,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,jo(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:a,roundOffsets:l})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,jo(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}};var ko={passive:!0};const Po={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,s=r.scroll,o=void 0===s||s,a=r.resize,i=void 0===a||a,l=Fs(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return o&&c.forEach((function(e){e.addEventListener("scroll",n.update,ko)})),i&&l.addEventListener("resize",n.update,ko),function(){o&&c.forEach((function(e){e.removeEventListener("scroll",n.update,ko)})),i&&l.removeEventListener("resize",n.update,ko)}},data:{}};var Co={left:"right",right:"left",bottom:"top",top:"bottom"};function To(e){return e.replace(/left|right|bottom|top/g,(function(e){return Co[e]}))}var So={start:"end",end:"start"};function Ro(e){return e.replace(/start|end/g,(function(e){return So[e]}))}function Do(e){var t=Fs(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function Lo(e){return Gs(eo(e)).left+Do(e).scrollLeft}function Io(e){var t=Zs(e),n=t.overflow,r=t.overflowX,s=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+s+r)}function Ao(e){return["html","body","#document"].indexOf(Js(e))>=0?e.ownerDocument.body:zs(e)&&Io(e)?e:Ao(to(e))}function $o(e,t){var n;void 0===t&&(t=[]);var r=Ao(e),s=r===(null==(n=e.ownerDocument)?void 0:n.body),o=Fs(r),a=s?[o].concat(o.visualViewport||[],Io(r)?r:[]):r,i=t.concat(a);return s?i:i.concat($o(to(a)))}function Mo(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Fo(e,t,n){return t===go?Mo(function(e,t){var n=Fs(e),r=eo(e),s=n.visualViewport,o=r.clientWidth,a=r.clientHeight,i=0,l=0;if(s){o=s.width,a=s.height;var c=Vs();(c||!c&&"fixed"===t)&&(i=s.offsetLeft,l=s.offsetTop)}return{width:o,height:a,x:i+Lo(e),y:l}}(e,n)):Bs(t)?function(e,t){var n=Gs(e,!1,"fixed"===t);return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}(t,n):Mo(function(e){var t,n=eo(e),r=Do(e),s=null==(t=e.ownerDocument)?void 0:t.body,o=Ks(n.scrollWidth,n.clientWidth,s?s.scrollWidth:0,s?s.clientWidth:0),a=Ks(n.scrollHeight,n.clientHeight,s?s.scrollHeight:0,s?s.clientHeight:0),i=-r.scrollLeft+Lo(e),l=-r.scrollTop;return"rtl"===Zs(s||n).direction&&(i+=Ks(n.clientWidth,s?s.clientWidth:0)-o),{width:o,height:a,x:i,y:l}}(eo(e)))}function Bo(e){var t,n=e.reference,r=e.element,s=e.placement,o=s?Ms(s):null,a=s?wo(s):null,i=n.x+n.width/2-r.width/2,l=n.y+n.height/2-r.height/2;switch(o){case lo:t={x:i,y:n.y-r.height};break;case co:t={x:i,y:n.y+n.height};break;case uo:t={x:n.x+n.width,y:l};break;case fo:t={x:n.x-r.width,y:l};break;default:t={x:n.x,y:n.y}}var c=o?so(o):null;if(null!=c){var u="y"===c?"height":"width";switch(a){case bo:t[c]=t[c]-(n[u]/2-r[u]/2);break;case yo:t[c]=t[c]+(n[u]/2-r[u]/2)}}return t}function zo(e,t){void 0===t&&(t={});var n=t,r=n.placement,s=void 0===r?e.placement:r,o=n.strategy,a=void 0===o?e.strategy:o,i=n.boundary,l=void 0===i?"clippingParents":i,c=n.rootBoundary,u=void 0===c?go:c,d=n.elementContext,f=void 0===d?vo:d,p=n.altBoundary,m=void 0!==p&&p,b=n.padding,y=void 0===b?0:b,g=ao("number"!=typeof y?y:io(y,mo)),v=f===vo?"reference":vo,h=e.rects.popper,x=e.elements[m?v:f],O=function(e,t,n,r){var s="clippingParents"===t?function(e){var t=$o(to(e)),n=["absolute","fixed"].indexOf(Zs(e).position)>=0&&zs(e)?ro(e):e;return Bs(n)?t.filter((function(e){return Bs(e)&&Xs(e,n)&&"body"!==Js(e)})):[]}(e):[].concat(t),o=[].concat(s,[n]),a=o[0],i=o.reduce((function(t,n){var s=Fo(e,n,r);return t.top=Ks(s.top,t.top),t.right=Us(s.right,t.right),t.bottom=Us(s.bottom,t.bottom),t.left=Ks(s.left,t.left),t}),Fo(e,a,r));return i.width=i.right-i.left,i.height=i.bottom-i.top,i.x=i.left,i.y=i.top,i}(Bs(x)?x:x.contextElement||eo(e.elements.popper),l,u,a),_=Gs(e.elements.reference),w=Bo({reference:_,element:h,strategy:"absolute",placement:s}),N=Mo(Object.assign({},h,w)),j=f===vo?N:_,E={top:O.top-j.top+g.top,bottom:j.bottom-O.bottom+g.bottom,left:O.left-j.left+g.left,right:j.right-O.right+g.right},k=e.modifiersData.offset;if(f===vo&&k){var P=k[s];Object.keys(E).forEach((function(e){var t=[uo,co].indexOf(e)>=0?1:-1,n=[lo,co].indexOf(e)>=0?"y":"x";E[e]+=P[n]*t}))}return E}const Ho={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var s=n.mainAxis,o=void 0===s||s,a=n.altAxis,i=void 0===a||a,l=n.fallbackPlacements,c=n.padding,u=n.boundary,d=n.rootBoundary,f=n.altBoundary,p=n.flipVariations,m=void 0===p||p,b=n.allowedAutoPlacements,y=t.options.placement,g=Ms(y),v=l||(g!==y&&m?function(e){if(Ms(e)===po)return[];var t=To(e);return[Ro(e),t,Ro(t)]}(y):[To(y)]),h=[y].concat(v).reduce((function(e,n){return e.concat(Ms(n)===po?function(e,t){void 0===t&&(t={});var n=t,r=n.placement,s=n.boundary,o=n.rootBoundary,a=n.padding,i=n.flipVariations,l=n.allowedAutoPlacements,c=void 0===l?xo:l,u=wo(r),d=u?i?ho:ho.filter((function(e){return wo(e)===u})):mo,f=d.filter((function(e){return c.indexOf(e)>=0}));0===f.length&&(f=d);var p=f.reduce((function(t,n){return t[n]=zo(e,{placement:n,boundary:s,rootBoundary:o,padding:a})[Ms(n)],t}),{});return Object.keys(p).sort((function(e,t){return p[e]-p[t]}))}(t,{placement:n,boundary:u,rootBoundary:d,padding:c,flipVariations:m,allowedAutoPlacements:b}):n)}),[]),x=t.rects.reference,O=t.rects.popper,_=new Map,w=!0,N=h[0],j=0;j<h.length;j++){var E=h[j],k=Ms(E),P=wo(E)===bo,C=[lo,co].indexOf(k)>=0,T=C?"width":"height",S=zo(t,{placement:E,boundary:u,rootBoundary:d,altBoundary:f,padding:c}),R=C?P?uo:fo:P?co:lo;x[T]>O[T]&&(R=To(R));var D=To(R),L=[];if(o&&L.push(S[k]<=0),i&&L.push(S[R]<=0,S[D]<=0),L.every((function(e){return e}))){N=E,w=!1;break}_.set(E,L)}if(w)for(var I=function(e){var t=h.find((function(t){var n=_.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return N=t,"break"},A=m?3:1;A>0&&"break"!==I(A);A--);t.placement!==N&&(t.modifiersData[r]._skip=!0,t.placement=N,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function Ko(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function Uo(e){return[lo,uo,co,fo].some((function(t){return e[t]>=0}))}const Wo={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,s=n.offset,o=void 0===s?[0,0]:s,a=xo.reduce((function(e,n){return e[n]=function(e,t,n){var r=Ms(e),s=[fo,lo].indexOf(r)>=0?-1:1,o="function"==typeof n?n(Object.assign({},t,{placement:e})):n,a=o[0],i=o[1];return a=a||0,i=(i||0)*s,[fo,uo].indexOf(r)>=0?{x:i,y:a}:{x:a,y:i}}(n,t.rects,o),e}),{}),i=a[t.placement],l=i.x,c=i.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=c),t.modifiersData[r]=a}},qo={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,s=n.mainAxis,o=void 0===s||s,a=n.altAxis,i=void 0!==a&&a,l=n.boundary,c=n.rootBoundary,u=n.altBoundary,d=n.padding,f=n.tether,p=void 0===f||f,m=n.tetherOffset,b=void 0===m?0:m,y=zo(t,{boundary:l,rootBoundary:c,padding:d,altBoundary:u}),g=Ms(t.placement),v=wo(t.placement),h=!v,x=so(g),O="x"===x?"y":"x",_=t.modifiersData.popperOffsets,w=t.rects.reference,N=t.rects.popper,j="function"==typeof b?b(Object.assign({},t.rects,{placement:t.placement})):b,E="number"==typeof j?{mainAxis:j,altAxis:j}:Object.assign({mainAxis:0,altAxis:0},j),k=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,P={x:0,y:0};if(_){if(o){var C,T="y"===x?lo:fo,S="y"===x?co:uo,R="y"===x?"height":"width",D=_[x],L=D+y[T],I=D-y[S],A=p?-N[R]/2:0,$=v===bo?w[R]:N[R],M=v===bo?-N[R]:-w[R],F=t.elements.arrow,B=p&&F?Ys(F):{width:0,height:0},z=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},H=z[T],K=z[S],U=oo(0,w[R],B[R]),W=h?w[R]/2-A-U-H-E.mainAxis:$-U-H-E.mainAxis,q=h?-w[R]/2+A+U+K+E.mainAxis:M+U+K+E.mainAxis,V=t.elements.arrow&&ro(t.elements.arrow),G=V?"y"===x?V.clientTop||0:V.clientLeft||0:0,Y=null!=(C=null==k?void 0:k[x])?C:0,X=D+q-Y,J=oo(p?Us(L,D+W-Y-G):L,D,p?Ks(I,X):I);_[x]=J,P[x]=J-D}if(i){var Z,Q="x"===x?lo:fo,ee="x"===x?co:uo,te=_[O],ne="y"===O?"height":"width",re=te+y[Q],se=te-y[ee],oe=-1!==[lo,fo].indexOf(g),ae=null!=(Z=null==k?void 0:k[O])?Z:0,ie=oe?re:te-w[ne]-N[ne]-ae+E.altAxis,le=oe?te+w[ne]+N[ne]-ae-E.altAxis:se,ce=p&&oe?function(e,t,n){var r=oo(e,t,n);return r>n?n:r}(ie,te,le):oo(p?ie:re,te,p?le:se);_[O]=ce,P[O]=ce-te}t.modifiersData[r]=P}},requiresIfExists:["offset"]};function Vo(e,t,n){void 0===n&&(n=!1);var r,s,o=zs(t),a=zs(t)&&function(e){var t=e.getBoundingClientRect(),n=Ws(t.width)/e.offsetWidth||1,r=Ws(t.height)/e.offsetHeight||1;return 1!==n||1!==r}(t),i=eo(t),l=Gs(e,a,n),c={scrollLeft:0,scrollTop:0},u={x:0,y:0};return(o||!o&&!n)&&(("body"!==Js(t)||Io(i))&&(c=(r=t)!==Fs(r)&&zs(r)?{scrollLeft:(s=r).scrollLeft,scrollTop:s.scrollTop}:Do(r)),zs(t)?((u=Gs(t,!0)).x+=t.clientLeft,u.y+=t.clientTop):i&&(u.x=Lo(i))),{x:l.left+c.scrollLeft-u.x,y:l.top+c.scrollTop-u.y,width:l.width,height:l.height}}function Go(e){var t=new Map,n=new Set,r=[];function s(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var r=t.get(e);r&&s(r)}})),r.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||s(e)})),r}var Yo={placement:"bottom",modifiers:[],strategy:"absolute"};function Xo(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}const Jo=function(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,s=t.defaultOptions,o=void 0===s?Yo:s;return function(e,t,n){void 0===n&&(n=o);var s,a,i={placement:"bottom",orderedModifiers:[],options:Object.assign({},Yo,o),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},l=[],c=!1,u={state:i,setOptions:function(n){var s="function"==typeof n?n(i.options):n;d(),i.options=Object.assign({},o,i.options,s),i.scrollParents={reference:Bs(e)?$o(e):e.contextElement?$o(e.contextElement):[],popper:$o(t)};var a,c,f=function(e){var t=Go(e);return Oo.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}((a=[].concat(r,i.options.modifiers),c=a.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{}),Object.keys(c).map((function(e){return c[e]}))));return i.orderedModifiers=f.filter((function(e){return e.enabled})),i.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,r=void 0===n?{}:n,s=e.effect;if("function"==typeof s){var o=s({state:i,name:t,instance:u,options:r});l.push(o||function(){})}})),u.update()},forceUpdate:function(){if(!c){var e=i.elements,t=e.reference,n=e.popper;if(Xo(t,n)){i.rects={reference:Vo(t,ro(n),"fixed"===i.options.strategy),popper:Ys(n)},i.reset=!1,i.placement=i.options.placement,i.orderedModifiers.forEach((function(e){return i.modifiersData[e.name]=Object.assign({},e.data)}));for(var r=0;r<i.orderedModifiers.length;r++)if(!0!==i.reset){var s=i.orderedModifiers[r],o=s.fn,a=s.options,l=void 0===a?{}:a,d=s.name;"function"==typeof o&&(i=o({state:i,options:l,name:d,instance:u})||i)}else i.reset=!1,r=-1}}},update:(s=function(){return new Promise((function(e){u.forceUpdate(),e(i)}))},function(){return a||(a=new Promise((function(e){Promise.resolve().then((function(){a=void 0,e(s())}))}))),a}),destroy:function(){d(),c=!0}};if(!Xo(e,t))return u;function d(){l.forEach((function(e){return e()})),l=[]}return u.setOptions(n).then((function(e){!c&&n.onFirstUpdate&&n.onFirstUpdate(e)})),u}}({defaultModifiers:[{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,s=t.rects.popper,o=t.modifiersData.preventOverflow,a=zo(t,{elementContext:"reference"}),i=zo(t,{altBoundary:!0}),l=Ko(a,r),c=Ko(i,s,o),u=Uo(l),d=Uo(c);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":d})}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=Bo({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},Eo,Po,Wo,Ho,qo,_o]}),Zo=["enabled","placement","strategy","modifiers"],Qo={name:"applyStyles",enabled:!1,phase:"afterWrite",fn:()=>{}},ea={name:"ariaDescribedBy",enabled:!0,phase:"afterWrite",effect:({state:e})=>()=>{const{reference:t,popper:n}=e.elements;if("removeAttribute"in t){const e=(t.getAttribute("aria-describedby")||"").split(",").filter((e=>e.trim()!==n.id));e.length?t.setAttribute("aria-describedby",e.join(",")):t.removeAttribute("aria-describedby")}},fn:({state:e})=>{var t;const{popper:n,reference:r}=e.elements,s=null==(t=n.getAttribute("role"))?void 0:t.toLowerCase();if(n.id&&"tooltip"===s&&"setAttribute"in r){const e=r.getAttribute("aria-describedby");if(e&&-1!==e.split(",").indexOf(n.id))return;r.setAttribute("aria-describedby",e?`${e},${n.id}`:n.id)}}},ta=[],na=function(e,t,n={}){let{enabled:r=!0,placement:s="bottom",strategy:o="absolute",modifiers:a=ta}=n,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(n,Zo);const c=(0,l.useRef)(a),u=(0,l.useRef)(),d=(0,l.useCallback)((()=>{var e;null==(e=u.current)||e.update()}),[]),f=(0,l.useCallback)((()=>{var e;null==(e=u.current)||e.forceUpdate()}),[]),[p,m]=function(e){const t=zt();return[e[0],(0,l.useCallback)((n=>{if(t())return e[1](n)}),[t,e[1]])]}((0,l.useState)({placement:s,update:d,forceUpdate:f,attributes:{},styles:{popper:{},arrow:{}}})),b=(0,l.useMemo)((()=>({name:"updateStateModifier",enabled:!0,phase:"write",requires:["computeStyles"],fn:({state:e})=>{const t={},n={};Object.keys(e.elements).forEach((r=>{t[r]=e.styles[r],n[r]=e.attributes[r]})),m({state:e,styles:t,attributes:n,update:d,forceUpdate:f,placement:e.placement})}})),[d,f,m]),y=(0,l.useMemo)((()=>($s(c.current,a)||(c.current=a),c.current)),[a]);return(0,l.useEffect)((()=>{u.current&&r&&u.current.setOptions({placement:s,strategy:o,modifiers:[...y,b,Qo]})}),[o,s,b,r,y]),(0,l.useEffect)((()=>{if(r&&null!=e&&null!=t)return u.current=Jo(e,t,Object.assign({},i,{placement:s,strategy:o,modifiers:[...y,ea,b]})),()=>{null!=u.current&&(u.current.destroy(),u.current=void 0,m((e=>Object.assign({},e,{attributes:{},styles:{popper:{}}}))))}}),[r,e,t]),p};function ra(e,t){return e.contains?e.contains(t):e.compareDocumentPosition?e===t||!!(16&e.compareDocumentPosition(t)):void 0}var sa=n(2),oa=n.n(sa);const aa=()=>{},ia=e=>e&&("current"in e?e.current:e),la={click:"mousedown",mouseup:"mousedown",pointerup:"pointerdown"},ca=function(e,t=aa,{disabled:n,clickTrigger:r="click"}={}){const s=(0,l.useRef)(!1),o=(0,l.useRef)(!1),a=(0,l.useCallback)((t=>{const n=ia(e);var r;oa()(!!n,"ClickOutside captured a close event but does not have a ref to compare it to. useClickOutside(), should be passed a ref that resolves to a DOM node"),s.current=!n||!!((r=t).metaKey||r.altKey||r.ctrlKey||r.shiftKey)||!function(e){return 0===e.button}(t)||!!ra(n,t.target)||o.current,o.current=!1}),[e]),i=Bt((t=>{const n=ia(e);n&&ra(n,t.target)?o.current=!0:o.current=!1})),c=Bt((e=>{s.current||t(e)}));(0,l.useEffect)((()=>{var t,s;if(n||null==e)return;const o=T(ia(e)),l=o.defaultView||window;let u=null!=(t=l.event)?t:null==(s=l.parent)?void 0:s.event,d=null;la[r]&&(d=ne(o,la[r],i,!0));const f=ne(o,r,a,!0),p=ne(o,r,(e=>{e!==u?c(e):u=void 0}));let m=[];return"ontouchstart"in o.documentElement&&(m=[].slice.call(o.body.children).map((e=>ne(e,"mousemove",aa)))),()=>{null==d||d(),f(),p(),m.forEach((e=>e()))}}),[e,n,r,a,i,c])};function ua(e={}){return Array.isArray(e)?e:Object.keys(e).map((t=>(e[t].name=t,e[t])))}function da({enabled:e,enableEvents:t,placement:n,flip:r,offset:s,fixed:o,containerPadding:a,arrowElement:i,popperConfig:l={}}){var c,u,d,f,p;const m=function(e){const t={};return Array.isArray(e)?(null==e||e.forEach((e=>{t[e.name]=e})),t):e||t}(l.modifiers);return Object.assign({},l,{placement:n,enabled:e,strategy:o?"fixed":l.strategy,modifiers:ua(Object.assign({},m,{eventListeners:{enabled:t,options:null==(c=m.eventListeners)?void 0:c.options},preventOverflow:Object.assign({},m.preventOverflow,{options:a?Object.assign({padding:a},null==(u=m.preventOverflow)?void 0:u.options):null==(d=m.preventOverflow)?void 0:d.options}),offset:{options:Object.assign({offset:s},null==(f=m.offset)?void 0:f.options)},arrow:Object.assign({},m.arrow,{enabled:!!i,options:Object.assign({},null==(p=m.arrow)?void 0:p.options,{element:i})}),flip:Object.assign({enabled:!!r},m.flip)}))})}const fa=["children","usePopper"],pa=()=>{};function ma(e={}){const t=(0,l.useContext)(Ls),[n,r]=Ft(),s=(0,l.useRef)(!1),{flip:o,offset:a,rootCloseEvent:i,fixed:c=!1,placement:u,popperConfig:d={},enableEventListeners:f=!0,usePopper:p=!!t}=e,m=null==(null==t?void 0:t.show)?!!e.show:t.show;m&&!s.current&&(s.current=!0);const{placement:b,setMenu:y,menuElement:g,toggleElement:v}=t||{},h=na(v,g,da({placement:u||b||"bottom-start",enabled:p,enableEvents:null==f?m:f,offset:a,flip:o,fixed:c,arrowElement:n,popperConfig:d})),x=Object.assign({ref:y||pa,"aria-labelledby":null==v?void 0:v.id},h.attributes.popper,{style:h.styles.popper}),O={show:m,placement:b,hasShown:s.current,toggle:null==t?void 0:t.toggle,popper:p?h:null,arrowProps:p?Object.assign({ref:r},h.attributes.arrow,{style:h.styles.arrow}):{}};return ca(g,(e=>{null==t||t.toggle(!1,e)}),{clickTrigger:i,disabled:!m}),[x,O]}function ba(e){let{children:t,usePopper:n=!0}=e,r=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,fa);const[s,o]=ma(Object.assign({},r,{usePopper:n}));return(0,O.jsx)(O.Fragment,{children:t(s,o)})}ba.displayName="DropdownMenu";const ya=ba,ga={prefix:String(Math.round(1e10*Math.random())),current:0},va=l.createContext(ga),ha=l.createContext(!1);let xa=Boolean("undefined"!=typeof window&&window.document&&window.document.createElement),Oa=new WeakMap;const _a="function"==typeof l.useId?function(e){let t=l.useId(),[n]=(0,l.useState)("function"==typeof l.useSyncExternalStore?l.useSyncExternalStore(ja,wa,Na):(0,l.useContext)(ha));return e||`${n?"react-aria":`react-aria${ga.prefix}`}-${t}`}:function(e){let t=(0,l.useContext)(va);t!==ga||xa||console.warn("When server rendering, you must wrap your application in an <SSRProvider> to ensure consistent ids are generated between the client and server.");let n=function(e=!1){let t=(0,l.useContext)(va),n=(0,l.useRef)(null);if(null===n.current&&!e){var r,s;let e=null===(s=l.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED)||void 0===s||null===(r=s.ReactCurrentOwner)||void 0===r?void 0:r.current;if(e){let n=Oa.get(e);null==n?Oa.set(e,{id:t.current,state:e.memoizedState}):e.memoizedState!==n.state&&(t.current=n.id,Oa.delete(e))}n.current=++t.current}return n.current}(!!e),r=`react-aria${t.prefix}`;return e||`${r}-${n}`};function wa(){return!1}function Na(){return!0}function ja(e){return()=>{}}const Ea=e=>{var t;return"menu"===(null==(t=e.getAttribute("role"))?void 0:t.toLowerCase())},ka=()=>{};function Pa(){const e=_a(),{show:t=!1,toggle:n=ka,setToggle:r,menuElement:s}=(0,l.useContext)(Ls)||{},o=(0,l.useCallback)((e=>{n(!t,e)}),[t,n]),a={id:e,ref:r||ka,onClick:o,"aria-expanded":!!t};return s&&Ea(s)&&(a["aria-haspopup"]=!0),[a,{show:t,toggle:n}]}function Ca({children:e}){const[t,n]=Pa();return(0,O.jsx)(O.Fragment,{children:e(t,n)})}Ca.displayName="DropdownToggle";const Ta=Ca,Sa=(e,t=null)=>null!=e?String(e):t||null,Ra=l.createContext(null),Da=l.createContext(null);Da.displayName="NavContext";const La=Da;function Ia(e){return`data-rr-ui-${e}`}const Aa=["eventKey","disabled","onClick","active","as"];function $a({key:e,href:t,active:n,disabled:r,onClick:s}){const o=(0,l.useContext)(Ra),a=(0,l.useContext)(La),{activeKey:i}=a||{},c=Sa(e,t),u=null==n&&null!=e?Sa(i)===c:n;return[{onClick:Bt((e=>{r||(null==s||s(e),o&&!e.isPropagationStopped()&&o(c,e))})),"aria-disabled":r||void 0,"aria-selected":u,[Ia("dropdown-item")]:""},{isActive:u}]}const Ma=l.forwardRef(((e,t)=>{let{eventKey:n,disabled:r,onClick:s,active:o,as:a=Gt}=e,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,Aa);const[l]=$a({key:n,href:i.href,disabled:r,onClick:s,active:o});return(0,O.jsx)(a,Object.assign({},i,{ref:t},l))}));Ma.displayName="DropdownItem";const Fa=Ma,Ba=(0,l.createContext)(X?window:void 0);function za(){return(0,l.useContext)(Ba)}function Ha(){const e=Ds(),t=(0,l.useRef)(null),n=(0,l.useCallback)((n=>{t.current=n,e()}),[e]);return[t,n]}function Ka({defaultShow:e,show:t,onSelect:n,onToggle:r,itemSelector:s=`* [${Ia("dropdown-item")}]`,focusFirstItemOnShow:o,placement:a="bottom-start",children:i}){const c=za(),[u,d]=Rs(t,e,r),[f,p]=Ha(),m=f.current,[b,y]=Ha(),g=b.current,v=Ht(u),h=(0,l.useRef)(null),x=(0,l.useRef)(!1),_=(0,l.useContext)(Ra),w=(0,l.useCallback)(((e,t,n=(null==t?void 0:t.type))=>{d(e,{originalEvent:t,source:n})}),[d]),N=Bt(((e,t)=>{null==n||n(e,t),w(!1,t,"select"),t.isPropagationStopped()||null==_||_(e,t)})),j=(0,l.useMemo)((()=>({toggle:w,placement:a,show:u,menuElement:m,toggleElement:g,setMenu:p,setToggle:y})),[w,a,u,m,g,p,y]);m&&v&&!u&&(x.current=m.contains(m.ownerDocument.activeElement));const E=Bt((()=>{g&&g.focus&&g.focus()})),k=Bt((()=>{const e=h.current;let t=o;if(null==t&&(t=!(!f.current||!Ea(f.current))&&"keyboard"),!1===t||"keyboard"===t&&!/^key.+$/.test(e))return;const n=Ss(f.current,s)[0];n&&n.focus&&n.focus()}));(0,l.useEffect)((()=>{u?k():x.current&&(x.current=!1,E())}),[u,x,E,k]),(0,l.useEffect)((()=>{h.current=null}));const P=(e,t)=>{if(!f.current)return null;const n=Ss(f.current,s);let r=n.indexOf(e)+t;return r=Math.max(0,Math.min(r,n.length)),n[r]};return function(e,t,n,r=!1){const s=Bt(n);(0,l.useEffect)((()=>{const n="function"==typeof e?e():e;return n.addEventListener(t,s,r),()=>n.removeEventListener(t,s,r)}),[e])}((0,l.useCallback)((()=>c.document),[c]),"keydown",(e=>{var t,n;const{key:r}=e,s=e.target,o=null==(t=f.current)?void 0:t.contains(s),a=null==(n=b.current)?void 0:n.contains(s);if(/input|textarea/i.test(s.tagName)&&(" "===r||"Escape"!==r&&o||"Escape"===r&&"search"===s.type))return;if(!o&&!a)return;if(!("Tab"!==r||f.current&&u))return;h.current=e.type;const i={originalEvent:e,source:e.type};switch(r){case"ArrowUp":{const t=P(s,-1);return t&&t.focus&&t.focus(),void e.preventDefault()}case"ArrowDown":if(e.preventDefault(),u){const e=P(s,1);e&&e.focus&&e.focus()}else d(!0,i);return;case"Tab":ee(s.ownerDocument,"keyup",(e=>{var t;("Tab"!==e.key||e.target)&&null!=(t=f.current)&&t.contains(e.target)||d(!1,i)}),{once:!0});break;case"Escape":"Escape"===r&&(e.preventDefault(),e.stopPropagation()),d(!1,i)}})),(0,O.jsx)(Ra.Provider,{value:N,children:(0,O.jsx)(Ls.Provider,{value:j,children:i})})}Ba.Provider,Ka.displayName="Dropdown",Ka.Menu=ya,Ka.Toggle=Ta,Ka.Item=Fa;const Ua=Ka,Wa=l.createContext({});Wa.displayName="DropdownContext";const qa=Wa,Va=l.forwardRef((({className:e,bsPrefix:t,as:n="hr",role:r="separator",...s},o)=>(t=E(t,"dropdown-divider"),(0,O.jsx)(n,{ref:o,className:g()(e,t),role:r,...s}))));Va.displayName="DropdownDivider";const Ga=Va,Ya=l.forwardRef((({className:e,bsPrefix:t,as:n="div",role:r="heading",...s},o)=>(t=E(t,"dropdown-header"),(0,O.jsx)(n,{ref:o,className:g()(e,t),role:r,...s}))));Ya.displayName="DropdownHeader";const Xa=Ya,Ja=l.forwardRef((({bsPrefix:e,className:t,eventKey:n,disabled:r=!1,onClick:s,active:o,as:a=Jt,...i},l)=>{const c=E(e,"dropdown-item"),[u,d]=$a({key:n,href:i.href,disabled:r,onClick:s,active:o});return(0,O.jsx)(a,{...i,...u,ref:l,className:g()(t,c,d.isActive&&"active",r&&"disabled")})}));Ja.displayName="DropdownItem";const Za=Ja,Qa=l.forwardRef((({className:e,bsPrefix:t,as:n="span",...r},s)=>(t=E(t,"dropdown-item-text"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));Qa.displayName="DropdownItemText";const ei=Qa,ti=void 0!==n.g&&n.g.navigator&&"ReactNative"===n.g.navigator.product,ni="undefined"!=typeof document||ti?l.useLayoutEffect:l.useEffect,ri=l.createContext(null);ri.displayName="InputGroupContext";const si=ri,oi=l.createContext(null);oi.displayName="NavbarContext";const ai=oi;function ii(e,t){return e}function li(e,t,n){let r=e?n?"bottom-start":"bottom-end":n?"bottom-end":"bottom-start";return"up"===t?r=e?n?"top-start":"top-end":n?"top-end":"top-start":"end"===t?r=e?n?"left-end":"right-end":n?"left-start":"right-start":"start"===t?r=e?n?"right-end":"left-end":n?"right-start":"left-start":"down-centered"===t?r="bottom":"up-centered"===t&&(r="top"),r}const ci=l.forwardRef((({bsPrefix:e,className:t,align:n,rootCloseEvent:r,flip:s=!0,show:o,renderOnMount:a,as:i="div",popperConfig:c,variant:u,...d},f)=>{let p=!1;const m=(0,l.useContext)(ai),b=E(e,"dropdown-menu"),{align:y,drop:v,isRTL:h}=(0,l.useContext)(qa);n=n||y;const x=(0,l.useContext)(si),_=[];if(n)if("object"==typeof n){const e=Object.keys(n);if(e.length){const t=e[0],r=n[t];p="start"===r,_.push(`${b}-${t}-${r}`)}}else"end"===n&&(p=!0);const w=li(p,v,h),[N,{hasShown:j,popper:k,show:P,toggle:C}]=ma({flip:s,rootCloseEvent:r,show:o,usePopper:!m&&0===_.length,offset:[0,2],popperConfig:c,placement:w});if(N.ref=ce(ii(f),N.ref),ni((()=>{P&&(null==k||k.update())}),[P]),!j&&!a&&!x)return null;"string"!=typeof i&&(N.show=P,N.close=()=>null==C?void 0:C(!1),N.align=n);let T=d.style;return null!=k&&k.placement&&(T={...d.style,...N.style},d["x-placement"]=k.placement),(0,O.jsx)(i,{...d,...N,style:T,...(_.length||m)&&{"data-bs-popper":"static"},className:g()(t,b,P&&"show",p&&`${b}-end`,u&&`${b}-${u}`,..._)})}));ci.displayName="DropdownMenu";const ui=ci,di=l.forwardRef((({bsPrefix:e,split:t,className:n,childBsPrefix:r,as:s=An,...o},a)=>{const i=E(e,"dropdown-toggle"),c=(0,l.useContext)(Ls);void 0!==r&&(o.bsPrefix=r);const[u]=Pa();return u.ref=ce(u.ref,ii(a)),(0,O.jsx)(s,{className:g()(n,i,t&&`${i}-split`,(null==c?void 0:c.show)&&"show"),...u,...o})}));di.displayName="DropdownToggle";const fi=di,pi=l.forwardRef(((e,t)=>{const{bsPrefix:n,drop:r="down",show:s,className:o,align:a="start",onSelect:i,onToggle:c,focusFirstItemOnShow:u,as:d="div",navbar:f,autoClose:p=!0,...m}=x(e,{show:"onToggle"}),b=(0,l.useContext)(si),y=E(n,"dropdown"),v=C(),h=Lt(((e,t)=>{var n,r;(null==(n=t.originalEvent)||null==(n=n.target)?void 0:n.classList.contains("dropdown-toggle"))&&"mousedown"===t.source||(t.originalEvent.currentTarget!==document||"keydown"===t.source&&"Escape"!==t.originalEvent.key||(t.source="rootClose"),r=t.source,(!1===p?"click"===r:"inside"===p?"rootClose"!==r:"outside"!==p||"select"!==r)&&(null==c||c(e,t)))})),_=li("end"===a,r,v),w=(0,l.useMemo)((()=>({align:a,drop:r,isRTL:v})),[a,r,v]),N={down:y,"down-centered":`${y}-center`,up:"dropup","up-centered":"dropup-center dropup",end:"dropend",start:"dropstart"};return(0,O.jsx)(qa.Provider,{value:w,children:(0,O.jsx)(Ua,{placement:_,show:s,onSelect:i,onToggle:h,focusFirstItemOnShow:u,itemSelector:`.${y}-item:not(.disabled):not(:disabled)`,children:b?m.children:(0,O.jsx)(d,{...m,ref:t,className:g()(o,s&&"show",N[r])})})})}));pi.displayName="Dropdown";const mi=Object.assign(pi,{Toggle:fi,Menu:ui,Item:Za,ItemText:ei,Divider:Ga,Header:Xa}),bi=e=>e&&"function"!=typeof e?t=>{e.current=t}:e,yi=function(e,t){return(0,l.useMemo)((()=>function(e,t){const n=bi(e),r=bi(t);return e=>{n&&n(e),r&&r(e)}}(e,t)),[e,t])},gi=l.createContext(null),vi=["as","active","eventKey"];function hi({key:e,onClick:t,active:n,id:r,role:s,disabled:o}){const a=(0,l.useContext)(Ra),i=(0,l.useContext)(La),c=(0,l.useContext)(gi);let u=n;const d={role:s};if(i){s||"tablist"!==i.role||(d.role="tab");const t=i.getControllerId(null!=e?e:null),o=i.getControlledId(null!=e?e:null);d[Ia("event-key")]=e,d.id=t||r,u=null==n&&null!=e?i.activeKey===e:n,!u&&(null!=c&&c.unmountOnExit||null!=c&&c.mountOnEnter)||(d["aria-controls"]=o)}return"tab"===d.role&&(d["aria-selected"]=u,u||(d.tabIndex=-1),o&&(d.tabIndex=-1,d["aria-disabled"]=!0)),d.onClick=Bt((n=>{o||(null==t||t(n),null!=e&&a&&!n.isPropagationStopped()&&a(e,n))})),[d,{isActive:u}]}const xi=l.forwardRef(((e,t)=>{let{as:n=Gt,active:r,eventKey:s}=e,o=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,vi);const[a,i]=hi(Object.assign({key:Sa(s,o.href),active:r},o));return a[Ia("active")]=i.isActive,(0,O.jsx)(n,Object.assign({},o,a,{ref:t}))}));xi.displayName="NavItem";const Oi=xi,_i=["as","onSelect","activeKey","role","onKeyDown"],wi=()=>{},Ni=Ia("event-key"),ji=l.forwardRef(((e,t)=>{let{as:n="div",onSelect:r,activeKey:s,role:o,onKeyDown:a}=e,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,_i);const c=Ds(),u=(0,l.useRef)(!1),d=(0,l.useContext)(Ra),f=(0,l.useContext)(gi);let p,m;f&&(o=o||"tablist",s=f.activeKey,p=f.getControlledId,m=f.getControllerId);const b=(0,l.useRef)(null),y=e=>{const t=b.current;if(!t)return null;const n=Ss(t,`[${Ni}]:not([aria-disabled=true])`),r=t.querySelector("[aria-selected=true]");if(!r||r!==document.activeElement)return null;const s=n.indexOf(r);if(-1===s)return null;let o=s+e;return o>=n.length&&(o=0),o<0&&(o=n.length-1),n[o]},g=(e,t)=>{null!=e&&(null==r||r(e,t),null==d||d(e,t))};(0,l.useEffect)((()=>{if(b.current&&u.current){const e=b.current.querySelector(`[${Ni}][aria-selected=true]`);null==e||e.focus()}u.current=!1}));const v=yi(t,b);return(0,O.jsx)(Ra.Provider,{value:g,children:(0,O.jsx)(La.Provider,{value:{role:o,activeKey:Sa(s),getControlledId:p||wi,getControllerId:m||wi},children:(0,O.jsx)(n,Object.assign({},i,{onKeyDown:e=>{if(null==a||a(e),!f)return;let t;switch(e.key){case"ArrowLeft":case"ArrowUp":t=y(-1);break;case"ArrowRight":case"ArrowDown":t=y(1);break;default:return}t&&(e.preventDefault(),g(t.dataset["rrUiEventKey"]||null,e),u.current=!0,c())},ref:v,role:o}))})})}));ji.displayName="Nav";const Ei=Object.assign(ji,{Item:Oi}),ki=l.forwardRef((({className:e,bsPrefix:t,as:n="div",...r},s)=>(t=E(t,"nav-item"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));ki.displayName="NavItem";const Pi=ki,Ci=l.forwardRef((({bsPrefix:e,className:t,as:n=Jt,active:r,eventKey:s,disabled:o=!1,...a},i)=>{e=E(e,"nav-link");const[l,c]=hi({key:Sa(s,a.href),active:r,disabled:o,...a});return(0,O.jsx)(n,{...a,...l,ref:i,disabled:o,className:g()(t,e,o&&"disabled",c.isActive&&"active")})}));Ci.displayName="NavLink";const Ti=Ci,Si=l.forwardRef(((e,t)=>{const{as:n="div",bsPrefix:r,variant:s,fill:o=!1,justify:a=!1,navbar:i,navbarScroll:c,className:u,activeKey:d,...f}=x(e,{activeKey:"onSelect"}),p=E(r,"nav");let m,b,y=!1;const v=(0,l.useContext)(ai),h=(0,l.useContext)(Xn);return v?(m=v.bsPrefix,y=null==i||i):h&&({cardHeaderBsPrefix:b}=h),(0,O.jsx)(Ei,{as:n,ref:t,activeKey:d,className:g()(u,{[p]:!y,[`${m}-nav`]:y,[`${m}-nav-scroll`]:y&&c,[`${b}-${s}`]:!!b,[`${p}-${s}`]:!!s,[`${p}-fill`]:o,[`${p}-justified`]:a}),...f})}));Si.displayName="Nav";const Ri=Object.assign(Si,{Item:Pi,Link:Ti});var Di=c().createContext({}),Li=["caret","split","className","as"],Ii=c().forwardRef((function(t,n){var r=t.caret,s=t.split,o=t.className,a=t.as,u=void 0===a?An:a,d=i(t,Li),f=(0,l.useContext)(Ls),p=(0,l.useContext)(si),m=Ie(Pa(),1)[0];return m.ref=ce(m.ref,ii(n)),c().createElement(u,e({className:g()(o,r&&"dropdown-toggle",s&&"dropdown-toggle-split",!!p&&(null==f?void 0:f.show)&&"show")},m,d))}));Ii.propTypes={as:d().elementType,children:d().node,caret:d().bool,split:d().bool,className:d().string};const Ai=Ii;var $i=["children","label","color","direction","size","disabled","class_name","align_end","in_navbar","nav","caret","menu_variant","group","toggle_style","toggle_class_name","className","toggleClassName"];function Mi(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Fi(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Mi(Object(n),!0).forEach((function(t){Rt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Mi(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Bi(t){var n=t.children,r=t.label,s=t.color,o=t.direction,a=t.size,u=t.disabled,d=void 0!==u&&u,f=t.class_name,p=t.align_end,m=t.in_navbar,y=t.nav,g=t.caret,v=void 0===g||g,h=t.menu_variant,x=void 0===h?"light":h,O=t.group,_=t.toggle_style,w=t.toggle_class_name,N=t.className,j=t.toggleClassName,E=i(t,$i),k=Ie((0,l.useState)(!1),2),P=k[0],C=k[1],T=cn.has(s)||"link"===s,S=function(){d||C(!P)};return c().createElement(Di.Provider,{value:{toggle:S,isOpen:P}},c().createElement(mi,e({as:y?Ri.Item:O?zn:void 0,show:P,disabled:d,navbar:m,className:f||N,drop:o,onToggle:function(e,t){t&&"select"===t.source||C(e)},align:p?"end":"start"},b(["setProps"],E),{"data-dash-is-loading":Nt()||void 0}),c().createElement(Ai,{caret:v,as:y?Ri.Link:void 0,onClick:S,disabled:d,size:a,variant:T?s:void 0,style:T?_:Fi({backgroundColor:s},_),className:w||j},r),c().createElement(mi.Menu,{renderOnMount:!0,variant:"dark"===x?"dark":void 0},n)))}Bi.propTypes={children:d().node,id:d().string,label:d().node,color:d().string,direction:d().oneOf(["down","start","up","end"]),size:d().oneOf(["sm","md","lg"]),disabled:d().bool,style:d().object,class_name:d().string,align_end:d().bool,in_navbar:d().bool,nav:d().bool,caret:d().bool,menu_variant:d().oneOf(["light","dark"]),group:d().bool,toggle_style:d().object,toggle_class_name:d().string,key:d().string,className:d().string,toggleClassName:d().string,setProps:d().func};const zi=Bi;var Hi=["children","href","n_clicks","class_name","disabled","header","divider","toggle","target","className","setProps"];function Ki(t){var n=t.children,r=t.href,s=t.n_clicks,o=void 0===s?0:s,a=t.class_name,u=t.disabled,d=t.header,f=t.divider,p=t.toggle,m=void 0===p||p,y=t.target,g=t.className,v=t.setProps,h=i(t,Hi),x=(0,l.useContext)(Di),O=r&&!u;return h[O?"preOnClick":"onClick"]=function(e){return function(e){!u&&v&&v({n_clicks:o+1}),m&&x.isOpen&&x.toggle(e)}(e)},d?c().createElement(mi.Header,null,n):f?c().createElement(mi.Divider,null):c().createElement(mi.Item,e({as:O?_n:"button",href:O?r:void 0,disabled:u,target:O?y:void 0,className:a||g},b(["setProps"],h),{"data-dash-is-loading":Nt()||void 0}),n)}Ki.propTypes={children:d().node,id:d().string,href:d().string,external_link:d().bool,n_clicks:d().number,style:d().object,class_name:d().string,active:d().bool,disabled:d().bool,divider:d().bool,header:d().bool,toggle:d().bool,target:d().string,key:d().string,className:d().string,setProps:d().func};const Ui=Ki;var Wi=["children","is_in","style","class_name","tag","className"];function qi(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Vi(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?qi(Object(n),!0).forEach((function(t){Rt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):qi(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Gi=c().forwardRef((function(t,n){var r=t.children,s=t.is_in,o=t.style,a=t.class_name,u=t.tag,d=t.className,f=i(t,Wi),p=Ie((0,l.useState)(!s),2),m=p[0],y=p[1];return c().createElement(nn,e({in:s,style:m?Vi({visibility:"hidden"},o):o,onEnter:function(){return y(!1)},onExited:function(){return y(!0)},className:a||d,as:u},b(["setProps"],f),{"data-dash-is-loading":Nt()||void 0}),c().createElement("div",{ref:n},r))}));Gi.propTypes={children:d().node,id:d().string,is_in:d().bool,style:d().object,class_name:d().string,timeout:d().oneOfType([d().number,d().shape({enter:d().number,exit:d().number}).isRequired]),appear:d().bool,enter:d().bool,exit:d().bool,tag:d().string,key:d().string,className:d().string,setProps:d().func};const Yi=Gi,Xi={type:d().string,tooltip:d().bool,as:d().elementType},Ji=l.forwardRef((({as:e="div",className:t,type:n="valid",tooltip:r=!1,...s},o)=>(0,O.jsx)(e,{...s,ref:o,className:g()(t,`${n}-${r?"tooltip":"feedback"}`)})));Ji.displayName="Feedback",Ji.propTypes=Xi;const Zi=Ji,Qi=l.createContext({}),el=l.forwardRef((({id:e,bsPrefix:t,className:n,type:r="checkbox",isValid:s=!1,isInvalid:o=!1,as:a="input",...i},c)=>{const{controlId:u}=(0,l.useContext)(Qi);return t=E(t,"form-check-input"),(0,O.jsx)(a,{...i,ref:c,type:r,id:e||u,className:g()(n,t,s&&"is-valid",o&&"is-invalid")})}));el.displayName="FormCheckInput";const tl=el,nl=l.forwardRef((({bsPrefix:e,className:t,htmlFor:n,...r},s)=>{const{controlId:o}=(0,l.useContext)(Qi);return e=E(e,"form-check-label"),(0,O.jsx)("label",{...r,ref:s,htmlFor:n||o,className:g()(t,e)})}));nl.displayName="FormCheckLabel";const rl=nl,sl=l.forwardRef((({id:e,bsPrefix:t,bsSwitchPrefix:n,inline:r=!1,reverse:s=!1,disabled:o=!1,isValid:a=!1,isInvalid:i=!1,feedbackTooltip:c=!1,feedback:u,feedbackType:d,className:f,style:p,title:m="",type:b="checkbox",label:y,children:v,as:h="input",...x},_)=>{t=E(t,"form-check"),n=E(n,"form-switch");const{controlId:w}=(0,l.useContext)(Qi),N=(0,l.useMemo)((()=>({controlId:e||w})),[w,e]),j=!v&&null!=y&&!1!==y||function(e,t){return l.Children.toArray(e).some((e=>l.isValidElement(e)&&e.type===t))}(v,rl),k=(0,O.jsx)(tl,{...x,type:"switch"===b?"checkbox":b,ref:_,isValid:a,isInvalid:i,disabled:o,as:h});return(0,O.jsx)(Qi.Provider,{value:N,children:(0,O.jsx)("div",{style:p,className:g()(f,j&&t,r&&`${t}-inline`,s&&`${t}-reverse`,"switch"===b&&n),children:v||(0,O.jsxs)(O.Fragment,{children:[k,j&&(0,O.jsx)(rl,{title:m,children:y}),u&&(0,O.jsx)(Zi,{type:d,tooltip:c,children:u})]})})})}));sl.displayName="FormCheck";const ol=Object.assign(sl,{Input:tl,Label:rl}),al=l.forwardRef((({bsPrefix:e,type:t,size:n,htmlSize:r,id:s,className:o,isValid:a=!1,isInvalid:i=!1,plaintext:c,readOnly:u,as:d="input",...f},p)=>{const{controlId:m}=(0,l.useContext)(Qi);return e=E(e,"form-control"),(0,O.jsx)(d,{...f,type:t,size:r,ref:p,readOnly:u,id:s||m,className:g()(o,c?`${e}-plaintext`:e,n&&`${e}-${n}`,"color"===t&&`${e}-color`,a&&"is-valid",i&&"is-invalid")})}));al.displayName="FormControl";const il=Object.assign(al,{Feedback:Zi}),ll=l.forwardRef((({className:e,bsPrefix:t,as:n="div",...r},s)=>(t=E(t,"form-floating"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));ll.displayName="FormFloating";const cl=ll,ul=l.forwardRef((({controlId:e,as:t="div",...n},r)=>{const s=(0,l.useMemo)((()=>({controlId:e})),[e]);return(0,O.jsx)(Qi.Provider,{value:s,children:(0,O.jsx)(t,{...n,ref:r})})}));ul.displayName="FormGroup";const dl=ul,fl=l.forwardRef((({as:e="label",bsPrefix:t,column:n=!1,visuallyHidden:r=!1,className:s,htmlFor:o,...a},i)=>{const{controlId:c}=(0,l.useContext)(Qi);t=E(t,"form-label");let u="col-form-label";"string"==typeof n&&(u=`${u} ${u}-${n}`);const d=g()(s,t,r&&"visually-hidden",n&&u);return o=o||c,n?(0,O.jsx)(bs,{ref:i,as:"label",className:d,htmlFor:o,...a}):(0,O.jsx)(e,{ref:i,className:d,htmlFor:o,...a})}));fl.displayName="FormLabel";const pl=fl,ml=l.forwardRef((({bsPrefix:e,className:t,id:n,...r},s)=>{const{controlId:o}=(0,l.useContext)(Qi);return e=E(e,"form-range"),(0,O.jsx)("input",{...r,type:"range",ref:s,className:g()(t,e),id:n||o})}));ml.displayName="FormRange";const bl=ml,yl=l.forwardRef((({bsPrefix:e,size:t,htmlSize:n,className:r,isValid:s=!1,isInvalid:o=!1,id:a,...i},c)=>{const{controlId:u}=(0,l.useContext)(Qi);return e=E(e,"form-select"),(0,O.jsx)("select",{...i,size:n,ref:c,className:g()(r,e,t&&`${e}-${t}`,s&&"is-valid",o&&"is-invalid"),id:a||u})}));yl.displayName="FormSelect";const gl=yl,vl=l.forwardRef((({bsPrefix:e,className:t,as:n="small",muted:r,...s},o)=>(e=E(e,"form-text"),(0,O.jsx)(n,{...s,ref:o,className:g()(t,e,r&&"text-muted")}))));vl.displayName="FormText";const hl=vl,xl=l.forwardRef(((e,t)=>(0,O.jsx)(ol,{...e,ref:t,type:"switch"})));xl.displayName="Switch";const Ol=Object.assign(xl,{Input:ol.Input,Label:ol.Label}),_l=l.forwardRef((({bsPrefix:e,className:t,children:n,controlId:r,label:s,...o},a)=>(e=E(e,"form-floating"),(0,O.jsxs)(dl,{ref:a,className:g()(t,e),controlId:r,...o,children:[n,(0,O.jsx)("label",{htmlFor:r,children:s})]}))));_l.displayName="FloatingLabel";const wl=_l,Nl={_ref:d().any,validated:d().bool,as:d().elementType},jl=l.forwardRef((({className:e,validated:t,as:n="form",...r},s)=>(0,O.jsx)(n,{...r,ref:s,className:g()(e,t&&"was-validated")})));jl.displayName="Form",jl.propTypes=Nl;const El=Object.assign(jl,{Group:dl,Control:il,Floating:cl,Check:ol,Switch:Ol,Label:pl,Text:hl,Range:bl,Select:gl,FloatingLabel:wl});var kl=["children","n_submit","prevent_default_on_submit","class_name","className","setProps"];function Pl(t){var n=t.children,r=t.n_submit,s=void 0===r?0:r,o=t.prevent_default_on_submit,a=void 0===o||o,l=t.class_name,u=t.className,d=t.setProps,f=i(t,kl);return c().createElement(El,e({onSubmit:function(e){a&&e.preventDefault(),d&&d({n_submit:s+1})},className:l||u},f,{"data-dash-is-loading":Nt()||void 0}),n)}Pl.propTypes={children:d().node,id:d().string,n_submit:d().number,style:d().object,class_name:d().string,action:d().string,method:d().oneOf(["GET","POST"]),prevent_default_on_submit:d().bool,key:d().string,className:d().string,setProps:d().func};const Cl=Pl;var Tl=["children","class_name","className"];function Sl(t){var n=t.children,r=t.class_name,s=t.className,o=i(t,Tl);return c().createElement(il.Feedback,e({className:r||s},b(["setProps"],o),{"data-dash-is-loading":Nt()||void 0}),n)}Sl.propTypes={children:d().node,id:d().string,type:d().oneOf(["valid","invalid"]),tooltip:d().bool,style:d().object,class_name:d().string,key:d().string,className:d().string,setProps:d().func};const Rl=Sl;var Dl=["children","html_for","class_name","className"];function Ll(t){var n=t.children,r=t.html_for,s=t.class_name,o=t.className,a=i(t,Dl);return c().createElement(cl,e({htmlFor:r,className:s||o},b(["setProps"],a),{"data-dash-is-loading":Nt()||void 0}),n)}Ll.propTypes={children:d().node,id:d().string,html_for:d().string,style:d().object,class_name:d().string,key:d().string,className:d().string,setProps:d().func};const Il=Ll;var Al=["children","color","style","class_name","className"];function $l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ml(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?$l(Object(n),!0).forEach((function(t){Rt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):$l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Fl(t){var n=t.children,r=t.color,s=t.style,o=t.class_name,a=t.className,l=i(t,Al),u=un.has(r),d=g()(o||a,u&&"text-".concat(r));return c().createElement(hl,e({style:u?s:Ml({color:r},s),className:d},b(["setProps"],l),{"data-dash-is-loading":Nt()||void 0}),n)}Fl.propTypes={children:d().node,id:d().string,color:d().string,style:d().object,class_name:d().string,key:d().string,className:d().string,setProps:d().func};const Bl=Fl;var zl=n(8),Hl=n.n(zl);const Kl=p((function(e){return null==e}));var Ul=["value","n_submit","n_blur","size","valid","invalid","plaintext","class_name","type","step","debounce","html_size","autocomplete","autofocus","inputmode","maxlength","minlength","readonly","tabindex","className","autoComplete","autoFocus","inputMode","maxLength","minLength","tabIndex","setProps"];function Wl(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ql(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Wl(Object(n),!0).forEach((function(t){Rt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Wl(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Vl=function(e){return Hl()(e)?+e:NaN},Gl=function(e,t){return e===t||isNaN(e)&&isNaN(t)};function Yl(t){var n=t.value,r=t.n_submit,s=void 0===r?0:r,o=t.n_blur,a=void 0===o?0:o,u=t.size,d=t.valid,f=t.invalid,p=t.plaintext,m=t.class_name,y=t.type,v=t.step,h=void 0===v?"any":v,x=t.debounce,O=void 0!==x&&x,_=t.html_size,w=t.autocomplete,N=t.autofocus,j=t.inputmode,E=t.maxlength,k=t.minlength,P=t.readonly,C=t.tabindex,T=t.className,S=t.autoComplete,R=t.autoFocus,D=t.inputMode,L=t.maxLength,I=t.minLength,A=t.tabIndex,$=t.setProps,M=i(t,Ul),F=(0,l.useRef)(null),B=(0,l.useRef)(null),z=p?"form-control-plaintext":"form-control",H=g()(m||T,f&&"is-invalid",d&&"is-valid",!!u&&"form-control-".concat(u),z);(0,l.useEffect)((function(){if("number"===y){var e=F.current.value,t=F.current.checkValidity()?Vl(e):NaN,r=Vl(n);Gl(r,t)||(F.current.value=Kl(r)?r:n)}else{var s=F.current.value;n!==s&&(F.current.value=null!=n?n:"")}}),[n]);var K=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if("number"===y){var t=F.current.value,r=F.current.checkValidity()?Vl(t):NaN,s=Vl(n);Gl(s,r)?Object.keys(e).length&&$(e):$(ql(ql({},e),{},{value:r}))}else e.value=F.current.value,$(e)};return c().createElement("input",e({ref:F,type:y,className:H,onChange:function(){O?Number.isFinite(O)&&(clearTimeout(B.current),B.current=setTimeout(K,O)):K()},onBlur:function(){if($){var e={n_blur:a+1};!0===O?K(e):$(e)}},onKeyUp:function(e){if($&&"Enter"===e.key){var t={n_submit:s+1};!0===O?K(t):$(t)}}},b(["persistence","persistence_type","persisted_props"],M),{"data-dash-is-loading":Nt()||void 0,autoComplete:w||S,autoFocus:N||R,inputMode:j||D,maxLength:E||L,minLength:k||I,readOnly:P,tabIndex:C||A,size:_,step:h}))}Yl.dashPersistence={persisted_props:["value"],persistence_type:"local"},Yl.propTypes={id:d().string,value:d().oneOfType([d().string,d().number]),n_submit:d().number,n_blur:d().number,size:d().string,valid:d().bool,invalid:d().bool,plaintext:d().bool,style:d().object,class_name:d().string,type:d().oneOf(["text","number","password","email","range","search","tel","url","hidden","time"]),step:d().oneOfType([d().string,d().number]),disabled:d().bool,placeholder:d().oneOfType([d().string,d().number]),debounce:d().oneOfType([d().bool,d().number]),html_size:d().string,autocomplete:d().string,autofocus:d().oneOfType([d().oneOf(["autoFocus","autofocus","AUTOFOCUS"]),d().bool]),inputmode:d().oneOf(["verbatim","latin","latin-name","latin-prose","full-width-latin","kana","katakana","numeric","tel","email","url"]),list:d().string,max:d().oneOfType([d().string,d().number]),maxlength:d().oneOfType([d().string,d().number]),min:d().oneOfType([d().string,d().number]),minlength:d().oneOfType([d().string,d().number]),required:d().oneOfType([d().oneOf(["required","REQUIRED"]),d().bool]),readonly:d().oneOfType([d().bool,d().oneOf(["readOnly","readonly","READONLY"])]),name:d().string,pattern:d().string,tabindex:d().string,persistence:d().oneOfType([d().bool,d().string,d().number]),persisted_props:d().arrayOf(d().oneOf(["value"])),persistence_type:d().oneOf(["local","session","memory"]),key:d().string,className:d().string,tabIndex:d().string,maxLength:d().oneOfType([d().string,d().number]),minLength:d().oneOfType([d().string,d().number]),inputMode:d().oneOf(["verbatim","latin","latin-name","latin-prose","full-width-latin","kana","katakana","numeric","tel","email","url"]),autoComplete:d().string,autoFocus:d().oneOfType([d().oneOf(["autoFocus","autofocus","AUTOFOCUS"]),d().bool]),setProps:d().func};const Xl=Yl,Jl=l.forwardRef((({className:e,bsPrefix:t,as:n="span",...r},s)=>(t=E(t,"input-group-text"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));Jl.displayName="InputGroupText";const Zl=Jl,Ql=l.forwardRef((({bsPrefix:e,size:t,hasValidation:n,className:r,as:s="div",...o},a)=>{e=E(e,"input-group");const i=(0,l.useMemo)((()=>({})),[]);return(0,O.jsx)(si.Provider,{value:i,children:(0,O.jsx)(s,{ref:a,...o,className:g()(r,e,t&&`${e}-${t}`,n&&"has-validation")})})}));Ql.displayName="InputGroup";const ec=Object.assign(Ql,{Text:Zl,Radio:e=>(0,O.jsx)(Zl,{children:(0,O.jsx)(tl,{type:"radio",...e})}),Checkbox:e=>(0,O.jsx)(Zl,{children:(0,O.jsx)(tl,{type:"checkbox",...e})})});var tc=["children","class_name","className"];function nc(t){var n=t.children,r=t.class_name,s=t.className,o=i(t,tc);return c().createElement(ec,e({className:r||s},b(["setProps"],o),{"data-dash-is-loading":Nt()||void 0}),n)}nc.propTypes={children:d().node,id:d().string,size:d().string,style:d().object,class_name:d().string,key:d().string,className:d().string,setProps:d().func};const rc=nc;var sc=["children","class_name","className"];function oc(t){var n=t.children,r=t.class_name,s=t.className,o=i(t,sc);return c().createElement(ec.Text,e({className:r||s},b(["setProps"],o),{"data-dash-is-loading":Nt()||void 0}),n)}oc.propTypes={children:d().node,id:d().string,style:d().object,class_name:d().string,key:d().string,className:d().string,setProps:d().func};const ac=oc;var ic=["children","html_for","width","xs","sm","md","lg","xl","xxl","size","class_name","color","style","check","align","className"];function lc(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function cc(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?lc(Object(n),!0).forEach((function(t){Rt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):lc(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var uc={start:"align-self-start",center:"align-self-center",end:"align-self-end"};function dc(t){var n=t.children,r=t.html_for,s=t.width,o=t.xs,a=t.sm,l=t.md,u=t.lg,d=t.xl,f=t.xxl,p=t.size,m=t.class_name,y=t.color,v=t.style,h=t.check,x=t.align,O=void 0===x?"center":x,_=t.className,w=i(t,ic),N=un.has(y),j=[s,o,a,l,u,d,f].filter((function(e){return e}));[s,o,a,l,u,d,f].forEach((function(e){"object"===Le(e)&&null!==e&&(e.span=e.size)}));var E=O&&uc[O],k=g()(m||_,j.length&&E,y&&N&&"text-".concat(y),h&&"form-check-label");return c().createElement(pl,e({htmlFor:r,column:p||j.length>0,xs:o||s,sm:a,md:l,lg:u,xl:d,xxl:f,className:k,style:N?v:cc({color:y},v)},b(["setProps"],w),{"data-dash-is-loading":Nt()||void 0}),n)}dc.propTypes={children:d().node,id:d().string,size:d().string,html_for:d().string,style:d().object,class_name:d().string,hidden:d().bool,check:d().bool,width:gs,xs:gs,sm:gs,md:gs,lg:gs,xl:gs,xxl:gs,align:d().oneOf(["start","center","end"]),color:d().string,key:d().string,className:d().string,setProps:d().func};const fc=dc,pc=l.forwardRef((({bsPrefix:e,active:t,disabled:n,eventKey:r,className:s,variant:o,action:a,as:i,...l},c)=>{e=E(e,"list-group-item");const[u,d]=hi({key:Sa(r,l.href),active:t,...l}),f=Lt((e=>{if(n)return e.preventDefault(),void e.stopPropagation();u.onClick(e)}));n&&void 0===l.tabIndex&&(l.tabIndex=-1,l["aria-disabled"]=!0);const p=i||(a?l.href?"a":"button":"div");return(0,O.jsx)(p,{ref:c,...l,...u,onClick:f,className:g()(s,e,d.isActive&&"active",n&&"disabled",o&&`${e}-${o}`,a&&`${e}-action`)})}));pc.displayName="ListGroupItem";const mc=pc,bc=l.forwardRef(((e,t)=>{const{className:n,bsPrefix:r,variant:s,horizontal:o,numbered:a,as:i="div",...l}=x(e,{activeKey:"onSelect"}),c=E(r,"list-group");let u;return o&&(u=!0===o?"horizontal":`horizontal-${o}`),(0,O.jsx)(Ei,{ref:t,...l,as:i,className:g()(n,c,s&&`${c}-${s}`,u&&`${c}-${u}`,a&&`${c}-numbered`)})}));bc.displayName="ListGroup";const yc=Object.assign(bc,{Item:mc});var gc=["children","numbered","flush","class_name","tag","className"];function vc(t){var n=t.children,r=t.numbered,s=void 0!==r&&r,o=t.flush,a=t.class_name,l=t.tag,u=void 0===l?"ul":l,d=t.className,f=i(t,gc);return c().createElement(yc,e({className:a||d,variant:o?"flush":null,as:u,numbered:s},b(["setProps"],f),{"data-dash-is-loading":Nt()||void 0}),n)}vc.propTypes={children:d().node,id:d().string,numbered:d().bool,horizontal:d().oneOfType([d().bool,d().string]),flush:d().bool,style:d().object,class_name:d().string,tag:d().string,key:d().string,className:d().string,setProps:d().func};const hc=vc;var xc=["children","n_clicks","href","disabled","color","target","style","class_name","className","setProps"];function Oc(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function _c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Oc(Object(n),!0).forEach((function(t){Rt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Oc(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function wc(t){var n=t.children,r=t.n_clicks,s=void 0===r?0:r,o=t.href,a=t.disabled,l=t.color,u=t.target,d=t.style,f=t.class_name,p=t.className,m=t.setProps,b=i(t,xc),y=cn.has(l),g=o&&!a;return b[g?"preOnClick":"onClick"]=function(){!a&&m&&m({n_clicks:s+1})},c().createElement(mc,e({as:g?_n:"li",href:o,target:g?u:void 0,disabled:a,variant:y?l:null,style:y?d:_c({backgroundColor:l},d),className:f||p},b,{"data-dash-is-loading":Nt()||void 0}),n)}wc.propTypes={children:d().node,id:d().string,n_clicks:d().number,style:d().object,class_name:d().string,tag:d().string,active:d().bool,disabled:d().bool,color:d().string,action:d().bool,href:d().string,external_link:d().bool,target:d().string,key:d().string,className:d().string,setProps:d().func};const Nc=wc;function jc(e){void 0===e&&(e=T());try{var t=e.activeElement;return t&&t.nodeName?t:null}catch(t){return e.body}}const Ec=Ia("modal-open"),kc=class{constructor({ownerDocument:e,handleContainerOverflow:t=!0,isRTL:n=!1}={}){this.handleContainerOverflow=t,this.isRTL=n,this.modals=[],this.ownerDocument=e}getScrollbarWidth(){return function(e=document){const t=e.defaultView;return Math.abs(t.innerWidth-e.documentElement.clientWidth)}(this.ownerDocument)}getElement(){return(this.ownerDocument||document).body}setModalAttributes(e){}removeModalAttributes(e){}setContainerStyle(e){const t={overflow:"hidden"},n=this.isRTL?"paddingLeft":"paddingRight",r=this.getElement();e.style={overflow:r.style.overflow,[n]:r.style[n]},e.scrollBarWidth&&(t[n]=`${parseInt(I(r,n)||"0",10)+e.scrollBarWidth}px`),r.setAttribute(Ec,""),I(r,t)}reset(){[...this.modals].forEach((e=>this.remove(e)))}removeContainerStyle(e){const t=this.getElement();t.removeAttribute(Ec),Object.assign(t.style,e.style)}add(e){let t=this.modals.indexOf(e);return-1!==t||(t=this.modals.length,this.modals.push(e),this.setModalAttributes(e),0!==t||(this.state={scrollBarWidth:this.getScrollbarWidth(),style:{}},this.handleContainerOverflow&&this.setContainerStyle(this.state))),t}remove(e){const t=this.modals.indexOf(e);-1!==t&&(this.modals.splice(t,1),!this.modals.length&&this.handleContainerOverflow&&this.removeContainerStyle(this.state),this.removeModalAttributes(e))}isTopModal(e){return!!this.modals.length&&this.modals[this.modals.length-1]===e}},Pc=(e,t)=>X?null==e?(t||T()).body:("function"==typeof e&&(e=e()),e&&"current"in e&&(e=e.current),e&&("nodeType"in e||e.getBoundingClientRect)?e:null):null;function Cc(e,t){const n=za(),[r,s]=(0,l.useState)((()=>Pc(e,null==n?void 0:n.document)));if(!r){const t=Pc(e);t&&s(t)}return(0,l.useEffect)((()=>{t&&r&&t(r)}),[t,r]),(0,l.useEffect)((()=>{const t=Pc(e);t!==r&&s(t)}),[e,r]),r}const Tc=function({children:e,in:t,onExited:n,mountOnEnter:r,unmountOnExit:s}){const o=(0,l.useRef)(null),a=(0,l.useRef)(t),i=Bt(n);(0,l.useEffect)((()=>{t?a.current=!0:i(o.current)}),[t,i]);const c=yi(o,Y(e)),u=(0,l.cloneElement)(e,{ref:c});return t?u:s||!a.current&&r?null:u},Sc=["onEnter","onEntering","onEntered","onExit","onExiting","onExited","addEndListener","children"],Rc=["component"],Dc=l.forwardRef(((e,t)=>{let{component:n}=e;const r=function(e){let{onEnter:t,onEntering:n,onEntered:r,onExit:s,onExiting:o,onExited:a,addEndListener:i,children:c}=e,u=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,Sc);const d=(0,l.useRef)(null),f=yi(d,Y(c)),p=e=>t=>{e&&d.current&&e(d.current,t)},m=(0,l.useCallback)(p(t),[t]),b=(0,l.useCallback)(p(n),[n]),y=(0,l.useCallback)(p(r),[r]),g=(0,l.useCallback)(p(s),[s]),v=(0,l.useCallback)(p(o),[o]),h=(0,l.useCallback)(p(a),[a]),x=(0,l.useCallback)(p(i),[i]);return Object.assign({},u,{nodeRef:d},t&&{onEnter:m},n&&{onEntering:b},r&&{onEntered:y},s&&{onExit:g},o&&{onExiting:v},a&&{onExited:h},i&&{addEndListener:x},{children:"function"==typeof c?(e,t)=>c(e,Object.assign({},t,{ref:f})):(0,l.cloneElement)(c,{ref:f})})}(function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,Rc));return(0,O.jsx)(n,Object.assign({ref:t},r))}));function Lc({children:e,in:t,onExited:n,onEntered:r,transition:s}){const[o,a]=(0,l.useState)(!t);t&&o&&a(!1);const i=function({in:e,onTransition:t}){const n=(0,l.useRef)(null),r=(0,l.useRef)(!0),s=Bt(t);return Ut((()=>{if(!n.current)return;let t=!1;return s({in:e,element:n.current,initial:r.current,isStale:()=>t}),()=>{t=!0}}),[e,s]),Ut((()=>(r.current=!1,()=>{r.current=!0})),[]),n}({in:!!t,onTransition:e=>{Promise.resolve(s(e)).then((()=>{e.isStale()||(e.in?null==r||r(e.element,e.initial):(a(!0),null==n||n(e.element)))}),(t=>{throw e.in||a(!0),t}))}}),c=yi(i,Y(e));return o&&!t?null:(0,l.cloneElement)(e,{ref:c})}function Ic(e,t,n){return e?(0,O.jsx)(Dc,Object.assign({},n,{component:e})):t?(0,O.jsx)(Lc,Object.assign({},n,{transition:t})):(0,O.jsx)(Tc,Object.assign({},n))}const Ac=["show","role","className","style","children","backdrop","keyboard","onBackdropClick","onEscapeKeyDown","transition","runTransition","backdropTransition","runBackdropTransition","autoFocus","enforceFocus","restoreFocus","restoreFocusOptions","renderDialog","renderBackdrop","manager","container","onShow","onHide","onExit","onExited","onExiting","onEnter","onEntering","onEntered"];let $c;const Mc=(0,l.forwardRef)(((e,t)=>{let{show:n=!1,role:r="dialog",className:s,style:o,children:a,backdrop:i=!0,keyboard:c=!0,onBackdropClick:u,onEscapeKeyDown:d,transition:f,runTransition:p,backdropTransition:m,runBackdropTransition:b,autoFocus:y=!0,enforceFocus:g=!0,restoreFocus:v=!0,restoreFocusOptions:h,renderDialog:x,renderBackdrop:_=e=>(0,O.jsx)("div",Object.assign({},e)),manager:w,container:N,onShow:j,onHide:E=()=>{},onExit:k,onExited:P,onExiting:C,onEnter:T,onEntering:S,onEntered:R}=e,D=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,Ac);const L=za(),I=Cc(N),A=function(e){const t=za(),n=e||function(e){return $c||($c=new kc({ownerDocument:null==e?void 0:e.document})),$c}(t),r=(0,l.useRef)({dialog:null,backdrop:null});return Object.assign(r.current,{add:()=>n.add(r.current),remove:()=>n.remove(r.current),isTopModal:()=>n.isTopModal(r.current),setDialogRef:(0,l.useCallback)((e=>{r.current.dialog=e}),[]),setBackdropRef:(0,l.useCallback)((e=>{r.current.backdrop=e}),[])})}(w),$=zt(),F=Ht(n),[B,z]=(0,l.useState)(!n),H=(0,l.useRef)(null);(0,l.useImperativeHandle)(t,(()=>A),[A]),X&&!F&&n&&(H.current=jc(null==L?void 0:L.document)),n&&B&&z(!1);const K=Bt((()=>{if(A.add(),J.current=ne(document,"keydown",V),Y.current=ne(document,"focus",(()=>setTimeout(W)),!0),j&&j(),y){var e,t;const n=jc(null!=(e=null==(t=A.dialog)?void 0:t.ownerDocument)?e:null==L?void 0:L.document);A.dialog&&n&&!ra(A.dialog,n)&&(H.current=n,A.dialog.focus())}})),U=Bt((()=>{var e;A.remove(),null==J.current||J.current(),null==Y.current||Y.current(),v&&(null==(e=H.current)||null==e.focus||e.focus(h),H.current=null)}));(0,l.useEffect)((()=>{n&&I&&K()}),[n,I,K]),(0,l.useEffect)((()=>{B&&U()}),[B,U]),function(e){const t=function(e){const t=(0,l.useRef)(e);return t.current=e,t}(e);(0,l.useEffect)((()=>()=>t.current()),[])}((()=>{U()}));const W=Bt((()=>{if(!g||!$()||!A.isTopModal())return;const e=jc(null==L?void 0:L.document);A.dialog&&e&&!ra(A.dialog,e)&&A.dialog.focus()})),q=Bt((e=>{e.target===e.currentTarget&&(null==u||u(e),!0===i&&E())})),V=Bt((e=>{c&&G(e)&&A.isTopModal()&&(null==d||d(e),e.defaultPrevented||E())})),Y=(0,l.useRef)(),J=(0,l.useRef)();if(!I)return null;const Z=Object.assign({role:r,ref:A.setDialogRef,"aria-modal":"dialog"===r||void 0},D,{style:o,className:s,tabIndex:-1});let Q=x?x(Z):(0,O.jsx)("div",Object.assign({},Z,{children:l.cloneElement(a,{role:"document"})}));Q=Ic(f,p,{unmountOnExit:!0,mountOnEnter:!0,appear:!0,in:!!n,onExit:k,onExiting:C,onExited:(...e)=>{z(!0),null==P||P(...e)},onEnter:T,onEntering:S,onEntered:R,children:Q});let ee=null;return i&&(ee=_({ref:A.setBackdropRef,onClick:q}),ee=Ic(m,b,{in:!!n,appear:!0,mountOnEnter:!0,unmountOnExit:!0,children:ee})),(0,O.jsx)(O.Fragment,{children:M().createPortal((0,O.jsxs)(O.Fragment,{children:[ee,Q]}),I)})}));Mc.displayName="Modal";const Fc=Object.assign(Mc,{Manager:kc});var Bc;function zc(e){if((!Bc&&0!==Bc||e)&&X){var t=document.createElement("div");t.style.position="absolute",t.style.top="-9999px",t.style.width="50px",t.style.height="50px",t.style.overflow="scroll",document.body.appendChild(t),Bc=t.offsetWidth-t.clientWidth,document.body.removeChild(t)}return Bc}function Hc(e,t){return e.classList?!!t&&e.classList.contains(t):-1!==(" "+(e.className.baseVal||e.className)+" ").indexOf(" "+t+" ")}function Kc(e,t){return e.replace(new RegExp("(^|\\s)"+t+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}const Uc=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",Wc=".sticky-top",qc=".navbar-toggler";class Vc extends kc{adjustAndStore(e,t,n){const r=t.style[e];t.dataset[e]=r,I(t,{[e]:`${parseFloat(I(t,e))+n}px`})}restore(e,t){const n=t.dataset[e];void 0!==n&&(delete t.dataset[e],I(t,{[e]:n}))}setContainerStyle(e){super.setContainerStyle(e);const t=this.getElement();var n,r;if(r="modal-open",(n=t).classList?n.classList.add(r):Hc(n,r)||("string"==typeof n.className?n.className=n.className+" "+r:n.setAttribute("class",(n.className&&n.className.baseVal||"")+" "+r)),!e.scrollBarWidth)return;const s=this.isRTL?"paddingLeft":"paddingRight",o=this.isRTL?"marginLeft":"marginRight";Ss(t,Uc).forEach((t=>this.adjustAndStore(s,t,e.scrollBarWidth))),Ss(t,Wc).forEach((t=>this.adjustAndStore(o,t,-e.scrollBarWidth))),Ss(t,qc).forEach((t=>this.adjustAndStore(o,t,e.scrollBarWidth)))}removeContainerStyle(e){super.removeContainerStyle(e);const t=this.getElement();var n,r;r="modal-open",(n=t).classList?n.classList.remove(r):"string"==typeof n.className?n.className=Kc(n.className,r):n.setAttribute("class",Kc(n.className&&n.className.baseVal||"",r));const s=this.isRTL?"paddingLeft":"paddingRight",o=this.isRTL?"marginLeft":"marginRight";Ss(t,Uc).forEach((e=>this.restore(s,e))),Ss(t,Wc).forEach((e=>this.restore(o,e))),Ss(t,qc).forEach((e=>this.restore(o,e)))}}let Gc;function Yc(e){return Gc||(Gc=new Vc(e)),Gc}const Xc=Vc,Jc=l.createContext({onHide(){}});var Zc=["className","contentClassName","centered","size","fullscreen","children","scrollable","contentStyle"],Qc=c().forwardRef((function(t,n){var r=t.className,s=t.contentClassName,o=t.centered,a=t.size,l=t.fullscreen,u=t.children,d=t.scrollable,f=t.contentStyle,p=i(t,Zc),m="modal-dialog",b="string"==typeof l?"modal-fullscreen-".concat(l):"modal-fullscreen";return c().createElement("div",e({},p,{ref:n,className:g()(m,r,a&&"modal-".concat(a),o&&"".concat(m,"-centered"),d&&"".concat(m,"-scrollable"),l&&b)}),c().createElement("div",{className:g()("modal-content",s),style:f},u))}));Qc.propTypes={children:d().node,className:d().string,contentClassName:d().string,centered:d().bool,size:d().oneOf(["xs","sm","md","lg","xl"]),fullscreen:d().oneOf([!0,"sm-down","md-down","lg-down","xl-down"]),scrollable:d().bool,contentStyle:d().object};const eu=Qc;var tu=["className","style","dialogClassName","contentClassName","children","dialogStyle","contentStyle","backdropStyle","data-bs-theme","aria-labelledby","aria-describedby","aria-label","onEscapeKeyDown","onShow","onHide","container","restoreFocusOptions","onEntered","onExit","onExiting","onEnter","onEntering","onExited","backdropClassName","zIndex","manager","show","backdrop","keyboard","autoFocus","enforceFocus","restoreFocus","animation","dialogAs"];function nu(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ru(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?nu(Object(n),!0).forEach((function(t){Rt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):nu(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function su(t){return c().createElement(nn,e({},t,{timeout:null}))}function ou(t){return c().createElement(nn,e({},t,{timeout:null}))}var au=c().forwardRef((function(t,n){var r=t.className,s=t.style,o=t.dialogClassName,a=t.contentClassName,u=t.children,d=t.dialogStyle,f=t.contentStyle,p=t.backdropStyle,m=t["data-bs-theme"],b=t["aria-labelledby"],y=t["aria-describedby"],v=t["aria-label"],h=t.onEscapeKeyDown,x=t.onShow,O=t.onHide,_=t.container,w=t.restoreFocusOptions,N=t.onEntered,j=t.onExit,E=t.onExiting,k=t.onEnter,P=t.onEntering,S=t.onExited,R=t.backdropClassName,D=t.zIndex,L=t.manager,I=t.show,A=void 0!==I&&I,$=t.backdrop,M=void 0===$||$,F=t.keyboard,B=void 0===F||F,z=t.autoFocus,H=void 0===z||z,K=t.enforceFocus,U=void 0===K||K,W=t.restoreFocus,q=void 0===W||W,V=t.animation,G=void 0===V||V,Y=t.dialogAs,J=void 0===Y?eu:Y,Z=i(t,tu),Q=Ie((0,l.useState)({}),2),ne=Q[0],se=Q[1],oe=Ie((0,l.useState)(!1),2),ae=oe[0],ie=oe[1],le=(0,l.useRef)(!1),ue=(0,l.useRef)(!1),de=(0,l.useRef)(null),fe=Ie((0,l.useState)(null),2),pe=fe[0],me=fe[1],be=ce(n,me),ye=Lt(O),ge=C(),ve=(0,l.useMemo)((function(){return{onHide:ye}}),[ye]);function he(){return L||Yc({isRTL:ge})}function xe(e){if(X){var t=he().getScrollbarWidth()>0,n=e.scrollHeight>T(e).documentElement.clientHeight;se({paddingRight:t&&!n?zc():void 0,paddingLeft:!t&&n?zc():void 0})}}var Oe=Lt((function(){pe&&xe(pe.dialog)}));Kr((function(){var e;te(window,"resize",Oe),null===(e=de.current)||void 0===e||e.call(de)}));var _e=function(){le.current=!0},we=function(e){le.current&&pe&&e.target===pe.dialog&&(ue.current=!0),le.current=!1},Ne=function(){ie(!0),de.current=re(pe.dialog,(function(){ie(!1)}))},je=function(e){"static"!==M?ue.current||e.target!==e.currentTarget?ue.current=!1:null==O||O():function(e){e.target===e.currentTarget&&Ne()}(e)},Ee=(0,l.useCallback)((function(t){return c().createElement("div",e({},t,{className:g()("modal-backdrop",R,!G&&"show"),style:ru(ru({},p),{},{zIndex:D})}))}),[G,R,D]),ke=ru(ru({},s),ne);return ke.display="block",c().createElement(Jc.Provider,{value:ve},c().createElement(Fc,{show:A,ref:be,backdrop:M,container:_,keyboard:!0,autoFocus:H,enforceFocus:U,restoreFocus:q,restoreFocusOptions:w,onEscapeKeyDown:function(e){B?null==h||h(e):(e.preventDefault(),"static"===M&&Ne())},onShow:x,onHide:O,onEnter:function(e,t){e&&xe(e),null==k||k(e,t)},onEntering:function(e,t){null==P||P(e,t),ee(window,"resize",Oe)},onEntered:N,onExit:function(e){var t;null===(t=de.current)||void 0===t||t.call(de),null==j||j(e)},onExiting:E,onExited:function(e){e&&(e.style.display=""),null==S||S(e),te(window,"resize",Oe)},manager:he(),transition:G?su:void 0,backdropTransition:G?ou:void 0,renderBackdrop:Ee,renderDialog:function(t){return c().createElement("div",e({role:"dialog"},t,{style:ke,className:g()(r,"modal",ae&&"modal-static",!G&&"show"),onClick:M?je:void 0,onMouseUp:we,"data-bs-theme":m,"aria-label":v,"aria-labelledby":b,"aria-describedby":y}),c().createElement(J,e({},Z,{onMouseDown:_e,className:o,style:d,contentClassName:a,contentStyle:f}),u))}}))}));au.propTypes={children:d().node,className:d().string,style:d().object,dialogStyle:d().object,contentStyle:d().object,backdropStyle:d().object,zIndex:d().oneOfType([d().number,d().string]),size:d().string,fullscreen:d().oneOfType([d().bool,d().string]),centered:d().bool,backdrop:d().oneOf(["static",!0,!1]),backdropClassName:d().string,keyboard:d().bool,scrollable:d().bool,animation:d().bool,dialogClassName:d().string,contentClassName:d().string,dialogAs:d().elementType,autoFocus:d().bool,enforceFocus:d().bool,restoreFocus:d().bool,restoreFocusOptions:d().shape({preventScroll:d().bool}),show:d().bool,onShow:d().func,onHide:d().func,onEscapeKeyDown:d().func,onEnter:d().func,onEntering:d().func,onEntered:d().func,onExit:d().func,onExiting:d().func,onExited:d().func,manager:d().object,container:d().any,"data-bs-theme":d().string,"aria-labelledby":d().string,"aria-describedby":d().string,"aria-label":d().string};const iu=au;var lu=["children","is_open","fade","style","dialog_style","content_style","backdrop_style","class_name","dialog_class_name","content_class_name","backdrop_class_name","autofocus","enforceFocus","labelledby","tag","zindex","autoFocus","labelledBy","zIndex","dialogStyle","contentStyle","backdropStyle","className","backdropClassName","contentClassName","dialogClassName","setProps"];function cu(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function uu(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?cu(Object(n),!0).forEach((function(t){Rt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):cu(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function du(t){var n=t.children,r=t.is_open,s=t.fade,o=t.style,a=t.dialog_style,l=t.content_style,u=t.backdrop_style,d=t.class_name,f=t.dialog_class_name,p=t.content_class_name,m=t.backdrop_class_name,y=t.autofocus,g=t.enforceFocus,v=t.labelledby,h=t.tag,x=t.zindex,O=t.autoFocus,_=t.labelledBy,w=t.zIndex,N=t.dialogStyle,j=t.contentStyle,E=t.backdropStyle,k=t.className,P=t.backdropClassName,C=t.contentClassName,T=t.dialogClassName,S=t.setProps,R=i(t,lu);return c().createElement(iu,e({animation:s,dialogAs:h,className:d||k,dialogStyle:a||N,dialogClassName:f||T,contentStyle:l||j,contentClassName:p||C,backdropStyle:u||E,backdropClassName:m||P,autoFocus:y||O,enforceFocus:g,"aria-labelledby":v||_,show:r,onHide:function(){S&&S({is_open:!1})},style:x||w?uu(uu({},o),{},{zIndex:x||w}):o,zIndex:x||w},b(["persistence","persistence_type","persisted_props"],R),{"data-dash-is-loading":Nt()||void 0}),n)}du.propTypes={children:d().node,id:d().string,is_open:d().bool,centered:d().bool,scrollable:d().bool,size:d().string,backdrop:d().oneOfType([d().bool,d().oneOf(["static"])]),fullscreen:d().oneOfType([d().bool,d().oneOf(["sm-down","md-down","lg-down","xl-down","xxl-down"])]),keyboard:d().bool,fade:d().bool,style:d().object,dialog_style:d().object,content_style:d().object,backdrop_style:d().object,class_name:d().string,dialog_class_name:d().string,backdrop_class_name:d().string,content_class_name:d().string,tag:d().string,autofocus:d().bool,enforceFocus:d().bool,role:d().string,labelledby:d().string,zindex:d().oneOfType([d().number,d().string]),dialogStyle:d().object,contentStyle:d().object,backdropStyle:d().object,className:d().string,backdropClassName:d().string,contentClassName:d().string,dialogClassName:d().string,autoFocus:d().bool,labelledBy:d().string,zIndex:d().oneOfType([d().number,d().string]),setProps:d().func};const fu=du,pu=l.forwardRef((({className:e,bsPrefix:t,as:n="div",...r},s)=>(t=E(t,"modal-body"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));pu.displayName="ModalBody";const mu=pu;var bu=["children","class_name","tag","className"];function yu(t){var n=t.children,r=t.class_name,s=t.tag,o=t.className,a=i(t,bu);return c().createElement(mu,e({as:s,className:r||o,"data-dash-is-loading":Nt()||void 0},b(["setProps"],a)),n)}yu.propTypes={children:d().node,id:d().string,style:d().object,class_name:d().string,tag:d().string,className:d().string,setProps:d().func};const gu=yu,vu=l.forwardRef((({className:e,bsPrefix:t,as:n="div",...r},s)=>(t=E(t,"modal-footer"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));vu.displayName="ModalFooter";const hu=vu;var xu=["children","class_name","tag","className"];function Ou(t){var n=t.children,r=t.class_name,s=t.tag,o=t.className,a=i(t,xu);return c().createElement(hu,e({as:s,className:r||o,"data-dash-is-loading":Nt()||void 0},b(["setProps"],a)),n)}Ou.propTypes={children:d().node,id:d().string,style:d().object,class_name:d().string,tag:d().string,className:d().string,setProps:d().func};const _u=Ou,wu=l.forwardRef((({closeLabel:e="Close",closeVariant:t,closeButton:n=!1,onHide:r,children:s,...o},a)=>{const i=(0,l.useContext)(Jc),c=Lt((()=>{null==i||i.onHide(),null==r||r()}));return(0,O.jsxs)("div",{ref:a,...o,children:[s,n&&(0,O.jsx)(on,{"aria-label":e,variant:t,onClick:c})]})})),Nu=wu,ju=l.forwardRef((({bsPrefix:e,className:t,closeLabel:n="Close",closeButton:r=!1,...s},o)=>(e=E(e,"modal-header"),(0,O.jsx)(Nu,{ref:o,...s,className:g()(t,e),closeLabel:n,closeButton:r}))));ju.displayName="ModalHeader";const Eu=ju;var ku=["children","close_button","class_name","tag","className"];function Pu(t){var n=t.children,r=t.close_button,s=void 0===r||r,o=t.class_name,a=t.tag,l=t.className,u=i(t,ku);return c().createElement(Eu,e({as:a,className:o||l,closeButton:s,"data-dash-is-loading":Nt()||void 0},b(["setProps"],u)),n)}Pu.propTypes={children:d().node,id:d().string,close_button:d().bool,style:d().object,class_name:d().string,tag:d().string,className:d().string,setProps:d().func};const Cu=Pu,Tu=It("h4"),Su=l.forwardRef((({className:e,bsPrefix:t,as:n=Tu,...r},s)=>(t=E(t,"modal-title"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));Su.displayName="ModalTitle";const Ru=Su;var Du=["children","class_name","tag","className"];function Lu(t){var n=t.children,r=t.class_name,s=t.tag,o=t.className,a=i(t,Du);return c().createElement(Ru,e({as:s,className:r||o,"data-dash-is-loading":Nt()||void 0},b(["setProps"],a)),n)}Lu.propTypes={children:d().node,id:d().string,style:d().object,class_name:d().string,tag:d().string,className:d().string,setProps:d().func};const Iu=Lu;var Au=["children","pills","vertical","horizontal","justified","navbar_scroll","class_name","className"],$u={start:"justify-content-start",center:"justify-content-center",end:"justify-content-end",around:"justify-content-around",between:"justify-content-between"},Mu={xs:"flex-xs-column",sm:"flex-sm-column",md:"flex-md-column",lg:"flex-lg-column",xl:"flex-xl-column"};function Fu(t){var n=t.children,r=t.pills,s=t.vertical,o=t.horizontal,a=t.justified,l=t.navbar_scroll,u=t.class_name,d=t.className,f=i(t,Au),p=o&&$u[o],m=!0===s?"flex-column":s&&Mu[s],y=g()(u||d,p,m);return c().createElement(Ri,e({className:y,variant:r?"pills":null,justify:a,navbarScroll:l},b(["setProps"],f),{"data-dash-is-loading":Nt()||void 0}),n)}Fu.propTypes={children:d().node,id:d().string,pills:d().bool,vertical:d().oneOfType([d().bool,d().string]),horizontal:d().oneOf(["start","center","end","between","around"]),fill:d().bool,justified:d().bool,card:d().bool,navbar:d().bool,navbar_scroll:d().bool,style:d().object,class_name:d().string,key:d().string,className:d().string,setProps:d().func};const Bu=Fu,zu=l.forwardRef((({bsPrefix:e,className:t,as:n,...r},s)=>{e=E(e,"navbar-brand");const o=n||(r.href?"a":"span");return(0,O.jsx)(o,{...r,ref:s,className:g()(t,e)})}));zu.displayName="NavbarBrand";const Hu=zu,Ku=l.forwardRef((({children:e,bsPrefix:t,...n},r)=>{t=E(t,"navbar-collapse");const s=(0,l.useContext)(ai);return(0,O.jsx)(be,{in:!(!s||!s.expanded),...n,children:(0,O.jsx)("div",{ref:r,className:t,children:e})})}));Ku.displayName="NavbarCollapse";const Uu=Ku,Wu=l.forwardRef((({bsPrefix:e,className:t,children:n,label:r="Toggle navigation",as:s="button",onClick:o,...a},i)=>{e=E(e,"navbar-toggler");const{onToggle:c,expanded:u}=(0,l.useContext)(ai)||{},d=Lt((e=>{o&&o(e),c&&c()}));return"button"===s&&(a.type="button"),(0,O.jsx)(s,{...a,ref:i,onClick:d,"aria-label":r,className:g()(t,e,!u&&"collapsed"),children:n||(0,O.jsx)("span",{className:`${e}-icon`})})}));Wu.displayName="NavbarToggle";const qu=Wu,Vu=new WeakMap,Gu=(e,t)=>{if(!e||!t)return;const n=Vu.get(t)||new Map;Vu.set(t,n);let r=n.get(e);return r||(r=t.matchMedia(e),r.refCount=0,n.set(r.media,r)),r};function Yu(e,t=("undefined"==typeof window?void 0:window)){const n=Gu(e,t),[r,s]=(0,l.useState)((()=>!!n&&n.matches));return ni((()=>{let n=Gu(e,t);if(!n)return s(!1);let r=Vu.get(t);const o=()=>{s(n.matches)};return n.refCount++,n.addListener(o),o(),()=>{n.removeListener(o),n.refCount--,n.refCount<=0&&(null==r||r.delete(n.media)),n=void 0}}),[e]),r}const Xu=function(e){const t=Object.keys(e);function n(e,t){return e===t?t:e?`${e} and ${t}`:t}return function(r,s,o){let a;return"object"==typeof r?(a=r,o=s,s=!0):(s=s||!0,a={[r]:s}),Yu((0,l.useMemo)((()=>Object.entries(a).reduce(((r,[s,o])=>("up"!==o&&!0!==o||(r=n(r,function(t){let n=e[t];return"number"==typeof n&&(n=`${n}px`),`(min-width: ${n})`}(s))),"down"!==o&&!0!==o||(r=n(r,function(n){const r=function(e){return t[Math.min(t.indexOf(e)+1,t.length-1)]}(n);let s=e[r];return s="number"==typeof s?s-.2+"px":`calc(${s} - 0.2px)`,`(max-width: ${s})`}(s))),r)),"")),[JSON.stringify(a)]),o)}}({xs:0,sm:576,md:768,lg:992,xl:1200,xxl:1400}),Ju=l.forwardRef((({className:e,bsPrefix:t,as:n="div",...r},s)=>(t=E(t,"offcanvas-body"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));Ju.displayName="OffcanvasBody";const Zu=Ju,Qu={[H]:"show",[K]:"show"},ed=l.forwardRef((({bsPrefix:e,className:t,children:n,in:r=!1,mountOnEnter:s=!1,unmountOnExit:o=!1,appear:a=!1,...i},c)=>(e=E(e,"offcanvas"),(0,O.jsx)(de,{ref:c,addEndListener:oe,in:r,mountOnEnter:s,unmountOnExit:o,appear:a,...i,childRef:Y(n),children:(r,s)=>l.cloneElement(n,{...s,className:g()(t,n.props.className,(r===H||r===U)&&`${e}-toggling`,Qu[r])})}))));ed.displayName="OffcanvasToggling";const td=ed,nd=l.forwardRef((({bsPrefix:e,className:t,closeLabel:n="Close",closeButton:r=!1,...s},o)=>(e=E(e,"offcanvas-header"),(0,O.jsx)(Nu,{ref:o,...s,className:g()(t,e),closeLabel:n,closeButton:r}))));nd.displayName="OffcanvasHeader";const rd=nd,sd=It("h5"),od=l.forwardRef((({className:e,bsPrefix:t,as:n=sd,...r},s)=>(t=E(t,"offcanvas-title"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));od.displayName="OffcanvasTitle";const ad=od;function id(e){return(0,O.jsx)(td,{...e})}function ld(e){return(0,O.jsx)(nn,{...e})}const cd=l.forwardRef((({bsPrefix:e,className:t,children:n,"aria-labelledby":r,placement:s="start",responsive:o,show:a=!1,backdrop:i=!0,keyboard:c=!0,scroll:u=!1,onEscapeKeyDown:d,onShow:f,onHide:p,container:m,autoFocus:b=!0,enforceFocus:y=!0,restoreFocus:v=!0,restoreFocusOptions:h,onEntered:x,onExit:_,onExiting:w,onEnter:N,onEntering:j,onExited:k,backdropClassName:P,manager:C,renderStaticNode:T=!1,...S},R)=>{const D=(0,l.useRef)();e=E(e,"offcanvas");const[L,I]=(0,l.useState)(!1),A=Lt(p),$=Xu(o||"xs","up");(0,l.useEffect)((()=>{I(o?a&&!$:a)}),[a,o,$]);const M=(0,l.useMemo)((()=>({onHide:A})),[A]),F=(0,l.useCallback)((t=>(0,O.jsx)("div",{...t,className:g()(`${e}-backdrop`,P)})),[P,e]),B=a=>(0,O.jsx)("div",{...a,...S,className:g()(t,o?`${e}-${o}`:e,`${e}-${s}`),"aria-labelledby":r,children:n});return(0,O.jsxs)(O.Fragment,{children:[!L&&(o||T)&&B({}),(0,O.jsx)(Jc.Provider,{value:M,children:(0,O.jsx)(Fc,{show:L,ref:R,backdrop:i,container:m,keyboard:c,autoFocus:b,enforceFocus:y&&!u,restoreFocus:v,restoreFocusOptions:h,onEscapeKeyDown:d,onShow:f,onHide:A,onEnter:(e,...t)=>{e&&(e.style.visibility="visible"),null==N||N(e,...t)},onEntering:j,onEntered:x,onExit:_,onExiting:w,onExited:(e,...t)=>{e&&(e.style.visibility=""),null==k||k(...t)},manager:C||(u?(D.current||(D.current=new Xc({handleContainerOverflow:!1})),D.current):Yc()),transition:id,backdropTransition:ld,renderBackdrop:F,renderDialog:B})})]})}));cd.displayName="Offcanvas";const ud=Object.assign(cd,{Body:Zu,Header:rd,Title:ad}),dd=l.forwardRef((({onHide:e,...t},n)=>{const r=(0,l.useContext)(ai),s=Lt((()=>{null==r||null==r.onToggle||r.onToggle(),null==e||e()}));return(0,O.jsx)(ud,{ref:n,show:!(null==r||!r.expanded),...t,renderStaticNode:!0,onHide:s})}));dd.displayName="NavbarOffcanvas";const fd=dd,pd=l.forwardRef((({className:e,bsPrefix:t,as:n="span",...r},s)=>(t=E(t,"navbar-text"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));pd.displayName="NavbarText";const md=pd,bd=l.forwardRef(((e,t)=>{const{bsPrefix:n,expand:r=!0,variant:s="light",bg:o,fixed:a,sticky:i,className:c,as:u="nav",expanded:d,onToggle:f,onSelect:p,collapseOnSelect:m=!1,...b}=x(e,{expanded:"onToggle"}),y=E(n,"navbar"),v=(0,l.useCallback)(((...e)=>{null==p||p(...e),m&&d&&(null==f||f(!1))}),[p,m,d,f]);void 0===b.role&&"nav"!==u&&(b.role="navigation");let h=`${y}-expand`;"string"==typeof r&&(h=`${h}-${r}`);const _=(0,l.useMemo)((()=>({onToggle:()=>null==f?void 0:f(!d),bsPrefix:y,expanded:!!d,expand:r})),[y,d,r,f]);return(0,O.jsx)(ai.Provider,{value:_,children:(0,O.jsx)(Ra.Provider,{value:v,children:(0,O.jsx)(u,{ref:t,...b,className:g()(c,y,r&&h,s&&`${y}-${s}`,o&&`bg-${o}`,i&&`sticky-${i}`,a&&`fixed-${a}`)})})})}));bd.displayName="Navbar";const yd=Object.assign(bd,{Brand:Hu,Collapse:Uu,Offcanvas:fd,Text:md,Toggle:qu});var gd=["children","dark","color","expand","style","class_name","tag","className"];function vd(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function hd(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?vd(Object(n),!0).forEach((function(t){Rt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):vd(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function xd(t){var n=t.children,r=t.dark,s=void 0===r||r,o=t.color,a=void 0===o?"primary":o,l=t.expand,u=void 0===l?"md":l,d=t.style,f=t.class_name,p=t.tag,m=t.className,y=i(t,gd),g=cn.has(a);return c().createElement(yd,e({variant:s?"dark":"light",as:p,bg:g?a:null,style:hd({backgroundColor:!g&&a},d),className:f||m,expand:u},b(["setProps"],y),{"data-dash-is-loading":Nt()||void 0}),n)}xd.propTypes={children:d().node,id:d().string,dark:d().bool,fixed:d().string,sticky:d().oneOf(["top"]),color:d().string,expand:d().oneOfType([d().bool,d().string]),style:d().object,class_name:d().string,role:d().string,tag:d().string,key:d().string,className:d().string,setProps:d().func};const Od=xd;var _d=["children","class_name","href","className"];function wd(t){var n=t.children,r=t.class_name,s=t.href,o=t.className,a=i(t,_d);return c().createElement(Hu,e({className:r||o},b(["setProps"],a),{href:s,as:s?_n:"span","data-dash-is-loading":Nt()||void 0}),n)}wd.propTypes={children:d().node,id:d().string,external_link:d().bool,href:d().string,style:d().object,class_name:d().string,key:d().string,className:d().string,setProps:d().func};const Nd=wd;var jd=["children","class_name","n_clicks","className","setProps"];function Ed(t){var n=t.children,r=t.class_name,s=t.n_clicks,o=void 0===s?0:s,a=t.className,l=t.setProps,u=i(t,jd);return c().createElement(qu,e({onClick:function(){l&&l({n_clicks:o+1})},className:r||a},b(["setProps"],u),{"data-dash-is-loading":Nt()||void 0}),n)}Ed.propTypes={children:d().node,id:d().string,n_clicks:d().number,style:d().object,class_name:d().string,type:d().string,key:d().string,className:d().string,setProps:d().func};const kd=Ed;var Pd=["children","color","dark","fluid","links_left","brand","brand_href","brand_external_link","expand","style","class_name","brand_style","className"];function Cd(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Td(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Cd(Object(n),!0).forEach((function(t){Rt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Cd(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Sd(t){var n=t.children,r=t.color,s=void 0===r?"primary":r,o=t.dark,a=void 0===o||o,u=t.fluid,d=void 0!==u&&u,f=t.links_left,p=void 0!==f&&f,m=t.brand,y=t.brand_href,g=t.brand_external_link,v=t.expand,h=void 0===v?"md":v,x=t.style,O=t.class_name,_=t.brand_style,w=t.className,N=i(t,Pd),j=cn.has(s),E=Ie((0,l.useState)(!1),2),k=E[0],P=E[1];return c().createElement(yd,e({variant:a?"dark":"light",bg:j?s:null,color:j?s:null,style:j?x:Td({backgroundColor:s},x),className:O||w,expand:h},b(["setProps"],N),{"data-dash-is-loading":Nt()||void 0}),c().createElement(Es,{fluid:d},m&&c().createElement(Nd,{href:y,style:_,external_link:g},m),c().createElement(kd,{onClick:function(){return P(!k)}}),c().createElement(yd.Collapse,{in:k},c().createElement(Bu,{className:p?"me-auto":"ms-auto"},n))))}Sd.propTypes={children:d().node,id:d().string,color:d().string,dark:d().bool,fluid:d().bool,links_left:d().bool,brand:d().node,brand_href:d().string,brand_external_link:d().bool,fixed:d().string,sticky:d().string,expand:d().oneOfType([d().bool,d().string]),style:d().object,class_name:d().string,brand_style:d().object,key:d().string,className:d().string,setProps:d().func};const Rd=Sd;var Dd=["children","class_name","className"];function Ld(t){var n=t.children,r=t.class_name,s=t.className,o=i(t,Dd);return c().createElement(Pi,e({className:r||s},b(["setProps"],o),{"data-dash-is-loading":Nt()||void 0}),n)}Ld.propTypes={children:d().node,id:d().string,style:d().object,class_name:d().string,key:d().string,className:d().string,setProps:d().func};const Id=Ld;var Ad=n(10),$d=["children","href","n_clicks","active","disabled","class_name","className","setProps"],Md=function(t){var n=t.children,r=t.href,s=t.n_clicks,o=void 0===s?0:s,a=t.active,u=void 0!==a&&a,d=t.disabled,f=void 0!==d&&d,p=t.class_name,m=t.className,b=t.setProps,y=i(t,$d),v=Ie((0,l.useState)(!1),2),h=v[0],x=v[1],O=function(e){x(!0===u||"exact"===u&&e===r||"partial"===u&&e.startsWith(r))};(0,l.useEffect)((function(){O(window.location.pathname),"string"==typeof u&&Ad.History.onChange((function(){O(window.location.pathname)}))}),[u]);var _=g()(p||m,"nav-link",{active:h,disabled:f});return c().createElement(_n,e({className:_,disabled:f,preOnClick:function(){!f&&b&&b({n_clicks:o+1})},href:r},y,{"data-dash-is-loading":Nt()||void 0}),n)};Md.propTypes={children:d().node,id:d().string,href:d().string,n_clicks:d().number,active:d().oneOfType([d().bool,d().oneOf(["partial","exact"])]),disabled:d().bool,external_link:d().bool,style:d().object,class_name:d().string,target:d().string,key:d().string,className:d().string,setProps:d().func};const Fd=Md;var Bd=["children","is_open","close_button","title","backdrop","scrollable","class_name","backdrop_class_name","autofocus","labelledby","className","backdropClassName","autoFocus","labelledBy","setProps"];function zd(t){var n=t.children,r=t.is_open,s=void 0!==r&&r,o=t.close_button,a=void 0===o||o,l=t.title,u=t.backdrop,d=void 0===u||u,f=t.scrollable,p=t.class_name,m=t.backdrop_class_name,b=t.autofocus,y=t.labelledby,g=t.className,v=t.backdropClassName,h=t.autoFocus,x=t.labelledBy,O=t.setProps,_=i(t,Bd),w=function(){O&&O({is_open:!s})},N=l||a?c().createElement(ud.Header,{closeButton:a,onHide:"static"===d&&a?w:null},c().createElement(ud.Title,null,l)):null;return c().createElement(ud,e({autoFocus:b||h,"aria-labelledby":y||x,className:p||g,backdropClassName:m||v,scroll:f,show:s,onHide:"static"!==d?w:null,backdrop:d||"static"===d,"data-dash-is-loading":Nt()||void 0},_),N,c().createElement(ud.Body,null,n))}zd.propTypes={children:d().node,id:d().string,is_open:d().bool,title:d().node,placement:d().oneOf(["start","end","top","bottom"]),backdrop:d().oneOfType([d().bool,d().oneOf(["static"])]),close_button:d().bool,keyboard:d().bool,scrollable:d().bool,style:d().object,class_name:d().string,backdrop_class_name:d().string,autofocus:d().bool,labelledby:d().string,className:d().string,backdropClassName:d().string,autoFocus:d().bool,labelledBy:d().string,setProps:d().func};const Hd=zd,Kd=l.forwardRef((({active:e=!1,disabled:t=!1,className:n,style:r,activeLabel:s="(current)",children:o,linkStyle:a,linkClassName:i,as:l=Jt,...c},u)=>{const d=e||t?"span":l;return(0,O.jsx)("li",{ref:u,style:r,className:g()(n,"page-item",{active:e,disabled:t}),children:(0,O.jsxs)(d,{className:g()("page-link",i),style:a,...c,children:[o,e&&s&&(0,O.jsx)("span",{className:"visually-hidden",children:s})]})})}));Kd.displayName="PageItem";const Ud=Kd;function Wd(e,t,n=e){const r=l.forwardRef((({children:e,...r},s)=>(0,O.jsxs)(Kd,{...r,ref:s,children:[(0,O.jsx)("span",{"aria-hidden":"true",children:e||t}),(0,O.jsx)("span",{className:"visually-hidden",children:n})]})));return r.displayName=e,r}const qd=Wd("First","«"),Vd=Wd("Prev","‹","Previous"),Gd=Wd("Ellipsis","…","More"),Yd=Wd("Next","›"),Xd=Wd("Last","»"),Jd=l.forwardRef((({bsPrefix:e,className:t,size:n,...r},s)=>{const o=E(e,"pagination");return(0,O.jsx)("ul",{ref:s,...r,className:g()(t,o,n&&`${o}-${n}`)})}));Jd.displayName="Pagination";const Zd=Object.assign(Jd,{First:qd,Prev:Vd,Ellipsis:Gd,Item:Ud,Next:Yd,Last:Xd});var Qd=["class_name","active_page","min_value","max_value","step","fully_expanded","previous_next","first_last","className","setProps"];function ef(t){var n=t.class_name,r=t.active_page,s=void 0===r?1:r,o=t.min_value,a=void 0===o?1:o,l=t.max_value,u=t.step,d=void 0===u?1:u,f=t.fully_expanded,p=void 0===f||f,m=t.previous_next,b=void 0!==m&&m,y=t.first_last,g=void 0!==y&&y,v=t.className,h=t.setProps,x=i(t,Qd);(l-a)%d!=0&&(l=l+d-(l-a)%d);var O=function(e){h&&h({active_page:e})},_=function(e){return c().createElement(Zd.Item,{key:e,active:e===s,onClick:function(){return O(e)}},e)},w=[];if(g&&w.push(c().createElement(Zd.First,{key:"first",disabled:s===a,onClick:function(){return O(a)}})),b&&w.push(c().createElement(Zd.Prev,{key:"previous",disabled:s===a,onClick:function(){return O(s-d)}})),p||Math.floor((l-a)/d)+1<=7)for(var N=a;N<=l;N+=d)w.push(_(N));else w.push(_(a)),s<=a+3*d?(w.push(_(a+d)),w.push(_(a+2*d)),w.push(_(a+3*d)),w.push(_(a+4*d)),w.push(c().createElement(Zd.Ellipsis,{disabled:!0,key:"ellipsis"}))):s>=l-3*d?(w.push(c().createElement(Zd.Ellipsis,{disabled:!0,key:"ellipsis"})),w.push(_(l-4*d)),w.push(_(l-3*d)),w.push(_(l-2*d)),w.push(_(l-d))):(w.push(c().createElement(Zd.Ellipsis,{disabled:!0,key:"ellipsis-1"})),w.push(_(s-d)),w.push(_(s)),w.push(_(s+d)),w.push(c().createElement(Zd.Ellipsis,{disabled:!0,key:"ellipsis-2"}))),w.push(_(l));return b&&w.push(c().createElement(Zd.Next,{key:"next",disabled:s===l,onClick:function(){return O(s+d)}})),g&&w.push(c().createElement(Zd.Last,{key:"last",disabled:s===l,onClick:function(){return O(l)}})),c().createElement(Zd,e({className:n||v,"data-dash-is-loading":Nt()||void 0},x),w)}ef.propTypes={id:d().string,active_page:d().number,min_value:d().number,max_value:d().number.isRequired,step:d().number,size:d().oneOf(["sm","lg"]),fully_expanded:d().bool,previous_next:d().bool,first_last:d().bool,style:d().object,class_name:d().string,className:d().string,setProps:d().func};const tf=ef;function nf({animation:e,bg:t,bsPrefix:n,size:r,...s}){n=E(n,"placeholder");const[{className:o,...a}]=ps(s);return{...a,className:g()(o,e?`${n}-${e}`:n,r&&`${n}-${r}`,t&&`bg-${t}`)}}const rf=l.forwardRef(((e,t)=>{const n=nf(e);return(0,O.jsx)(An,{...n,ref:t,disabled:!0,tabIndex:-1})}));rf.displayName="PlaceholderButton";const sf=rf,of=l.forwardRef((({as:e="span",...t},n)=>{const r=nf(t);return(0,O.jsx)(e,{...r,ref:n})}));of.displayName="Placeholder";const af=Object.assign(of,{Button:sf});var lf=["children","className","class_name","color","style","animation","delay_hide","delay_show","show_initially","button","display","target_components"];function cf(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function uf(t){var n,r=t.children,s=t.className,o=t.class_name,a=t.color,u=t.style,f=t.animation,p=t.delay_hide,m=void 0===p?0:p,y=t.delay_show,v=void 0===y?0:y,h=t.show_initially,x=void 0===h||h,O=t.button,_=void 0!==O&&O,w=t.display,N=void 0===w?"auto":w,j=t.target_components,E=i(t,lf),k=null===(n=window.dash_component_api)||void 0===n?void 0:n.useDashContext(),P=!!k&&k.useSelector(jt(k.componentPath,j),st),C=Ie((0,l.useState)(x),2),T=C[0],S=C[1],R=(0,l.useRef)(),D=(0,l.useRef)();(0,l.useEffect)((function(){"show"===N||"hide"===N?S("show"===N):P?(R.current&&(R.current=clearTimeout(R.current)),T||D.current||(D.current=setTimeout((function(){S(!0),D.current=null}),v))):(D.current&&(D.current=clearTimeout(D.current)),T&&!R.current&&(R.current=setTimeout((function(){S(!1),R.current=null}),m)))}),[m,v,P,T,N]);var L=g()(o||s,f&&"placeholder"),I=function(t){var n=t.finalStyle;return _?c().createElement(af.Button,e({variant:a,className:L,style:n,animation:f},b(["setProps"],E))):c().createElement(af,e({bg:a,className:L,style:n,animation:f},b(["setProps"],E)))};if(I.propTypes={finalStyle:d().object},r){var A=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?cf(Object(n),!0).forEach((function(t){Rt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):cf(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({display:"block",margin:"1rem auto"},u);return c().createElement("div",{style:T?{visibility:"hidden",position:"relative"}:{}},r,T&&c().createElement("div",{style:{visibility:"visible",position:"absolute",top:0,height:"100%",width:"100%",display:"flex",justifyContent:"left",alignItems:"start"}},c().createElement(I,{finalStyle:A})))}return c().createElement(I,{finalStyle:u})}uf.propTypes={children:d().node,id:d().string,animation:d().oneOf(["glow","wave"]),color:d().string,size:d().oneOf(["xs","sm","lg"]),button:d().bool,delay_hide:d().number,delay_show:d().number,show_initially:d().bool,style:d().object,class_name:d().string,xs:d().number,sm:d().number,md:d().number,lg:d().number,xl:d().number,xxl:d().number,target_components:d().objectOf(d().oneOfType([d().string,d().arrayOf(d().string)])),display:d().oneOf(["auto","show","hide"]),key:d().string,className:d().string,setProps:d().func};const df=uf;function ff(e,t,n,r,s,o,a){try{var i=e[o](a),l=i.value}catch(e){return void n(e)}i.done?t(l):Promise.resolve(l).then(r,s)}function pf(e){return function(){var t=this,n=arguments;return new Promise((function(r,s){var o=e.apply(t,n);function a(e){ff(o,r,s,a,i,"next",e)}function i(e){ff(o,r,s,a,i,"throw",e)}a(void 0)}))}}var mf=n(11),bf=n.n(mf);const yf=()=>{},gf=l.forwardRef(((e,t)=>{const{flip:n,offset:r,placement:s,containerPadding:o,popperConfig:a={},transition:i,runTransition:c}=e,[u,d]=Ft(),[f,p]=Ft(),m=yi(d,t),b=Cc(e.container),y=Cc(e.target),[g,v]=(0,l.useState)(!e.show),h=na(y,u,da({placement:s,enableEvents:!!e.show,containerPadding:o||5,flip:n,offset:r,arrowElement:f,popperConfig:a}));e.show&&g&&v(!1);const x=e.show||!g;if(function(e,t,{disabled:n,clickTrigger:r}={}){const s=t||yf;ca(e,s,{disabled:n,clickTrigger:r});const o=Bt((e=>{G(e)&&s(e)}));(0,l.useEffect)((()=>{if(n||null==e)return;const t=T(ia(e));let r=(t.defaultView||window).event;const s=ne(t,"keyup",(e=>{e!==r?o(e):r=void 0}));return()=>{s()}}),[e,n,o])}(u,e.onHide,{disabled:!e.rootClose||e.rootCloseDisabled,clickTrigger:e.rootCloseEvent}),!x)return null;const{onExit:O,onExiting:_,onEnter:w,onEntering:N,onEntered:j}=e;let E=e.children(Object.assign({},h.attributes.popper,{style:h.styles.popper,ref:m}),{popper:h,placement:s,show:!!e.show,arrowProps:Object.assign({},h.attributes.arrow,{style:h.styles.arrow,ref:p})});return E=Ic(i,c,{in:!!e.show,appear:!0,mountOnEnter:!0,unmountOnExit:!0,children:E,onExit:O,onExiting:_,onExited:(...t)=>{v(!0),e.onExited&&e.onExited(...t)},onEnter:w,onEntering:N,onEntered:j}),b?M().createPortal(E,b):null}));gf.displayName="Overlay";const vf=gf,hf=l.forwardRef((({className:e,bsPrefix:t,as:n="div",...r},s)=>(t=E(t,"popover-header"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));hf.displayName="PopoverHeader";const xf=hf,Of=l.forwardRef((({className:e,bsPrefix:t,as:n="div",...r},s)=>(t=E(t,"popover-body"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));Of.displayName="PopoverBody";const _f=Of;function wf(e,t){let n=e;return"left"===e?n=t?"end":"start":"right"===e&&(n=t?"start":"end"),n}function Nf(e="absolute"){return{position:e,top:"0",left:"0",opacity:"0",pointerEvents:"none"}}l.Component;const jf=l.forwardRef((({bsPrefix:e,placement:t="right",className:n,style:r,children:s,body:o,arrowProps:a,hasDoneInitialMeasure:i,popper:l,show:c,...u},d)=>{const f=E(e,"popover"),p=C(),[m]=(null==t?void 0:t.split("-"))||[],b=wf(m,p);let y=r;return c&&!i&&(y={...r,...Nf(null==l?void 0:l.strategy)}),(0,O.jsxs)("div",{ref:d,role:"tooltip",style:y,"x-placement":m,className:g()(n,f,m&&`bs-popover-${b}`),...u,children:[(0,O.jsx)("div",{className:"popover-arrow",...a}),o?(0,O.jsx)(_f,{children:s}):s]})})),Ef=Object.assign(jf,{Header:xf,Body:_f,POPPER_OFFSET:[0,8]}),kf=l.forwardRef((({bsPrefix:e,placement:t="right",className:n,style:r,children:s,arrowProps:o,hasDoneInitialMeasure:a,popper:i,show:l,...c},u)=>{e=E(e,"tooltip");const d=C(),[f]=(null==t?void 0:t.split("-"))||[],p=wf(f,d);let m=r;return l&&!a&&(m={...r,...Nf(null==i?void 0:i.strategy)}),(0,O.jsxs)("div",{ref:u,style:m,role:"tooltip","x-placement":f,className:g()(n,e,`bs-tooltip-${p}`),...c,children:[(0,O.jsx)("div",{className:"tooltip-arrow",...o}),(0,O.jsx)("div",{className:`${e}-inner`,children:s})]})}));kf.displayName="Tooltip";const Pf=Object.assign(kf,{TOOLTIP_OFFSET:[0,6]}),Cf=l.forwardRef((({children:e,transition:t=nn,popperConfig:n={},rootClose:r=!1,placement:s="top",show:o=!1,...a},i)=>{const c=(0,l.useRef)({}),[u,d]=(0,l.useState)(null),[f,p]=function(e){const t=(0,l.useRef)(null),n=E(void 0,"popover"),r=E(void 0,"tooltip"),s=(0,l.useMemo)((()=>({name:"offset",options:{offset:()=>{if(e)return e;if(t.current){if(Hc(t.current,n))return Ef.POPPER_OFFSET;if(Hc(t.current,r))return Pf.TOOLTIP_OFFSET}return[0,0]}}})),[e,n,r]);return[t,[s]]}(a.offset),m=ce(i,f),b=!0===t?nn:t||void 0,y=Lt((e=>{d(e),null==n||null==n.onFirstUpdate||n.onFirstUpdate(e)}));return ni((()=>{u&&a.target&&(null==c.current.scheduleUpdate||c.current.scheduleUpdate())}),[u,a.target]),(0,l.useEffect)((()=>{o||d(null)}),[o]),(0,O.jsx)(vf,{...a,ref:m,popperConfig:{...n,modifiers:p.concat(n.modifiers||[]),onFirstUpdate:y},transition:b,rootClose:r,placement:s,show:o,children:(r,{arrowProps:s,popper:o,show:a})=>{var i;!function(e,t){const{ref:n}=e,{ref:r}=t;e.ref=n.__wrapped||(n.__wrapped=e=>n(ue(e))),t.ref=r.__wrapped||(r.__wrapped=e=>r(ue(e)))}(r,s);const d=null==o?void 0:o.placement,f=Object.assign(c.current,{state:null==o?void 0:o.state,scheduleUpdate:null==o?void 0:o.update,placement:d,outOfBoundaries:(null==o||null==(i=o.state)||null==(i=i.modifiersData.hide)?void 0:i.isReferenceHidden)||!1,strategy:n.strategy}),p=!!u;return"function"==typeof e?e({...r,placement:d,show:a,...!t&&a&&{className:"show"},popper:f,arrowProps:s,hasDoneInitialMeasure:p}):l.cloneElement(e,{...r,placement:d,arrowProps:s,popper:f,hasDoneInitialMeasure:p,className:g()(e.props.className,!t&&a&&"show"),style:{...e.props.style,...r.style}})}})}));Cf.displayName="Overlay";const Tf=Cf;var Sf=["children","target","delay","trigger","defaultShow","setProps","autohide"],Rf=(0,l.createContext)({});function Df(t){var n,r,s,o,a=t.children,u=t.target,d=t.delay,f=t.trigger,p=t.defaultShow,m=t.setProps,b=t.autohide,y=i(t,Sf),g=(n=Ie((0,l.useState)(false),2),r=n[0],s=n[1],o=(0,l.useRef)(r),(0,l.useEffect)((function(){o.current=r}),[r]),[r,s,o]),v=Ie(g,3),h=v[0],x=v[1],O=v[2],_=(0,l.useRef)(null),w=(0,l.useRef)(null),N=(0,l.useRef)(null),j=(0,l.useRef)(!1);(0,l.useEffect)((function(){return j.current=!0,function(){j.current=!1}}));var E,k="string"==typeof f?f.split(" "):[],P="object"!==Le(E=u)?E:"{"+Object.keys(E).sort().map((function(e){return JSON.stringify(e)+":"+((t=E[e])&&t.wild||JSON.stringify(t));var t})).join(",")+"}",C=Ie((0,l.useState)(!1),2),T=C[0],S=C[1],R=function(){O.current&&(w.current=clearTimeout(w.current),x(!1),m&&j.current&&m({is_open:!1}))},D=function(){!O.current&&N.current?(N.current=clearTimeout(N.current),R()):O.current&&(clearTimeout(w.current),w.current=setTimeout(R,d.hide))},L=function(){O.current||(N.current=clearTimeout(N.current),x(!0),m&&j.current&&m({is_open:!0}))},I=function(){O.current&&w.current?(w.current=clearTimeout(w.current),L()):O.current||(clearTimeout(N.current),N.current=setTimeout(L,d.show))},A=function(e){var t,n;t=e.target,(n=_.current)&&(t===n||n.contains(t))&&(w.current&&(w.current=clearTimeout(w.current)),O.current?D():I())};(0,l.useEffect)((function(){setTimeout((function(){return x(p)}),50)}),[p]),(0,l.useEffect)((function(){k.indexOf("legacy")>-1?S(!0):S(!1)}),[k]);var $=function(e){return new Promise((function(t){return setTimeout(t,e)}))},M=function(){var e=pf(bf().mark((function e(t){var n,r,s=arguments;return bf().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=s.length>1&&void 0!==s[1]?s[1]:0,!(null===(r=document.getElementById(t))&&n<4)){e.next=6;break}return e.next=5,$(100*Math.pow(2,n));case 5:return e.abrupt("return",M(t,n+1));case 6:return e.abrupt("return",r);case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return(0,l.useEffect)((function(){var e=function(){var e=pf(bf().mark((function e(){return bf().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,M(P);case 2:_.current=e.sent,(t=_.current)&&(k.indexOf("hover")>-1&&(t.addEventListener("mouseover",I,!0),t.addEventListener("mouseout",D,!0)),k.indexOf("focus")>-1&&(t.addEventListener("focusin",I,!0),t.addEventListener("focusout",D,!0)),(k.indexOf("click")>-1||k.indexOf("legacy")>-1)&&document.addEventListener("click",A,!0),t.addEventListener("keydown",(function(e){"Escape"===e.key&&R()})));case 4:case"end":return e.stop()}var t}),e)})));return function(){return e.apply(this,arguments)}}();e()}),[P]),c().createElement(Rf.Provider,{value:{handleMouseOverTooltipContent:function(){k.indexOf("hover")>-1&&!b&&(w.current&&(w.current=clearTimeout(w.current)),L())},handleMouseLeaveTooltipContent:function(e){k.indexOf("hover")>-1&&!b&&(N.current&&(N.current=clearTimeout(N.current)),e.persist(),D())}}},c().createElement(Tf,e({show:h,rootClose:T,onHide:function(){x(!1),m&&j.current&&m({is_open:!1})},target:_.current},y),a))}Df.propTypes={children:d().node,target:d().oneOfType([d().string,d().object]),delay:d().exact({show:d().number,hide:d().number}),trigger:d().string,defaultShow:d().bool,setProps:d().func,autohide:d().bool};const Lf=Df;var If=["placement","className","style","children","body","arrowProps","hasDoneInitialMeasure","popper","show","hideArrow"],Af=["placement","className","style","children","arrowProps","popper","show","hasDoneInitialMeasure"];function $f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Mf(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?$f(Object(n),!0).forEach((function(t){Rt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):$f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Ff=function(e){var t=e;return"left"===e?t="start":"right"===e&&(t="end"),t},Bf=c().forwardRef((function(t,n){var r=t.placement,s=void 0===r?"right":r,o=t.className,a=t.style,u=t.children,d=t.body,f=t.arrowProps,p=t.hasDoneInitialMeasure,m=t.popper,b=t.show,y=t.hideArrow,v=i(t,If),h=Ie((null==s?void 0:s.split("-"))||[],1)[0],x=Ff(h),O=a;b&&!p&&(O=Mf(Mf({},a),{position:null==m?void 0:m.strategy,top:"0",left:"0",opacity:"0",pointerEvents:"none"}));var _=(0,l.useContext)(Rf),w=_.handleMouseOverTooltipContent,N=_.handleMouseLeaveTooltipContent;return c().createElement("div",e({ref:n,role:"tooltip",style:O,"data-x-placement":h,className:g()(o,"popover",h&&"bs-popover-".concat(x)),onMouseOver:w,onMouseLeave:N},v),!y&&c().createElement("div",e({className:"popover-arrow"},f)),d?c().createElement(_f,null,u):u)})),zf=c().forwardRef((function(t,n){var r=t.placement,s=t.className,o=t.style,a=t.children,u=t.arrowProps,d=t.popper,f=t.show,p=t.hasDoneInitialMeasure,m=i(t,Af),b=Ie((null==r?void 0:r.split("-"))||[],1)[0],y=Ff(b),v=o;f&&!p&&(v=Mf(Mf({},o),{},{position:null==d?void 0:d.strategy,top:"0",left:"0",opacity:"0",pointerEvents:"none"}));var h=(0,l.useContext)(Rf),x=h.handleMouseOverTooltipContent,O=h.handleMouseLeaveTooltipContent;return c().createElement("div",e({ref:n,style:v,role:"tooltip","data-x-placement":b,className:g()(s,"tooltip","bs-tooltip-".concat(y)),onMouseOver:x,onMouseLeave:O},m),c().createElement("div",e({className:"tooltip-arrow"},u)),c().createElement("div",{className:"tooltip-inner"},a))}));Bf.propTypes={placement:d().string,className:d().string,style:d().object,children:d().node,body:d().bool,arrowProps:d().object,hasDoneInitialMeasure:d().bool,popper:d().object,show:d().bool,hideArrow:d().bool},zf.propTypes={placement:d().string,className:d().string,style:d().object,children:d().node,arrowProps:d().object,popper:d().object,show:d().bool,hasDoneInitialMeasure:d().bool};var Hf=["children","id","is_open","placement","hide_arrow","delay","offset","flip","body","autohide","style","class_name","className"];function Kf(t){var n=t.children,r=t.id,s=t.is_open,o=t.placement,a=void 0===o?"right":o,l=t.hide_arrow,u=t.delay,d=void 0===u?{show:0,hide:50}:u,f=t.offset,p=t.flip,m=void 0===p||p,y=t.body,g=t.autohide,v=void 0!==g&&g,h=t.style,x=t.class_name,O=t.className,_=i(t,Hf),w=f?{modifiers:[{name:"offset",options:{offset:"string"==typeof f?f.split(",").map((function(e){return parseInt(e)})):[0,f]}}]}:{};return c().createElement(Lf,e({"data-dash-is-loading":Nt()||void 0,defaultShow:s,popperConfig:w,delay:d,placement:a,flip:m,autohide:v},b(["persistence","persisted_props","persistence_type"],_)),c().createElement(Bf,{style:h,id:r,className:x||O,hideArrow:l,body:y},n))}Kf.dashPersistence={persisted_props:["is_open"],persistence_type:"local"},Kf.propTypes={children:d().node,id:d().string,target:d().oneOfType([d().string,d().object]),is_open:d().bool,trigger:d().string,placement:d().oneOf(["auto","auto-start","auto-end","top","top-start","top-end","right","right-start","right-end","bottom","bottom-start","bottom-end","left","left-start","left-end"]),hide_arrow:d().bool,delay:d().oneOfType([d().shape({show:d().number,hide:d().number}),d().number]),offset:d().oneOfType([d().string,d().number]),flip:d().bool,body:d().bool,autohide:d().bool,style:d().object,class_name:d().string,persistence:d().oneOfType([d().bool,d().string,d().number]),persisted_props:d().arrayOf(d().oneOf(["is_open"])),persistence_type:d().oneOf(["local","session","memory"]),key:d().string,className:d().string,setProps:d().func};const Uf=Kf;var Wf=["children","class_name","className"];function qf(t){var n=t.children,r=t.class_name,s=t.className,o=i(t,Wf);return c().createElement(_f,e({className:r||s},b(["setProps"],o),{"data-dash-is-loading":Nt()||void 0}),n)}qf.propTypes={children:d().node,id:d().string,style:d().object,class_name:d().string,tag:d().string,key:d().string,className:d().string,setProps:d().func};const Vf=qf;var Gf=["children","class_name","className"];function Yf(t){var n=t.children,r=t.class_name,s=t.className,o=i(t,Gf);return c().createElement(xf,e({className:r||s},b(["setProps"],o),{"data-dash-is-loading":Nt()||void 0}),n)}Yf.propTypes={children:d().node,id:d().string,style:d().object,class_name:d().string,tag:d().string,key:d().string,className:d().string,setProps:d().func};const Xf=Yf;var Jf=["min","now","max","label","style","variant","barStyle","animated","visuallyHidden","striped","className"],Zf=["isChild","min","max"],Qf=["now","label","visuallyHidden","striped","animated","variant","className","children","barStyle"],ep=["children","value","color","bar","hide_label","class_name","className"];function tp(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function np(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?tp(Object(n),!0).forEach((function(t){Rt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):tp(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var rp=c().createContext({});function sp(e,t,n){var r=(e-t)/(n-t)*100;return Math.round(1e3*r)/1e3}function op(t,n){var r=t.min,s=t.now,o=t.max,a=t.label,l=t.style,u=t.variant,d=t.barStyle,f=t.animated,p=void 0!==f&&f,m=t.visuallyHidden,b=void 0!==m&&m,y=t.striped,v=void 0!==y&&y,h=t.className,x=i(t,Jf);return c().createElement("div",e({ref:n},x,{role:"progressbar",className:g()(h,"progress-bar",Rt(Rt(Rt({},"bg-".concat(u),u),"progress-bar-animated",p),"progress-bar-striped",p||v)),style:np(np({width:"".concat(sp(s,r,o),"%")},l),d),"aria-valuenow":s,"aria-valuemin":r,"aria-valuemax":o}),b?c().createElement("span",{className:"visually-hidden"},a):a)}var ap=c().forwardRef((function(t,n){var r=t.isChild,s=void 0!==r&&r,o=t.min,a=t.max,u=i(t,Zf);if(s){var d=(0,l.useContext)(rp);return op(np(np({},u),{},{max:a||d.max,min:o||d.min}),n)}var f=u.now,p=u.label,m=u.visuallyHidden,b=u.striped,y=u.animated,v=u.variant,h=u.className,x=u.children,O=u.barStyle,_=i(u,Qf);return o=void 0===o?0:o,a=void 0===a?100:a,c().createElement("div",e({ref:n},_,{className:g()(h,"progress")}),c().createElement(rp.Provider,{value:{min:o,max:a}},x?Jr(x,(function(e){return(0,l.cloneElement)(e,{isChild:!0})})):op({min:o,now:f,max:a,label:p,visuallyHidden:m,striped:b,animated:y,variant:v,barStyle:O},n)))}));function ip(t){var n=t.children,r=t.value,s=t.color,o=t.bar,a=t.hide_label,l=void 0!==a&&a,u=t.class_name,d=t.className,f=i(t,ep),p=cn.has(s);return c().createElement(ap,e({className:u||d},b(["setProps"],f),{"data-dash-is-loading":Nt()||void 0,now:r,isChild:o,variant:p?s:null,visuallyHidden:l,barStyle:p?{}:{backgroundColor:s}}),n)}ap.propTypes={children:d().node,style:d().object,class_name:d().string,className:d().string,min:d().number,max:d().number,now:d().number,label:d().string,visuallyHidden:d().bool,animated:d().bool,striped:d().bool,variant:d().string,isChild:d().bool,barStyle:d().object},ip.propTypes={children:d().node,id:d().string,value:d().oneOfType([d().string,d().number]),label:d().string,min:d().number,max:d().number,color:d().string,bar:d().bool,hide_label:d().bool,animated:d().bool,striped:d().bool,style:d().object,class_name:d().string,key:d().string,className:d().string,setProps:d().func};const lp=ip;function cp(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function up(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?cp(Object(n),!0).forEach((function(t){Rt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):cp(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function dp(e){var t=e.className,n=e.class_name,r=e.style,s=e.key,o=e.name,a=e.id,i=e.inputClassName,l=e.input_class_name,u=e.inputCheckedStyle,d=e.input_checked_style,f=e.inputStyle,p=e.input_style,m=e.inputCheckedClassName,b=e.input_checked_class_name,y=e.labelClassName,v=e.label_class_name,h=e.labelCheckedClassName,x=e.label_checked_class_name,O=e.labelStyle,_=e.label_style,w=e.labelCheckedStyle,N=e.label_checked_style,j=e.setProps,E=e.inline,k=e.value,P=e.switch,C=e.options,T=_t(void 0===C?[]:C).map((function(e){return function(e){var t=e.value===k,n=t?up(up({},_||O),N||w):_||O,r=e.input_id||"_dbcprivate_radioitems_".concat(a,"_input_").concat(e.value);return c().createElement("div",{className:g()("form-check",E&&"form-check-inline",P&&"form-switch"),key:e.value},c().createElement("input",{id:r,name:o,value:e.value,checked:t,className:g()("form-check-input",l||i,t&&(b||m)),disabled:Boolean(e.disabled),style:t?d||u:p||f,type:"radio",onChange:function(){j({value:e.value})}}),c().createElement("label",{id:e.label_id,style:n,className:g()("form-check-label",v||y,t&&(x||h)),key:e.value,htmlFor:r},e.label))}(e)}));return c().createElement("div",{id:a,className:n||t,style:r,key:s,"data-dash-is-loading":Nt()||void 0},T)}dp.dashPersistence={persisted_props:["value"],persistence_type:"local"},dp.propTypes={options:d().oneOfType([d().arrayOf(d().oneOfType([d().string,d().number])),d().object,d().arrayOf(d().exact({label:d().node.isRequired,value:d().oneOfType([d().string,d().number]).isRequired,disabled:d().bool,input_id:d().string,label_id:d().string}))]),value:d().oneOfType([d().string,d().number]),id:d().string,inline:d().bool,switch:d().bool,style:d().object,class_name:d().string,input_style:d().object,input_checked_style:d().object,input_class_name:d().string,input_checked_class_name:d().string,label_style:d().object,label_class_name:d().string,label_checked_style:d().object,label_checked_class_name:d().string,name:d().string,persistence:d().oneOfType([d().bool,d().string,d().number]),persisted_props:d().arrayOf(d().oneOf(["value"])),persistence_type:d().oneOf(["local","session","memory"]),key:d().string,className:d().string,inputClassName:d().string,inputStyle:d().object,inputCheckedStyle:d().object,inputCheckedClassName:d().string,labelStyle:d().object,labelClassName:d().string,labelCheckedStyle:d().object,labelCheckedClassName:d().string,setProps:d().func};const fp=dp;function pp(e){var t=e.className,n=e.class_name,r=e.style,s=e.id,o=e.input_class_name,a=e.inputClassName,i=e.input_style,l=e.label,u=e.label_id,d=e.label_style,f=e.label_class_name,p=e.labelClassName,m=e.name,b=e.setProps,y=e.disabled,v=void 0!==y&&y,h=e.value,x=void 0!==h&&h;return c().createElement("div",{className:g()("form-check",n||t),style:r,"data-dash-is-loading":Nt()||void 0},c().createElement("input",{id:s,name:m,checked:x,className:g()("form-check-input",o||a),disabled:v,style:i,type:"radio",onClick:function(){v||b&&b({value:!x})},onChange:function(){}}),c().createElement("label",{id:u,style:d,className:g()(f||p,"form-check-label","form-label"),htmlFor:s},l))}pp.propTypes={id:d().string,value:d().bool,disabled:d().bool,class_name:d().string,style:d().object,input_style:d().object,input_class_name:d().string,label:d().node,label_id:d().string,label_style:d().object,label_class_name:d().string,name:d().string,persistence:d().oneOfType([d().bool,d().string,d().number]),persisted_props:d().arrayOf(d().oneOf(["value"])),persistence_type:d().oneOf(["local","session","memory"]),className:d().string,inputStyle:d().object,inputClassName:d().string,labelStyle:d().object,labelClassName:d().string,setProps:d().func},pp.dashPersistence={persisted_props:["value"],persistence_type:"local"};const mp=pp,bp=l.forwardRef((({bsPrefix:e,className:t,as:n="div",...r},s)=>{const o=E(e,"row"),a=k(),i=P(),l=`${o}-cols`,c=[];return a.forEach((e=>{const t=r[e];let n;delete r[e],null!=t&&"object"==typeof t?({cols:n}=t):n=t;const s=e!==i?`-${e}`:"";null!=n&&c.push(`${l}${s}-${n}`)})),(0,O.jsx)(n,{ref:s,...r,className:g()(t,o,...c)})}));bp.displayName="Row";const yp=bp;var gp=["children","class_name","align","justify","className"],vp={start:"align-items-start",center:"align-items-center",end:"align-items-end",stretch:"align-items-stretch",baseline:"align-items-baseline"},hp={start:"justify-content-start",center:"justify-content-center",end:"justify-content-end",around:"justify-content-around",between:"justify-content-between",evenly:"justify-content-evenly"};function xp(t){var n=t.children,r=t.class_name,s=t.align,o=t.justify,a=t.className,l=i(t,gp),u=s&&vp[s],d=o&&hp[o],f=g()(r||a,u,d);return c().createElement(yp,e({className:f},b(["setProps"],l),{"data-dash-is-loading":Nt()||void 0}),n)}xp.propTypes={children:d().node,id:d().string,style:d().object,class_name:d().string,align:d().oneOf(["start","center","end","stretch","baseline"]),justify:d().oneOf(["start","center","end","around","between","evenly"]),key:d().string,className:d().string,setProps:d().func};const Op=xp;var _p=["options","value","valid","invalid","class_name","html_size","placeholder","className","setProps"];function wp(t){var n=t.options,r=void 0===n?[]:n,s=t.value,o=void 0===s?"":s,a=t.valid,l=t.invalid,u=t.class_name,d=t.html_size,f=t.placeholder,p=void 0===f?"":f,m=t.className,y=t.setProps,g=i(t,_p);return c().createElement(gl,e({},b(["persistence","persistence_type","persisted_props"],g),{isInvalid:l,isValid:a,onChange:function(e){y&&y({value:e.target.value})},className:u||m,htmlSize:d,value:o||""}),c().createElement("option",{value:"",disabled:!0,hidden:!0},p),r&&_t(r).map((function(e){return c().createElement("option",{key:e.value,value:e.value,disabled:e.disabled,title:e.title},e.label)})))}wp.dashPersistence={persisted_props:["value"],persistence_type:"local"},wp.propTypes={options:d().oneOfType([d().arrayOf(d().oneOfType([d().string,d().number])),d().object,d().arrayOf(d().exact({label:d().oneOfType([d().string,d().number]).isRequired,value:d().string.isRequired,disabled:d().bool,title:d().string}))]),value:d().oneOfType([d().string,d().number]),id:d().string,placeholder:d().string,disabled:d().bool,style:d().object,class_name:d().string,required:d().oneOfType([d().oneOf(["required","REQUIRED"]),d().bool]),valid:d().bool,invalid:d().bool,size:d().string,html_size:d().string,name:d().string,persistence:d().oneOfType([d().bool,d().string,d().number]),persisted_props:d().arrayOf(d().oneOf(["value"])),persistence_type:d().oneOf(["local","session","memory"]),key:d().string,className:d().string,setProps:d().func};const Np=wp,jp=l.forwardRef((({bsPrefix:e,variant:t,animation:n="border",size:r,as:s="div",className:o,...a},i)=>{const l=`${e=E(e,"spinner")}-${n}`;return(0,O.jsx)(s,{ref:i,...a,className:g()(o,l,r&&`${l}-${r}`,t&&`text-${t}`)})}));jp.displayName="Spinner";const Ep=jp;var kp=["children","color","type","show_initially","delay_hide","delay_show","spinner_style","spinner_class_name","fullscreen","fullscreen_class_name","fullscreen_style","target_components","display","spinnerClassName","fullscreenClassName"];function Pp(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Cp(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Pp(Object(n),!0).forEach((function(t){Rt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Pp(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Tp(t){var n,r=t.children,s=t.color,o=t.type,a=void 0===o?"border":o,u=t.show_initially,f=void 0===u||u,p=t.delay_hide,m=void 0===p?0:p,y=t.delay_show,g=void 0===y?0:y,v=t.spinner_style,h=t.spinner_class_name,x=t.fullscreen,O=t.fullscreen_class_name,_=t.fullscreen_style,w=t.target_components,N=t.display,j=t.spinnerClassName,E=t.fullscreenClassName,k=i(t,kp),P=null===(n=window.dash_component_api)||void 0===n?void 0:n.useDashContext(),C=!!P&&P.useSelector(jt(P.componentPath,w),st),T=Ie((0,l.useState)(f),2),S=T[0],R=T[1],D=(0,l.useRef)(),L=(0,l.useRef)();(0,l.useEffect)((function(){"show"===N||"hide"===N?R("show"===N):C?(D.current&&(D.current=clearTimeout(D.current)),S||L.current||(L.current=setTimeout((function(){R(!0),L.current=null}),g))):(L.current&&(L.current=clearTimeout(L.current)),S&&!D.current&&(D.current=setTimeout((function(){R(!1),D.current=null}),m)))}),[m,g,C,S,N]);var I=cn.has(s),A=Cp({position:"fixed",width:"100vw",height:"100vh",top:0,left:0,backgroundColor:"white",display:"flex",justifyContent:"center",alignItems:"center",zIndex:99,visibility:"visible"},_);function $(t){var n=t.style;return c().createElement(Ep,e({variant:I?s:null,animation:a,style:Cp({color:!I&&s},n),className:h||j},b(["setProps"],k)))}if($.propTypes={style:d().object},r){var M=Cp({display:"block",margin:"1rem auto"},v);return c().createElement("div",{style:S?{visibility:"hidden",position:"relative"}:{}},r,S&&c().createElement("div",{style:x?A:{visibility:"visible",position:"absolute",top:0,height:"100%",width:"100%",display:"flex",justifyContent:"center",alignItems:"center"},className:x&&(O||E)},c().createElement($,{style:M})))}return x?c().createElement("div",{className:O||E,style:A},c().createElement($,{style:v})):c().createElement($,{style:v})}Tp.propTypes={children:d().node,id:d().string,color:d().string,type:d().string,size:d().string,fullscreen:d().bool,delay_hide:d().number,delay_show:d().number,show_initially:d().bool,spinner_style:d().object,spinner_class_name:d().string,fullscreen_style:d().object,fullscreen_class_name:d().string,display:d().oneOf(["auto","show","hide"]),target_components:d().objectOf(d().oneOfType([d().string,d().arrayOf(d().string)])),fullscreenClassName:d().string,spinnerClassName:d().string,setProps:d().func};const Sp=Tp;function Rp(e,t=_,n="xs"){const r=[];return Object.entries(e).forEach((([e,s])=>{null!=s&&("object"==typeof s?t.forEach((t=>{const o=s[t];if(null!=o){const s=t!==n?`-${t}`:"";r.push(`${e}${s}-${o}`)}})):r.push(`${e}-${s}`))})),r}const Dp=l.forwardRef((({as:e="div",bsPrefix:t,className:n,direction:r,gap:s,...o},a)=>{t=E(t,"horizontal"===r?"hstack":"vstack");const i=k(),l=P();return(0,O.jsx)(e,{...o,ref:a,className:g()(n,t,...Rp({gap:s},i,l))})}));Dp.displayName="Stack";const Lp=Dp;var Ip=["children","class_name","className"];function Ap(t){var n=t.children,r=t.class_name,s=t.className,o=i(t,Ip);return c().createElement(Lp,e({className:r||s},b(["setProps"],o),{"data-dash-is-loading":Nt()||void 0}),n)}Ap.defaultPropTypes={direction:"vertical"},Ap.propTypes={children:d().node,id:d().string,direction:d().oneOf(["vertical","horizontal"]),gap:d().number,style:d().object,class_name:d().string,key:d().string,className:d().string,setProps:d().func};const $p=Ap;function Mp(e){var t=e.value,n=void 0!==t&&t,r=e.id,s=e.label,o=e.disabled,a=void 0!==o&&o,i=e.style,l=e.class_name,u=e.input_class_name,d=e.input_style,f=e.label_id,p=e.label_style,m=e.label_class_name,b=e.name,y=e.className,v=e.inputClassName,h=e.labelClassName,x=e.setProps;return c().createElement("div",{className:g()("form-check form-switch",l||y),style:i,"data-dash-is-loading":Nt()||void 0},c().createElement("input",{id:r,name:b,checked:n,className:g()("form-check-input",u||v),disabled:a,style:d,type:"checkbox",onChange:function(){a||x&&x({value:!n})}}),c().createElement("label",{id:f,style:p,className:g()(m||h,"form-check-label","form-label"),htmlFor:r},s))}Mp.dashPersistence={persisted_props:["value"],persistence_type:"local"},Mp.propTypes={id:d().string,value:d().bool,disabled:d().bool,label:d().node,class_name:d().string,style:d().object,input_style:d().object,input_class_name:d().string,label_id:d().string,label_style:d().object,label_class_name:d().string,name:d().string,persistence:d().oneOfType([d().bool,d().string,d().number]),persisted_props:d().arrayOf(d().oneOf(["value"])),persistence_type:d().oneOf(["local","session","memory"]),className:d().string,inputStyle:d().object,inputClassName:d().string,labelStyle:d().object,labelClassName:d().string,setProps:d().func};const Fp=Mp;function Bp(e){return c().createElement("div",null,e.children)}Bp.propTypes={children:d().node,id:d().string,label:d().string,tab_id:d().string,disabled:d().bool,style:d().object,tab_style:d().object,active_tab_style:d().object,label_style:d().object,active_label_style:d().object,class_name:d().string,tab_class_name:d().string,active_tab_class_name:d().string,label_class_name:d().string,active_label_class_name:d().string,key:d().string,className:d().string,tabClassName:d().string,activeTabClassName:d().string,labelClassName:d().string,activeLabelClassName:d().string,setProps:d().func};const zp=Bp,Hp=["active","eventKey","mountOnEnter","transition","unmountOnExit","role","onEnter","onEntering","onEntered","onExit","onExiting","onExited"],Kp=["activeKey","getControlledId","getControllerId"],Up=["as"];function Wp(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}function qp(e){let{active:t,eventKey:n,mountOnEnter:r,transition:s,unmountOnExit:o,role:a="tabpanel",onEnter:i,onEntering:c,onEntered:u,onExit:d,onExiting:f,onExited:p}=e,m=Wp(e,Hp);const b=(0,l.useContext)(gi);if(!b)return[Object.assign({},m,{role:a}),{eventKey:n,isActive:t,mountOnEnter:r,transition:s,unmountOnExit:o,onEnter:i,onEntering:c,onEntered:u,onExit:d,onExiting:f,onExited:p}];const{activeKey:y,getControlledId:g,getControllerId:v}=b,h=Wp(b,Kp),x=Sa(n);return[Object.assign({},m,{role:a,id:g(n),"aria-labelledby":v(n)}),{eventKey:n,isActive:null==t&&null!=x?Sa(y)===x:t,transition:s||h.transition,mountOnEnter:null!=r?r:h.mountOnEnter,unmountOnExit:null!=o?o:h.unmountOnExit,onEnter:i,onEntering:c,onEntered:u,onExit:d,onExiting:f,onExited:p}]}const Vp=l.forwardRef(((e,t)=>{let{as:n="div"}=e,r=Wp(e,Up);const[s,{isActive:o,onEnter:a,onEntering:i,onEntered:l,onExit:c,onExiting:u,onExited:d,mountOnEnter:f,unmountOnExit:p,transition:m=Tc}]=qp(r);return(0,O.jsx)(gi.Provider,{value:null,children:(0,O.jsx)(Ra.Provider,{value:null,children:(0,O.jsx)(m,{in:o,onEnter:a,onEntering:i,onEntered:l,onExit:c,onExiting:u,onExited:d,mountOnEnter:f,unmountOnExit:p,children:(0,O.jsx)(n,Object.assign({},s,{ref:t,hidden:!o,"aria-hidden":!o}))})})})}));Vp.displayName="TabPanel";const Gp=e=>{const{id:t,generateChildId:n,onSelect:r,activeKey:s,defaultActiveKey:o,transition:a,mountOnEnter:i,unmountOnExit:c,children:u}=e,[d,f]=Rs(s,o,r),p=_a(t),m=(0,l.useMemo)((()=>n||((e,t)=>p?`${p}-${t}-${e}`:null)),[p,n]),b=(0,l.useMemo)((()=>({onSelect:f,activeKey:d,transition:a,mountOnEnter:i||!1,unmountOnExit:c||!1,getControlledId:e=>m(e,"tabpane"),getControllerId:e=>m(e,"tab")})),[f,d,a,i,c,m]);return(0,O.jsx)(gi.Provider,{value:b,children:(0,O.jsx)(Ra.Provider,{value:f||null,children:u})})};Gp.Panel=Vp;const Yp=Gp;function Xp(e){return"boolean"==typeof e?e?nn:Tc:e}const Jp=({transition:e,...t})=>(0,O.jsx)(Yp,{...t,transition:Xp(e)});Jp.displayName="TabContainer";const Zp=Jp,Qp=l.forwardRef((({className:e,bsPrefix:t,as:n="div",...r},s)=>(t=E(t,"tab-content"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));Qp.displayName="TabContent";const em=Qp,tm=l.forwardRef((({bsPrefix:e,transition:t,...n},r)=>{const[{className:s,as:o="div",...a},{isActive:i,onEnter:l,onEntering:c,onEntered:u,onExit:d,onExiting:f,onExited:p,mountOnEnter:m,unmountOnExit:b,transition:y=nn}]=qp({...n,transition:Xp(t)}),v=E(e,"tab-pane");return(0,O.jsx)(gi.Provider,{value:null,children:(0,O.jsx)(Ra.Provider,{value:null,children:(0,O.jsx)(y,{in:i,onEnter:l,onEntering:c,onEntered:u,onExit:d,onExiting:f,onExited:p,mountOnEnter:m,unmountOnExit:b,children:(0,O.jsx)(o,{...a,ref:r,className:g()(s,v,i&&"active")})})})})}));tm.displayName="TabPane";const nm=tm,rm={eventKey:d().oneOfType([d().string,d().number]),title:d().node.isRequired,disabled:d().bool,tabClassName:d().string,tabAttrs:d().object},sm=()=>{throw new Error("ReactBootstrap: The `Tab` component is not meant to be rendered! It's an abstract component that is only valid as a direct Child of the `Tabs` Component. For custom tabs components use TabPane and TabsContainer directly")};sm.propTypes=rm;const om=Object.assign(sm,{Container:Zp,Content:em,Pane:nm});var am=["children","tabId"];function im(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function lm(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?im(Object(n),!0).forEach((function(t){Rt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):im(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function cm(t){var n=t.children,r=t.tabId,s=i(t,am);return c().createElement(om.Pane,e({eventKey:r,key:r,"data-dash-is-loading":Nt()||void 0},s),n)}function um(e){var t=e.children,n=e.id,r=e.active_tab,s=e.style,o=e.class_name,a=e.key,i=e.className,u=e.setProps;t=xt(t),(0,l.useEffect)((function(){u&&void 0===r&&u({active_tab:t&&(Ot(t[0]).tab_id||"tab-0")})}),[]);var d=t&&t.map((function(e,t){var n=Ot(e),s=n.key||n.tab_id||"tab-"+t,o=r===s;return c().createElement(Ri.Item,{id:wt(n.id),key:s,style:o?lm(lm({},n.tab_style),n.active_tab_style):n.tab_style,className:g()(n.tab_class_name||n.tabClassName,o&&(n.active_tab_class_name||n.activeTabClassName))},c().createElement(Ri.Link,{className:g()(n.label_class_name||n.labelClassName,o&&(n.active_label_class_name||n.activeLabelClassName),{active:o}),style:lm(lm(lm({},!n.disabled&&{cursor:"pointer"}),n.label_style),o&&n.active_label_style),disabled:n.disabled,onClick:function(){var e;n.disabled||(e=s,u&&r!==e&&u({active_tab:e}))}},n.label))})),f=t&&t.map((function(e,t){var n=Ot(e),r=n.style,s=n.className,o=n.class_name,a=n.tab_id,i=n.disabled,l=void 0!==i&&i,u=a||"tab-"+t;return c().createElement(cm,{key:u,disabled:l,style:r,className:o||s,tabId:u},e)}));return c().createElement(om.Container,{key:a,activeKey:r,onSelect:function(e){return u({active_tab:e})},"data-dash-is-loading":Nt()||void 0},c().createElement(Ri,{id:n,variant:"tabs",as:"ul",className:o||i,style:s},d),c().createElement(om.Content,null,f))}um.dashPersistence={persisted_props:["active_tab"],persistence_type:"local"},um.dashChildrenUpdate=!0,um.propTypes={children:d().node,id:d().string,active_tab:d().string,style:d().object,class_name:d().string,persistence:d().oneOfType([d().bool,d().string,d().number]),persisted_props:d().arrayOf(d().oneOf(["active_tab"])),persistence_type:d().oneOf(["local","session","memory"]),key:d().string,className:d().string,setProps:d().func};const dm=um,fm=l.forwardRef((({bsPrefix:e,className:t,striped:n,bordered:r,borderless:s,hover:o,size:a,variant:i,responsive:l,...c},u)=>{const d=E(e,"table"),f=g()(t,d,i&&`${d}-${i}`,a&&`${d}-${a}`,n&&`${d}-${"string"==typeof n?`striped-${n}`:"striped"}`,r&&`${d}-bordered`,s&&`${d}-borderless`,o&&`${d}-hover`),p=(0,O.jsx)("table",{...c,className:f,ref:u});if(l){let e=`${d}-responsive`;return"string"==typeof l&&(e=`${e}-${l}`),(0,O.jsx)("div",{className:e,children:p})}return p})),pm=fm;var mm=["children","class_name","color","className"];function bm(t){var n=t.children,r=t.class_name,s=t.color,o=t.className,a=i(t,mm);return c().createElement(pm,e({className:r||o,variant:s},b(["setProps"],a),{"data-dash-is-loading":Nt()||void 0}),n)}bm.propTypes={children:d().node,id:d().string,style:d().object,class_name:d().string,size:d().string,bordered:d().bool,borderless:d().bool,striped:d().bool,color:d().string,hover:d().bool,responsive:d().oneOfType([d().bool,d().string]),key:d().string,className:d().string,setProps:d().func};const ym=bm;var gm=["value","n_blur","n_submit","n_clicks","class_name","invalid","valid","size","debounce","submit_on_enter","autofocus","maxlength","minlength","readonly","accesskey","contenteditable","contextmenu","spellcheck","tabindex","className","accessKey","autoFocus","contentEditable","contextMenu","maxLength","minLength","readOnly","spellCheck","tabIndex","setProps"];function vm(t){var n=t.value,r=void 0===n?"":n,s=t.n_blur,o=void 0===s?0:s,a=t.n_submit,u=void 0===a?0:a,d=t.n_clicks,f=void 0===d?0:d,p=t.class_name,m=t.invalid,y=t.valid,v=t.size,h=t.debounce,x=void 0!==h&&h,O=t.submit_on_enter,_=void 0===O||O,w=t.autofocus,N=t.maxlength,j=t.minlength,E=t.readonly,k=t.accesskey,P=t.contenteditable,C=t.contextmenu,T=t.spellcheck,S=t.tabindex,R=t.className,D=t.accessKey,L=t.autoFocus,I=t.contentEditable,A=t.contextMenu,$=t.maxLength,M=t.minLength,F=t.readOnly,B=t.spellCheck,z=t.tabIndex,H=t.setProps,K=i(t,gm),U=Ie((0,l.useState)(r||""),2),W=U[0],q=U[1],V=(0,l.useRef)(null);(0,l.useEffect)((function(){r!==W&&q(r||"")}),[r]);var G=g()(p||R,m&&"is-invalid",y&&"is-valid",!!v&&"form-control-".concat(v),"form-control");return c().createElement("textarea",e({value:W,className:G,onChange:function(e){var t=e.target.value;q(t),x?Number.isFinite(x)&&(clearTimeout(V.current),V.current=setTimeout((function(){return H({value:t})}),x)):H({value:t})},onBlur:function(e){if(H){var t={n_blur:o+1};!0===x&&(t.value=e.target.value),H(t)}},onKeyUp:function(e){if(_&&H&&"Enter"===e.key&&!e.shiftKey){e.preventDefault();var t={n_submit:u+1};!0===x&&(t.value=e.target.value),H(t)}},onClick:function(){H&&H({n_clicks:f+1})},autoFocus:w||L,maxLength:N||$,minLength:j||M,readOnly:E||F,accessKey:k||D,contentEditable:P||I,contextMenu:C||A,spellCheck:T||B,tabIndex:S||z},b(["persistence","persistence_type","persisted_props"],K),{"data-dash-is-loading":Nt()||void 0}))}vm.dashPersistence={persisted_props:["value"],persistence_type:"local"},vm.propTypes={id:d().string,value:d().string,n_blur:d().number,n_submit:d().number,n_clicks:d().number,valid:d().bool,invalid:d().bool,placeholder:d().string,size:d().string,style:d().object,class_name:d().string,accesskey:d().string,autofocus:d().string,contenteditable:d().oneOfType([d().string,d().number]),contextmenu:d().string,cols:d().oneOfType([d().string,d().number]),dir:d().string,disabled:d().oneOfType([d().string,d().bool]),draggable:d().oneOfType([d().oneOf(["true","false"]),d().bool]),form:d().string,hidden:d().string,lang:d().string,maxlength:d().oneOfType([d().string,d().number]),minlength:d().oneOfType([d().string,d().number]),name:d().string,readonly:d().oneOfType([d().bool,d().oneOf(["readOnly","readonly","READONLY"])]),required:d().oneOfType([d().oneOf(["required","REQUIRED"]),d().bool]),rows:d().oneOfType([d().string,d().number]),spellcheck:d().oneOfType([d().oneOf(["true","false"]),d().bool]),tabindex:d().oneOfType([d().string,d().number]),title:d().string,wrap:d().string,submit_on_enter:d().bool,debounce:d().oneOfType([d().bool,d().number]),persistence:d().oneOfType([d().bool,d().string,d().number]),persisted_props:d().arrayOf(d().oneOf(["value"])),persistence_type:d().oneOf(["local","session","memory"]),key:d().string,className:d().string,accessKey:d().string,autoFocus:d().string,contentEditable:d().oneOfType([d().string,d().number]),contextMenu:d().string,maxLength:d().oneOfType([d().string,d().number]),minLength:d().oneOfType([d().string,d().number]),readOnly:d().oneOfType([d().bool,d().oneOf(["readOnly","readonly","READONLY"])]),spellCheck:d().oneOfType([d().oneOf(["true","false"]),d().bool]),tabIndex:d().oneOfType([d().string,d().number]),setProps:d().func};const hm=vm,xm={[H]:"showing",[U]:"showing show"},Om=l.forwardRef(((e,t)=>(0,O.jsx)(nn,{...e,ref:t,transitionClasses:xm})));Om.displayName="ToastFade";const _m=Om,wm=l.createContext({onClose(){}}),Nm=l.forwardRef((({bsPrefix:e,closeLabel:t="Close",closeVariant:n,closeButton:r=!0,className:s,children:o,...a},i)=>{e=E(e,"toast-header");const c=(0,l.useContext)(wm),u=Lt((e=>{null==c||null==c.onClose||c.onClose(e)}));return(0,O.jsxs)("div",{ref:i,...a,className:g()(e,s),children:[o,r&&(0,O.jsx)(on,{"aria-label":t,variant:n,onClick:u,"data-dismiss":"toast"})]})}));Nm.displayName="ToastHeader";const jm=Nm,Em=l.forwardRef((({className:e,bsPrefix:t,as:n="div",...r},s)=>(t=E(t,"toast-body"),(0,O.jsx)(n,{ref:s,className:g()(e,t),...r}))));Em.displayName="ToastBody";const km=Em,Pm=l.forwardRef((({bsPrefix:e,className:t,transition:n=_m,show:r=!0,animation:s=!0,delay:o=5e3,autohide:a=!1,onClose:i,onEntered:c,onExit:u,onExiting:d,onEnter:f,onEntering:p,onExited:m,bg:b,...y},v)=>{e=E(e,"toast");const h=(0,l.useRef)(o),x=(0,l.useRef)(i);(0,l.useEffect)((()=>{h.current=o,x.current=i}),[o,i]);const _=qr(),w=!(!a||!r),N=(0,l.useCallback)((()=>{w&&(null==x.current||x.current())}),[w]);(0,l.useEffect)((()=>{_.set(N,h.current)}),[_,N]);const j=(0,l.useMemo)((()=>({onClose:i})),[i]),k=!(!n||!s),P=(0,O.jsx)("div",{...y,ref:v,className:g()(e,t,b&&`bg-${b}`,!k&&(r?"show":"hide")),role:"alert","aria-live":"assertive","aria-atomic":"true"});return(0,O.jsx)(wm.Provider,{value:j,children:k&&n?(0,O.jsx)(n,{in:r,onEnter:f,onEntering:p,onEntered:c,onExit:u,onExiting:d,onExited:m,unmountOnExit:!0,children:P}):P})}));Pm.displayName="Toast";const Cm=Object.assign(Pm,{Body:km,Header:jm});var Tm=["children","is_open","dismissable","duration","n_dismiss","color","header","icon","header_style","header_class_name","body_style","body_class_name","class_name","className","headerClassName","bodyClassName","setProps"];function Sm(t){var n=t.children,r=t.is_open,s=void 0===r||r,o=t.dismissable,a=void 0!==o&&o,u=t.duration,d=t.n_dismiss,f=void 0===d?0:d,p=t.color,m=t.header,y=t.icon,v=t.header_style,h=t.header_class_name,x=t.body_style,O=t.body_class_name,_=t.class_name,w=t.className,N=t.headerClassName,j=t.bodyClassName,E=t.setProps,k=i(t,Tm),P=function(){E&&E({is_open:!1,n_dismiss:f+1})},C=(0,l.useRef)(null);return(0,l.useEffect)((function(){return u&&(s?C.current=setTimeout(P,u):C.current&&(clearTimeout(C.current),C.current=null)),function(){return clearTimeout(C.current)}}),[s]),c().createElement(Cm,e({show:s,onClose:a&&P,className:_||w,bg:p,"data-dash-is-loading":Nt()||void 0},b(["persistence","persisted_props","persistence_type","setProps"],k)),c().createElement(Cm.Header,{style:v,className:h||N,closeButton:a},y&&c().createElement("svg",{className:"rounded text-".concat(y),width:"20",height:"20",xmlns:"http://www.w3.org/2000/svg",preserveAspectRatio:"xMidYMid slice",focusable:"false",role:"img"},c().createElement("rect",{fill:"currentColor",width:"100%",height:"100%"})),c().createElement("strong",{className:g()("me-auto",y&&"ms-2")},m)),c().createElement(Cm.Body,{style:x,className:O||j},n))}Sm.dashPersistence={persisted_props:["is_open"],persistence_type:"local"},Sm.propTypes={children:d().node,id:d().string,is_open:d().bool,dismissable:d().bool,duration:d().number,n_dismiss:d().number,header:d().node,icon:d().string,color:d().string,style:d().object,class_name:d().string,header_style:d().object,header_class_name:d().string,body_style:d().object,body_class_name:d().string,tag:d().string,persistence:d().oneOfType([d().bool,d().string,d().number]),persisted_props:d().arrayOf(d().oneOf(["is_open"])),persistence_type:d().oneOf(["local","session","memory"]),key:d().string,className:d().string,headerClassName:d().string,bodyClassName:d().string,setProps:d().func};const Rm=Sm;var Dm=["children","id","is_open","trigger","style","class_name","delay","placement","flip","autohide","fade","className"];function Lm(t){var n=t.children,r=t.id,s=t.is_open,o=t.trigger,a=void 0===o?"hover focus":o,l=t.style,u=t.class_name,d=t.delay,f=void 0===d?{show:0,hide:50}:d,p=t.placement,m=void 0===p?"auto":p,b=t.flip,y=void 0===b||b,g=t.autohide,v=void 0===g||g,h=t.fade,x=void 0===h||h,O=t.className,_=i(t,Dm);return c().createElement(Lf,e({"data-dash-is-loading":Nt()||void 0,defaultShow:s,delay:f,placement:m,flip:y,autohide:v,trigger:a},_,{transition:x}),c().createElement(zf,{id:r,style:l,className:u||O},n))}Lm.propTypes={children:d().node,id:d().string,target:d().oneOfType([d().string,d().object]),is_open:d().bool,trigger:d().string,placement:d().oneOf(["auto","auto-start","auto-end","top","top-start","top-end","right","right-start","right-end","bottom","bottom-start","bottom-end","left","left-start","left-end"]),delay:d().shape({show:d().number,hide:d().number}),flip:d().bool,autohide:d().bool,fade:d().bool,style:d().object,class_name:d().string,key:d().string,className:d().string,setProps:d().func};const Im=Lm})(),window.dash_bootstrap_components=r})();