/*! For license information please see dash_renderer.min.js.LICENSE.txt */
!function(){var t={56:function(t,e,r){"use strict";t.exports=function(t){var e=r.nc;e&&t.setAttribute("nonce",e)}},63:function(t,e,r){"use strict";var n=r(609),o="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},i=n.useState,u=n.useEffect,a=n.useLayoutEffect,c=n.useDebugValue;function s(t){var e=t.getSnapshot;t=t.value;try{var r=e();return!o(t,r)}catch(t){return!0}}var f="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(t,e){return e()}:function(t,e){var r=e(),n=i({inst:{value:r,getSnapshot:e}}),o=n[0].inst,f=n[1];return a((function(){o.value=r,o.getSnapshot=e,s(o)&&f({inst:o})}),[t,r,e]),u((function(){return s(o)&&f({inst:o}),t((function(){s(o)&&f({inst:o})}))}),[t]),c(r),r};e.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:f},72:function(t){"use strict";var e=[];function r(t){for(var r=-1,n=0;n<e.length;n++)if(e[n].identifier===t){r=n;break}return r}function n(t,n){for(var i={},u=[],a=0;a<t.length;a++){var c=t[a],s=n.base?c[0]+n.base:c[0],f=i[s]||0,l="".concat(s," ").concat(f);i[s]=f+1;var p=r(l),y={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==p)e[p].references++,e[p].updater(y);else{var d=o(y,n);n.byIndex=a,e.splice(a,0,{identifier:l,updater:d,references:1})}u.push(l)}return u}function o(t,e){var r=e.domAPI(e);return r.update(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap&&e.supports===t.supports&&e.layer===t.layer)return;r.update(t=e)}else r.remove()}}t.exports=function(t,o){var i=n(t=t||[],o=o||{});return function(t){t=t||[];for(var u=0;u<i.length;u++){var a=r(i[u]);e[a].references--}for(var c=n(t,o),s=0;s<i.length;s++){var f=r(i[s]);0===e[f].references&&(e[f].updater(),e.splice(f,1))}i=c}}},113:function(t){"use strict";t.exports=function(t,e){if(e.styleSheet)e.styleSheet.cssText=t;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(t))}}},131:function(t,e){function r(t,e,r,n){var i={};return function(u){if(!i[u]){var a={},c=[],s=[];for(s.push({node:u,processed:!1});s.length>0;){var f=s[s.length-1],l=f.processed,p=f.node;if(l)s.pop(),c.pop(),a[p]=!1,i[p]=!0,e&&0!==t[p].length||r.push(p);else{if(i[p]){s.pop();continue}if(a[p]){if(n){s.pop();continue}throw c.push(p),new o(c)}a[p]=!0,c.push(p);for(var y=t[p],d=y.length-1;d>=0;d--)s.push({node:y[d],processed:!1});f.processed=!0}}}}}var n=e.w=function(t){this.nodes={},this.outgoingEdges={},this.incomingEdges={},this.circular=t&&!!t.circular};n.prototype={size:function(){return Object.keys(this.nodes).length},addNode:function(t,e){this.hasNode(t)||(this.nodes[t]=2===arguments.length?e:t,this.outgoingEdges[t]=[],this.incomingEdges[t]=[])},removeNode:function(t){this.hasNode(t)&&(delete this.nodes[t],delete this.outgoingEdges[t],delete this.incomingEdges[t],[this.incomingEdges,this.outgoingEdges].forEach((function(e){Object.keys(e).forEach((function(r){var n=e[r].indexOf(t);n>=0&&e[r].splice(n,1)}),this)})))},hasNode:function(t){return this.nodes.hasOwnProperty(t)},getNodeData:function(t){if(this.hasNode(t))return this.nodes[t];throw new Error("Node does not exist: "+t)},setNodeData:function(t,e){if(!this.hasNode(t))throw new Error("Node does not exist: "+t);this.nodes[t]=e},addDependency:function(t,e){if(!this.hasNode(t))throw new Error("Node does not exist: "+t);if(!this.hasNode(e))throw new Error("Node does not exist: "+e);return-1===this.outgoingEdges[t].indexOf(e)&&this.outgoingEdges[t].push(e),-1===this.incomingEdges[e].indexOf(t)&&this.incomingEdges[e].push(t),!0},removeDependency:function(t,e){var r;this.hasNode(t)&&(r=this.outgoingEdges[t].indexOf(e))>=0&&this.outgoingEdges[t].splice(r,1),this.hasNode(e)&&(r=this.incomingEdges[e].indexOf(t))>=0&&this.incomingEdges[e].splice(r,1)},clone:function(){var t=this,e=new n;return Object.keys(t.nodes).forEach((function(r){e.nodes[r]=t.nodes[r],e.outgoingEdges[r]=t.outgoingEdges[r].slice(0),e.incomingEdges[r]=t.incomingEdges[r].slice(0)})),e},directDependenciesOf:function(t){if(this.hasNode(t))return this.outgoingEdges[t].slice(0);throw new Error("Node does not exist: "+t)},directDependantsOf:function(t){if(this.hasNode(t))return this.incomingEdges[t].slice(0);throw new Error("Node does not exist: "+t)},dependenciesOf:function(t,e){if(this.hasNode(t)){var n=[];r(this.outgoingEdges,e,n,this.circular)(t);var o=n.indexOf(t);return o>=0&&n.splice(o,1),n}throw new Error("Node does not exist: "+t)},dependantsOf:function(t,e){if(this.hasNode(t)){var n=[];r(this.incomingEdges,e,n,this.circular)(t);var o=n.indexOf(t);return o>=0&&n.splice(o,1),n}throw new Error("Node does not exist: "+t)},overallOrder:function(t){var e=this,n=[],o=Object.keys(this.nodes);if(0===o.length)return n;if(!this.circular){var i=r(this.outgoingEdges,!1,[],this.circular);o.forEach((function(t){i(t)}))}var u=r(this.outgoingEdges,t,n,this.circular);return o.filter((function(t){return 0===e.incomingEdges[t].length})).forEach((function(t){u(t)})),this.circular&&o.filter((function(t){return-1===n.indexOf(t)})).forEach((function(t){u(t)})),n},entryNodes:function(){var t=this;return Object.keys(this.nodes).filter((function(e){return 0===t.incomingEdges[e].length}))}},n.prototype.directDependentsOf=n.prototype.directDependantsOf,n.prototype.dependentsOf=n.prototype.dependantsOf;var o=function(t){var e="Dependency Cycle Found: "+t.join(" -> "),r=new Error(e);return r.cyclePath=t,Object.setPrototypeOf(r,Object.getPrototypeOf(this)),Error.captureStackTrace&&Error.captureStackTrace(r,o),r};o.prototype=Object.create(Error.prototype,{constructor:{value:Error,enumerable:!1,writable:!0,configurable:!0}}),Object.setPrototypeOf(o,Error)},146:function(t,e,r){"use strict";var n=r(363),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},i={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},u={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},a={};function c(t){return n.isMemo(t)?u:a[t.$$typeof]||o}a[n.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},a[n.Memo]=u;var s=Object.defineProperty,f=Object.getOwnPropertyNames,l=Object.getOwnPropertySymbols,p=Object.getOwnPropertyDescriptor,y=Object.getPrototypeOf,d=Object.prototype;t.exports=function t(e,r,n){if("string"!=typeof r){if(d){var o=y(r);o&&o!==d&&t(e,o,n)}var u=f(r);l&&(u=u.concat(l(r)));for(var a=c(e),h=c(r),v=0;v<u.length;++v){var b=u[v];if(!(i[b]||n&&n[b]||h&&h[b]||a&&a[b])){var m=p(r,b);try{s(e,b,m)}catch(t){}}}}return e}},217:function(t,e,r){"use strict";var n=r(601),o=r.n(n),i=r(314),u=r.n(i)()(o());u.push([t.id,"._dash-undo-redo {\n    position: fixed;\n    bottom: 30px;\n    left: 30px;\n    font-size: 20px;\n    text-align: center;\n    z-index: 9999;\n    background-color: rgba(255, 255, 255, 0.9);\n}\n._dash-undo-redo > div {\n    position: relative;\n}\n._dash-undo-redo-link {\n    color: #0074d9;\n    cursor: pointer;\n    margin-left: 10px;\n    margin-right: 10px;\n    display: inline-block;\n    opacity: 0.2;\n}\n._dash-undo-redo-link:hover {\n    opacity: 1;\n}\n._dash-undo-redo-link ._dash-icon-undo {\n    font-size: 20px;\n    transform: rotate(270deg);\n}\n._dash-undo-redo-link ._dash-icon-redo {\n    font-size: 20px;\n    transform: rotate(90deg);\n}\n._dash-undo-redo-link ._dash-undo-redo-label {\n    font-size: 15px;\n}\n",""]),e.A=u},221:function(t,e){"use strict";e.qg=function(t,e){var u=new r,a=t.length;if(a<2)return u;var c=(null==e?void 0:e.decode)||i,s=0;do{var f=t.indexOf("=",s);if(-1===f)break;var l=t.indexOf(";",s),p=-1===l?a:l;if(f>p)s=t.lastIndexOf(";",f-1)+1;else{var y=n(t,s,f),d=o(t,f,y),h=t.slice(y,d);if(void 0===u[h]){var v=n(t,f+1,p),b=o(t,p,v),m=c(t.slice(v,b));u[h]=m}s=p+1}}while(s<a);return u};Object.prototype.toString;var r=function(){var t=function(){};return t.prototype=Object.create(null),t}();function n(t,e,r){do{var n=t.charCodeAt(e);if(32!==n&&9!==n)return e}while(++e<r);return r}function o(t,e,r){for(;e>r;){var n=t.charCodeAt(--e);if(32!==n&&9!==n)return e+1}return r}function i(t){if(-1===t.indexOf("%"))return t;try{return decodeURIComponent(t)}catch(e){return t}}},242:function(t,e,r){"use strict";t.exports=r(940)},296:function(t,e,r){var n;window,t.exports=(n=r(609),function(t){var e={};function r(n){if(e[n])return e[n].exports;var o=e[n]={i:n,l:!1,exports:{}};return t[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)r.d(n,o,function(e){return t[e]}.bind(null,o));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=1)}([function(t,e){t.exports=n},function(t,e,r){"use strict";r.r(e),r.d(e,"asyncDecorator",(function(){return u})),r.d(e,"inheritAsyncDecorator",(function(){return a})),r.d(e,"isReady",(function(){return c})),r.d(e,"History",(function(){return l}));var n=r(0);function o(t,e,r,n,o,i,u){try{var a=t[i](u),c=a.value}catch(t){return void r(t)}a.done?e(c):Promise.resolve(c).then(n,o)}function i(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var u=t.apply(e,r);function a(t){o(u,n,i,a,c,"next",t)}function c(t){o(u,n,i,a,c,"throw",t)}a(void 0)}))}}var u=function(t,e){var r,o={isReady:new Promise((function(t){r=t})),get:Object(n.lazy)((function(){return Promise.resolve(e()).then((function(t){return setTimeout(i(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,r(!0);case 2:o.isReady=!0;case 3:case"end":return t.stop()}}),t)}))),0),t}))}))};return Object.defineProperty(t,"_dashprivate_isLazyComponentReady",{get:function(){return o.isReady}}),o.get},a=function(t,e){Object.defineProperty(t,"_dashprivate_isLazyComponentReady",{get:function(){return c(e)}})},c=function(t){return t&&t._dashprivate_isLazyComponentReady};function s(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}var f="_dashprivate_historychange",l=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}var e,r;return e=t,r=[{key:"dispatchChangeEvent",value:function(){window.dispatchEvent(new CustomEvent(f))}},{key:"onChange",value:function(t){return window.addEventListener(f,t),function(){return window.removeEventListener(f,t)}}}],null&&s(e.prototype,null),r&&s(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}()}]))},311:function(t){"use strict";t.exports=function(t,e,r,n,o,i,u,a){if(!t){var c;if(void 0===e)c=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var s=[r,n,o,i,u,a],f=0;(c=new Error(e.replace(/%s/g,(function(){return s[f++]})))).name="Invariant Violation"}throw c.framesToPop=1,c}}},314:function(t){"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var r="",n=void 0!==e[5];return e[4]&&(r+="@supports (".concat(e[4],") {")),e[2]&&(r+="@media ".concat(e[2]," {")),n&&(r+="@layer".concat(e[5].length>0?" ".concat(e[5]):""," {")),r+=t(e),n&&(r+="}"),e[2]&&(r+="}"),e[4]&&(r+="}"),r})).join("")},e.i=function(t,r,n,o,i){"string"==typeof t&&(t=[[null,t,void 0]]);var u={};if(n)for(var a=0;a<this.length;a++){var c=this[a][0];null!=c&&(u[c]=!0)}for(var s=0;s<t.length;s++){var f=[].concat(t[s]);n&&u[f[0]]||(void 0!==i&&(void 0===f[5]||(f[1]="@layer".concat(f[5].length>0?" ".concat(f[5]):""," {").concat(f[1],"}")),f[5]=i),r&&(f[2]?(f[1]="@media ".concat(f[2]," {").concat(f[1],"}"),f[2]=r):f[2]=r),o&&(f[4]?(f[1]="@supports (".concat(f[4],") {").concat(f[1],"}"),f[4]=o):f[4]="".concat(o)),e.push(f))}},e}},363:function(t,e,r){"use strict";t.exports=r(799)},365:function(t,e,r){"use strict";var n=r(886);t.exports=function(t){var e=typeof t;if("string"===e){var r=t;if(0==(t=+t)&&n(r))return!1}else if("number"!==e)return!1;return t-t<1}},516:function(t,e,r){"use strict";t.exports=r(712)},540:function(t){"use strict";t.exports=function(t){var e=document.createElement("style");return t.setAttributes(e,t.attributes),t.insert(e,t.options),e}},601:function(t){"use strict";t.exports=function(t){return t[1]}},609:function(t){"use strict";t.exports=window.React},659:function(t){"use strict";var e={};t.exports=function(t,r){var n=function(t){if(void 0===e[t]){var r=document.querySelector(t);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(t){r=null}e[t]=r}return e[t]}(t);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(r)}},712:function(t,e){"use strict";function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}var n=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),u=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),s=Symbol.for("react.context"),f=Symbol.for("react.server_context"),l=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),y=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),h=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),e.isContextConsumer=function(t){return function(t){if("object"===r(t)&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case i:case a:case u:case p:case y:return t;default:switch(t=t&&t.$$typeof){case f:case s:case l:case h:case d:case c:return t;default:return e}}case o:return e}}}(t)===s}},799:function(t,e){"use strict";var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,o=r?Symbol.for("react.portal"):60106,i=r?Symbol.for("react.fragment"):60107,u=r?Symbol.for("react.strict_mode"):60108,a=r?Symbol.for("react.profiler"):60114,c=r?Symbol.for("react.provider"):60109,s=r?Symbol.for("react.context"):60110,f=r?Symbol.for("react.async_mode"):60111,l=r?Symbol.for("react.concurrent_mode"):60111,p=r?Symbol.for("react.forward_ref"):60112,y=r?Symbol.for("react.suspense"):60113,d=r?Symbol.for("react.suspense_list"):60120,h=r?Symbol.for("react.memo"):60115,v=r?Symbol.for("react.lazy"):60116,b=r?Symbol.for("react.block"):60121,m=r?Symbol.for("react.fundamental"):60117,g=r?Symbol.for("react.responder"):60118,O=r?Symbol.for("react.scope"):60119;function w(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case f:case l:case i:case a:case u:case y:return t;default:switch(t=t&&t.$$typeof){case s:case p:case v:case h:case c:return t;default:return e}}case o:return e}}}function S(t){return w(t)===l}e.AsyncMode=f,e.ConcurrentMode=l,e.ContextConsumer=s,e.ContextProvider=c,e.Element=n,e.ForwardRef=p,e.Fragment=i,e.Lazy=v,e.Memo=h,e.Portal=o,e.Profiler=a,e.StrictMode=u,e.Suspense=y,e.isAsyncMode=function(t){return S(t)||w(t)===f},e.isConcurrentMode=S,e.isContextConsumer=function(t){return w(t)===s},e.isContextProvider=function(t){return w(t)===c},e.isElement=function(t){return"object"==typeof t&&null!==t&&t.$$typeof===n},e.isForwardRef=function(t){return w(t)===p},e.isFragment=function(t){return w(t)===i},e.isLazy=function(t){return w(t)===v},e.isMemo=function(t){return w(t)===h},e.isPortal=function(t){return w(t)===o},e.isProfiler=function(t){return w(t)===a},e.isStrictMode=function(t){return w(t)===u},e.isSuspense=function(t){return w(t)===y},e.isValidElementType=function(t){return"string"==typeof t||"function"==typeof t||t===i||t===l||t===a||t===u||t===y||t===d||"object"==typeof t&&null!==t&&(t.$$typeof===v||t.$$typeof===h||t.$$typeof===c||t.$$typeof===s||t.$$typeof===p||t.$$typeof===m||t.$$typeof===g||t.$$typeof===O||t.$$typeof===b)},e.typeOf=w},825:function(t){"use strict";t.exports=function(t){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var e=t.insertStyleElement(t);return{update:function(r){!function(t,e,r){var n="";r.supports&&(n+="@supports (".concat(r.supports,") {")),r.media&&(n+="@media ".concat(r.media," {"));var o=void 0!==r.layer;o&&(n+="@layer".concat(r.layer.length>0?" ".concat(r.layer):""," {")),n+=r.css,o&&(n+="}"),r.media&&(n+="}"),r.supports&&(n+="}");var i=r.sourceMap;i&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),e.styleTagTransform(n,t,e.options)}(e,t,r)},remove:function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(e)}}}},886:function(t){"use strict";t.exports=function(t){for(var e,r=t.length,n=0;n<r;n++)if(((e=t.charCodeAt(n))<9||e>13)&&32!==e&&133!==e&&160!==e&&5760!==e&&6158!==e&&(e<8192||e>8205)&&8232!==e&&8233!==e&&8239!==e&&8287!==e&&8288!==e&&12288!==e&&65279!==e)return!1;return!0}},888:function(t,e,r){"use strict";t.exports=r(63)},925:function(t){"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},940:function(t,e,r){"use strict";var n=r(609),o=r(888),i="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},u=o.useSyncExternalStore,a=n.useRef,c=n.useEffect,s=n.useMemo,f=n.useDebugValue;e.useSyncExternalStoreWithSelector=function(t,e,r,n,o){var l=a(null);if(null===l.current){var p={hasValue:!1,value:null};l.current=p}else p=l.current;l=s((function(){function t(t){if(!c){if(c=!0,u=t,t=n(t),void 0!==o&&p.hasValue){var e=p.value;if(o(e,t))return a=e}return a=t}if(e=a,i(u,t))return e;var r=n(t);return void 0!==o&&o(e,r)?e:(u=t,a=r)}var u,a,c=!1,s=void 0===r?null:r;return[function(){return t(e())},null===s?void 0:function(){return t(s())}]}),[e,r,n,o]);var y=u(t,l[0],l[1]);return c((function(){p.hasValue=!0,p.value=y}),[y]),f(y),y}}},e={};function r(n){var o=e[n];if(void 0!==o)return o.exports;var i=e[n]={id:n,exports:{}};return t[n](i,i.exports,r),i.exports}r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,{a:e}),e},r.d=function(t,e){for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.nc=void 0,function(){"use strict";var t="undefined"!=typeof globalThis&&globalThis||"undefined"!=typeof self&&self||void 0!==r.g&&r.g||{},e="URLSearchParams"in t,n="Symbol"in t&&"iterator"in Symbol,o="FileReader"in t&&"Blob"in t&&function(){try{return new Blob,!0}catch(t){return!1}}(),i="FormData"in t,u="ArrayBuffer"in t;if(u)var a=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],c=ArrayBuffer.isView||function(t){return t&&a.indexOf(Object.prototype.toString.call(t))>-1};function s(t){if("string"!=typeof t&&(t=String(t)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(t)||""===t)throw new TypeError('Invalid character in header field name: "'+t+'"');return t.toLowerCase()}function f(t){return"string"!=typeof t&&(t=String(t)),t}function l(t){var e={next:function(){var e=t.shift();return{done:void 0===e,value:e}}};return n&&(e[Symbol.iterator]=function(){return e}),e}function p(t){this.map={},t instanceof p?t.forEach((function(t,e){this.append(e,t)}),this):Array.isArray(t)?t.forEach((function(t){if(2!=t.length)throw new TypeError("Headers constructor: expected name/value pair to be length 2, found"+t.length);this.append(t[0],t[1])}),this):t&&Object.getOwnPropertyNames(t).forEach((function(e){this.append(e,t[e])}),this)}function y(t){if(!t._noBody)return t.bodyUsed?Promise.reject(new TypeError("Already read")):void(t.bodyUsed=!0)}function d(t){return new Promise((function(e,r){t.onload=function(){e(t.result)},t.onerror=function(){r(t.error)}}))}function h(t){var e=new FileReader,r=d(e);return e.readAsArrayBuffer(t),r}function v(t){if(t.slice)return t.slice(0);var e=new Uint8Array(t.byteLength);return e.set(new Uint8Array(t)),e.buffer}function b(){return this.bodyUsed=!1,this._initBody=function(t){var r;this.bodyUsed=this.bodyUsed,this._bodyInit=t,t?"string"==typeof t?this._bodyText=t:o&&Blob.prototype.isPrototypeOf(t)?this._bodyBlob=t:i&&FormData.prototype.isPrototypeOf(t)?this._bodyFormData=t:e&&URLSearchParams.prototype.isPrototypeOf(t)?this._bodyText=t.toString():u&&o&&(r=t)&&DataView.prototype.isPrototypeOf(r)?(this._bodyArrayBuffer=v(t.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):u&&(ArrayBuffer.prototype.isPrototypeOf(t)||c(t))?this._bodyArrayBuffer=v(t):this._bodyText=t=Object.prototype.toString.call(t):(this._noBody=!0,this._bodyText=""),this.headers.get("content-type")||("string"==typeof t?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):e&&URLSearchParams.prototype.isPrototypeOf(t)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},o&&(this.blob=function(){var t=y(this);if(t)return t;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))}),this.arrayBuffer=function(){if(this._bodyArrayBuffer)return y(this)||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer));if(o)return this.blob().then(h);throw new Error("could not read as ArrayBuffer")},this.text=function(){var t,e,r,n,o,i=y(this);if(i)return i;if(this._bodyBlob)return t=this._bodyBlob,r=d(e=new FileReader),o=(n=/charset=([A-Za-z0-9_-]+)/.exec(t.type))?n[1]:"utf-8",e.readAsText(t,o),r;if(this._bodyArrayBuffer)return Promise.resolve(function(t){for(var e=new Uint8Array(t),r=new Array(e.length),n=0;n<e.length;n++)r[n]=String.fromCharCode(e[n]);return r.join("")}(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},i&&(this.formData=function(){return this.text().then(O)}),this.json=function(){return this.text().then(JSON.parse)},this}p.prototype.append=function(t,e){t=s(t),e=f(e);var r=this.map[t];this.map[t]=r?r+", "+e:e},p.prototype.delete=function(t){delete this.map[s(t)]},p.prototype.get=function(t){return t=s(t),this.has(t)?this.map[t]:null},p.prototype.has=function(t){return this.map.hasOwnProperty(s(t))},p.prototype.set=function(t,e){this.map[s(t)]=f(e)},p.prototype.forEach=function(t,e){for(var r in this.map)this.map.hasOwnProperty(r)&&t.call(e,this.map[r],r,this)},p.prototype.keys=function(){var t=[];return this.forEach((function(e,r){t.push(r)})),l(t)},p.prototype.values=function(){var t=[];return this.forEach((function(e){t.push(e)})),l(t)},p.prototype.entries=function(){var t=[];return this.forEach((function(e,r){t.push([r,e])})),l(t)},n&&(p.prototype[Symbol.iterator]=p.prototype.entries);var m=["CONNECT","DELETE","GET","HEAD","OPTIONS","PATCH","POST","PUT","TRACE"];function g(e,r){if(!(this instanceof g))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');var n,o,i=(r=r||{}).body;if(e instanceof g){if(e.bodyUsed)throw new TypeError("Already read");this.url=e.url,this.credentials=e.credentials,r.headers||(this.headers=new p(e.headers)),this.method=e.method,this.mode=e.mode,this.signal=e.signal,i||null==e._bodyInit||(i=e._bodyInit,e.bodyUsed=!0)}else this.url=String(e);if(this.credentials=r.credentials||this.credentials||"same-origin",!r.headers&&this.headers||(this.headers=new p(r.headers)),this.method=(o=(n=r.method||this.method||"GET").toUpperCase(),m.indexOf(o)>-1?o:n),this.mode=r.mode||this.mode||null,this.signal=r.signal||this.signal||function(){if("AbortController"in t)return(new AbortController).signal}(),this.referrer=null,("GET"===this.method||"HEAD"===this.method)&&i)throw new TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(i),!("GET"!==this.method&&"HEAD"!==this.method||"no-store"!==r.cache&&"no-cache"!==r.cache)){var u=/([?&])_=[^&]*/;u.test(this.url)?this.url=this.url.replace(u,"$1_="+(new Date).getTime()):this.url+=(/\?/.test(this.url)?"&":"?")+"_="+(new Date).getTime()}}function O(t){var e=new FormData;return t.trim().split("&").forEach((function(t){if(t){var r=t.split("="),n=r.shift().replace(/\+/g," "),o=r.join("=").replace(/\+/g," ");e.append(decodeURIComponent(n),decodeURIComponent(o))}})),e}function w(t,e){if(!(this instanceof w))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');if(e||(e={}),this.type="default",this.status=void 0===e.status?200:e.status,this.status<200||this.status>599)throw new RangeError("Failed to construct 'Response': The status provided (0) is outside the range [200, 599].");this.ok=this.status>=200&&this.status<300,this.statusText=void 0===e.statusText?"":""+e.statusText,this.headers=new p(e.headers),this.url=e.url||"",this._initBody(t)}g.prototype.clone=function(){return new g(this,{body:this._bodyInit})},b.call(g.prototype),b.call(w.prototype),w.prototype.clone=function(){return new w(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new p(this.headers),url:this.url})},w.error=function(){var t=new w(null,{status:200,statusText:""});return t.ok=!1,t.status=0,t.type="error",t};var S=[301,302,303,307,308];w.redirect=function(t,e){if(-1===S.indexOf(e))throw new RangeError("Invalid status code");return new w(null,{status:e,headers:{location:t}})};var j=t.DOMException;try{new j}catch(t){(j=function(t,e){this.message=t,this.name=e;var r=Error(t);this.stack=r.stack}).prototype=Object.create(Error.prototype),j.prototype.constructor=j}function _(e,r){return new Promise((function(n,i){var a=new g(e,r);if(a.signal&&a.signal.aborted)return i(new j("Aborted","AbortError"));var c=new XMLHttpRequest;function l(){c.abort()}if(c.onload=function(){var t,e,r={statusText:c.statusText,headers:(t=c.getAllResponseHeaders()||"",e=new p,t.replace(/\r?\n[\t ]+/g," ").split("\r").map((function(t){return 0===t.indexOf("\n")?t.substr(1,t.length):t})).forEach((function(t){var r=t.split(":"),n=r.shift().trim();if(n){var o=r.join(":").trim();try{e.append(n,o)}catch(t){console.warn("Response "+t.message)}}})),e)};0===a.url.indexOf("file://")&&(c.status<200||c.status>599)?r.status=200:r.status=c.status,r.url="responseURL"in c?c.responseURL:r.headers.get("X-Request-URL");var o="response"in c?c.response:c.responseText;setTimeout((function(){n(new w(o,r))}),0)},c.onerror=function(){setTimeout((function(){i(new TypeError("Network request failed"))}),0)},c.ontimeout=function(){setTimeout((function(){i(new TypeError("Network request timed out"))}),0)},c.onabort=function(){setTimeout((function(){i(new j("Aborted","AbortError"))}),0)},c.open(a.method,function(e){try{return""===e&&t.location.href?t.location.href:e}catch(t){return e}}(a.url),!0),"include"===a.credentials?c.withCredentials=!0:"omit"===a.credentials&&(c.withCredentials=!1),"responseType"in c&&(o?c.responseType="blob":u&&(c.responseType="arraybuffer")),r&&"object"==typeof r.headers&&!(r.headers instanceof p||t.Headers&&r.headers instanceof t.Headers)){var y=[];Object.getOwnPropertyNames(r.headers).forEach((function(t){y.push(s(t)),c.setRequestHeader(t,f(r.headers[t]))})),a.headers.forEach((function(t,e){-1===y.indexOf(e)&&c.setRequestHeader(e,t)}))}else a.headers.forEach((function(t,e){c.setRequestHeader(e,t)}));a.signal&&(a.signal.addEventListener("abort",l),c.onreadystatechange=function(){4===c.readyState&&a.signal.removeEventListener("abort",l)}),c.send(void 0===a._bodyInit?null:a._bodyInit)}))}_.polyfill=!0,t.fetch||(t.fetch=_,t.Headers=p,t.Request=g,t.Response=w)}(),function(){"use strict";var t=r(609),e=r.n(t),n=window.ReactDOM,o=r.n(n),i=window.PropTypes,u=r.n(i),a=r(888),c=r(242),s=function(t){t()},f=function(){return s},l=(0,t.createContext)(null);function p(){return(0,t.useContext)(l)}var y=function(){throw new Error("uSES not initialized!")},d=y,h=function(t,e){return t===e};function v(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:l,r=e===l?p:function(){return(0,t.useContext)(e)};return function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:h,o=r(),i=o.store,u=o.subscription,a=o.getServerState,c=d(u.addNestedSub,i.getState,a||i.getState,e,n);return(0,t.useDebugValue)(c),c}}var b=v();function m(){return m=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},m.apply(null,arguments)}function g(t,e){if(null==t)return{};var r={};for(var n in t)if({}.hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}var O=r(146),w=r.n(O),S=r(516),j=["initMapStateToProps","initMapDispatchToProps","initMergeProps"];function _(t,e,r,n,o){var i,u,a,c,s,f=o.areStatesEqual,l=o.areOwnPropsEqual,p=o.areStatePropsEqual,y=!1;return function(o,d){return y?function(o,y){var d,h,v=!l(y,u),b=!f(o,i,y,u);return i=o,u=y,v&&b?(a=t(i,u),e.dependsOnOwnProps&&(c=e(n,u)),s=r(a,c,u)):v?(t.dependsOnOwnProps&&(a=t(i,u)),e.dependsOnOwnProps&&(c=e(n,u)),s=r(a,c,u)):b?(d=t(i,u),h=!p(d,a),a=d,h&&(s=r(a,c,u)),s):s}(o,d):(a=t(i=o,u=d),c=e(n,u),s=r(a,c,u),y=!0,s)}}function E(t){return function(e){var r=t(e);function n(){return r}return n.dependsOnOwnProps=!1,n}}function P(t){return t.dependsOnOwnProps?Boolean(t.dependsOnOwnProps):1!==t.length}function A(t,e){return function(e,r){r.displayName;var n=function(t,e){return n.dependsOnOwnProps?n.mapToProps(t,e):n.mapToProps(t,void 0)};return n.dependsOnOwnProps=!0,n.mapToProps=function(e,r){n.mapToProps=t,n.dependsOnOwnProps=P(t);var o=n(e,r);return"function"==typeof o&&(n.mapToProps=o,n.dependsOnOwnProps=P(o),o=n(e,r)),o},n}}function k(t){return k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},k(t)}function x(t,e){return function(r,n){throw new Error("Invalid value of type ".concat(k(t)," for ").concat(e," argument when connecting component ").concat(n.wrappedComponentName,"."))}}function T(t){return T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},T(t)}function I(t,e,r){return m({},r,t,e)}var C={notify(){},get:function(){return[]}};function R(t,e){var r,n=C;function o(){u.onStateChange&&u.onStateChange()}function i(){r||(r=e?e.addNestedSub(o):t.subscribe(o),n=function(){var t=f(),e=null,r=null;return{clear(){e=null,r=null},notify(){t((function(){for(var t=e;t;)t.callback(),t=t.next}))},get(){for(var t=[],r=e;r;)t.push(r),r=r.next;return t},subscribe(t){var n=!0,o=r={callback:t,next:null,prev:r};return o.prev?o.prev.next=o:e=o,function(){n&&null!==e&&(n=!1,o.next?o.next.prev=o.prev:r=o.prev,o.prev?o.prev.next=o.next:e=o.next)}}}}())}var u={addNestedSub:function(t){return i(),n.subscribe(t)},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:o,isSubscribed:function(){return Boolean(r)},trySubscribe:i,tryUnsubscribe:function(){r&&(r(),r=void 0,n.clear(),n=C)},getListeners:function(){return n}};return u}var D="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?t.useLayoutEffect:t.useEffect;function N(t){return N="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},N(t)}function M(t,e){return t===e?0!==t||0!==e||1/t==1/e:t!=t&&e!=e}function U(t,e){if(M(t,e))return!0;if("object"!==N(t)||null===t||"object"!==N(e)||null===e)return!1;var r=Object.keys(t),n=Object.keys(e);if(r.length!==n.length)return!1;for(var o=0;o<r.length;o++)if(!Object.prototype.hasOwnProperty.call(e,r[o])||!M(t[r[o]],e[r[o]]))return!1;return!0}function L(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,u,a=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(a.push(n.value),a.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(u=r.return(),Object(u)!==u))return}finally{if(s)throw o}}return a}}(t,e)||q(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function q(t,e){if(t){if("string"==typeof t)return B(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?B(t,e):void 0}}function B(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var G=["reactReduxForwardedRef"],F=y,H=[null,null];function $(t,e,r,n,o,i){t.current=n,r.current=!1,o.current&&(o.current=null,i())}function J(t,e){return t===e}var z=function(r,n,o){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},u=(i.pure,i.areStatesEqual),a=void 0===u?J:u,c=i.areOwnPropsEqual,s=void 0===c?U:c,f=i.areStatePropsEqual,p=void 0===f?U:f,y=i.areMergedPropsEqual,d=void 0===y?U:y,h=i.forwardRef,v=void 0!==h&&h,b=i.context,O=void 0===b?l:b,P=function(t){return t?"function"==typeof t?A(t):x(t,"mapStateToProps"):E((function(){return{}}))}(r),k=function(t){return t&&"object"===T(t)?E((function(e){return function(t,e){var r={},n=function(){var n=t[o];"function"==typeof n&&(r[o]=function(){return e(n.apply(void 0,arguments))})};for(var o in t)n();return r}(t,e)})):t?"function"==typeof t?A(t):x(t,"mapDispatchToProps"):E((function(t){return{dispatch:t}}))}(n),C=function(t){return t?"function"==typeof t?function(t){return function(e,r){r.displayName;var n,o=r.areMergedPropsEqual,i=!1;return function(e,r,u){var a=t(e,r,u);return i?o(a,n)||(n=a):(i=!0,n=a),n}}}(t):x(t,"mergeProps"):function(){return I}}(o),N=Boolean(r);return function(r){var n=r.displayName||r.name||"Component",o="Connect(".concat(n,")"),i={shouldHandleStateChanges:N,displayName:o,wrappedComponentName:n,WrappedComponent:r,initMapStateToProps:P,initMapDispatchToProps:k,initMergeProps:C,areStatesEqual:a,areStatePropsEqual:p,areOwnPropsEqual:s,areMergedPropsEqual:d};function u(n){var o=(0,t.useMemo)((function(){var t=n.reactReduxForwardedRef,e=g(n,G);return[n.context,t,e]}),[n]),u=L(o,3),a=u[0],c=u[1],s=u[2],f=(0,t.useMemo)((function(){return a&&a.Consumer&&(0,S.isContextConsumer)(e().createElement(a.Consumer,null))?a:O}),[a,O]),l=(0,t.useContext)(f),p=Boolean(n.store)&&Boolean(n.store.getState)&&Boolean(n.store.dispatch),y=Boolean(l)&&Boolean(l.store),d=p?n.store:l.store,h=y?l.getServerState:d.getState,v=(0,t.useMemo)((function(){return function(t,e){var r=e.initMapStateToProps,n=e.initMapDispatchToProps,o=e.initMergeProps,i=g(e,j);return _(r(t,i),n(t,i),o(t,i),t,i)}(d.dispatch,i)}),[d]),b=(0,t.useMemo)((function(){if(!N)return H;var t=R(d,p?void 0:l.subscription),e=t.notifyNestedSubs.bind(t);return[t,e]}),[d,p,l]),w=L(b,2),E=w[0],P=w[1],A=(0,t.useMemo)((function(){return p?l:m({},l,{subscription:E})}),[p,l,E]),k=(0,t.useRef)(),x=(0,t.useRef)(s),T=(0,t.useRef)(),I=(0,t.useRef)(!1),C=((0,t.useRef)(!1),(0,t.useRef)(!1)),M=(0,t.useRef)();D((function(){return C.current=!0,function(){C.current=!1}}),[]);var U,J,z,W=(0,t.useMemo)((function(){return function(){return T.current&&s===x.current?T.current:v(d.getState(),s)}}),[d,s]),V=(0,t.useMemo)((function(){return function(t){return E?function(t,e,r,n,o,i,u,a,c,s,f){if(!t)return function(){};var l=!1,p=null,y=function(){if(!l&&a.current){var t,r,y=e.getState();try{t=n(y,o.current)}catch(t){r=t,p=t}r||(p=null),t===i.current?u.current||s():(i.current=t,c.current=t,u.current=!0,f())}};return r.onStateChange=y,r.trySubscribe(),y(),function(){if(l=!0,r.tryUnsubscribe(),r.onStateChange=null,p)throw p}}(N,d,E,v,x,k,I,C,T,P,t):function(){}}}),[E]);U=$,J=[x,k,I,s,T,P],D((function(){return U.apply(void 0,function(t){if(Array.isArray(t))return B(t)}(t=J)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||q(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}());var t}),undefined);try{z=F(V,W,h?function(){return v(h(),s)}:W)}catch(t){throw M.current&&(t.message+="\nThe error may be correlated with this previous error:\n".concat(M.current.stack,"\n\n")),t}D((function(){M.current=void 0,T.current=void 0,k.current=z}));var K=(0,t.useMemo)((function(){return e().createElement(r,m({},z,{ref:c}))}),[c,r,z]);return(0,t.useMemo)((function(){return N?e().createElement(f.Provider,{value:A},K):K}),[f,K,A])}var c=e().memo(u);if(c.WrappedComponent=r,c.displayName=u.displayName=o,v){var f=e().forwardRef((function(t,r){return e().createElement(c,m({},t,{reactReduxForwardedRef:r}))}));return f.displayName=o,f.WrappedComponent=r,w()(f,r)}return w()(c,r)}},W=function(r){var n=r.store,o=r.context,i=r.children,u=r.serverState,a=(0,t.useMemo)((function(){var t=R(n);return{store:n,subscription:t,getServerState:u?function(){return u}:void 0}}),[n,u]),c=(0,t.useMemo)((function(){return n.getState()}),[n]);D((function(){var t=a.subscription;return t.onStateChange=t.notifyNestedSubs,t.trySubscribe(),c!==n.getState()&&t.notifyNestedSubs(),function(){t.tryUnsubscribe(),t.onStateChange=void 0}}),[a,c]);var s=o||l;return e().createElement(s.Provider,{value:a},i)};function V(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:l,r=e===l?p:function(){return(0,t.useContext)(e)};return function(){return r().store}}var K=V();function Y(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:l,e=t===l?K:V(t);return function(){return e().dispatch}}var Q,X,Z=Y();function tt(t,e){switch(t){case 0:return function(){return e.apply(this,arguments)};case 1:return function(t){return e.apply(this,arguments)};case 2:return function(t,r){return e.apply(this,arguments)};case 3:return function(t,r,n){return e.apply(this,arguments)};case 4:return function(t,r,n,o){return e.apply(this,arguments)};case 5:return function(t,r,n,o,i){return e.apply(this,arguments)};case 6:return function(t,r,n,o,i,u){return e.apply(this,arguments)};case 7:return function(t,r,n,o,i,u,a){return e.apply(this,arguments)};case 8:return function(t,r,n,o,i,u,a,c){return e.apply(this,arguments)};case 9:return function(t,r,n,o,i,u,a,c,s){return e.apply(this,arguments)};case 10:return function(t,r,n,o,i,u,a,c,s,f){return e.apply(this,arguments)};default:throw new Error("First argument to _arity must be a non-negative integer no greater than ten")}}function et(t){return et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},et(t)}function rt(t){return null!=t&&"object"===et(t)&&!0===t["@@functional/placeholder"]}function nt(t){return function e(r){return 0===arguments.length||rt(r)?e:t.apply(this,arguments)}}Q=c.useSyncExternalStoreWithSelector,d=Q,function(t){F=t}(a.useSyncExternalStore),X=n.unstable_batchedUpdates,s=X;var ot=nt((function(t){var e,r=!1;return tt(t.length,(function(){return r?e:(r=!0,e=t.apply(this,arguments))}))})),it=ot;function ut(t){return ut="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ut(t)}function at(t,e,r){return(e=function(t){var e=function(t){if("object"!=ut(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=ut(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==ut(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ct(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function st(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ct(Object(r),!0).forEach((function(e){at(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ct(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function ft(t){return"Minified Redux error #"+t+"; visit https://redux.js.org/Errors?code="+t+" for the full message or use the non-minified dev environment for full errors. "}var lt="function"==typeof Symbol&&Symbol.observable||"@@observable",pt=function(){return Math.random().toString(36).substring(7).split("").join(".")},yt={INIT:"@@redux/INIT"+pt(),REPLACE:"@@redux/REPLACE"+pt(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+pt()}};function dt(t,e,r){var n;if("function"==typeof e&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw new Error(ft(0));if("function"==typeof e&&void 0===r&&(r=e,e=void 0),void 0!==r){if("function"!=typeof r)throw new Error(ft(1));return r(dt)(t,e)}if("function"!=typeof t)throw new Error(ft(2));var o=t,i=e,u=[],a=u,c=!1;function s(){a===u&&(a=u.slice())}function f(){if(c)throw new Error(ft(3));return i}function l(t){if("function"!=typeof t)throw new Error(ft(4));if(c)throw new Error(ft(5));var e=!0;return s(),a.push(t),function(){if(e){if(c)throw new Error(ft(6));e=!1,s();var r=a.indexOf(t);a.splice(r,1),u=null}}}function p(t){if(!function(t){if("object"!=typeof t||null===t)return!1;for(var e=t;null!==Object.getPrototypeOf(e);)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e}(t))throw new Error(ft(7));if(void 0===t.type)throw new Error(ft(8));if(c)throw new Error(ft(9));try{c=!0,i=o(i,t)}finally{c=!1}for(var e=u=a,r=0;r<e.length;r++)(0,e[r])();return t}return p({type:yt.INIT}),(n={dispatch:p,subscribe:l,getState:f,replaceReducer:function(t){if("function"!=typeof t)throw new Error(ft(10));o=t,p({type:yt.REPLACE})}})[lt]=function(){var t,e=l;return t={subscribe:function(t){if("object"!=typeof t||null===t)throw new Error(ft(11));function r(){t.next&&t.next(f())}return r(),{unsubscribe:e(r)}}},t[lt]=function(){return this},t},n}function ht(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return 0===e.length?function(t){return t}:1===e.length?e[0]:e.reduce((function(t,e){return function(){return t(e.apply(void 0,arguments))}}))}function vt(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return function(t){return function(){var r=t.apply(void 0,arguments),n=function(){throw new Error(ft(15))},o={getState:r.getState,dispatch:function(){return n.apply(void 0,arguments)}},i=e.map((function(t){return t(o)}));return n=ht.apply(void 0,i)(r.dispatch),st(st({},r),{},{dispatch:n})}}}function bt(t){return function(e){var r=e.dispatch,n=e.getState;return function(e){return function(o){return"function"==typeof o?o(r,n,t):e(o)}}}}var mt=bt();mt.withExtraArgument=bt;var gt=mt;function Ot(t){return function e(r,n){switch(arguments.length){case 0:return e;case 1:return rt(r)?e:nt((function(e){return t(r,e)}));default:return rt(r)&&rt(n)?e:rt(r)?nt((function(e){return t(e,n)})):rt(n)?nt((function(e){return t(r,e)})):t(r,n)}}}function wt(t){for(var e,r=[];!(e=t.next()).done;)r.push(e.value);return r}function St(t,e,r){for(var n=0,o=r.length;n<o;){if(t(e,r[n]))return!0;n+=1}return!1}function jt(t,e){return Object.prototype.hasOwnProperty.call(e,t)}var _t="function"==typeof Object.is?Object.is:function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e},Et=Object.prototype.toString,Pt=function(){return"[object Arguments]"===Et.call(arguments)?function(t){return"[object Arguments]"===Et.call(t)}:function(t){return jt("callee",t)}}(),At=Pt,kt=!{toString:null}.propertyIsEnumerable("toString"),xt=["constructor","valueOf","isPrototypeOf","toString","propertyIsEnumerable","hasOwnProperty","toLocaleString"],Tt=function(){return arguments.propertyIsEnumerable("length")}(),It=function(t,e){for(var r=0;r<t.length;){if(t[r]===e)return!0;r+=1}return!1},Ct="function"!=typeof Object.keys||Tt?nt((function(t){if(Object(t)!==t)return[];var e,r,n=[],o=Tt&&At(t);for(e in t)!jt(e,t)||o&&"length"===e||(n[n.length]=e);if(kt)for(r=xt.length-1;r>=0;)jt(e=xt[r],t)&&!It(n,e)&&(n[n.length]=e),r-=1;return n})):nt((function(t){return Object(t)!==t?[]:Object.keys(t)})),Rt=nt((function(t){return null===t?"Null":void 0===t?"Undefined":Object.prototype.toString.call(t).slice(8,-1)}));function Dt(t){return Dt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dt(t)}function Nt(t,e,r,n){var o=wt(t);function i(t,e){return Mt(t,e,r.slice(),n.slice())}return!St((function(t,e){return!St(i,e,t)}),wt(e),o)}function Mt(t,e,r,n){if(_t(t,e))return!0;var o,i,u=Rt(t);if(u!==Rt(e))return!1;if("function"==typeof t["fantasy-land/equals"]||"function"==typeof e["fantasy-land/equals"])return"function"==typeof t["fantasy-land/equals"]&&t["fantasy-land/equals"](e)&&"function"==typeof e["fantasy-land/equals"]&&e["fantasy-land/equals"](t);if("function"==typeof t.equals||"function"==typeof e.equals)return"function"==typeof t.equals&&t.equals(e)&&"function"==typeof e.equals&&e.equals(t);switch(u){case"Arguments":case"Array":case"Object":if("function"==typeof t.constructor&&"Promise"===(o=t.constructor,null==(i=String(o).match(/^function (\w*)/))?"":i[1]))return t===e;break;case"Boolean":case"Number":case"String":if(Dt(t)!==Dt(e)||!_t(t.valueOf(),e.valueOf()))return!1;break;case"Date":if(!_t(t.valueOf(),e.valueOf()))return!1;break;case"Error":return t.name===e.name&&t.message===e.message;case"RegExp":if(t.source!==e.source||t.global!==e.global||t.ignoreCase!==e.ignoreCase||t.multiline!==e.multiline||t.sticky!==e.sticky||t.unicode!==e.unicode)return!1}for(var a=r.length-1;a>=0;){if(r[a]===t)return n[a]===e;a-=1}switch(u){case"Map":return t.size===e.size&&Nt(t.entries(),e.entries(),r.concat([t]),n.concat([e]));case"Set":return t.size===e.size&&Nt(t.values(),e.values(),r.concat([t]),n.concat([e]));case"Arguments":case"Array":case"Object":case"Boolean":case"Number":case"String":case"Date":case"Error":case"RegExp":case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"ArrayBuffer":break;default:return!1}var c=Ct(t);if(c.length!==Ct(e).length)return!1;var s=r.concat([t]),f=n.concat([e]);for(a=c.length-1;a>=0;){var l=c[a];if(!jt(l,e)||!Mt(e[l],t[l],s,f))return!1;a-=1}return!0}var Ut=Ot((function(t,e){return Mt(t,e,[],[])}));function Lt(t){return Lt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Lt(t)}function qt(t,e,r){var n,o;if("function"==typeof t.indexOf)switch(Lt(e)){case"number":if(0===e){for(n=1/e;r<t.length;){if(0===(o=t[r])&&1/o===n)return r;r+=1}return-1}if(e!=e){for(;r<t.length;){if("number"==typeof(o=t[r])&&o!=o)return r;r+=1}return-1}return t.indexOf(e,r);case"string":case"boolean":case"function":case"undefined":return t.indexOf(e,r);case"object":if(null===e)return t.indexOf(e,r)}for(;r<t.length;){if(Ut(t[r],e))return r;r+=1}return-1}function Bt(t,e){return qt(e,t,0)>=0}var Gt=Ot(Bt);function Ft(t){return function e(r,n,o){switch(arguments.length){case 0:return e;case 1:return rt(r)?e:Ot((function(e,n){return t(r,e,n)}));case 2:return rt(r)&&rt(n)?e:rt(r)?Ot((function(e,r){return t(e,n,r)})):rt(n)?Ot((function(e,n){return t(r,e,n)})):nt((function(e){return t(r,n,e)}));default:return rt(r)&&rt(n)&&rt(o)?e:rt(r)&&rt(n)?Ot((function(e,r){return t(e,r,o)})):rt(r)&&rt(o)?Ot((function(e,r){return t(e,n,r)})):rt(n)&&rt(o)?Ot((function(e,n){return t(r,e,n)})):rt(r)?nt((function(e){return t(e,n,o)})):rt(n)?nt((function(e){return t(r,e,o)})):rt(o)?nt((function(e){return t(r,n,e)})):t(r,n,o)}}}var Ht=Number.isInteger||function(t){return(0|t)===t};function $t(t){return"[object String]"===Object.prototype.toString.call(t)}function Jt(t,e){var r=t<0?e.length+t:t;return $t(e)?e.charAt(r):e[r]}function zt(t,e){for(var r=e,n=0;n<t.length;n+=1){if(null==r)return;var o=t[n];r=Ht(o)?Jt(o,r):r[o]}return r}var Wt=Ot((function(t,e){return null==e||e!=e?t:e})),Vt=Ft((function(t,e,r){return Wt(t,zt(e,r))})),Kt=Array.isArray||function(t){return null!=t&&t.length>=0&&"[object Array]"===Object.prototype.toString.call(t)},Yt=nt((function(t){return null==t}));function Qt(t){return Qt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qt(t)}var Xt=Ft((function t(e,r,n){if(0===e.length)return r;var o=e[0];if(e.length>1){var i=!Yt(n)&&jt(o,n)&&"object"===Qt(n[o])?n[o]:Ht(e[1])?[]:{};r=t(Array.prototype.slice.call(e,1),r,i)}return function(t,e,r){if(Ht(t)&&Kt(r)){var n=[].concat(r);return n[t]=e,n}var o={};for(var i in r)o[i]=r[i];return o[t]=e,o}(o,r,n)})),Zt=Ft((function(t,e,r){return Xt([t],e,r)}));function te(t,e){return function(){var r=arguments.length;if(0===r)return e();var n=arguments[r-1];return Kt(n)||"function"!=typeof n[t]?e.apply(this,arguments):n[t].apply(n,Array.prototype.slice.call(arguments,0,r-1))}}var ee=Ot(te("forEach",(function(t,e){for(var r=e.length,n=0;n<r;)t(e[n]),n+=1;return e}))),re=ee,ne=Ot(zt);function oe(t){return"[object Object]"===Object.prototype.toString.call(t)}var ie=nt((function(t){return null!=t&&"function"==typeof t["fantasy-land/empty"]?t["fantasy-land/empty"]():null!=t&&null!=t.constructor&&"function"==typeof t.constructor["fantasy-land/empty"]?t.constructor["fantasy-land/empty"]():null!=t&&"function"==typeof t.empty?t.empty():null!=t&&null!=t.constructor&&"function"==typeof t.constructor.empty?t.constructor.empty():Kt(t)?[]:$t(t)?"":oe(t)?{}:At(t)?function(){return arguments}():(e=t,"[object Uint8ClampedArray]"===(r=Object.prototype.toString.call(e))||"[object Int8Array]"===r||"[object Uint8Array]"===r||"[object Int16Array]"===r||"[object Uint16Array]"===r||"[object Int32Array]"===r||"[object Uint32Array]"===r||"[object Float32Array]"===r||"[object Float64Array]"===r||"[object BigInt64Array]"===r||"[object BigUint64Array]"===r?t.constructor.from(""):void 0);var e,r})),ue=ie,ae=nt((function(t){return null!=t&&Ut(t,ue(t))})),ce=Ft((function(t,e,r){var n,o={};for(n in r=r||{},e=e||{})jt(n,e)&&(o[n]=jt(n,r)?t(n,e[n],r[n]):e[n]);for(n in r)jt(n,r)&&!jt(n,o)&&(o[n]=r[n]);return o})),se=ce,fe=Ft((function(t,e,r){return se((function(e,r,n){return t(r,n)}),e,r)})),le=fe,pe=Ot((function(t,e){if(null!=e)return Ht(t)?Jt(t,e):e[t]})),ye=Ot((function(t,e){return t.map((function(t){return pe(t,e)}))}));function de(t,e,r){for(var n=0,o=r.length;n<o;)e=t(e,r[n]),n+=1;return e}function he(t,e,r){return function(){if(0===arguments.length)return r();var n=arguments[arguments.length-1];if(!Kt(n)){for(var o=0;o<t.length;){if("function"==typeof n[t[o]])return n[t[o]].apply(n,Array.prototype.slice.call(arguments,0,-1));o+=1}if(function(t){return null!=t&&"function"==typeof t["@@transducer/step"]}(n))return e.apply(null,Array.prototype.slice.call(arguments,0,-1))(n)}return r.apply(this,arguments)}}function ve(t,e){for(var r=0,n=e.length,o=[];r<n;)t(e[r])&&(o[o.length]=e[r]),r+=1;return o}var be=function(){return this.xf["@@transducer/init"]()},me=function(t){return this.xf["@@transducer/result"](t)},ge=function(){function t(t,e){this.xf=e,this.f=t}return t.prototype["@@transducer/init"]=be,t.prototype["@@transducer/result"]=me,t.prototype["@@transducer/step"]=function(t,e){return this.f(e)?this.xf["@@transducer/step"](t,e):t},t}();function Oe(t){return function(e){return new ge(t,e)}}var we=Ot(he(["fantasy-land/filter","filter"],Oe,(function(t,e){return oe(e)?de((function(r,n){return t(e[n])&&(r[n]=e[n]),r}),{},Ct(e)):ve(t,e)})));function Se(t){return Se="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Se(t)}var je=nt((function(t){return!!Kt(t)||!!t&&"object"===Se(t)&&!$t(t)&&(0===t.length||t.length>0&&t.hasOwnProperty(0)&&t.hasOwnProperty(t.length-1))}));function _e(t){return function e(r){for(var n,o,i,u=[],a=0,c=r.length;a<c;){if(je(r[a]))for(i=0,o=(n=t?e(r[a]):r[a]).length;i<o;)u[u.length]=n[i],i+=1;else u[u.length]=r[a];a+=1}return u}}var Ee=nt(_e(!0));function Pe(t,e){for(var r=0,n=e.length,o=Array(n);r<n;)o[r]=t(e[r]),r+=1;return o}var Ae=function(){function t(t,e){this.xf=e,this.f=t}return t.prototype["@@transducer/init"]=be,t.prototype["@@transducer/result"]=me,t.prototype["@@transducer/step"]=function(t,e){return this.xf["@@transducer/step"](t,this.f(e))},t}(),ke=function(t){return function(e){return new Ae(t,e)}};function xe(t,e,r){return function(){for(var n=[],o=0,i=t,u=0,a=!1;u<e.length||o<arguments.length;){var c;u<e.length&&(!rt(e[u])||o>=arguments.length)?c=e[u]:(c=arguments[o],o+=1),n[u]=c,rt(c)?a=!0:i-=1,u+=1}return!a&&i<=0?r.apply(this,n):tt(Math.max(0,i),xe(t,n,r))}}var Te=Ot((function(t,e){return 1===t?nt(e):tt(t,xe(t,[],e))})),Ie=Te,Ce=Ot(he(["fantasy-land/map","map"],ke,(function(t,e){switch(Object.prototype.toString.call(e)){case"[object Function]":return Ie(e.length,(function(){return t.call(this,e.apply(this,arguments))}));case"[object Object]":return de((function(r,n){return r[n]=t(e[n]),r}),{},Ct(e));default:return Pe(t,e)}}))),Re=Ce,De="undefined"!=typeof Symbol?Symbol.iterator:"@@iterator";function Ne(t,e,r){return function(n,o,i){if(je(i))return t(n,o,i);if(null==i)return o;if("function"==typeof i["fantasy-land/reduce"])return e(n,o,i,"fantasy-land/reduce");if(null!=i[De])return r(n,o,i[De]());if("function"==typeof i.next)return r(n,o,i);if("function"==typeof i.reduce)return e(n,o,i,"reduce");throw new TypeError("reduce: list must be array or iterable")}}function Me(t,e,r){for(var n=0,o=r.length;n<o;){if((e=t["@@transducer/step"](e,r[n]))&&e["@@transducer/reduced"]){e=e["@@transducer/value"];break}n+=1}return t["@@transducer/result"](e)}var Ue=Ot((function(t,e){return tt(t.length,(function(){return t.apply(e,arguments)}))})),Le=Ue;function qe(t,e,r){for(var n=r.next();!n.done;){if((e=t["@@transducer/step"](e,n.value))&&e["@@transducer/reduced"]){e=e["@@transducer/value"];break}n=r.next()}return t["@@transducer/result"](e)}function Be(t,e,r,n){return t["@@transducer/result"](r[n](Le(t["@@transducer/step"],t),e))}var Ge=Ne(Me,Be,qe),Fe=function(){function t(t){this.f=t}return t.prototype["@@transducer/init"]=function(){throw new Error("init not implemented on XWrap")},t.prototype["@@transducer/result"]=function(t){return t},t.prototype["@@transducer/step"]=function(t,e){return this.f(t,e)},t}();function He(t){return new Fe(t)}var $e=Ft((function(t,e,r){return Ge("function"==typeof t?He(t):t,e,r)}));function Je(t){var e=Object.prototype.toString.call(t);return"[object Function]"===e||"[object AsyncFunction]"===e||"[object GeneratorFunction]"===e||"[object AsyncGeneratorFunction]"===e}function ze(t){return'"'+t.replace(/\\/g,"\\\\").replace(/[\b]/g,"\\b").replace(/\f/g,"\\f").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\t/g,"\\t").replace(/\v/g,"\\v").replace(/\0/g,"\\0").replace(/"/g,'\\"')+'"'}var We=function(t){return(t<10?"0":"")+t},Ve="function"==typeof Date.prototype.toISOString?function(t){return t.toISOString()}:function(t){return t.getUTCFullYear()+"-"+We(t.getUTCMonth()+1)+"-"+We(t.getUTCDate())+"T"+We(t.getUTCHours())+":"+We(t.getUTCMinutes())+":"+We(t.getUTCSeconds())+"."+(t.getUTCMilliseconds()/1e3).toFixed(3).slice(2,5)+"Z"},Ke=Ot((function(t,e){return we((r=t,function(){return!r.apply(this,arguments)}),e);var r})),Ye=Ke;function Qe(t){return Qe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qe(t)}function Xe(t,e){var r=function(r){var n=e.concat([t]);return Bt(r,n)?"<Circular>":Xe(r,n)},n=function(t,e){return Pe((function(e){return ze(e)+": "+r(t[e])}),e.slice().sort())};switch(Object.prototype.toString.call(t)){case"[object Arguments]":return"(function() { return arguments; }("+Pe(r,t).join(", ")+"))";case"[object Array]":return"["+Pe(r,t).concat(n(t,Ye((function(t){return/^\d+$/.test(t)}),Ct(t)))).join(", ")+"]";case"[object Boolean]":return"object"===Qe(t)?"new Boolean("+r(t.valueOf())+")":t.toString();case"[object Date]":return"new Date("+(isNaN(t.valueOf())?r(NaN):ze(Ve(t)))+")";case"[object Map]":return"new Map("+r(Array.from(t))+")";case"[object Null]":return"null";case"[object Number]":return"object"===Qe(t)?"new Number("+r(t.valueOf())+")":1/t==-1/0?"-0":t.toString(10);case"[object Set]":return"new Set("+r(Array.from(t).sort())+")";case"[object String]":return"object"===Qe(t)?"new String("+r(t.valueOf())+")":ze(t);case"[object Undefined]":return"undefined";default:if("function"==typeof t.toString){var o=t.toString();if("[object Object]"!==o)return o}return"{"+n(t,Ct(t)).join(", ")+"}"}}var Ze=nt((function(t){return Xe(t,[])})),tr=Ot((function(t,e){if(Kt(t)){if(Kt(e))return t.concat(e);throw new TypeError(Ze(e)+" is not an array")}if($t(t)){if($t(e))return t+e;throw new TypeError(Ze(e)+" is not a string")}if(null!=t&&Je(t["fantasy-land/concat"]))return t["fantasy-land/concat"](e);if(null!=t&&Je(t.concat))return t.concat(e);throw new TypeError(Ze(t)+' does not have a method named "concat" or "fantasy-land/concat"')}));function er(t){return t&&t["@@transducer/reduced"]?t:{"@@transducer/value":t,"@@transducer/reduced":!0}}var rr=function(){function t(t,e){this.xf=e,this.f=t,this.all=!0}return t.prototype["@@transducer/init"]=be,t.prototype["@@transducer/result"]=function(t){return this.all&&(t=this.xf["@@transducer/step"](t,!0)),this.xf["@@transducer/result"](t)},t.prototype["@@transducer/step"]=function(t,e){return this.f(e)||(this.all=!1,t=er(this.xf["@@transducer/step"](t,!1))),t},t}();function nr(t){return function(e){return new rr(t,e)}}var or=Ot(he(["all"],nr,(function(t,e){for(var r=0;r<e.length;){if(!t(e[r]))return!1;r+=1}return!0}))),ir=or;function ur(t){return ur="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ur(t)}var ar=Ot((function(t,e){if(t===e)return e;function r(t,e){if(t>e!=e>t)return e>t?e:t}var n=r(t,e);if(void 0!==n)return n;var o=r(ur(t),ur(e));if(void 0!==o)return o===ur(t)?t:e;var i=Ze(t),u=r(i,Ze(e));return void 0!==u&&u===i?t:e})),cr=Ot((function(t,e){return Re(pe(t),e)})),sr=Ot((function(t,e){return Ie($e(ar,0,cr("length",e)),(function(){var r=arguments,n=this;return t.apply(n,Pe((function(t){return t.apply(n,r)}),e))}))})),fr=sr,lr=nt((function(t){return fr((function(){return Array.prototype.slice.call(arguments,0)}),t)})),pr=lr([we,Ye]);function yr(t){return yr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},yr(t)}function dr(t,e,r){var n,o=yr(t);switch(o){case"string":case"number":return 0===t&&1/t==-1/0?!!r._items["-0"]||(e&&(r._items["-0"]=!0),!1):null!==r._nativeSet?e?(n=r._nativeSet.size,r._nativeSet.add(t),r._nativeSet.size===n):r._nativeSet.has(t):o in r._items?t in r._items[o]||(e&&(r._items[o][t]=!0),!1):(e&&(r._items[o]={},r._items[o][t]=!0),!1);case"boolean":if(o in r._items){var i=t?1:0;return!!r._items[o][i]||(e&&(r._items[o][i]=!0),!1)}return e&&(r._items[o]=t?[!1,!0]:[!0,!1]),!1;case"function":return null!==r._nativeSet?e?(n=r._nativeSet.size,r._nativeSet.add(t),r._nativeSet.size===n):r._nativeSet.has(t):o in r._items?!!Bt(t,r._items[o])||(e&&r._items[o].push(t),!1):(e&&(r._items[o]=[t]),!1);case"undefined":return!!r._items[o]||(e&&(r._items[o]=!0),!1);case"object":if(null===t)return!!r._items.null||(e&&(r._items.null=!0),!1);default:return(o=Object.prototype.toString.call(t))in r._items?!!Bt(t,r._items[o])||(e&&r._items[o].push(t),!1):(e&&(r._items[o]=[t]),!1)}}var hr=function(){function t(){this._nativeSet="function"==typeof Set?new Set:null,this._items={}}return t.prototype.add=function(t){return!dr(t,!0,this)},t.prototype.has=function(t){return dr(t,!1,this)},t}(),vr=Ot((function(t,e){for(var r=[],n=0,o=t.length,i=e.length,u=new hr,a=0;a<i;a+=1)u.add(e[a]);for(;n<o;)u.add(t[n])&&(r[r.length]=t[n]),n+=1;return r})),br=Ot((function(t,e){var r={};for(var n in e)t(e[n],n,e)&&(r[n]=e[n]);return r})),mr=Ot((function(t,e){for(var r=0,n=Math.min(t.length,e.length),o={};r<n;)o[t[r]]=e[r],r+=1;return o})),gr=mr,Or=r(131),wr=r(365),Sr=r.n(wr),jr=Ot((function(t,e){for(var r=Ct(e),n=0;n<r.length;){var o=r[n];t(e[o],o,e),n+=1}return e})),_r=jr,Er=Ot((function(t,e){for(var r=Math.min(t.length,e.length),n=Array(r),o=0;o<r;)n[o]=[t[o],e[o]],o+=1;return n}));function Pr(t){return t}var Ar=nt(Pr),kr=function(){function t(t,e){this.xf=e,this.f=t,this.set=new hr}return t.prototype["@@transducer/init"]=be,t.prototype["@@transducer/result"]=me,t.prototype["@@transducer/step"]=function(t,e){return this.set.add(this.f(e))?this.xf["@@transducer/step"](t,e):t},t}();function xr(t){return function(e){return new kr(t,e)}}var Tr=Ot(he([],xr,(function(t,e){for(var r,n,o=new hr,i=[],u=0;u<e.length;)r=t(n=e[u]),o.add(r)&&i.push(n),u+=1;return i}))),Ir=Tr(Ar),Cr=Ot((function(t,e){for(var r=new hr,n=0;n<t.length;n+=1)r.add(t[n]);return Ir(ve(r.has.bind(r),e))})),Rr=nt((function(t){for(var e=Ct(t),r=e.length,n=[],o=0;o<r;)n[o]=t[e[o]],o+=1;return n}));function Dr(t){return Dr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dr(t)}var Nr=Ot((function t(e,r){if(!oe(r)&&!Kt(r))return r;var n,o,i,u=r instanceof Array?[]:{};for(o in r)i=Dr(n=e[o]),u[o]="function"===i?n(r[o]):n&&"object"===i?t(n,r[o]):r[o];return u}));function Mr(t,e){var r;e=e||[];var n=(t=t||[]).length,o=e.length,i=[];for(r=0;r<n;)i[i.length]=t[r],r+=1;for(r=0;r<o;)i[i.length]=e[r],r+=1;return i}function Ur(t,e,r){for(var n=r.next();!n.done;)e=t(e,n.value),n=r.next();return e}function Lr(t,e,r,n){return r[n](t,e)}var qr=Ne(de,Lr,Ur),Br=Ot((function(t,e){return"function"==typeof e["fantasy-land/ap"]?e["fantasy-land/ap"](t):"function"==typeof t.ap?t.ap(e):"function"==typeof t?function(r){return t(r)(e(r))}:qr((function(t,r){return Mr(t,Re(r,e))}),[],t)})),Gr=function(){function t(t,e){this.xf=e,this.f=t,this.idx=-1,this.found=!1}return t.prototype["@@transducer/init"]=be,t.prototype["@@transducer/result"]=function(t){return this.found||(t=this.xf["@@transducer/step"](t,-1)),this.xf["@@transducer/result"](t)},t.prototype["@@transducer/step"]=function(t,e){return this.idx+=1,this.f(e)&&(this.found=!0,t=er(this.xf["@@transducer/step"](t,this.idx))),t},t}();function Fr(t){return function(e){return new Gr(t,e)}}var Hr=Ot(he([],Fr,(function(t,e){for(var r=0,n=e.length;r<n;){if(t(e[r]))return r;r+=1}return-1}))),$r=Hr,Jr="function"==typeof Object.assign?Object.assign:function(t){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),r=1,n=arguments.length;r<n;){var o=arguments[r];if(null!=o)for(var i in o)jt(i,o)&&(e[i]=o[i]);r+=1}return e},zr=Ot((function(t,e){return Jr({},t,e)})),Wr=function(){function t(t,e){this.xf=e,this.f=t,this.any=!1}return t.prototype["@@transducer/init"]=be,t.prototype["@@transducer/result"]=function(t){return this.any||(t=this.xf["@@transducer/step"](t,!1)),this.xf["@@transducer/result"](t)},t.prototype["@@transducer/step"]=function(t,e){return this.f(e)&&(this.any=!0,t=er(this.xf["@@transducer/step"](t,!0))),t},t}();function Vr(t){return function(e){return new Wr(t,e)}}var Kr=Ot(he(["any"],Vr,(function(t,e){for(var r=0;r<e.length;){if(t(e[r]))return!0;r+=1}return!1}))),Yr=Kr,Qr=function(){function t(t,e){this.xf=e,this.n=t,this.i=0}return t.prototype["@@transducer/init"]=be,t.prototype["@@transducer/result"]=me,t.prototype["@@transducer/step"]=function(t,e){this.i+=1;var r=0===this.n?t:this.xf["@@transducer/step"](t,e);return this.n>=0&&this.i>=this.n?er(r):r},t}();function Xr(t){return function(e){return new Qr(t,e)}}var Zr=Ft(te("slice",(function(t,e,r){return Array.prototype.slice.call(r,t,e)}))),tn=Ot(he(["take"],Xr,(function(t,e){return Zr(0,t<0?1/0:t,e)}))),en=Ot((function(t,e){return Ut(tn(t.length,e),t)})),rn=Ot((function(t,e){return"function"!=typeof e.indexOf||Kt(e)?qt(e,t,0):e.indexOf(t)})),nn=Ft((function(t,e,r){t=t<r.length&&t>=0?t:r.length;var n=Array.prototype.slice.call(r,0);return n.splice(t,0,e),n})),on=function(){function t(t,e){this.xf=e,this.f=t,this.found=!1}return t.prototype["@@transducer/init"]=be,t.prototype["@@transducer/result"]=function(t){return this.found||(t=this.xf["@@transducer/step"](t,void 0)),this.xf["@@transducer/result"](t)},t.prototype["@@transducer/step"]=function(t,e){return this.f(e)&&(this.found=!0,t=er(this.xf["@@transducer/step"](t,e))),t},t}();function un(t){return function(e){return new on(t,e)}}var an=Ot(he(["find"],un,(function(t,e){for(var r=0,n=e.length;r<n;){if(t(e[r]))return e[r];r+=1}}))),cn=an,sn=Ft((function(t,e,r){return Ut(t,pe(e,r))})),fn=Ot((function(t,e){if(0===t.length||Yt(e))return!1;for(var r=e,n=0;n<t.length;){if(Yt(r)||!jt(t[n],r))return!1;r=r[t[n]],n+=1}return!0})),ln=fn,pn=Ot((function(t,e){return ln([t],e)})),yn=Ot((function(t,e){return Mr(e,[t])}));function dn(t){return dn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},dn(t)}function hn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,vn(n.key),n)}}function vn(t){var e=function(t){if("object"!=dn(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=dn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==dn(e)?e:e+""}function bn(t){return function(t){if(Array.isArray(t))return gn(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||mn(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function mn(t,e){if(t){if("string"==typeof t)return gn(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?gn(t,e):void 0}}function gn(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function On(t){var e=pn("url_base_pathname",t),r=pn("requests_pathname_prefix",t);if("Object"!==Rt(t)||!e&&!r)throw new Error('\n            Trying to make an API request but neither\n            "url_base_pathname" nor "requests_pathname_prefix"\n            is in `config`. `config` is: ',t);var n=r?t.requests_pathname_prefix:t.url_base_pathname;return"/"===n.charAt(n.length-1)?n:n+"/"}var wn=["props","children"],Sn=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:void 0;if(Array.isArray(t))t.forEach((function(t,o){if(n){var i=$r((function(t){return Gt("{}",t)}),n);if(-1!==i){var u=Zr(0,i,n),a=Zr(i,n.length,n);if(u.length)Sn(ne(u,t),e,tr(r,tr([o],u)),a);else{var c,s=a.map((function(t){return t.replace("{}","")})).filter((function(t){return t})),f=tr([o],s);for(var l in c=s.length?ne(s,t):t){var p=c[l];Sn(p,e,tr(r,f.concat([l])))}}}else Sn(ne(n,t),e,tr(r,tr([o],n)))}else Sn(t,e,yn(o,r))}));else if("Object"===Rt(t)){e(t,r);var o=ne(wn,t);if(o){var i=tr(r,wn);Sn(o,e,i)}Vt([],[t.namespace,t.type],window.__dashprivate_childrenProps).forEach((function(n){if(n.includes("[]")){var o=(O=n.split("[]").map((function(t){return t.split(".").filter((function(t){return t}))})),w=2,function(t){if(Array.isArray(t))return t}(O)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,u,a=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(a.push(n.value),a.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(u=r.return(),Object(u)!==u))return}finally{if(s)throw o}}return a}}(O,w)||mn(O,w)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),i=o[0],u=o[1],a=tr(["props"],i),c=tr(r,a);Sn(ne(a,t),e,c,u)}else if(n.includes("{}")){for(var s=n.split("."),f=[],l=[],p=!1,y=0;y<s.length;y++){var d=s[y];!p&&d.includes("{}")?(p=!0,f.push(d.replace("{}",""))):p?l.push(d):f.push(d)}var h=tr(r,["props"].concat(f)),v=ne(["props"].concat(f),t);if(void 0!==v)for(var b in v){var m=v[b];l.length?Sn(ne(l,m),e,tr(h,[b].concat(l))):Sn(m,e,[].concat(bn(h),[b]))}}else{var g=tr(r,["props"].concat(bn(n.split("."))));Sn(ne(["props"].concat(bn(n.split("."))),t),e,g)}var O,w}))}},jn=function(){return t=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this._ev={}},e=[{key:"on",value:function(t,e){var r=this;return(this._ev[t]=this._ev[t]||[]).push(e),function(){return r.removeListener(t,e)}}},{key:"removeListener",value:function(t,e){var r=this._ev[t];if(r){var n=r.indexOf(e);n>-1&&r.splice(n,1)}}},{key:"emit",value:function(t){for(var e=this,r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];var i=this._ev[t];i&&i.forEach((function(t){return t.apply(e,n)}))}},{key:"once",value:function(t,e){var r=this,n=this.on(t,(function(){n();for(var t=arguments.length,o=new Array(t),i=0;i<t;i++)o[i]=arguments[i];e.apply(r,o)}))}}],e&&hn(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e}();function _n(t){return _n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_n(t)}function En(t,e,r,n){var o=r||{strs:{},objs:{}},i=o.strs,u=o.objs,a=function(t){return e.some((function(e,r){return t[r]!==e}))},c=e.length,s=c?we(a,i):{},f={};return c&&_r((function(t,e){var r=we((function(t){var e=t.path;return a(e)}),t);r.length&&(f[e]=r)}),u),Sn(t,(function(t,r){var n=ne(["props","id"],t);if(n)if("object"===_n(n)){var o=Object.keys(n).sort(),i=ye(o,n),a=o.join(","),c=f[a]=f[a]||[],l=u[a]||[],p={values:i,path:tr(e,r)},y=rn(p,l);-1===y?c.push(p):f[a]=nn(y,p,c)}else s[n]=tr(e,r)})),{strs:s,objs:f,events:n||r.events}}function Pn(t,e){if("object"===_n(e)){var r=Object.keys(e).sort(),n=r.join(","),o=t.objs[n];if(!o)return!1;var i=ye(r,e),u=cn(sn(i,"values"),o);return u&&u.path}return t.strs[e]}var An=function(t){var e=t.type,r=t.namespace,n=window[r];if(n){if(n[e])return n[e];throw new Error("Component ".concat(e," not found in ").concat(r))}throw new Error("".concat(r," was not found."))};function kn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function xn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?kn(Object(r),!0).forEach((function(e){Tn(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):kn(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Tn(t,e,r){return(e=function(t){var e=function(t){if("object"!=Dn(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=Dn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Dn(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function In(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,u,a=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(a.push(n.value),a.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(u=r.return(),Object(u)!==u))return}finally{if(s)throw o}}return a}}(t,e)||Cn(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Cn(t,e){if(t){if("string"==typeof t)return Rn(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Rn(t,e):void 0}}function Rn(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function Dn(t){return Dn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dn(t)}var Nn=function(t){return t.startsWith("..")},Mn={wild:"ALL",multi:1},Un={wild:"MATCH"},Ln={wild:"ALLSMALLER",multi:1,expand:1},qn={ALL:Mn,MATCH:Un,ALLSMALLER:Ln},Bn={Output:{ALL:Mn,MATCH:Un},Input:qn,State:qn},Gn=["string","number","boolean"],Fn=[".","{"];function Hn(t){var e=t.lastIndexOf(".");return{id:$n(t.substr(0,e)),property:t.substr(e+1)}}function $n(t){return function(t){return t.startsWith("{")}(t)?function(t){return Re((function(t){return Array.isArray(t)&&qn[t[0]]||t}),JSON.parse(t))}(t):t}function Jn(t){return"object"!==Dn(t)?t:"{"+Object.keys(t).sort().map((function(e){return JSON.stringify(e)+":"+((r=t[e])&&r.wild||JSON.stringify(r));var r})).join(",")+"}"}function zn(t,e){var r=Sr()(e);if(Sr()(t)){if(r){var n=Number(t),o=Number(e);return n>o?1:n<o?-1:0}return-1}if(r)return 1;var i="boolean"==typeof t;return i!==("boolean"==typeof e)?i?-1:1:t>e?1:t<e?-1:0}var Wn=function(t){return"string"==typeof t?t+"z":"z"};function Vn(t,e,r,n){var o=t[e]=t[e]||{};(o[r]=o[r]||[]).push(n)}function Kn(t,e,r,n){for(var o=Object.keys(e).sort(),i=o.join(","),u=ye(o,e),a=t[i]=t[i]||{},c=a[r]=a[r]||[],s=!1,f=0;f<c.length;f++)if(Ut(u,c[f].values)){s=c[f];break}s||(s={keys:o,values:u,callbacks:[]},c.push(s)),s.callbacks.push(n)}var Yn=function(t){var e=In(t,2),r=e[0],n=e[1],o=r&&r.wild,i=n&&n.wild;return o&&i?!(r===Un&&n===Ln||r===Ln&&n===Un):r===n||o||i};function Qn(t,e){var r,n=t.id,o=t.property,i=Ct(n).sort(),u=ye(i,n),a=function(t){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=Cn(t))){e&&(t=e);var r=0,n=function(){};return{s:n,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,u=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return i=t.done,t},e:function(t){u=!0,o=t},f:function(){try{i||null==e.return||e.return()}finally{if(u)throw o}}}}(e);try{for(a.s();!(r=a.n()).done;){var c=r.value,s=c.id;if(c.property===o&&"string"!=typeof s&&Ut(Ct(s).sort(),i)&&ir(Yn,Er(u,ye(i,s))))return c}}catch(t){a.e(t)}finally{a.f()}return!1}function Xn(t,e){var r=new Or.w,n={},o=Re(Nr({id:$n})),i=Re((function(t){var e,r=t.output,n=t.no_output,i=Nr({inputs:o,state:o},t);return n?(i.outputs=[],i.noOutput=!0):i.outputs=Re((function(t){return Zt("out",!0,Hn(t))}),Nn(r)?(e=r).substr(2,e.length-4).split("..."):[r]),i}),t),u=!1;!function(t,e){var r={},n=[];t.forEach((function(t){var o=t.inputs,i=t.outputs,u=t.state,a=!0;1!==i.length||i[0].id||i[0].property||(a=!1);var c="In the callback for output(s):\n  "+i.map(mo).join("\n  ");o.length||e("A callback is missing Inputs",[c,"there are no `Input` elements.","Without `Input` elements, it will never get called.","","Subscribing to `Input` components will cause the","callback to be called whenever their values change."]),[[i,"Output"],[o,"Input"],[u,"State"]].forEach((function(t){var r=In(t,2),n=r[0],o=r[1];("Output"!==o||a)&&(Array.isArray(n)||e("Callback ".concat(o,"(s) must be an Array"),[c,"For ".concat(o,"(s) we found:"),JSON.stringify(n),"but we expected an Array."]),n.forEach((function(t,r){!function(t,e,r,n,o){var i=t.id,u=t.property;if("string"==typeof u&&u||o("Callback property error",[e,"".concat(r,"[").concat(n,"].property = ").concat(JSON.stringify(u)),"but we expected `property` to be a non-empty string."]),"object"===Dn(i))ae(i)&&o("Callback item missing ID",[e,"".concat(r,"[").concat(n,"].id = {}"),"Every item linked to a callback needs an ID"]),_r((function(t,i){i||o("Callback wildcard ID error",[e,"".concat(r,"[").concat(n,'].id has key "').concat(i,'"'),"Keys must be non-empty strings."]),"object"===Dn(t)&&t.wild?Bn[r][t.wild]!==t&&o("Callback wildcard ID error",[e,"".concat(r,"[").concat(n,'].id["').concat(i,'"] = ').concat(t.wild),"Allowed wildcards for ".concat(r,"s are:"),Ct(Bn[r]).join(", ")]):Gt(Dn(t),Gn)||o("Callback wildcard ID error",[e,"".concat(r,"[").concat(n,'].id["').concat(i,'"] = ').concat(JSON.stringify(t)),"Wildcard callback ID values must be either wildcards","or constants of one of these types:",Gn.join(", ")])}),i);else if("string"==typeof i){i||o("Callback item missing ID",[e,"".concat(r,"[").concat(n,'].id = "').concat(i,'"'),"Every item linked to a callback needs an ID"]);var a=Fn.filter((function(t){return Gt(t,i)}));a.length&&o("Callback invalid ID string",[e,"".concat(r,"[").concat(n,"].id = '").concat(i,"'"),"characters '".concat(a.join("', '"),"' are not allowed.")])}else o("Callback ID type error",[e,"".concat(r,"[").concat(n,"].id = ").concat(JSON.stringify(i)),"IDs must be strings or wildcard-compatible objects."])}(t,c,o,r,e)})))})),a&&(function(t,e,r,n,o){var i={},u=[];t.forEach((function(t,a){var c=t.id,s=t.property;if("string"==typeof c){var f=mo({id:c,property:s});i[f]?r("Duplicate callback Outputs",[e,"Output ".concat(a," (").concat(f,") is already used by this callback.")]):n[f]?r("Duplicate callback outputs",[e,"Output ".concat(a," (").concat(f,") is already in use."),"To resolve this, set `allow_duplicate=True` on","duplicate outputs, or combine the outputs into","one callback function, distinguishing the trigger","by using `dash.callback_context` if necessary."]):i[f]=1}else{var l={id:c,property:s},p=Qn(l,u),y=p||Qn(l,o);if(p||y){var d=mo(l),h=mo(p||y);r("Overlapping wildcard callback outputs",[e,"Output ".concat(a," (").concat(d,")"),"overlaps another output (".concat(h,")"),"used in ".concat(p?"this":"a different"," callback.")])}else u.push(l)}})),Ct(i).forEach((function(t){n[t]=1})),u.forEach((function(t){o.push(t)}))}(i,c,e,r,n),function(t,e,r,n,o){var i=Zn(t.length?t[0].id:void 0).matchKeys;t.forEach((function(e,r){r&&!Ut(Zn(e.id).matchKeys,i)&&o("Mismatched `MATCH` wildcards across `Output`s",[n,"Output ".concat(r," (").concat(mo(e),")"),"does not have MATCH wildcards on the same keys as","Output 0 (".concat(mo(t[0]),")."),"MATCH wildcards must be on the same keys for all Outputs.","ALL wildcards need not match, only MATCH."])})),[[e,"Input"],[r,"State"]].forEach((function(e){var r=In(e,2),u=r[0],a=r[1];u.forEach((function(e,r){var u=Zn(e.id),c=u.matchKeys,s=u.allsmallerKeys,f=c.concat(s),l=vr(f,i);l.length&&(l.sort(),o("`Input` / `State` wildcards not in `Output`s",[n,"".concat(a," ").concat(r," (").concat(mo(e),")"),"has MATCH or ALLSMALLER on key(s) ".concat(l.join(", ")),"where Output 0 (".concat(mo(t[0]),")"),"does not have a MATCH wildcard. Inputs and State do not","need every MATCH from the Output(s), but they cannot have","extras beyond the Output(s)."]))}))}))}(i,o,u,c,e))}))}(i,(function(t,r){u=!0,e(t,r)}));var a={},c={},s={},f={},l={MultiGraph:r,outputMap:a,inputMap:c,outputPatterns:s,inputPatterns:f,callbacks:i};if(u)return l;function p(t,e){var r=[{}];return _r((function(t,o){var i=n[o].vals,u=i.indexOf(e[o]),a=[t];t&&t.wild&&(a=t===Ln?u>0?i.slice(0,u):[]:-1===u||t===Mn?i:[e[o]]),r=Br(Br([Zt(o)],a),r)}),t),r}i.forEach((function(t){var e=t.outputs,r=t.inputs;e.concat(r).forEach((function(t){var e=t.id;"object"===Dn(e)&&_r((function(t,e){n[e]||(n[e]={exact:[],expand:0});var r=n[e];t&&t.wild?t.expand&&(r.expand+=1):-1===r.exact.indexOf(t)&&r.exact.push(t)}),e)}))})),_r((function(t){var e,r=t.exact,n=t.expand,o=r.slice().sort(zn);if(n)for(var i=0;i<n;i++)r.length?(o.splice(0,0,[(e=o[0],Sr()(e)?e-1:0)]),o.push(Wn(o[o.length-1]))):o.push(i);else r.length||o.push(0);t.vals=o}),n);var y="__output",d=[],h=[],v=[];function b(t,e){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];r.addNode(t),r.addDependency(t,e),n&&(h[h.length-1].push(t),v[v.length-1].push(e))}return i.forEach((function(t){var e=t.outputs,n=t.inputs;function o(t,e){r.addNode(e),n.forEach((function(r){var n=r.id,o=r.property;"object"===Dn(n)?p(n,t).forEach((function(t){b(mo({id:t,property:o}),e)})):b(mo(r),e)}))}h.push([]),v.push([]);var i=Zn(e.length?e[0].id:void 0).matchKeys,u=$r((function(t){return!ro(t.id)}),e),l=zr({matchKeys:i,firstSingleOutput:u,outputs:e},t);e.forEach((function(t){var e=t.id,r=t.property,i=function(t,e){var r=t.id,n=t.property;return e.some((function(e){var o=e.id,i=e.property;if(n!==i||Dn(r)!==Dn(o))return!1;if("string"==typeof r){if(r===o)return!0}else if(Qn(e,[t]))return!0;return!1}))}(t,n);if("object"===Dn(e))p(e,{}).forEach((function(t){var e={id:t,property:r},n=mo(e);i&&(d.push(e),n+=y),o(t,n)})),Kn(s,e,r,l);else{var u=mo(t);i&&(d.push(t),u+=y),o({},u),Vn(a,e,r,l)}})),n.forEach((function(t){var e=t.id,r=t.property;"object"===Dn(e)?Kn(f,e,r,l):Vn(c,e,r,l)}))})),d.forEach((function(t){for(var e=mo(t),r=e.concat(y),n=0;n<h.length;n++)h[n].some((function(t){return t===e}))&&(v[n].some((function(t){return t===r}))||v[n].forEach((function(t){b(r,t,!1)})))})),l}function Zn(t){var e=[],r=[];return"object"===Dn(t)&&(_r((function(t,n){t===Un?e.push(n):t===Ln&&r.push(n)}),t),e.sort(),r.sort()),{matchKeys:e,allsmallerKeys:r}}function to(t,e,r,n,o,i){for(var u=0;u<t.length;u++){var a=e[u],c=r[u];if(c.wild){if(n&&c!==Mn){var s=n.indexOf(t[u]),f=i[s];if(c===Ln&&f===Ln)throw new Error("invalid wildcard id pair: "+JSON.stringify({keys:t,patternVals:r,vals:e,refKeys:n,refPatternVals:i,refVals:o}));if(zn(a,o[s])!==(c===Ln?-1:f===Ln?1:0))return!1}}else if(a!==c)return!1}return!0}function eo(t,e){for(var r=[],n=0;n<t.length;n++)t[n]===Un&&r.push(e[n]);return r.length?JSON.stringify(r):""}function ro(t){var e=t.id;return"object"===Dn(e)&&Yr((function(t){return t.multi}),Rr(e))}function no(t,e,r,n){var o,i,u="";if("string"==typeof r){var a=(t.outputMap[r]||{})[n];a&&(i=a[0],o=Ao())}else{var c=Object.keys(r).sort(),s=ye(c,r),f=c.join(","),l=(t.outputPatterns[f]||{})[n];if(l)for(var p=0;p<l.length;p++){var y=l[p].values;if(to(c,s,y)){i=l[p].callbacks[0],o=Ao(c,s,y),u=eo(y,s);break}}}return!!o&&Eo(i,o,u)}function oo(t,e,r,n){var o=Object.keys(e.id).sort(),i=ye(o,e.id),u={};r.forEach((function(e){var r=e.id,a=ye(o,r),c=Eo(t,Ao(o,a,i),eo(i,a)),s=c.resolvedId;u[s]||(n.push(c),u[s]=!0)}))}function io(t,e,r){return function(n){var o=n.matchKeys,i=n.firstSingleOutput,u=n.outputs;if(o.length){var a=u[i];if(a)oo(n,a,t(e)(a),r);else{var c={};u.forEach((function(i){var u=t(e)(i).filter((function(t){var e=JSON.stringify(ye(o,t.id));return!c[e]&&(c[e]=1,!0)}));oo(n,i,u,r)}))}}else{var s=Eo(n,t,"");r.push(s)}}}function uo(t){return uo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},uo(t)}function ao(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function co(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ao(Object(r),!0).forEach((function(e){so(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ao(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function so(t,e,r){return(e=function(t){var e=function(t){if("object"!=uo(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=uo(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==uo(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fo(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,u,a=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(a.push(n.value),a.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(u=r.return(),Object(u)!==u))return}finally{if(s)throw o}}return a}}(t,e)||po(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function lo(t){return function(t){if(Array.isArray(t))return yo(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||po(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function po(t,e){if(t){if("string"==typeof t)return yo(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?yo(t,e):void 0}}function yo(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var ho=2,vo=1,bo=le(Math.max),mo=function(t){var e=t.id,r=t.property;return"".concat(Jn(e),".").concat(r)};function go(t,e,r,n,o){var i=!(arguments.length>5&&void 0!==arguments[5])||arguments[5],u=[],a=mo({id:r,property:n});if("string"==typeof r){var c=(t.inputMap[r]||{})[n];if(!c)return[];c.forEach(io(Ao(),e,u))}else{var s=Object.keys(r).sort(),f=ye(s,r),l=s.join(","),p=(t.inputPatterns[l]||{})[n];if(!p)return[];p.forEach((function(t){to(s,f,t.values)&&t.callbacks.forEach(io(Ao(s,f,t.values),e,u))}))}return u.forEach((function(r){r.changedPropIds[a]=o||ho,i&&(r.priority=Oo(t,e,r))})),u}function Oo(t,e,r){for(var n=[r],o={},i={},u=[];n.length;){n=we((function(t){var e=i[t.resolvedId];return i[t.resolvedId]=!0,e}),n);var a=we((function(t){return!o[mo(t)]}),Ee(Re((function(t){return Ee(t.getOutputs(e))}),n)));a.forEach((function(t){return o[mo(t)]=!0})),(n=Ee(Re((function(r){var n=r.id,o=r.property;return go(t,e,n,o,vo,!1)}),a))).length&&u.push(n.length)}return u.unshift(u.length),Re((function(t){return Math.min(t,35).toString(36)}),u).join("")}var wo=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if(!e.length)return[];var o=Re(mo,$e((function(e,r){return tr(e,Ee(r.getOutputs(t)))}),[],r)),i={};if(o.forEach((function(t){return i[t]=!0})),Object.keys(n).length){var u=Ee(Re((function(e){return function(t,e,r){for(var n=[r],o={};n.length;){var i=we((function(t){return!o[mo(t)]}),Ee(Re((function(t){return Ee(t.getOutputs(e))}),n)));o=$e((function(t,e){return Zt(mo(e),!0,t)}),o,i),n=Ee(Re((function(r){var n=r.id,o=r.property;return go(t,e,n,o,vo,!1)}),i))}return o}(n,t,e)}),r));u.length>0&&(i=Object.assign.apply(Object,[u[0]].concat(lo(u))))}return we((function(e){return ir((function(t){return!i[mo(t)]}),function(t,e){return t.filter((function(t){return!e.some((function(e){return mo(t)===mo(e)}))}))}(Ee(e.getInputs(t)),Ee(e.getOutputs(t))))}),e)},So=function(t,e,r,n){for(var o=[],i=function(t,e,r,n){var o=n.outputsOnly,i=n.removedArrayInputsOnly,u=n.newPaths,a=n.chunkPath,c={},s=[];function f(t){if(t){var e=c[t.resolvedId];if(void 0!==e){var r=s[e];r.changedPropIds=bo(r.changedPropIds,t.changedPropIds),t.initialCall&&(r.initialCall=!0)}else c[t.resolvedId]=s.length,s.push(t)}}function l(r,n,c){if(n)for(var s in n){var l=no(t,0,r,s);l&&(l.callback.prevent_initial_call||(l.initialCall=!0,f(l)))}if(!o&&c){var p=i?(h=Jn(r),function(t){return t.getInputs(e).some((function(e){return!(!Array.isArray(e)||!e.some((function(t){return Jn(t.id)===h}))||(Ee(t.getOutputs(u)).length&&(t.initialCall=!0,t.changedPropIds={},f(t)),0))}))}):f,y=p;for(var d in a&&(y=function(t){ir(en(a),cr("path",Ee(t.getOutputs(e))))||p(t)}),c)go(t,e,r,d,vo).forEach(y)}var h}return Sn(r,(function(e){var r=ne(["props","id"],e);if(r)if("string"!=typeof r||i){var n=Object.keys(r).sort().join(",");l(r,!i&&t.outputPatterns[n],t.inputPatterns[n])}else l(r,t.outputMap[r],t.inputMap[r])})),Re((function(r){return xn(xn({},r),{},{priority:Oo(t,e,r)})}),s)}(t,e,r,n);;){var u=fo(pr((function(t){var r=t.callback.inputs,n=t.getInputs;return ir(ro,r)||!ae(vr(Re(mo,Ee(n(e))),o))}),i),2),a=u[0],c=u[1];if(!c.length)break;i=a,o=tr(o,Re(mo,Ee(Re((function(t){return(0,t.getOutputs)(e)}),c))))}if(n.filterRoot){var s=ne(["props","id"],r);s&&(s=Jn(s),i=i.filter((function(t){return t.callback.inputs.reduce((function(t,e){return t||Jn(e.id)==s&&n.filterRoot.includes(e.property)}),!1)})))}var f=Math.random().toString(16);return Re((function(t){return co(co({},t),{},{executionGroup:f})}),i)},jo=function(t){var e=t.anyVals,r=t.callback,n=r.inputs,o=r.outputs,i=r.state;return tr(Re(mo,[].concat(lo(n),lo(o),lo(i))),Array.isArray(e)?e:""===e?[]:[e]).join(",")};function _o(t,e,r,n){return Ee(Re((function(e){return go(r,n,t,e)}),Ct(e)))}var Eo=function(t,e,r){return{callback:t,anyVals:r,resolvedId:t.output+r,getOutputs:function(r){return t.outputs.map(e(r))},getInputs:function(r){return t.inputs.map(e(r))},getState:function(r){return t.state.map(e(r))},changedPropIds:{},initialCall:!1}};function Po(t,e){var r=fo(pr((function(t){var r=t.getOutputs,n=t.callback.outputs;return Ee(r(e)).length===n.length}),t),2)[1],n=fo(pr((function(t){var r=t.getOutputs;return!Ee(r(e)).length}),r),2)[1];return{added:Re((function(t){return Zt("changedPropIds",br((function(t,r){return Pn(e,Hn(r).id)}),t.changedPropIds),t)}),n),removed:r}}function Ao(t,e,r){return function(n){return function(o){var i=o.id,u=o.property;if("string"==typeof i){var a=Pn(n,i);return a?[{id:i,property:u,path:a}]:[]}var c=Object.keys(i).sort(),s=ye(c,i),f=c.join(","),l=n.objs[f];if(!l)return[];var p=[];return l.forEach((function(n){var o=n.values,i=n.path;to(c,o,s,t,e,r)&&p.push({id:gr(c,o),property:u,path:i})})),p}}}var ko={ON_PROP_CHANGE:1,SET_REQUEST_QUEUE:1,SET_GRAPHS:1,SET_PATHS:1,SET_LAYOUT:1,SET_APP_LIFECYCLE:1,SET_CONFIG:1,ADD_HTTP_HEADERS:1,ON_ERROR:1,SET_HOOKS:1,INSERT_COMPONENT:1,REMOVE_COMPONENT:1},xo=function(t){if(ko[t])return t;throw new Error("".concat(t," is not defined."))};function To(t){var e={STARTED:"STARTED",HYDRATED:"HYDRATED",DESTROYED:"DESTROYED"};if(e[t])return e[t];throw new Error("".concat(t," is not a valid app state."))}var Io,Co,Ro=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:To("STARTED"),e=arguments.length>1?arguments[1]:void 0;return e.type===xo("SET_APP_LIFECYCLE")?To(e.payload):t};function Do(t){return Do="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Do(t)}function No(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Mo(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?No(Object(r),!0).forEach((function(e){Uo(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):No(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Uo(t,e,r){return(e=function(t){var e=function(t){if("object"!=Do(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=Do(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Do(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}!function(t){t.AddBlocked="Callbacks.AddBlocked",t.AddExecuted="Callbacks.AddExecuted",t.AddExecuting="Callbacks.AddExecuting",t.AddPrioritized="Callbacks.AddPrioritized",t.AddRequested="Callbacks.AddRequested",t.AddStored="Callbacks.AddStored",t.AddWatched="Callbacks.AddWatched",t.RemoveBlocked="Callbacks.RemoveBlocked",t.RemoveExecuted="Callbacks.RemoveExecuted",t.RemoveExecuting="Callbacks.RemoveExecuting",t.RemovePrioritized="Callbacks.RemovePrioritized",t.RemoveRequested="Callbacks.RemoveRequested",t.RemoveStored="Callbacks.RemoveStored",t.RemoveWatched="Callbacks.RemoveWatched"}(Io||(Io={})),function(t){t.AddCompleted="Callbacks.Completed",t.Aggregate="Callbacks.Aggregate"}(Co||(Co={}));var Lo={blocked:[],executed:[],executing:[],prioritized:[],requested:[],stored:[],watched:[],completed:0},qo={[Io.AddBlocked]:tr,[Io.AddExecuted]:tr,[Io.AddExecuting]:tr,[Io.AddPrioritized]:tr,[Io.AddRequested]:tr,[Io.AddStored]:tr,[Io.AddWatched]:tr,[Io.RemoveBlocked]:vr,[Io.RemoveExecuted]:vr,[Io.RemoveExecuting]:vr,[Io.RemovePrioritized]:vr,[Io.RemoveRequested]:vr,[Io.RemoveStored]:vr,[Io.RemoveWatched]:vr},Bo={[Io.AddBlocked]:"blocked",[Io.AddExecuted]:"executed",[Io.AddExecuting]:"executing",[Io.AddPrioritized]:"prioritized",[Io.AddRequested]:"requested",[Io.AddStored]:"stored",[Io.AddWatched]:"watched",[Io.RemoveBlocked]:"blocked",[Io.RemoveExecuted]:"executed",[Io.RemoveExecuting]:"executing",[Io.RemovePrioritized]:"prioritized",[Io.RemoveRequested]:"requested",[Io.RemoveStored]:"stored",[Io.RemoveWatched]:"watched"},Go=function(){var t=arguments.length>1?arguments[1]:void 0;return $e((function(t,e){return null===e?t:e.type===Co.AddCompleted?function(t,e){return Mo(Mo({},t),{},{completed:t.completed+e.payload})}(t,e):function(t,e){var r=qo[e.type],n=Bo[e.type];return r&&n&&0!==e.payload.length?Mo(Mo({},t),{},{[n]:r(t[n],e.payload)}):t}(t,e)}),arguments.length>0&&void 0!==arguments[0]?arguments[0]:Lo,t.type===Co.Aggregate?t.payload:[t])},Fo=Ft((function t(e,r,n){return se((function(r,n,o){return oe(n)&&oe(o)?t(e,n,o):e(r,n,o)}),r,n)})),Ho=Fo,$o=Ot((function(t,e){return Ho((function(t,e,r){return r}),t,e)}));function Jo(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1?arguments[1]:void 0;return e.type===xo("SET_CONFIG")?(window.__dashprivate_childrenProps=$o(window.__dashprivate_childrenProps||{},e.payload.children_props),e.payload):e.type===xo("ADD_HTTP_HEADERS")?$o(t,{fetch:{headers:e.payload}}):t}var zo={},Wo=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:zo,e=arguments.length>1?arguments[1]:void 0;return"SET_GRAPHS"===e.type?e.payload:t};function Vo(t){return function(t){if(Array.isArray(t))return Ko(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return Ko(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ko(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ko(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var Yo={frontEnd:[],backEnd:[],backEndConnected:!0};function Qo(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Yo,e=arguments.length>1?arguments[1]:void 0;switch(e.type){case"ON_ERROR":var r=t.frontEnd,n=t.backEnd,o=t.backEndConnected;return console.error(e.payload.error),"frontEnd"===e.payload.type?{frontEnd:[zr(e.payload,{timestamp:new Date})].concat(Vo(r)),backEnd:n,backEndConnected:o}:"backEnd"===e.payload.type?{frontEnd:r,backEnd:[zr(e.payload,{timestamp:new Date})].concat(Vo(n)),backEndConnected:o}:t;case"SET_CONNECTION_STATUS":return zr(t,{backEndConnected:e.payload});default:return t}}function Xo(t){return function(t){if(Array.isArray(t))return Zo(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return Zo(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Zo(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Zo(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var ti={past:[],present:{},future:[]},ei=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ti;switch((arguments.length>1?arguments[1]:void 0).type){case"UNDO":var e=t.past,r=t.present,n=t.future,o=e[e.length-1];return{past:e.slice(0,e.length-1),present:o,future:[r].concat(Xo(n))};case"REDO":var i=t.past,u=t.present,a=t.future,c=a[0],s=a.slice(1);return{past:[].concat(Xo(i),[u]),present:c,future:s};case"REVERT":var f=t.past,l=t.future,p=f[f.length-1];return{past:f.slice(0,f.length-1),present:p,future:Xo(l)};default:return t}},ri=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{layout_pre:null,layout_post:null,request_pre:null,request_post:null,callback_resolved:null,request_refresh_jwt:null,bear:!1},e=arguments.length>1?arguments[1]:void 0;return"SET_HOOKS"===e.type?e.payload:t};function ni(t){return ni="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ni(t)}function oi(t,e,r){if(r||(r=new ui),o=ni(n=t),null==n||"object"!=o&&"function"!=o)return t;var n,o,i,u=function(n){var o=r.get(t);if(o)return o;for(var i in r.set(t,n),t)Object.prototype.hasOwnProperty.call(t,i)&&(n[i]=e?oi(t[i],!0,r):t[i]);return n};switch(Rt(t)){case"Object":return u(Object.create(Object.getPrototypeOf(t)));case"Array":return u(Array(t.length));case"Date":return new Date(t.valueOf());case"RegExp":return i=t,new RegExp(i.source,i.flags?i.flags:(i.global?"g":"")+(i.ignoreCase?"i":"")+(i.multiline?"m":"")+(i.sticky?"y":"")+(i.unicode?"u":"")+(i.dotAll?"s":""));case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":return t.slice();default:return t}}var ii,ui=function(){function t(){this.map={},this.length=0}return t.prototype.set=function(t,e){var r=this.hash(t),n=this.map[r];n||(this.map[r]=n=[]),n.push([t,e]),this.length+=1},t.prototype.hash=function(t){var e=[];for(var r in t)e.push(Object.prototype.toString.call(t[r]));return e.join()},t.prototype.get=function(t){if(this.length<=180){for(var e in this.map)for(var r=this.map[e],n=0;n<r.length;n+=1)if((i=r[n])[0]===t)return i[1]}else{var o=this.hash(t);if(r=this.map[o])for(n=0;n<r.length;n+=1){var i;if((i=r[n])[0]===t)return i[1]}}},t}(),ai=nt((function(t){return null!=t&&"function"==typeof t.clone?t.clone():oi(t,!0)})),ci="JWT Expired",si=200,fi=204,li={[si]:"SUCCESS",[fi]:"NO_UPDATE"},pi=["__dash_client","__dash_server","__dash_upload","__dash_download"],yi={count:0,total:0,compute:0,network:{time:0,upload:0,download:0},resources:{},status:{latest:null},result:{}},di={updated:[],resources:{},callbacks:{},graphLayout:null},hi=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:di,e=arguments.length>1?arguments[1]:void 0;if("UPDATE_RESOURCE_USAGE"===e.type){var r=e.payload,n=r.id,o=r.usage,i=r.status,u=li[i]||i,a={updated:[n],resources:t.resources,callbacks:t.callbacks,graphLayout:t.graphLayout};a.callbacks[n]=a.callbacks[n]||ai(yi);var c=a.callbacks[n],s=c.resources,f=a.resources;if(c.count+=1,c.status.latest=u,c.status[u]=(c.status[u]||0)+1,c.result=e.payload.result,c.inputs=e.payload.inputs,c.state=e.payload.state,o){var l=o.__dash_client,p=o.__dash_server,y=o.__dash_upload,d=o.__dash_download,h=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if({}.hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],-1===e.indexOf(r)&&{}.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(o,pi);for(var v in c.total+=l,c.compute+=p,c.network.time+=l-p,c.network.upload+=y,c.network.download+=d,h)h.hasOwnProperty(v)&&(s[v]=(s[v]||0)+h[v],f[v]=(f[v]||0)+h[v])}return a}return t},vi={id:null,props:{}},bi=function(){return arguments.length>0&&void 0!==arguments[0]?arguments[0]:vi};!function(t){t.Set="IsLoading.Set"}(ii||(ii={}));var mi=function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],e=arguments.length>1?arguments[1]:void 0;return e.type===ii.Set?e.payload:t},gi=function(t){return{value:t,"fantasy-land/map":function(){return this}}},Oi=Ot((function(t,e){return t(gi)(e).value})),wi=Ot((function(t,e){return function(r){return function(n){return Re((function(t){return e(t,n)}),r(t(n)))}}})),Si=nt((function(t){return wi((function(e){return zt(t,e)}),Xt(t))})),ji=Ft((function(t,e,r){var n=Array.prototype.slice.call(r,0);return n.splice(t,e),n})),_i=Ot((function t(e,r){if(null==r)return r;switch(e.length){case 0:return r;case 1:return function(t,e){if(null==e)return e;if(Ht(t)&&Kt(e))return ji(t,1,e);var r={};for(var n in e)r[n]=e[n];return delete r[t],r}(e[0],r);default:var n=e[0],o=Array.prototype.slice.call(e,1);return null==r[n]?function(t,e){if(Ht(t)&&Kt(e))return[].concat(e);var r={};for(var n in e)r[n]=e[n];return r}(n,r):Zt(n,t(o,r[n]),r)}}));function Ei(t){return Ei="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ei(t)}function Pi(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Ai(t,e,r){return(e=function(t){var e=function(t){if("object"!=Ei(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=Ei(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Ei(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ki(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var xi=function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{components:[]},r=arguments.length>1?arguments[1]:void 0;if(r.type===xo("SET_LAYOUT"))return Array.isArray(r.payload)?e.components=function(t){if(Array.isArray(t))return ki(t)}(t=r.payload)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return ki(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ki(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}():e.components=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Pi(Object(r),!0).forEach((function(e){Ai(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Pi(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},r.payload),e;if(Gt(r.type,["UNDO_PROP_CHANGE","REDO_PROP_CHANGE",xo("ON_PROP_CHANGE")])){var n=yn("props",r.payload.itempath),o=Oi(Si(n),e),i=zr(o,r.payload.props);return Xt(n,i,e)}if(r.type===xo("INSERT_COMPONENT")){var u=r.payload,a=u.component,c=u.componentPath;return Xt(c,a,e)}if(r.type===xo("REMOVE_COMPONENT")){var s=r.payload.componentPath;return _i(s,e)}return e},Ti={strs:{},objs:{}},Ii=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Ti,e=arguments.length>1?arguments[1]:void 0;return e.type===xo("SET_PATHS")?e.payload:t},Ci=Ot((function(t,e){return _i([t],e)}));function Ri(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;switch(e.type){case"ADD_CALLBACK_JOB":return function(t,e){return Zt(t.jobId,t,e)}(e.payload,t);case"REMOVE_CALLBACK_JOB":return function(t,e){return Ci(t,e)}(e.payload.jobId,t);case"CALLBACK_JOB_OUTDATED":return function(t,e){return Xt([t,"outdated"],!0,e)}(e.payload.jobId,t);default:return t}}function Di(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;switch(e.type){case"LOADED":return e.payload.reduce((function(t,e){var r=[JSON.stringify(e.path)],n=Vt([],r,t);return Xt(r,n.filter((function(t){return t.property!==e.property})),t)}),t);case"LOADING":return e.payload.reduce((function(t,e){var r=[JSON.stringify(e.path)],n=Vt([],r,t);return Gt(e,n)||n.push(e),Xt(r,n,t)}),t);default:return t}}var Ni=Ot((function(t,e){return Ie(t+1,(function(){var r=arguments[t];if(null!=r&&Je(r[e]))return r[e].apply(r,Array.prototype.slice.call(arguments,0,t));throw new TypeError(Ze(r)+' does not have a method named "'+e+'"')}))})),Mi=Ni(1,"join");function Ui(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function Li(t,r,n,o){var i,u=zr(r,n);return Array.isArray(o)?e().createElement.apply(e(),[t,u].concat(function(t){if(Array.isArray(t))return Ui(t)}(i=o)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(i)||function(t,e){if(t){if("string"==typeof t)return Ui(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ui(t,e):void 0}}(i)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())):e().createElement(t,u,o)}function qi(t){return"Object"===Rt(t)&&pn("type",t)&&pn("namespace",t)&&pn("props",t)}function Bi(t){return Mi(",",t)}function Gi(t,e){return ne(t,e.layout)}function Fi(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var Hi=["dependenciesRequest","layoutRequest","reloadRequest","loginRequest"],$i=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;if(Gt(e.type,["UNDO_PROP_CHANGE","REDO_PROP_CHANGE","ON_PROP_CHANGE"])){var r=Bi(e.payload.itempath),n=Vt(0,[r,"hash"],t);t=Zt(r,{hash:n+1,changedProps:e.payload.props,renderType:e.payload.renderType},t)}return t};function Ji(){var t={appLifecycle:Ro,callbacks:Go,config:Jo,error:Qo,graphs:Wo,history:ei,hooks:ri,profile:hi,changed:bi,isLoading:mi,layout:xi,paths:Ii,layoutHashes:$i,loading:Di};return re((function(e){var r;t[e]=(r=e,function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0,n=t;if(e.type===r){var o=e.payload,i=o.id,u={status:o.status,content:o.content};n=Array.isArray(i)?Xt(i,u,t):i?Zt(i,u,t):zr(t,u)}return n})}),Hi),t.callbackJobs=Ri,function(t){for(var e=Object.keys(t),r={},n=0;n<e.length;n++){var o=e[n];"function"==typeof t[o]&&(r[o]=t[o])}var i,u=Object.keys(r);try{!function(t){Object.keys(t).forEach((function(e){var r=t[e];if(void 0===r(void 0,{type:yt.INIT}))throw new Error(ft(12));if(void 0===r(void 0,{type:yt.PROBE_UNKNOWN_ACTION()}))throw new Error(ft(13))}))}(r)}catch(t){i=t}return function(t,e){if(void 0===t&&(t={}),i)throw i;for(var n=!1,o={},a=0;a<u.length;a++){var c=u[a],s=r[c],f=t[c],l=s(f,e);if(void 0===l)throw e&&e.type,new Error(ft(14));o[c]=l,n=n||l!==f}return(n=n||u.length!==Object.keys(t).length)?o:t}}(t)}function zi(t,e,r){var n,o=e.graphs,i=e.paths,u=e.layout,a=t.itempath,c=t.props,s=ne(a.concat(["props"]),u)||{},f=s.id;return f&&(r&&(e.changed={id:f,props:c}),n={id:f,props:{}},Ct(c).forEach((function(t){go(o,i,f,t).length&&(n.props[t]=s[t])}))),n}function Wi(t){return Wi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wi(t)}function Vi(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Qi(n.key),n)}}function Ki(t,e,r){return e&&Vi(t.prototype,e),r&&Vi(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function Yi(t,e,r){return(e=Qi(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Qi(t){var e=function(t){if("object"!=Wi(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=Wi(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Wi(e)?e:e+""}var Xi=Ki((function t(e){var r=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),Yi(this,"_store",void 0),Yi(this,"_unsubscribe",void 0),Yi(this,"_observers",[]),Yi(this,"observe",(function(t,e){if("function"==typeof t){if(!Array.isArray(e))throw new Error("inputs must be an array");return r.add(t,e),function(){return r.remove(t)}}return r.add(t.observer,t.inputs),function(){return r.remove(t.observer)}})),Yi(this,"setStore",(function(t){r.__finalize__(),r.__init__(t)})),Yi(this,"__finalize__",(function(){var t;return null===(t=r._unsubscribe)||void 0===t?void 0:t.call(r)})),Yi(this,"__init__",(function(t){r._store=t,t&&(r._unsubscribe=t.subscribe(r.notify)),r._observers.forEach((function(t){t.lastState=null}))})),Yi(this,"add",(function(t,e){return r._observers.push({inputPaths:Re((function(t){return t.split(".")}),e),lastState:null,observer:t,triggered:!1})})),Yi(this,"notify",(function(){var t=r._store;if(t){var e=t.getState(),n=we((function(t){return!t.triggered&&Yr((function(r){return ne(r,e)!==ne(r,t.lastState)}),t.inputPaths)}),r._observers);n.forEach((function(t){t.triggered=!0})),n.forEach((function(e){e.lastState=t.getState(),e.observer(t),e.triggered=!1}))}})),Yi(this,"remove",(function(t){return r._observers.splice(r._observers.findIndex((function(e){return t===e.observer}),r._observers),1)})),this.__init__(e)})),Zi=function(t){var e=t(),r=e.config,n=e.isLoading,o=null==r?void 0:r.update_title;o&&(n?document.title!==o&&(tu.title=document.title,document.title=o):document.title===o?document.title=tu.title:tu.title=document.title)},tu={inputs:["isLoading"],mutationObserver:void 0,observer:function(t){var e=t.getState,r=e().config;if(tu.config!==r){var n;tu.config=r,null===(n=tu.mutationObserver)||void 0===n||n.disconnect(),tu.mutationObserver=new MutationObserver((function(){return Zi(e)}));var o=document.querySelector("title");o&&tu.mutationObserver.observe(o,{subtree:!0,childList:!0,attributes:!0,characterData:!0})}Zi(e)}},eu=tu,ru=nt((function(t){var e=[];for(var r in t)jt(r,t)&&(e[e.length]=[r,t[r]]);return e})),nu=Ot((function(t,e){for(var r={},n=0;n<t.length;)t[n]in e&&(r[t[n]]=e[t[n]]),n+=1;return r})),ou=r(311),iu=r.n(ou),uu=function(t){return"function"==typeof t},au=function(t){return t},cu=function(t){return null===t};function su(t,e,r){void 0===e&&(e=au),iu()(uu(e)||cu(e),"Expected payloadCreator to be a function, undefined or null");var n=cu(e)||e===au?au:function(t){for(var r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];return t instanceof Error?t:e.apply(void 0,[t].concat(n))},o=uu(r),i=t.toString(),u=function(){var e=n.apply(void 0,arguments),i={type:t};return e instanceof Error&&(i.error=!0),void 0!==e&&(i.payload=e),o&&(i.meta=r.apply(void 0,arguments)),i};return u.toString=function(){return i},u}var fu=r(221),lu=nt((function(t){return function(){return t}})),pu=function(t){return{value:t,map:function(e){return pu(e(t))}}},yu=Ft((function(t,e,r){return t((function(t){return pu(e(t))}))(r).value})),du=Ft((function(t,e,r){return yu(t,lu(e),r)}));function hu(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,u,a=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(a.push(n.value),a.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(u=r.return(),Object(u)!==u))return}finally{if(s)throw o}}return a}}(t,e)||function(t,e){if(t){if("string"==typeof t)return vu(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?vu(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function vu(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function bu(t){return bu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},bu(t)}function mu(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function gu(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,wu(n.key),n)}}function Ou(t,e,r){return e&&gu(t.prototype,e),r&&gu(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function wu(t){var e=function(t){if("object"!=bu(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=bu(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==bu(e)?e:e+""}var Su="_dash_persistence.";function ju(t){var e="string"==typeof t?new Error(t):t;return su("ON_ERROR")({type:"frontEnd",error:e})}function _u(t,e){var r=t+e,n=r.length;return function(e){return e===t||e.substr(0,n)===r}}var Eu=function(t){return"U"===t?void 0:JSON.parse(t||null)},Pu=function(t){return void 0===t?"U":JSON.stringify(t)},Au=function(){return Ou((function t(e){mu(this,t),this._name=e,this._storage=window[e]}),[{key:"hasItem",value:function(t){return null!==this._storage.getItem(Su+t)}},{key:"getItem",value:function(t){return Eu(this._storage.getItem(Su+t))}},{key:"_setItem",value:function(t,e){this._storage.setItem(Su+t,Pu(e))}},{key:"setItem",value:function(t,e,r){try{this._setItem(t,e)}catch(e){r(ju("".concat(t," failed to save in ").concat(this._name,". Persisted props may be lost.")))}}},{key:"removeItem",value:function(t){this._storage.removeItem(Su+t)}},{key:"clear",value:function(t){for(var e=this,r=_u(Su+(t||""),t?".":""),n=[],o=0;o<this._storage.length;o++){var i=this._storage.key(o);r(i)&&n.push(i)}re((function(t){return e._storage.removeItem(t)}),n)}}])}(),ku={memory:new(function(){return Ou((function t(){mu(this,t),this._data={}}),[{key:"hasItem",value:function(t){return t in this._data}},{key:"getItem",value:function(t){return Eu(this._data[t])}},{key:"setItem",value:function(t,e){this._data[t]=Pu(e)}},{key:"removeItem",value:function(t){delete this._data[t]}},{key:"clear",value:function(t){var e=this;t?re((function(t){return delete e._data[t]}),we(_u(t,"."),Ct(this._data))):this._data={}}}])}())},xu={local:"localStorage",session:"sessionStorage"};function Tu(t,e){return ku[t]||(ku[t]=function(t,e){var r=new Au(t),n=ku.memory,o=function(){for(var t="Spam",e=2;e<16;e++)t+=t;return t}(),i=Su+"x.x";try{return r._setItem(i,o),r.getItem(i)!==o?(e(ju("".concat(t," init failed set/get, falling back to memory"))),n):(r.removeItem(i),r)}catch(r){e(ju("".concat(t," init first try failed; clearing and retrying")))}try{if(r.clear(),r._setItem(i,o),r.getItem(i)!==o)throw new Error("nope");return r.removeItem(i),e(ju("".concat(t," init set/get succeeded after clearing!"))),r}catch(r){return e(ju("".concat(t," init still failed, falling back to memory"))),n}}(xu[t],e)),ku[t]}var Iu={extract:function(t){return t},apply:function(t,e){return t}},Cu=function(t,e,r){return t.persistenceTransforms&&t.persistenceTransforms[e]?r?t.persistenceTransforms[e][r]:t.persistenceTransforms[e]:Iu},Ru=function(t,e,r){return"".concat(Jn(t),".").concat(e,".").concat(JSON.stringify(r))},Du=function(t){var e=t.props,r=t.type,n=t.namespace;if(!r||!n)return{props:e};var o=e.id,i=e.persistence,u=An(t),a=function(t){return e[t]||(u.defaultProps||u.dashPersistence||{})[t]},c=a("persisted_props"),s=a("persistence_type");return{canPersist:o&&c&&s,id:o,props:e,element:u,persistence:i,persisted_props:c,persistence_type:s}};function Nu(t,e){return Array.isArray(t)?t.map((function(t){return qi(t)?Uu(t,t,[],e):t})):Uu(t,t,[],e)}function Mu(t,e,r,n,o,i,u){if(e.hasItem(t)){var a=hu(e.getItem(t),2),c=a[0],s=a[1],f=u?c:s,l=u?s:c,p=hu(o.split("."),2),y=p[0],d=p[1],h=Cu(r,y,d);Ut(f,h.extract(n[y]))?i[y]=h.apply(l,y in i?i[y]:n[y]):e.removeItem(t)}}function Uu(t,e,r,n){var o=Du(e),i=o.canPersist,u=o.id,a=o.props,c=o.element,s=o.persistence,f=o.persisted_props,l=o.persistence_type,p=t;if(i&&s){var y=Tu(l,n),d={};for(var h in re((function(t){return Mu(Ru(u,t,s),y,c,a,t,d)}),f),d)p=du(Si(r.concat("props",h)),d[h],p)}var v=a.children;return Array.isArray(v)?v.forEach((function(t,e){"Object"===Rt(t)&&t.props&&(p=Uu(p,t,r.concat("props","children",e),n))})):"Object"===Rt(v)&&v.props&&(p=Uu(p,v,r.concat("props","children"),n)),p}function Lu(){var t,e,r="function"==typeof Symbol?Symbol:{},n=r.iterator||"@@iterator",o=r.toStringTag||"@@toStringTag";function i(r,n,o,i){var c=n&&n.prototype instanceof a?n:a,s=Object.create(c.prototype);return qu(s,"_invoke",function(r,n,o){var i,a,c,s=0,f=o||[],l=!1,p={p:0,n:0,v:t,a:y,f:y.bind(t,4),d:function(e,r){return i=e,a=0,c=t,p.n=r,u}};function y(r,n){for(a=r,c=n,e=0;!l&&s&&!o&&e<f.length;e++){var o,i=f[e],y=p.p,d=i[2];r>3?(o=d===n)&&(c=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=t):i[0]<=y&&((o=r<2&&y<i[1])?(a=0,p.v=n,p.n=i[1]):y<d&&(o=r<3||i[0]>n||n>d)&&(i[4]=r,i[5]=n,p.n=d,a=0))}if(o||r>1)return u;throw l=!0,n}return function(o,f,d){if(s>1)throw TypeError("Generator is already running");for(l&&1===f&&y(f,d),a=f,c=d;(e=a<2?t:c)||!l;){i||(a?a<3?(a>1&&(p.n=-1),y(a,c)):p.n=c:p.v=c);try{if(s=2,i){if(a||(o="next"),e=i[o]){if(!(e=e.call(i,c)))throw TypeError("iterator result is not an object");if(!e.done)return e;c=e.value,a<2&&(a=0)}else 1===a&&(e=i.return)&&e.call(i),a<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),a=1);i=t}else if((e=(l=p.n<0)?c:r.call(n,p))!==u)break}catch(e){i=t,a=1,c=e}finally{s=1}}return{value:e,done:l}}}(r,o,i),!0),s}var u={};function a(){}function c(){}function s(){}e=Object.getPrototypeOf;var f=[][n]?e(e([][n]())):(qu(e={},n,(function(){return this})),e),l=s.prototype=a.prototype=Object.create(f);function p(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,s):(t.__proto__=s,qu(t,o,"GeneratorFunction")),t.prototype=Object.create(l),t}return c.prototype=s,qu(l,"constructor",s),qu(s,"constructor",c),c.displayName="GeneratorFunction",qu(s,o,"GeneratorFunction"),qu(l),qu(l,o,"Generator"),qu(l,n,(function(){return this})),qu(l,"toString",(function(){return"[object Generator]"})),(Lu=function(){return{w:i,m:p}})()}function qu(t,e,r,n){var o=Object.defineProperty;try{o({},"",{})}catch(t){o=0}qu=function(t,e,r,n){if(e)o?o(t,e,{value:r,enumerable:!n,configurable:!n,writable:!n}):t[e]=r;else{var i=function(e,r){qu(t,e,(function(t){return this._invoke(e,r,t)}))};i("next",0),i("throw",1),i("return",2)}},qu(t,e,r,n)}function Bu(t,e,r,n,o,i,u){try{var a=t[i](u),c=a.value}catch(t){return void r(t)}a.done?e(c):Promise.resolve(c).then(n,o)}var Gu=su(xo("ON_ERROR")),Fu=su(xo("SET_APP_LIFECYCLE")),Hu=su(xo("SET_CONFIG")),$u=su(xo("ADD_HTTP_HEADERS")),Ju=su(xo("SET_GRAPHS")),zu=su(xo("SET_HOOKS")),Wu=su(xo("SET_LAYOUT")),Vu=su(xo("SET_PATHS")),Ku=(su(xo("SET_REQUEST_QUEUE")),su(xo("INSERT_COMPONENT"))),Yu=su(xo("REMOVE_COMPONENT")),Qu=su(xo("ON_PROP_CHANGE"));function Xu(t){return function(e,r){!function(t,e,r){var n=Du(t),o=n.canPersist,i=n.id,u=n.props,a=n.element,c=n.persistence,s=n.persisted_props,f=n.persistence_type,l=void 0!==(null==e?void 0:e.persistence)&&e.persistence!==c;o&&c&&!l&&re((function(t){var n=hu(t.split("."),2),o=n[0],s=n[1];if(void 0!==e[o]){var l=Tu(f,r),p=Cu(a,o,s).extract,y=Ru(i,t,c),d=p(u[o]),h=p(e[o]);if(d!==h){l.hasItem(y)&&(d=l.getItem(y)[1]);var v=void 0===d?[h]:[h,d];l.setItem(y,v,r)}}}),s)}(ne(t.itempath,r().layout),t.props,e),e(Qu(t))}}var Zu=function(t){return function(e,r){return t(Gu({type:"backEnd",error:{message:e,html:r.join("\n")}}))}};var ta=it(console.warn);function ea(){try{return{"X-CSRFToken":fu.qg(document.cookie)._csrf_token}}catch(t){return ta(t),{}}}var ra=ia("REDO"),na=ia("UNDO"),oa=ia("REVERT");function ia(t){return function(e,r){var n=r(),o=n.history,i=n.paths;e(su(t)());var u=("REDO"===t?o.future[0]:o.past[o.past.length-1])||{},a=u.id,c=u.props;a&&(e(su("UNDO_PROP_CHANGE")({itempath:Pn(i,a),props:c})),e(ua({id:a,props:c})))}}function ua(t){var e=t.id,r=t.props;return function(){var t,n=(t=Lu().m((function t(n,o){var i,u,a;return Lu().w((function(t){for(;;)switch(t.n){case 0:i=o(),u=i.graphs,a=i.paths,n(tc(_o(e,r,u,a)));case 1:return t.a(2)}}),t)})),function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function u(t){Bu(i,n,o,u,a,"next",t)}function a(t){Bu(i,n,o,u,a,"throw",t)}u(void 0)}))});return function(t,e){return n.apply(this,arguments)}}()}function aa(t,e,r){if(t&&"function"==typeof t.text)t.text().then((function(t){r(Gu({type:"backEnd",error:{message:e,html:t}}))}));else{var n=t instanceof Error?t:{message:e,html:t};r(Gu({type:"backEnd",error:n}))}}function ca(t){return ca="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ca(t)}var sa=Ot((function(t,e){return e instanceof t||null!=e&&(e.constructor===t||"Object"===t.name&&"object"===ca(e))})),fa=Ot((function(t,e){return Mr([t],e)})),la=nt((function(t){return $t(t)?t.split("").reverse().join(""):Array.prototype.slice.call(t,0).reverse()}));function pa(t){return pa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},pa(t)}function ya(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function da(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ya(Object(r),!0).forEach((function(e){ha(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ya(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function ha(t,e,r){return(e=function(t){var e=function(t){if("object"!=pa(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=pa(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==pa(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function va(t,e){return sa(Number,t)&&t<0?e.length+t:t}function ba(t,e){for(var r=[],n=0;n<t.length;n++){var o=va(t[n],ne(r,e));r.push(o)}return r}var ma={Assign:function(t,e){var r=e.params,n=e.location;return Xt(n,r.value,t)},Merge:function(t,e){var r=ne(e.location,t);return Xt(e.location,da(da({},r),e.params.value),t)},Extend:function(t,e){var r=ne(e.location,t);return Xt(e.location,tr(r,e.params.value),t)},Delete:function(t,e){return _i(e.location,t)},Insert:function(t,e){var r=ne(e.location,t);return Xt(e.location,nn(va(e.params.index,r),e.params.value,r),t)},Append:function(t,e){var r=ne(e.location,t);return Xt(e.location,yn(e.params.value,r),t)},Prepend:function(t,e){var r=ne(e.location,t);return Xt(e.location,fa(e.params.value,r),t)},Add:function(t,e){var r=ne(e.location,t);return Xt(e.location,r+e.params.value,t)},Sub:function(t,e){var r=ne(e.location,t);return Xt(e.location,r-e.params.value,t)},Mul:function(t,e){var r=ne(e.location,t);return Xt(e.location,r*e.params.value,t)},Div:function(t,e){var r=ne(e.location,t);return Xt(e.location,r/e.params.value,t)},Clear:function(t,e){var r=ne(e.location,t);return Xt(e.location,ue(r),t)},Reverse:function(t,e){var r=ne(e.location,t);return Xt(e.location,la(r),t)},Remove:function(t,e){var r=ne(e.location,t);return Xt(e.location,r.filter((function(t){return!Ut(t,e.params.value)})),t)}};function ga(t,e){for(var r=t,n=0;n<e.operations.length;n++){var o=e.operations[n];o.location=ba(o.location,r);var i=ma[o.operation];if(!i)throw new Error("Invalid Operation ".concat(o.operation));r=i(r,o)}return r}function Oa(){var t,e,r="function"==typeof Symbol?Symbol:{},n=r.iterator||"@@iterator",o=r.toStringTag||"@@toStringTag";function i(r,n,o,i){var c=n&&n.prototype instanceof a?n:a,s=Object.create(c.prototype);return wa(s,"_invoke",function(r,n,o){var i,a,c,s=0,f=o||[],l=!1,p={p:0,n:0,v:t,a:y,f:y.bind(t,4),d:function(e,r){return i=e,a=0,c=t,p.n=r,u}};function y(r,n){for(a=r,c=n,e=0;!l&&s&&!o&&e<f.length;e++){var o,i=f[e],y=p.p,d=i[2];r>3?(o=d===n)&&(c=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=t):i[0]<=y&&((o=r<2&&y<i[1])?(a=0,p.v=n,p.n=i[1]):y<d&&(o=r<3||i[0]>n||n>d)&&(i[4]=r,i[5]=n,p.n=d,a=0))}if(o||r>1)return u;throw l=!0,n}return function(o,f,d){if(s>1)throw TypeError("Generator is already running");for(l&&1===f&&y(f,d),a=f,c=d;(e=a<2?t:c)||!l;){i||(a?a<3?(a>1&&(p.n=-1),y(a,c)):p.n=c:p.v=c);try{if(s=2,i){if(a||(o="next"),e=i[o]){if(!(e=e.call(i,c)))throw TypeError("iterator result is not an object");if(!e.done)return e;c=e.value,a<2&&(a=0)}else 1===a&&(e=i.return)&&e.call(i),a<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),a=1);i=t}else if((e=(l=p.n<0)?c:r.call(n,p))!==u)break}catch(e){i=t,a=1,c=e}finally{s=1}}return{value:e,done:l}}}(r,o,i),!0),s}var u={};function a(){}function c(){}function s(){}e=Object.getPrototypeOf;var f=[][n]?e(e([][n]())):(wa(e={},n,(function(){return this})),e),l=s.prototype=a.prototype=Object.create(f);function p(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,s):(t.__proto__=s,wa(t,o,"GeneratorFunction")),t.prototype=Object.create(l),t}return c.prototype=s,wa(l,"constructor",s),wa(s,"constructor",c),c.displayName="GeneratorFunction",wa(s,o,"GeneratorFunction"),wa(l),wa(l,o,"Generator"),wa(l,n,(function(){return this})),wa(l,"toString",(function(){return"[object Generator]"})),(Oa=function(){return{w:i,m:p}})()}function wa(t,e,r,n){var o=Object.defineProperty;try{o({},"",{})}catch(t){o=0}wa=function(t,e,r,n){if(e)o?o(t,e,{value:r,enumerable:!n,configurable:!n,writable:!n}):t[e]=r;else{var i=function(e,r){wa(t,e,(function(t){return this._invoke(e,r,t)}))};i("next",0),i("throw",1),i("return",2)}},wa(t,e,r,n)}function Sa(t,e,r,n,o,i,u){try{var a=t[i](u),c=a.value}catch(t){return void r(t)}a.done?e(c):Promise.resolve(c).then(n,o)}var ja=it(console.warn),_a={GET:function(t,e){return fetch(t,$o(e,{method:"GET",headers:ea()}))},POST:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return fetch(t,$o(e,{method:"POST",headers:ea(),body:r?JSON.stringify(r):null}))}};function Ea(t,e,r,n,o){return function(){var i,u=(i=Oa().m((function i(u,a){var c,s,f,l,p,y,d,h,v,b,m;return Oa().w((function(i){for(;;)switch(i.n){case 0:y=function(t){a().error.backEndConnected!==t&&u({type:"SET_CONNECTION_STATUS",payload:t})},c=a(),s=c.config,f=c.hooks,l=null,p="".concat(On(s)).concat(t),u({type:r,payload:{id:n,status:"loading"}}),i.p=1,h=0;case 2:if(!(h<=1)){i.n=11;break}return i.p=3,i.n=4,_a[e](p,s.fetch,o);case 4:d=i.v,i.n=6;break;case 5:return i.p=5,i.v,console.log("fetch error",d),y(!1),i.a(2);case 6:if(401!==d.status&&400!==d.status){i.n=9;break}if(!f.request_refresh_jwt){i.n=9;break}return i.n=7,d.text();case 7:if(!i.v.includes(ci)){i.n=9;break}return i.n=8,f.request_refresh_jwt(s.fetch.headers.Authorization.substr(7));case 8:if(!(v=i.v)){i.n=9;break}return l={Authorization:"Bearer ".concat(v)},s=$o(s,{fetch:{headers:l}}),i.a(3,10);case 9:return i.a(3,11);case 10:h++,i.n=2;break;case 11:if(b=d.headers.get("content-type"),l&&u($u(l)),y(!0),!b||-1===b.indexOf("application/json")){i.n=12;break}return i.a(2,d.json().then((function(t){return u({type:r,payload:{status:d.status,content:t,id:n}}),t})));case 12:return i.n=13,d.text();case 13:return m=i.v,ja("Response is missing header: content-type: application/json"),i.a(2,u({type:r,payload:{id:n,status:d.status,content:m}}));case 14:i.p=14,aa(i.v,"Error from API call: "+t,u);case 15:return i.a(2)}}),i,null,[[3,5],[1,14]])})),function(){var t=this,e=arguments;return new Promise((function(r,n){var o=i.apply(t,e);function u(t){Sa(o,r,n,u,a,"next",t)}function a(t){Sa(o,r,n,u,a,"throw",t)}u(void 0)}))});return function(t,e){return u.apply(this,arguments)}}()}function Pa(t){return Pa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Pa(t)}function Aa(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ka(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Aa(Object(r),!0).forEach((function(e){xa(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Aa(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function xa(t,e,r){return(e=function(t){var e=function(t){if("object"!=Pa(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=Pa(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Pa(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Ta(t){var e,r=document.querySelector("head");if("_js_dist"===t.type){var n=document.createElement("script");n.src=t.url,n.async=!0,e=new Promise((function(t,e){n.onload=function(){t()},n.onerror=function(t){return e(t)}})),null==r||r.appendChild(n)}else if("_css_dist"===t.type){var o=document.createElement("link");o.href=t.url,o.rel="stylesheet",e=new Promise((function(t,e){o.onload=function(){t()},o.onerror=function(t){return e(t)}})),null==r||r.appendChild(o)}return e}function Ia(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function Ca(t){var e,r,n=t.lastIndexOf("}");return n+2<t.length?(r=t.substring(n+2),e=JSON.parse(t.substring(0,n+1))):e=JSON.parse(t),[e,r]}function Ra(t,e,r){var n=Ct(t),o=n.join(",");return e.paths.objs[o].map((function(t){return n.reduce((function(e,r,n){return e[r]=t.values[n],e}),{})})).filter((function(e){return Ut(Ci(r,e),Ci(r,t))}))}var Da=su("LOADING"),Na=su("LOADED");function Ma(t){return Ma="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ma(t)}function Ua(t){if(null!=t){var e=t["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],r=0;if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length))return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}}throw new TypeError(Ma(t)+" is not iterable")}function La(){var t,e,r="function"==typeof Symbol?Symbol:{},n=r.iterator||"@@iterator",o=r.toStringTag||"@@toStringTag";function i(r,n,o,i){var c=n&&n.prototype instanceof a?n:a,s=Object.create(c.prototype);return qa(s,"_invoke",function(r,n,o){var i,a,c,s=0,f=o||[],l=!1,p={p:0,n:0,v:t,a:y,f:y.bind(t,4),d:function(e,r){return i=e,a=0,c=t,p.n=r,u}};function y(r,n){for(a=r,c=n,e=0;!l&&s&&!o&&e<f.length;e++){var o,i=f[e],y=p.p,d=i[2];r>3?(o=d===n)&&(c=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=t):i[0]<=y&&((o=r<2&&y<i[1])?(a=0,p.v=n,p.n=i[1]):y<d&&(o=r<3||i[0]>n||n>d)&&(i[4]=r,i[5]=n,p.n=d,a=0))}if(o||r>1)return u;throw l=!0,n}return function(o,f,d){if(s>1)throw TypeError("Generator is already running");for(l&&1===f&&y(f,d),a=f,c=d;(e=a<2?t:c)||!l;){i||(a?a<3?(a>1&&(p.n=-1),y(a,c)):p.n=c:p.v=c);try{if(s=2,i){if(a||(o="next"),e=i[o]){if(!(e=e.call(i,c)))throw TypeError("iterator result is not an object");if(!e.done)return e;c=e.value,a<2&&(a=0)}else 1===a&&(e=i.return)&&e.call(i),a<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),a=1);i=t}else if((e=(l=p.n<0)?c:r.call(n,p))!==u)break}catch(e){i=t,a=1,c=e}finally{s=1}}return{value:e,done:l}}}(r,o,i),!0),s}var u={};function a(){}function c(){}function s(){}e=Object.getPrototypeOf;var f=[][n]?e(e([][n]())):(qa(e={},n,(function(){return this})),e),l=s.prototype=a.prototype=Object.create(f);function p(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,s):(t.__proto__=s,qa(t,o,"GeneratorFunction")),t.prototype=Object.create(l),t}return c.prototype=s,qa(l,"constructor",s),qa(s,"constructor",c),c.displayName="GeneratorFunction",qa(s,o,"GeneratorFunction"),qa(l),qa(l,o,"Generator"),qa(l,n,(function(){return this})),qa(l,"toString",(function(){return"[object Generator]"})),(La=function(){return{w:i,m:p}})()}function qa(t,e,r,n){var o=Object.defineProperty;try{o({},"",{})}catch(t){o=0}qa=function(t,e,r,n){if(e)o?o(t,e,{value:r,enumerable:!n,configurable:!n,writable:!n}):t[e]=r;else{var i=function(e,r){qa(t,e,(function(t){return this._invoke(e,r,t)}))};i("next",0),i("throw",1),i("return",2)}},qa(t,e,r,n)}function Ba(t,e,r,n,o,i,u){try{var a=t[i](u),c=a.value}catch(t){return void r(t)}a.done?e(c):Promise.resolve(c).then(n,o)}function Ga(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function u(t){Ba(i,n,o,u,a,"next",t)}function a(t){Ba(i,n,o,u,a,"throw",t)}u(void 0)}))}}function Fa(t){return function(t){if(Array.isArray(t))return Ja(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||$a(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ha(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,u,a=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(a.push(n.value),a.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(u=r.return(),Object(u)!==u))return}finally{if(s)throw o}}return a}}(t,e)||$a(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $a(t,e){if(t){if("string"==typeof t)return Ja(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ja(t,e):void 0}}function Ja(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function za(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Wa(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?za(Object(r),!0).forEach((function(e){Va(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):za(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Va(t,e,r){return(e=function(t){var e=function(t){if("object"!=Ma(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=Ma(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Ma(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Ka=su(Io.AddBlocked),Ya=su(Co.AddCompleted),Qa=su(Io.AddExecuted),Xa=su(Io.AddExecuting),Za=su(Io.AddPrioritized),tc=su(Io.AddRequested),ec=su(Io.AddStored),rc=su(Io.AddWatched),nc=su(Io.RemoveExecuted),oc=su(Io.RemoveBlocked),ic=su(Io.RemoveExecuting),uc=su(Io.RemovePrioritized),ac=su(Io.RemoveRequested),cc=su(Io.RemoveStored),sc=su(Io.RemoveWatched),fc=su(Co.Aggregate),lc=su("UPDATE_RESOURCE_USAGE"),pc=su("ADD_CALLBACK_JOB"),yc=su("REMOVE_CALLBACK_JOB"),dc=su("CALLBACK_JOB_OUTDATED");function hc(t,e,r,n,o){var i="";if(ro(r))return[e,i];if(1!==e.length)if(e.length)i="Multiple objects were found for an `"+o+"` of a callback that only takes one value. The id spec is "+JSON.stringify(r.id)+(n?" with MATCH values "+n:"")+" and the property is `"+r.property+"`. The objects we found are: "+JSON.stringify(Re(nu(["id","property"]),e));else if(r.allow_optional)e=[Wa(Wa({},r),{},{value:null})],i="";else{var u="string"==typeof r.id;i="A nonexistent object was used in an `"+o+"` of a Dash callback. The id of this object is "+(u?"`"+r.id+"`":JSON.stringify(r.id)+(n?" with MATCH values "+n:""))+" and the property is `"+r.property+(u?"`. The string ids in the current layout are: ["+Ct(t.strs).join(", ")+"]":"`. The wildcard ids currently available are logged above.")}return[e[0],i]}function vc(t,e,r,n,o){var i=arguments.length>5&&void 0!==arguments[5]&&arguments[5],u="Input"===o?r.getInputs:r.getState,a=[],c=0,s=u(t).map((function(i,u){var s=Ha(hc(t,i.map((function(t){var r=t.id,n=t.property,o=t.path;return{id:r,property:n,value:ne([].concat(Fa(o),["props",n]),e)}})),n[u],r.anyVals,o),2),f=s[0],l=s[1];return ro(n[u])&&!f.length&&c++,l&&a.push(l),f}));if(a.length){if(i&&a.length+c===s.length)return null;bc(a,t)}return s}function bc(t,e){var r=t[0];throw-1!==r.indexOf("logged above")&&console.error(e.objs),new ReferenceError(r)}var mc=function(t){return Array.isArray(t)?cr("value",t):t.value},gc=function(t,e){return Array.isArray(t)?Er(t,e):[[t,e]]};function Oc(t){return t.split("@")[0]}function wc(t,e,r,n){return Sc.apply(this,arguments)}function Sc(){return Sc=Ga(La().m((function t(e,r,n,o){var i,u,a,c,s,f,l,p,y,d,h,v,b,m,g,O,w,S;return La().w((function(t){for(;;)switch(t.n){case 0:if((i=window.dash_clientside=window.dash_clientside||{}).no_update||(Object.defineProperty(i,"no_update",{value:{description:"Return to prevent updating an Output."},writable:!1}),Object.defineProperty(i,"PreventUpdate",{value:{description:"Throw to prevent updating all Outputs."},writable:!1})),u=o.inputs,a=o.outputs,c=o.state,s=Date.now(),f=Ec(u),l=Ec(c),p={},y=si,t.p=1,v=r.namespace,b=r.function_name,m=u.map(mc),c&&(m=tr(m,c.map(mc))),i.callback_context={},i.callback_context.triggered=o.changedPropIds.map((function(t){return{prop_id:t,value:f[t]}})),i.callback_context.triggered_id=Pc(o.changedPropIds),i.callback_context.inputs_list=u,i.callback_context.inputs=f,i.callback_context.states_list=c,i.callback_context.states=l,i.callback_context.outputs_list=a,g=(d=i[v])[b].apply(d,Fa(m)),delete i.callback_context,"function"!=typeof(null===(h=g)||void 0===h?void 0:h.then)){t.n=3;break}return t.n=2,g;case 2:g=t.v;case 3:a&&gc(a,g).forEach((function(t){var e=Ha(t,2),r=e[0],n=e[1];gc(r,n).forEach((function(t){var e=Ha(t,2),r=e[0],n=e[1],o=r.id,u=r.property,a=Jn(o),c=p[a]=p[a]||{};n!==i.no_update&&(c[Oc(u)]=n)}))})),t.n=6;break;case 4:if(t.p=4,(S=t.v)!==i.PreventUpdate){t.n=5;break}y=204,t.n=6;break;case 5:throw y="CLIENTSIDE_ERROR",S;case 6:return t.p=6,delete i.callback_context,O=Date.now()-s,w={__dash_server:O,__dash_client:O,__dash_upload:0,__dash_download:0},n.ui&&e(lc({id:o.output,usage:w,status:y,result:p,inputs:u,state:c})),t.f(6);case 7:return t.a(2,p)}}),t,null,[[1,4,6,7]])}))),Sc.apply(this,arguments)}function jc(t,e){return function(r,n){ru(t).reduce((function(t,r,o){var i,u=Ha(r,2),a=u[0],c=u[1],s=a,f=[];if(a.startsWith("{")){var l=Ha(Ca(a),2);s=l[0],i=l[1],f=function(t,e,r,n){var o=[],i={};return ru(t).forEach((function(u){var a,c,s=(c=2,function(t){if(Array.isArray(t))return t}(a=u)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,u,a=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(a.push(n.value),a.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(u=r.return(),Object(u)!==u))return}finally{if(s)throw o}}return a}}(a,c)||function(t,e){if(t){if("string"==typeof t)return Ia(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ia(t,e):void 0}}(a,c)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),f=s[0],l=s[1];if(!o.length)if(Array.isArray(l)){var p=(e.parsedChangedPropsIds[r]||e.parsedChangedPropsIds[0])[f];l.includes("MATCH")?i[f]=p:l.includes("ALL")?o=Ra(t,n(),f):l.includes("ALLSMALLER")&&(o=Ra(t,n(),f).filter((function(t){return t[f]<p})))}else i[f]=l})),o.length?o:[i]}(s,e,o,n)}else if(a.includes(".")){var p=Ha(a.split("."),2);s=p[0],i=p[1]}var y=i?{[i]:c}:c;return 0===f.length?t.push([s,y]):1===f.length?t.push([f[0],y]):f.forEach((function(e){t.push([e,y])})),t}),[]).forEach((function(t){var n=Ha(t,2),o=n[0],i=n[1];r(function(t,e,r){return function(n,o){var i=o(),u=i.paths,a=i.config,c=Pn(u,t);c?(n(Xu({props:e,itempath:c,renderType:"callback"})),n(ua({id:t,props:e}))):a.suppress_callback_exceptions||Zu(n)("ID running component not found in layout",["Component defined in running keyword not found in layout.",'Component id: "'.concat(Jn(t),'"'),"This ID was used in the callback(s) for Output(s):","".concat(r.output),"You can suppress this exception by setting","`suppress_callback_exceptions=True`."])}}(o,i,e))}))}}function _c(t,e,r,n,o,i,u,a){e.request_pre&&e.request_pre(n);var c,s,f,l,p=Date.now(),y=JSON.stringify(n),d=i;return a&&(t(jc(a.running,n)),f=a.runningOff),new Promise((function(i,a){var h=function(d){var h=d.status;if(s){var v=u().callbackJobs[s];if(null!=v&&v.outdated)return t(yc({jobId:s})),i({})}function m(e){if(r.ui){var o={__dash_server:0,__dash_client:Date.now()-p,__dash_upload:y.length,__dash_download:Number(d.headers.get("Content-Length"))};(d.headers.get("Server-Timing")||"").split(",").forEach((function(t){var e=t.split(";")[0],r=t.match(/;dur=[0-9.]+/);r&&(o[e]=Number(r[0].slice(5)))})),t(lc({id:n.output,usage:o,status:h,result:e,inputs:n.inputs,state:n.state}))}}var g=function(t){var r,o=t.multi,u=t.response;if(e.request_post&&e.request_post(n,u),o)r=u;else{var a=n.output,c=a.substr(0,a.lastIndexOf("."));r={[c]:u.props}}m(r),i(r)},O=function(){s&&t(yc({jobId:s})),f&&t(jc(f,n)),l&&t(jc(l,n))};h===si?d.json().then((function(e){if(!c&&e.cacheKey&&(c=e.cacheKey),!s&&e.job){var r={jobId:e.job,cacheKey:e.cacheKey,cancelInputs:e.cancel,progressDefault:e.progressDefault,output:JSON.stringify(n.outputs)};t(pc(r)),s=e.job}e.sideUpdate&&t(jc(e.sideUpdate,n)),e.progress&&t(jc(e.progress,n)),!l&&e.progressDefault&&(l=e.progressDefault),o&&void 0===e.response?setTimeout(b,void 0!==o.interval?o.interval:500):e.dist?Promise.all(e.dist.map(Ta)).then((function(){O(),g(e)})):(O(),g(e))})):204===h?(O(),m({}),i({})):(O(),a(d))},v=function(){r.ui&&t(lc({id:n.output,status:"NO_RESPONSE",result:{},inputs:n.inputs,state:n.state})),a(new Error("Callback failed: the server did not respond."))},b=function(){(function(){var t=ea(),e="".concat(On(r),"_dash-update-component"),n=y,o=function(t,r){var n="?";e.includes("?")&&(n="&"),e="".concat(e).concat(n).concat(t,"=").concat(r)};if(c||s){c&&o("cacheKey",c),s&&o("job",s);for(var i=JSON.parse(n),u=0;u<i.inputs.length;u++)i.inputs[u].value=null;for(var a=0;a<((null==i?void 0:i.state)||[]).length;a++)i.state[a].value=null;n=JSON.stringify(i)}return d&&(d.forEach((function(t){var e=Ha(t,2),r=e[0],n=e[1];return o(r,n)})),d=d.filter((function(t){var e=Ha(t,3);return e[0],e[1],!e[2]}))),fetch(e,$o(r.fetch,{method:"POST",headers:t,body:n}))})().then(h,v)};b()}))}function Ec(t){if(!t)return{};for(var e={},r=0;r<t.length;r++){var n;if(Array.isArray(t[r]))for(var o=t[r],i=0;i<o.length;i++){var u;e["".concat(Jn(o[i].id),".").concat(o[i].property)]=null!==(u=o[i].value)&&void 0!==u?u:null}else e["".concat(Jn(t[r].id),".").concat(t[r].property)]=null!==(n=t[r].value)&&void 0!==n?n:null}return e}function Pc(t){if(t&&t.length){var e=t[0];return e.startsWith("{")?JSON.parse(e.substring(0,e.lastIndexOf("}")+1)):e.split(".")[0]}}function Ac(t,e,r,o,i,u,a,c){var s=u.allOutputs,f=t.callback,l=f.output,p=f.inputs,y=f.state,d=f.clientside_function,h=f.background,v=f.dynamic_creator;try{var b=vc(o,i,t,p,"Input",!0);if(null===b)return Wa(Wa({},t),{},{executionPromise:null});var m=[],g=[];if(s.forEach((function(e,r){var n=Ha(hc(o,Re(nu(["id","property"]),e),t.callback.outputs[r],t.anyVals,"Output"),2),i=n[0],u=n[1];m.push(i),u&&g.push(u)})),g.length)return Ee(b).length&&bc(g,o),Wa(Wa({},t),{},{executionPromise:null});var O=function(){var u=Ga(La().m((function u(){var s,f,p,g,O,w,S,j,_,E,P,A,k,x,T;return La().w((function(u){for(;;)switch(u.n){case 0:if(s=m.map((function(t){var e;return{path:Pn(o,t.id),property:null===(e=t.property)||void 0===e?void 0:e.split("@")[0],id:Jn(t.id)}})),a(Da(s)),u.p=1,f=Ct(t.changedPropIds),p=f.map((function(t){return t.startsWith("{")?Ca(t)[0]:t})),g={output:l,outputs:Nn(l)?m:m[0],inputs:b,changedPropIds:f,parsedChangedPropsIds:p,state:t.callback.state.length?vc(o,i,t,y,"State"):void 0},!d){u.n=5;break}return u.p=2,u.n=3,wc(a,d,e,g);case 3:return O=u.v,u.a(2,{data:O,payload:g});case 4:return u.p=4,x=u.v,u.a(2,{error:x,payload:g});case 5:w=e,S=null,_=[],E=JSON.stringify(g.outputs),Rr(c().callbackJobs).forEach((function(e){E===e.output&&(_.push(["oldJob",e.jobId,!0]),a(dc({jobId:e.jobId}))),e.cancelInputs&&Cr(e.cancelInputs,t.callback.inputs).length&&(_.push(["cancelJob",e.jobId]),e.progressDefault&&a(jc(e.progressDefault,g)))})),P=La().m((function i(){var u,s,f,l,p;return La().w((function(i){for(;;)switch(i.n){case 0:return i.p=0,i.n=1,_c(a,r,w,g,h,_.length?_:void 0,c,t.callback.running);case 1:return u=i.v,S&&a($u(S)),s=c().layout,Ee(m).forEach((function(t){var e=Oc(t.property),r=Pn(o,t.id),n=ne(r.concat(["props",e]),s),i=[Jn(t.id),e],a=ne(i,u);if(pn("__dash_patch_update",a)){if(void 0===n)throw new Error("Cannot patch undefined");u=Xt(i,ga(n,a),u)}})),v&&setTimeout((function(){return a((function(t,e){(0,n.unstable_batchedUpdates)((function(){var r=e().graphs;t(Ju(ka(ka({},r),{},{reset:!0}))),t(Ea("_dash-dependencies","GET","dependenciesRequest"))}))}))}),0),i.a(2,{v:{data:u,payload:g}});case 2:if(i.p=2,p=i.v,j=p,!(k<=1)||401!==p.status&&400!==p.status){i.n=5;break}return i.n=3,p.text();case 3:if(!i.v.includes(ci)){i.n=5;break}if(null===r.request_refresh_jwt){i.n=5;break}return f=null,e.fetch.headers.Authorization&&(f=e.fetch.headers.Authorization.substr(7)),i.n=4,r.request_refresh_jwt(f);case 4:if(!(l=i.v)){i.n=5;break}return S={Authorization:"Bearer ".concat(l)},w=$o(e,{fetch:{headers:S}}),i.a(2,0);case 5:return i.a(2,1)}}),i,null,[[0,2]])})),k=0;case 6:if(!(k<=1)){u.n=11;break}return u.d(Ua(P()),7);case 7:if(0!==(A=u.v)){u.n=8;break}return u.a(3,10);case 8:if(1!==A){u.n=9;break}return u.a(3,11);case 9:if(!A){u.n=10;break}return u.a(2,A.v);case 10:k++,u.n=6;break;case 11:return u.a(2,{error:j,payload:null});case 12:return u.p=12,T=u.v,u.a(2,{error:T,payload:null});case 13:return u.p=13,a(Na(s)),u.f(13);case 14:return u.a(2)}}),u,null,[[2,4],[1,12,13,14]])})));return function(){return u.apply(this,arguments)}}();return Wa(Wa({},t),{},{executionPromise:O()})}catch(e){return Wa(Wa({},t),{},{executionPromise:{error:e,payload:null}})}}function kc(t){return kc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kc(t)}function xc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Tc(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?xc(Object(r),!0).forEach((function(e){Ic(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):xc(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ic(t,e,r){return(e=function(t){var e=function(t){if("object"!=kc(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=kc(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==kc(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Cc(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,u,a=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(a.push(n.value),a.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(u=r.return(),Object(u)!==u))return}finally{if(s)throw o}}return a}}(t,e)||function(t,e){if(t){if("string"==typeof t)return Rc(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Rc(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Rc(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var Dc={observer:function(t){var e=t.dispatch,r=t.getState,n=r().callbacks.executed;var o=[],i=[];n.forEach((function(t){var n,u=tr(null!==(n=t.predecessors)&&void 0!==n?n:[],[t.callback]),a=t.callback,c=a.clientside_function,s=a.output,f=t.executionResult;if(!Yt(f)){var l=f.data,p=f.error,y=f.payload;if(void 0!==l&&(Object.entries(l).forEach((function(t){var n=Cc(t,2),i=n[0],a=n[1],c=$n(i),s=r(),f=s.graphs,l=s.layout,p=s.paths,y=function(t,n){var o=r(),i=o.layout,u=Pn(o.paths,t);if(!u)return!1;n=function(t,e,r){var n=Du(t),o=n.canPersist,i=n.id,u=n.props,a=n.persistence,c=n.persisted_props,s=n.persistence_type,f=n.element,l=function(t,r){return t in e?e[t]:r},p=l("persistence",a);if(!o||!a&&!p)return e;var y=l("persistence_type",s),d=l("persisted_props",c),h=p!==a||y!==s||d!==c,v=function(t){return!(t.split(".")[0]in e)},b={},m=u;if(h&&a){var g=Tu(s,r);re((function(t){return Mu(Ru(i,t,a),g,f,u,t,b,!0)}),we(v,c)),m=zr(u,b)}if(p&&h){var O=Tu(y,r);re((function(t){return Mu(Ru(i,t,p),O,f,m,t,b)}),we(v,d))}return h?zr(e,b):e}(ne(u,i),n,e);var a=Nu({props:n},e).props;return e(Xu({itempath:u,props:a,source:"response",renderType:"callback"})),a}(c,a);o=tr(o,Ee(Re((function(t){return go(f,p,c,t,!0)}),Ct(a))).map((function(t){return Tc(Tc({},t),{},{predecessors:u})})));var d=Pn(p,c);if(d){var h=ne(d,l),v=Vt("defaultValue",[h.namespace,h.type],window.__dashprivate_childrenProps),b=function(t,n,i){var a=arguments.length>3&&void 0!==arguments[3]&&arguments[3],c=En(t,i,r().paths);e(Vu(c)),o=tr(o,So(f,c,t,{chunkPath:i,filterRoot:a}).map((function(t){return Tc(Tc({},t),{},{predecessors:u})}))),o=tr(o,So(f,p,n,{removedArrayInputsOnly:!0,newPaths:c,chunkPath:i,filterRoot:a}).map((function(t){return Tc(Tc({},t),{},{predecessors:u})})))},m=!1;["children"].concat(v).forEach((function(t){if(!m)if(t.includes("[]")){var e=Cc(t.split("[]").map((function(t){return t.split(".").filter((function(t){return t}))})),1)[0];if(!ne(e,y))return;b(Tc(Tc({},h),{},{props:Tc(Tc({},h.props),y)}),h,d,Ct(y)),m=!0}else{var r=t.split("."),n=ne(r,y);if(!n)return;var o=tr(Pn(p,c),["props"].concat(r)),i=ne(o,l);b(n,i,o)}}));var g=br((function(t,e){return!(e in a)}),y);if(!ae(g)){var O=r(),w=O.graphs,S=O.paths;o=tr(o,_o(i,g,w,S).map((function(t){return Tc(Tc({},t),{},{predecessors:u})})))}}})),i.push(Tc(Tc({},t),{},{executionMeta:{allProps:Re(mo,Ee(t.getOutputs(r().paths))),updatedProps:Ee(Re((function(t){var e=Cc(t,2),r=e[0],n=e[1];return Re((function(t){return mo({id:r,property:t})}),Ct(n))}),ru(l)))}}))),void 0!==p){var d;if(t.callback.no_output){var h=Ct(t.changedPropIds).join(", ");d="Callback error with no output from input ".concat(h)}else{var v=y?Re(mo,Ee([y.outputs])).join(", "):s;d="Callback error updating ".concat(v)}if(c){var b=c.namespace,m=c.function_name;d+=" via clientside function ".concat(b,".").concat(m)}aa(p,d,e),i.push(Tc(Tc({},t),{},{executionMeta:{allProps:Re(mo,Ee(t.getOutputs(r().paths))),updatedProps:[]}}))}}})),e(fc([n.length?nc(n):null,n.length?Ya(n.length):null,i.length?ec(i):null,o.length?tc(o):null]))},inputs:["callbacks.executed"]},Nc=Dc;function Mc(t){return Mc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mc(t)}function Uc(){var t,e,r="function"==typeof Symbol?Symbol:{},n=r.iterator||"@@iterator",o=r.toStringTag||"@@toStringTag";function i(r,n,o,i){var c=n&&n.prototype instanceof a?n:a,s=Object.create(c.prototype);return Lc(s,"_invoke",function(r,n,o){var i,a,c,s=0,f=o||[],l=!1,p={p:0,n:0,v:t,a:y,f:y.bind(t,4),d:function(e,r){return i=e,a=0,c=t,p.n=r,u}};function y(r,n){for(a=r,c=n,e=0;!l&&s&&!o&&e<f.length;e++){var o,i=f[e],y=p.p,d=i[2];r>3?(o=d===n)&&(c=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=t):i[0]<=y&&((o=r<2&&y<i[1])?(a=0,p.v=n,p.n=i[1]):y<d&&(o=r<3||i[0]>n||n>d)&&(i[4]=r,i[5]=n,p.n=d,a=0))}if(o||r>1)return u;throw l=!0,n}return function(o,f,d){if(s>1)throw TypeError("Generator is already running");for(l&&1===f&&y(f,d),a=f,c=d;(e=a<2?t:c)||!l;){i||(a?a<3?(a>1&&(p.n=-1),y(a,c)):p.n=c:p.v=c);try{if(s=2,i){if(a||(o="next"),e=i[o]){if(!(e=e.call(i,c)))throw TypeError("iterator result is not an object");if(!e.done)return e;c=e.value,a<2&&(a=0)}else 1===a&&(e=i.return)&&e.call(i),a<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),a=1);i=t}else if((e=(l=p.n<0)?c:r.call(n,p))!==u)break}catch(e){i=t,a=1,c=e}finally{s=1}}return{value:e,done:l}}}(r,o,i),!0),s}var u={};function a(){}function c(){}function s(){}e=Object.getPrototypeOf;var f=[][n]?e(e([][n]())):(Lc(e={},n,(function(){return this})),e),l=s.prototype=a.prototype=Object.create(f);function p(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,s):(t.__proto__=s,Lc(t,o,"GeneratorFunction")),t.prototype=Object.create(l),t}return c.prototype=s,Lc(l,"constructor",s),Lc(s,"constructor",c),c.displayName="GeneratorFunction",Lc(s,o,"GeneratorFunction"),Lc(l),Lc(l,o,"Generator"),Lc(l,n,(function(){return this})),Lc(l,"toString",(function(){return"[object Generator]"})),(Uc=function(){return{w:i,m:p}})()}function Lc(t,e,r,n){var o=Object.defineProperty;try{o({},"",{})}catch(t){o=0}Lc=function(t,e,r,n){if(e)o?o(t,e,{value:r,enumerable:!n,configurable:!n,writable:!n}):t[e]=r;else{var i=function(e,r){Lc(t,e,(function(t){return this._invoke(e,r,t)}))};i("next",0),i("throw",1),i("return",2)}},Lc(t,e,r,n)}function qc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Bc(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?qc(Object(r),!0).forEach((function(e){Gc(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):qc(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Gc(t,e,r){return(e=function(t){var e=function(t){if("object"!=Mc(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=Mc(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Mc(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Fc(t,e,r,n,o,i,u){try{var a=t[i](u),c=a.value}catch(t){return void r(t)}a.done?e(c):Promise.resolve(c).then(n,o)}function Hc(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var $c={observer:function(t){var e,r,n=t.dispatch,o=t.getState,i=o().callbacks.executing,u=(e=pr((function(t){return t.executionPromise instanceof Promise}),i),r=2,function(t){if(Array.isArray(t))return t}(e)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,u,a=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(a.push(n.value),a.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(u=r.return(),Object(u)!==u))return}finally{if(s)throw o}}return a}}(e,r)||function(t,e){if(t){if("string"==typeof t)return Hc(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Hc(t,e):void 0}}(e,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),a=u[0],c=u[1];n(fc([i.length?ic(i):null,a.length?rc(a):null,c.length?Qa(c.map((function(t){return Zt("executionResult",t.executionPromise,t)}))):null])),a.forEach(function(){var t,e=(t=Uc().m((function t(e){var r,i,u,a,c,s;return Uc().w((function(t){for(;;)switch(t.n){case 0:return t.n=1,e.executionPromise;case 1:if(r=t.v,i=o(),u=i.callbacks.watched,a=i.appLifecycle,c=i.hooks.callback_resolved,a===To("HYDRATED")){t.n=2;break}return t.a(2);case 2:if(c&&c(e.callback,r),s=cn((function(t){return t===e||t.executionPromise===e.executionPromise}),u)){t.n=3;break}return t.a(2);case 3:n(fc([sc([s]),Qa([Bc(Bc({},s),{},{executionResult:r})])]));case 4:return t.a(2)}}),t)})),function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function u(t){Fc(i,n,o,u,a,"next",t)}function a(t){Fc(i,n,o,u,a,"throw",t)}u(void 0)}))});return function(t){return e.apply(this,arguments)}}())},inputs:["callbacks.executing"]},Jc=$c,zc=Ot((function(t,e){for(var r={},n={},o=0,i=t.length;o<i;)n[t[o]]=1,o+=1;for(var u in e)n.hasOwnProperty(u)||(r[u]=e[u]);return r}));function Wc(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var Vc=function(t){var e,r;return(e=Array()).concat.apply(e,function(t){if(Array.isArray(t))return Wc(t)}(r=Rr(zc(["stored","completed"],t)))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(r)||function(t,e){if(t){if("string"==typeof t)return Wc(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Wc(t,e):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())},Kc=su(ii.Set),Yc={observer:function(t){var e=t.dispatch,r=(0,t.getState)(),n=r.callbacks,o=r.isLoading,i=Vc(n),u=Boolean(i.length);o!==u&&e(Kc(u))},inputs:["callbacks"]},Qc=Yc,Xc=Ot((function(t,e){return Array.prototype.slice.call(e,0).sort(t)})),Zc=r(296),ts=function(t,e,r){if(!r.length)return!0;var n=[],o=e.events,i=new Promise((function(t){o.once("rendered",t)}));return r.forEach((function(r){var o=Pn(e,r);if(o){var u=ne(o,t);if(u){var a=An(u),c=(0,Zc.isReady)(a);c&&"function"==typeof c.then&&n.push(Promise.race([c,i.then((function(){return document.getElementById(Jn(r))&&c}))]))}}})),!n.length||Promise.all(n)};function es(t){return es="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},es(t)}function rs(){var t,e,r="function"==typeof Symbol?Symbol:{},n=r.iterator||"@@iterator",o=r.toStringTag||"@@toStringTag";function i(r,n,o,i){var c=n&&n.prototype instanceof a?n:a,s=Object.create(c.prototype);return ns(s,"_invoke",function(r,n,o){var i,a,c,s=0,f=o||[],l=!1,p={p:0,n:0,v:t,a:y,f:y.bind(t,4),d:function(e,r){return i=e,a=0,c=t,p.n=r,u}};function y(r,n){for(a=r,c=n,e=0;!l&&s&&!o&&e<f.length;e++){var o,i=f[e],y=p.p,d=i[2];r>3?(o=d===n)&&(c=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=t):i[0]<=y&&((o=r<2&&y<i[1])?(a=0,p.v=n,p.n=i[1]):y<d&&(o=r<3||i[0]>n||n>d)&&(i[4]=r,i[5]=n,p.n=d,a=0))}if(o||r>1)return u;throw l=!0,n}return function(o,f,d){if(s>1)throw TypeError("Generator is already running");for(l&&1===f&&y(f,d),a=f,c=d;(e=a<2?t:c)||!l;){i||(a?a<3?(a>1&&(p.n=-1),y(a,c)):p.n=c:p.v=c);try{if(s=2,i){if(a||(o="next"),e=i[o]){if(!(e=e.call(i,c)))throw TypeError("iterator result is not an object");if(!e.done)return e;c=e.value,a<2&&(a=0)}else 1===a&&(e=i.return)&&e.call(i),a<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),a=1);i=t}else if((e=(l=p.n<0)?c:r.call(n,p))!==u)break}catch(e){i=t,a=1,c=e}finally{s=1}}return{value:e,done:l}}}(r,o,i),!0),s}var u={};function a(){}function c(){}function s(){}e=Object.getPrototypeOf;var f=[][n]?e(e([][n]())):(ns(e={},n,(function(){return this})),e),l=s.prototype=a.prototype=Object.create(f);function p(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,s):(t.__proto__=s,ns(t,o,"GeneratorFunction")),t.prototype=Object.create(l),t}return c.prototype=s,ns(l,"constructor",s),ns(s,"constructor",c),c.displayName="GeneratorFunction",ns(s,o,"GeneratorFunction"),ns(l),ns(l,o,"Generator"),ns(l,n,(function(){return this})),ns(l,"toString",(function(){return"[object Generator]"})),(rs=function(){return{w:i,m:p}})()}function ns(t,e,r,n){var o=Object.defineProperty;try{o({},"",{})}catch(t){o=0}ns=function(t,e,r,n){if(e)o?o(t,e,{value:r,enumerable:!n,configurable:!n,writable:!n}):t[e]=r;else{var i=function(e,r){ns(t,e,(function(t){return this._invoke(e,r,t)}))};i("next",0),i("throw",1),i("return",2)}},ns(t,e,r,n)}function os(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function is(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?os(Object(r),!0).forEach((function(e){us(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):os(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function us(t,e,r){return(e=function(t){var e=function(t){if("object"!=es(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=es(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==es(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function as(t,e,r,n,o,i,u){try{var a=t[i](u),c=a.value}catch(t){return void r(t)}a.done?e(c):Promise.resolve(c).then(n,o)}function cs(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function u(t){as(i,n,o,u,a,"next",t)}function a(t){as(i,n,o,u,a,"throw",t)}u(void 0)}))}}function ss(t){return function(t){if(Array.isArray(t))return ls(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||fs(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function fs(t,e){if(t){if("string"==typeof t)return ls(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ls(t,e):void 0}}function ls(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var ps=function(t,e){var r,n;return(null!==(r=t.priority)&&void 0!==r?r:"")>(null!==(n=e.priority)&&void 0!==n?n:"")?-1:1},ys=function(t,e){var r=(0,t.getOutputs)(e),n=Ee(r),o=[],i={};return n.forEach((function(t){var e=t.id,r=t.property,n=Jn(e);(i[n]=i[n]||[]).push(r),o.push(mo({id:n,property:r}))})),{allOutputs:r,allPropIds:o}},ds=function(t,e){var r=[].concat(ss(Ee(t.getInputs(e))),ss(Ee(t.getState(e)))),n=new Map(r.map((function(t){return[Jn(t.id),t]})));return Array.from(n.values())},hs={observer:function(){var t=cs(rs().m((function t(e){var r,n,o,i,u,a,c,s,f,l,p,y,d,h,v,b,m,g,O,w,S;return rs().w((function(t){for(;;)switch(t.n){case 0:if(r=e.dispatch,n=e.getState,o=n(),i=o.callbacks,u=i.executing,a=i.watched,c=o.config,s=o.hooks,f=o.layout,l=o.paths,p=o.appLifecycle,y=n(),d=y.callbacks.prioritized,p===To("HYDRATED")){t.n=1;break}return t.a(2);case 1:h=Math.max(0,12-u.length-a.length),d=Xc(ps,d),v=pr((function(t){return!0===ts(f,l,ds(t,l))}),d),_=2,b=function(t){if(Array.isArray(t))return t}(j=v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,u,a=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(a.push(n.value),a.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(u=r.return(),Object(u)!==u))return}finally{if(s)throw o}}return a}}(j,_)||fs(j,_)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),m=b[0],g=b[1],O=m.slice(0,h),w=g.slice(0,h-O.length),O.length&&r(fc([uc(O),Xa(Re((function(t){return Ac(t,c,s,l,f,ys(t,l),r,n)}),O))])),w.length&&(S=Re((function(t){return is(is(is({},t),ys(t,l)),{},{isReady:ts(f,l,ds(t,l))})}),w),r(fc([uc(w),Ka(S)])),S.forEach(function(){var t=cs(rs().m((function t(e){var o,i,u;return rs().w((function(t){for(;;)switch(t.n){case 0:return t.n=1,e.isReady;case 1:if(o=n(),i=o.callbacks.blocked,cn((function(t){return t===e||t.isReady===e.isReady}),i)){t.n=2;break}return t.a(2);case 2:u=Ac(e,c,s,l,f,e,r,n),r(fc([oc([e]),Xa([u])]));case 3:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}()));case 2:return t.a(2)}var j,_}),t)})));return function(e){return t.apply(this,arguments)}}(),inputs:["callbacks.prioritized","callbacks.completed"]},vs=hs,bs=function(){function t(t,e,r,n){this.valueFn=t,this.valueAcc=e,this.keyFn=r,this.xf=n,this.inputs={}}return t.prototype["@@transducer/init"]=be,t.prototype["@@transducer/result"]=function(t){var e;for(e in this.inputs)if(jt(e,this.inputs)&&(t=this.xf["@@transducer/step"](t,this.inputs[e]))["@@transducer/reduced"]){t=t["@@transducer/value"];break}return this.inputs=null,this.xf["@@transducer/result"](t)},t.prototype["@@transducer/step"]=function(t,e){var r=this.keyFn(e);return this.inputs[r]=this.inputs[r]||[r,oi(this.valueAcc,!1)],this.inputs[r][1]=this.valueFn(this.inputs[r][1],e),t},t}();function ms(t,e,r){return function(n){return new bs(t,e,r,n)}}var gs=Ot(te("groupBy",xe(4,[],he([],ms,(function(t,e,r,n){var o=He((function(n,o){var i=r(o),u=t(jt(i,n)?n[i]:oi(e,!1),o);return u&&u["@@transducer/reduced"]?er(n):(n[i]=u,n)}));return Ge(o,{},n)})))((function(t,e){return t.push(e),t}),[]))),Os=Ot((function(t,e){return Jr({},e,t)}));function ws(){var t,e,r="function"==typeof Symbol?Symbol:{},n=r.iterator||"@@iterator",o=r.toStringTag||"@@toStringTag";function i(r,n,o,i){var c=n&&n.prototype instanceof a?n:a,s=Object.create(c.prototype);return Ss(s,"_invoke",function(r,n,o){var i,a,c,s=0,f=o||[],l=!1,p={p:0,n:0,v:t,a:y,f:y.bind(t,4),d:function(e,r){return i=e,a=0,c=t,p.n=r,u}};function y(r,n){for(a=r,c=n,e=0;!l&&s&&!o&&e<f.length;e++){var o,i=f[e],y=p.p,d=i[2];r>3?(o=d===n)&&(c=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=t):i[0]<=y&&((o=r<2&&y<i[1])?(a=0,p.v=n,p.n=i[1]):y<d&&(o=r<3||i[0]>n||n>d)&&(i[4]=r,i[5]=n,p.n=d,a=0))}if(o||r>1)return u;throw l=!0,n}return function(o,f,d){if(s>1)throw TypeError("Generator is already running");for(l&&1===f&&y(f,d),a=f,c=d;(e=a<2?t:c)||!l;){i||(a?a<3?(a>1&&(p.n=-1),y(a,c)):p.n=c:p.v=c);try{if(s=2,i){if(a||(o="next"),e=i[o]){if(!(e=e.call(i,c)))throw TypeError("iterator result is not an object");if(!e.done)return e;c=e.value,a<2&&(a=0)}else 1===a&&(e=i.return)&&e.call(i),a<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),a=1);i=t}else if((e=(l=p.n<0)?c:r.call(n,p))!==u)break}catch(e){i=t,a=1,c=e}finally{s=1}}return{value:e,done:l}}}(r,o,i),!0),s}var u={};function a(){}function c(){}function s(){}e=Object.getPrototypeOf;var f=[][n]?e(e([][n]())):(Ss(e={},n,(function(){return this})),e),l=s.prototype=a.prototype=Object.create(f);function p(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,s):(t.__proto__=s,Ss(t,o,"GeneratorFunction")),t.prototype=Object.create(l),t}return c.prototype=s,Ss(l,"constructor",s),Ss(s,"constructor",c),c.displayName="GeneratorFunction",Ss(s,o,"GeneratorFunction"),Ss(l),Ss(l,o,"Generator"),Ss(l,n,(function(){return this})),Ss(l,"toString",(function(){return"[object Generator]"})),(ws=function(){return{w:i,m:p}})()}function Ss(t,e,r,n){var o=Object.defineProperty;try{o({},"",{})}catch(t){o=0}Ss=function(t,e,r,n){if(e)o?o(t,e,{value:r,enumerable:!n,configurable:!n,writable:!n}):t[e]=r;else{var i=function(e,r){Ss(t,e,(function(t){return this._invoke(e,r,t)}))};i("next",0),i("throw",1),i("return",2)}},Ss(t,e,r,n)}function js(t,e,r,n,o,i,u){try{var a=t[i](u),c=a.value}catch(t){return void r(t)}a.done?e(c):Promise.resolve(c).then(n,o)}var _s=function(){var t,e=(t=ws().m((function t(e){var r,n;return ws().w((function(t){for(;;)if(0===t.n)return n=new Promise((function(t){return r=t})),setTimeout(r,e),t.a(2,n)}),t)})),function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function u(t){js(i,n,o,u,a,"next",t)}function a(t){js(i,n,o,u,a,"throw",t)}u(void 0)}))});return function(t){return e.apply(this,arguments)}}();function Es(t){return Es="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Es(t)}function Ps(t){if(null!=t){var e=t["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],r=0;if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length))return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}}throw new TypeError(Es(t)+" is not iterable")}function As(){var t,e,r="function"==typeof Symbol?Symbol:{},n=r.iterator||"@@iterator",o=r.toStringTag||"@@toStringTag";function i(r,n,o,i){var c=n&&n.prototype instanceof a?n:a,s=Object.create(c.prototype);return ks(s,"_invoke",function(r,n,o){var i,a,c,s=0,f=o||[],l=!1,p={p:0,n:0,v:t,a:y,f:y.bind(t,4),d:function(e,r){return i=e,a=0,c=t,p.n=r,u}};function y(r,n){for(a=r,c=n,e=0;!l&&s&&!o&&e<f.length;e++){var o,i=f[e],y=p.p,d=i[2];r>3?(o=d===n)&&(c=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=t):i[0]<=y&&((o=r<2&&y<i[1])?(a=0,p.v=n,p.n=i[1]):y<d&&(o=r<3||i[0]>n||n>d)&&(i[4]=r,i[5]=n,p.n=d,a=0))}if(o||r>1)return u;throw l=!0,n}return function(o,f,d){if(s>1)throw TypeError("Generator is already running");for(l&&1===f&&y(f,d),a=f,c=d;(e=a<2?t:c)||!l;){i||(a?a<3?(a>1&&(p.n=-1),y(a,c)):p.n=c:p.v=c);try{if(s=2,i){if(a||(o="next"),e=i[o]){if(!(e=e.call(i,c)))throw TypeError("iterator result is not an object");if(!e.done)return e;c=e.value,a<2&&(a=0)}else 1===a&&(e=i.return)&&e.call(i),a<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),a=1);i=t}else if((e=(l=p.n<0)?c:r.call(n,p))!==u)break}catch(e){i=t,a=1,c=e}finally{s=1}}return{value:e,done:l}}}(r,o,i),!0),s}var u={};function a(){}function c(){}function s(){}e=Object.getPrototypeOf;var f=[][n]?e(e([][n]())):(ks(e={},n,(function(){return this})),e),l=s.prototype=a.prototype=Object.create(f);function p(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,s):(t.__proto__=s,ks(t,o,"GeneratorFunction")),t.prototype=Object.create(l),t}return c.prototype=s,ks(l,"constructor",s),ks(s,"constructor",c),c.displayName="GeneratorFunction",ks(s,o,"GeneratorFunction"),ks(l),ks(l,o,"Generator"),ks(l,n,(function(){return this})),ks(l,"toString",(function(){return"[object Generator]"})),(As=function(){return{w:i,m:p}})()}function ks(t,e,r,n){var o=Object.defineProperty;try{o({},"",{})}catch(t){o=0}ks=function(t,e,r,n){if(e)o?o(t,e,{value:r,enumerable:!n,configurable:!n,writable:!n}):t[e]=r;else{var i=function(e,r){ks(t,e,(function(t){return this._invoke(e,r,t)}))};i("next",0),i("throw",1),i("return",2)}},ks(t,e,r,n)}function xs(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Ts(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?xs(Object(r),!0).forEach((function(e){Is(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):xs(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Is(t,e,r){return(e=function(t){var e=function(t){if("object"!=Es(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=Es(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Es(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Cs(t,e,r,n,o,i,u){try{var a=t[i](u),c=a.value}catch(t){return void r(t)}a.done?e(c):Promise.resolve(c).then(n,o)}var Rs={observer:function(){var t,e=(t=As().m((function t(e){var r,n,o,i,u,a,c,s,f,l,p,y,d,h,v,b,m,g,O,w,S,j,_,E,P,A,k,x,T,I,C,R,D,N,M,U,L,q,B,G,F,H,$,J,z,W,V;return As().w((function(t){for(;;)switch(t.n){case 0:return r=e.dispatch,n=e.getState,t.n=1,_s(0);case 1:if(o=n(),i=o.callbacks,u=o.callbacks,a=u.prioritized,c=u.blocked,s=u.executing,f=u.watched,l=u.stored,p=o.paths,y=o.graphs,d=n(),h=d.callbacks.requested,v=h.slice(0),b=Vc(i),m=we((function(t){var e;return Gt(t.callback,null!==(e=t.predecessors)&&void 0!==e?e:[])}),h),h=vr(h,m),g=[],O=[],Rr(gs(jo,h)).forEach((function(t){if(1===t.length)O.push(t[0]);else{var e=t.find((function(t){return t.initialCall}));e&&g.push(e);var r=t.filter((function(t){return t!==e}));1===r.length?O.push(r[0]):(g=tr(g,r),O.push(Os({changedPropIds:$e(le(Math.max),{},cr("changedPropIds",r)),executionGroup:we((function(t){return Boolean(t)}),cr("executionGroup",r)).slice(-1)[0]},r.slice(-1)[0])))}})),w=Ee(Re((function(t){return t.slice(0,-1)}),Rr(gs(jo,tr(a,h=O))))),S=Ee(Re((function(t){return t.slice(0,-1)}),Rr(gs(jo,tr(c,h))))),j=Ee(Re((function(t){return t.slice(0,-1)}),Rr(gs(jo,tr(s,h))))),_=Ee(Re((function(t){return t.slice(0,-1)}),Rr(gs(jo,tr(f,h))))),E=Po(h,p),P=E.added,A=E.removed,k=Po(a,p),x=k.added,T=k.removed,I=Po(c,p),C=I.added,R=I.removed,D=Po(s,p),N=D.added,M=D.removed,U=Po(f,p),L=U.added,q=U.removed,h=tr(vr(h,A),P),B=wo(p,h,b,y),G=[],F=[],B.length||!h.length||h.length!==b.length){t.n=4;break}H=h.slice(0),$=As().m((function t(){var e,r,n;return As().w((function(t){for(;;)switch(t.n){case 0:e=H[0],B.push(e),H=H.slice(1),H=wo(p,H,B),r=vr(H,H),n=we((function(t){return!t.predecessors||!Gt(e.callback,t.predecessors)}),r),G=tr(G,n),F=tr(F,n.map((function(t){var r;return Ts(Ts({},t),{},{predecessors:tr(null!==(r=t.predecessors)&&void 0!==r?r:[],[e.callback])})})));case 1:return t.a(2)}}),t)}));case 2:if(!H.length){t.n=4;break}return t.d(Ps($()),3);case 3:t.n=2;break;case 4:h=tr(vr(h,G),F),J=gs((function(t){return t.executionGroup}),we((function(t){return!Yt(t.executionGroup)}),l)),z=we((function(t){if(!t.executionGroup||!J[t.executionGroup]||!J[t.executionGroup].length)return!1;var e=Re(mo,Ee(t.getInputs(p))),r=Ee(Re((function(t){return t.executionMeta.allProps}),J[t.executionGroup])),n=Ee(Re((function(t){return t.executionMeta.updatedProps}),J[t.executionGroup]));return ae(Cr(e,n))&&ae(vr(e,r))&&!ir(ro,t.callback.inputs)}),B),h=vr(h,z),B=vr(B,z),h=vr(h,B),W=vr(h,v),V=vr(v,h),r(fc([W.length?tc(W):null,V.length?ac(V):null,w.length?uc(w):null,S.length?oc(S):null,j.length?ic(j):null,_.length?sc(_):null,T.length?uc(T):null,x.length?Za(x):null,R.length?oc(R):null,C.length?Ka(C):null,M.length?ic(M):null,N.length?Xa(N):null,q.length?sc(q):null,L.length?rc(L):null,B.length?Za(B):null]));case 5:return t.a(2)}}),t)})),function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function u(t){Cs(i,n,o,u,a,"next",t)}function a(t){Cs(i,n,o,u,a,"throw",t)}u(void 0)}))});return function(t){return e.apply(this,arguments)}}(),inputs:["callbacks.requested","callbacks.completed"]},Ds=Rs;function Ns(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,u,a=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(a.push(n.value),a.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(u=r.return(),Object(u)!==u))return}finally{if(s)throw o}}return a}}(t,e)||function(t,e){if(t){if("string"==typeof t)return Ms(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ms(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ms(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var Us={observer:function(t){var e=t.dispatch,r=t.getState,n=r().callbacks,o=Vc(n),i=r().callbacks.stored,u=Ns(pr((function(t){return Yt(t.executionGroup)}),i),2),a=u[0],c=u[1],s=gs((function(t){return t.executionGroup}),c),f=gs((function(t){return t.executionGroup}),we((function(t){return!Yt(t.executionGroup)}),o)),l=$e((function(t,e){var r=Ns(e,2),n=r[0],o=r[1];return f[n]?t:tr(t,o)}),[],ru(s));e(fc([a.length?cc(a):null,l.length?cc(l):null]))},inputs:["callbacks.stored","callbacks.completed"]},Ls=Us;function qs(t){return qs="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},qs(t)}function Bs(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Fs(n.key),n)}}function Gs(t,e,r){return(e=Fs(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Fs(t){var e=function(t){if("object"!=qs(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=qs(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==qs(e)?e:e+""}var Hs=function(){return t=function t(){var e=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),Gs(this,"__store",void 0),Gs(this,"storeObserver",new Xi),Gs(this,"setObservers",it((function(){var t=e.storeObserver.observe;t(eu),t(Qc),t(Ds),t(vs),t(Jc),t(Nc),t(Ls)}))),Gs(this,"createAppStore",(function(t,r){e.__store=dt(t,r),e.storeObserver.setStore(e.__store);var n=window.dash_stores=window.dash_stores||[];n.includes(e.__store)||n.push(e.__store),e.setObservers()})),Gs(this,"initializeStore",(function(t){if(e.__store&&!t)return e.__store;var r=function(){return function(t){return function(e,r){var n=e||{},o=n.history,i=n.config,u=n.hooks,a=e;return"RELOAD"===r.type?a={history:o,config:i,hooks:u}:"SET_CONFIG"===r.type&&(a={hooks:u}),t(a,r)}}((t=Ji(),function(e,r){var n=r.type,o=r.payload;if("ON_PROP_CHANGE"===n){var i=zi(o,e,!0);i&&!ae(i.props)&&(e.history.present=i)}var u,a=t(e,r);if("ON_PROP_CHANGE"===n&&"response"!==o.source){var c=zi(o,a);c&&!ae(c.props)&&(a.history={past:[].concat((u=a.history.past,function(t){if(Array.isArray(t))return Fi(t)}(u)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(u)||function(t,e){if(t){if("string"==typeof t)return Fi(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Fi(t,e):void 0}}(u)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),[e.history.present]),present:c,future:[]})}return a}));var t}();return e.createAppStore(r,vt(gt)),t||(window.store=e.__store),e.__store})),this.__store=this.initializeStore()},(e=[{key:"store",get:function(){return this.__store}}])&&Bs(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e}();function $s(t){return $s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$s(t)}function Js(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,zs(n.key),n)}}function zs(t){var e=function(t){if("object"!=$s(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=$s(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==$s(e)?e:e+""}function Ws(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Ws=function(){return!!t})()}function Vs(t){return Vs=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Vs(t)}function Ks(t,e){return Ks=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Ks(t,e)}var Ys=function(t){function r(t){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,r),function(t,e,r){return e=Vs(e),function(t,e){if(e&&("object"==$s(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Ws()?Reflect.construct(e,r||[],Vs(t).constructor):e.apply(t,r))}(this,r,[t])}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ks(t,e)}(r,t),n=r,(o=[{key:"render",value:function(){return e().createElement("div",{id:"_dash-app-content"},this.props.children)}}])&&Js(n.prototype,o),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o}(t.Component);Ys.propTypes={children:u().object};var Qs=Ys,Xs=["String","Number","Null","Boolean"],Zs=function(t){return Gt(Rt(t),Xs)},tf=Ot((function(t,e){return de((function(r,n){return r[n]=t(e[n],n,e),r}),{},Ct(e))})),ef=tf;function rf(t){return rf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},rf(t)}function nf(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,of(n.key),n)}}function of(t){var e=function(t){if("object"!=rf(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=rf(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==rf(e)?e:e+""}function uf(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(uf=function(){return!!t})()}function af(t){return af=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},af(t)}function cf(t,e){return cf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},cf(t,e)}var sf=function(t){function e(t){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(r=function(t,e,r){return e=af(e),function(t,e){if(e&&("object"==rf(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,uf()?Reflect.construct(e,r||[],af(t).constructor):e.apply(t,r))}(this,e,[t])).state={myID:t.componentId,oldChildren:null,hasError:!1},r}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&cf(t,e)}(e,t),r=e,n=[{key:"componentDidCatch",value:function(t,e){var r=this.props.dispatch;r(Gu({myID:this.state.myID,type:"frontEnd",error:t,info:e})),r(oa)}},{key:"componentDidUpdate",value:function(t,e){var r=t.children;this.state.hasError||r===e.oldChildren||r===this.props.children||this.setState({oldChildren:r})}},{key:"render",value:function(){var t=this.state,e=t.hasError,r=t.oldChildren;return e?r:this.props.children}}],o=[{key:"getDerivedStateFromError",value:function(t){return{hasError:!0}}}],n&&nf(r.prototype,n),o&&nf(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}(t.Component);sf.propTypes={children:u().object,componentId:u().string,error:u().object,dispatch:u().func};var ff=sf;function lf(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,u,a=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(a.push(n.value),a.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(u=r.return(),Object(u)!==u))return}finally{if(s)throw o}}return a}}(t,e)||function(t,e){if(t){if("string"==typeof t)return pf(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?pf(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pf(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var yf={};function df(t,e){var r=lf(t,3),n=(r[0],r[1],r[2]),o=lf(e,3);return o[0],o[1],n===o[2]}function hf(t){return t.config}var vf=r(925),bf=r.n(vf);function mf(t){return mf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mf(t)}function gf(t){var e=t.element,r=t.props,n=t.children,o=t.component;!function(t){if("Array"===Rt(t))throw new Error("The children property of a component is a list of lists, instead of just a list. This can sometimes be due to a trailing comma. Check the component that has the following contents and remove one of the levels of nesting: \n"+JSON.stringify(t,null,2));if("Object"===Rt(t)&&!(pn("namespace",t)&&pn("type",t)&&pn("props",t)))throw new Error("An object was provided as `children` instead of a component, string, or number (or list of those). Check the children property that looks something like:\n"+JSON.stringify(t,null,2))}(o);var i=function(t,e,r,n){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,i=[];for(var u in t)if(t.hasOwnProperty(u)){var a=void 0;try{"function"!=typeof t[u]?(a=Error((n||"React class")+": "+r+" type `"+u+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+mf(t[u])+"`.")).name="Invariant Violation":a=t[u](e,u,n,r,null,bf())}catch(t){a=t}if(!a||a instanceof Error||i.push((n||"React class")+": type specification of "+r+" `"+u+"` is invalid; the type checker function must return `null` or an `Error` but returned a "+mf(a)+". You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument)."),a instanceof Error){var c=o&&o()||"";i.push("Failed "+r+" type: "+a.message+c)}}return i.join("\n\n")}(e.propTypes,r,"component prop",e);return i&&function(t,e,r){var n,o=t.split("`");if(Gt("is marked as required",t)){var i=o[1];n="".concat(i," in ").concat(r),e.id&&(n+=' with ID "'.concat(e.id,'"')),n+=" is required but it was not provided."}else if(Gt("Bad object",t))n=t.split("supplied to ")[0]+"supplied to ".concat(r)+".\nBad"+t.split(".\nBad")[1];else{if(!Gt("Invalid ",t)||!Gt(" supplied to ",t))throw new Error(t);var u=o[1];if(n="Invalid argument `".concat(u,"` passed into ").concat(r),e.id&&(n+=' with ID "'.concat(e.id,'"')),n+=".",Gt(", expected ",t)){var a=t.split(", expected ")[1];n+="\nExpected ".concat(a)}if(Gt(" of type `",t)){var c=t.split(" of type `")[1].split("`")[0];n+="\nWas supplied type `".concat(c,"`.")}if(pn(u,e)){var s=JSON.stringify(e[u],null,2);s&&(Gt("\n",s)?n+="\nValue provided: \n".concat(s):n+="\nValue provided: ".concat(s))}}throw new Error(n)}(i,r,o.type),n}var Of=e().createContext({});function wf(r){var n=r.children,o=r.componentPath,i=(0,t.useMemo)((function(){return JSON.stringify(o)}),[o]),u=K(),a=(0,t.useCallback)((function(t){var e=t||{},r=e.extraPath,n=e.rawPath,a=e.filterFunc,c=[i];r?c=[JSON.stringify(tr(o,r))]:n&&(c=[JSON.stringify(n)]);var s=Vt([],c,u.getState().loading);return a?s.filter(a).length>0:s.length>0}),[i]),c=(0,t.useCallback)((function(t){var e=t||{},r=e.filterFunc,n=e.extraPath,u=e.rawPath;return b((function(t){var e=[i];n?e=[JSON.stringify(tr(o,n))]:u&&(e=[JSON.stringify(u)]);var a=Vt([],e,t.loading);return r?a.filter(r).length>0:a.length>0}))}),[i]),s=(0,t.useMemo)((function(){return{componentPath:o,isLoading:a,useLoading:c,useSelector:b,useStore:K,useDispatch:Z}}),[i]);return e().createElement(Of.Provider,{value:s},n)}function Sf(t){return Sf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Sf(t)}var jf=["componentPath","_dashprivate_error","_passedComponent","_newRender"],_f=["_dash_error"];function Ef(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Pf(t,e,r){return(e=function(t){var e=function(t){if("object"!=Sf(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=Sf(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Sf(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Af(t){return function(t){if(Array.isArray(t))return xf(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||kf(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function kf(t,e){if(t){if("string"==typeof t)return xf(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?xf(t,e):void 0}}function xf(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function Tf(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if({}.hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],-1===e.indexOf(r)&&{}.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var If=function r(o){var i,u,a=o.componentPath,c=o._dashprivate_error,s=o._passedComponent,f=o._newRender,l=Tf(o,jf),p=Z(),y=(0,t.useRef)({}),d=(0,t.useRef)(!1),h=(0,t.useRef)(a),v=null,m=null,g=null,O=b(hf),w=(i=b(function(t){return function(e){var r,n,o=Gi(t,e),i=Bi(t);r="dashChildrenUpdate"in Vt({},[null==(n=o)?void 0:n.namespace,null==n?void 0:n.type],window)?function(t,e){var r,n=0,o={};return Object.entries(t.layoutHashes).forEach((function(t){var i=lf(t,2),u=i[0],a=i[1],c=function(t,e){var r=t.split(","),n=e.split(",");if(!n.every((function(t,e){return r[e]===t})))return[!1,[]];var o=r.slice(n.length);return[o.filter((function(t){return"props"===t})).length<2,o]}(u,e),s=lf(c,2),f=s[0],l=s[1];if(f){var p=Vt({},[u],yf);n+=Vt(0,["hash"],a),p!==a&&(u!==e?(Object.assign(o,{[l[1]]:!0}),r="components"):(Object.assign(o,Vt({},["changedProps"],a)),r=Vt({},["renderType"],a)),yf[u]=a)}})),{hash:n,changedProps:o,renderType:r}}(e,i):e.layoutHashes[i];var u=0,a={},c="";return r&&(u=r.hash,a=r.changedProps,c=r.renderType),[o,null==o?void 0:o.props,u,a,c]}}(a),df),u=5,function(t){if(Array.isArray(t))return t}(i)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,u,a=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(a.push(n.value),a.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(u=r.return(),Object(u)!==u))return}finally{if(s)throw o}}return a}}(i,u)||kf(i,u)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),S=w[0],j=w[1],_=w[2],E=w[3],P=w[4];v=S,m=j,g=_,(0,t.useMemo)((function(){f?(d.current=!0,(g=0)in y.current&&delete y.current[g]):d.current=!1,h.current=a}),[f]);var A,k=(0,t.useCallback)((function(t,n,o){var i;return Zs(v)?v:e().createElement(r,{key:null!=t&&null!==(i=t.props)&&void 0!==i&&i.id?Jn(t.props.id):Bi(n),_dashprivate_error:c,componentPath:n,_passedComponent:t,_newRender:o})}),[]),x=(0,t.useCallback)((function(t,e,r){return Array.isArray(t)?t.map((function(t,n){return qi(t)?k(t,tr(a,["props"].concat(Af(e),[n])),r):t})):qi(t)?k(t,tr(a,["props"].concat(Af(e))),r):t}),[a]),T=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ef(Object(r),!0).forEach((function(e){Pf(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ef(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({setProps:function(t){var e=m.id,r=t._dash_error,o=Tf(t,_f);p((function(t,i){var u=i(),a=u.graphs,c=Gi(h.current,u);if(c){var s=c.props;if(s){var f=br((function(t,e){return!Ut(t,s[e])}),o);if(r&&t(Gu({type:"frontEnd",error:r})),!ae(f)){var l=function(t,e,r){if(!(t&&r&&e.length))return[];if("string"==typeof t){var n=r.inputMap[t];return n?e.filter((function(t){return n[t]})):[]}var o=Object.keys(t).sort(),i=ye(o,t),u=o.join(","),a=r.inputPatterns[u];return a?e.filter((function(t){var e=a[t];return e&&e.some((function(t){return to(o,i,t.values)}))})):[]}(e,Ct(f),a);(0,n.unstable_batchedUpdates)((function(){l.length&&t(ua({id:e,props:nu(l,f)})),t(Xu({props:f,itempath:h.current,renderType:"internal"}))}))}}}}))}},l);"dashRenderType"in Vt({},[null==(A=v)?void 0:A.namespace,null==A?void 0:A.type],window)&&(T.dashRenderType=d.current?"parent":E?P:"parent");var I=null;return g in y.current&&!d.current&&(I=e().isValidElement(y.current[g])?y.current[g]:null),I||(I=function(){if(d.current&&(v=s,m=null==s?void 0:s.props),!v)return null;var t,r=An(v),n=function(t,e){for(var r=Vt([],["children_props",null==t?void 0:t.namespace,null==t?void 0:t.type],O),n=zr(Ci("children",e),T),o=function(){var t=r[i],e=0;(t.split(".")[0].replace("[]","").replace("{}","")in E||d.current||!g)&&(e={});var o=function(t,r){return ef((function(t,n){return x(t,[].concat(Af(r),[n]),e)}),t)};if(t.includes(".")){var u,a,c=t.split(".");if(t.includes("[]")){var s,f=[],l=[],p=!1,y=!1;if(c.forEach((function(t){p?t.includes("{}")?(y=!0,l.push(t.replace("{}",""))):l.push(t):t.includes("[]")?(p=!0,t.includes("{}")?(y=!0,f.push(t.replace("{}","").replace("[]",""))):f.push(t.replace("[]",""))):t.includes("{}")?(y=!0,f.push(t.replace("{}",""))):f.push(t)})),void 0===(u=ne(f,n))||null===(s=u)||void 0===s||!s.length)return 0;if(!ne(l,u[0]))return 0;a=u.map((function(t,r){var n,i=tr(f,tr([r],l));return n=y?l.length?o(ne(l,t),i):o(t,i):x(ne(l,t),i,e),Xt(l,n,t)})),c=f}else if(t.includes("{}")){for(var h=[],v=[],b=!1,m=[],O=0;O<c.length;O++){var w=c[O];w.includes("{}")?(v=tr(h,[w.replace("{}","")]),O<c.length-1&&(b=!0)):b?m.push(w):h.push(w)}var S=ne(v,n);void 0!==S&&(a=ef((function(t,r){return x(b?ne(m,t):t,tr(v,b?tr([r],m):[r]),e)}),S),c=v)}else{if(void 0===(u=ne(c,n)))return 0;a=x(u,c,e)}n=Xt(c,a,n)}else if(t.includes("{}")){var j=t.replace("{}",""),_=t.includes("[]");_&&(j=j.replace("[]",""));var P=n[j];if(void 0!==P)if(_)for(var A=0;A<P.length;A++){var k=tr([j],[A]);n=Xt(k,o(P[A],k),n)}else n=Zt(j,o(P,[j]),n)}else{var T=n[t];void 0!==T&&(n=Zt(t,x(T,[t],e),n))}},i=0;i<r.length;i++)o();return"Object"===Rt(n.id)&&(n.id=Jn(n.id)),n}(v,m);return void 0!==m.children&&(t=x(m.children,["children"],!g||d.current||"children"in E?{}:0)),d.current=!1,O.props_check?e().createElement(gf,{element:r,props:n,component:v},Li(r,n,T,t)):Li(r,n,T,t)}(),y.current={[g]:I}),v?e().createElement(ff,{componentType:v.type,componentId:sa(Object,m.id)?Jn(m.id):m.id,error:c,dispatch:p},e().createElement(wf,{componentPath:a},e().isValidElement(I)?I:e().createElement("div",null))):e().createElement("div",null)};function Cf(){var t,e,r="function"==typeof Symbol?Symbol:{},n=r.iterator||"@@iterator",o=r.toStringTag||"@@toStringTag";function i(r,n,o,i){var c=n&&n.prototype instanceof a?n:a,s=Object.create(c.prototype);return Rf(s,"_invoke",function(r,n,o){var i,a,c,s=0,f=o||[],l=!1,p={p:0,n:0,v:t,a:y,f:y.bind(t,4),d:function(e,r){return i=e,a=0,c=t,p.n=r,u}};function y(r,n){for(a=r,c=n,e=0;!l&&s&&!o&&e<f.length;e++){var o,i=f[e],y=p.p,d=i[2];r>3?(o=d===n)&&(c=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=t):i[0]<=y&&((o=r<2&&y<i[1])?(a=0,p.v=n,p.n=i[1]):y<d&&(o=r<3||i[0]>n||n>d)&&(i[4]=r,i[5]=n,p.n=d,a=0))}if(o||r>1)return u;throw l=!0,n}return function(o,f,d){if(s>1)throw TypeError("Generator is already running");for(l&&1===f&&y(f,d),a=f,c=d;(e=a<2?t:c)||!l;){i||(a?a<3?(a>1&&(p.n=-1),y(a,c)):p.n=c:p.v=c);try{if(s=2,i){if(a||(o="next"),e=i[o]){if(!(e=e.call(i,c)))throw TypeError("iterator result is not an object");if(!e.done)return e;c=e.value,a<2&&(a=0)}else 1===a&&(e=i.return)&&e.call(i),a<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),a=1);i=t}else if((e=(l=p.n<0)?c:r.call(n,p))!==u)break}catch(e){i=t,a=1,c=e}finally{s=1}}return{value:e,done:l}}}(r,o,i),!0),s}var u={};function a(){}function c(){}function s(){}e=Object.getPrototypeOf;var f=[][n]?e(e([][n]())):(Rf(e={},n,(function(){return this})),e),l=s.prototype=a.prototype=Object.create(f);function p(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,s):(t.__proto__=s,Rf(t,o,"GeneratorFunction")),t.prototype=Object.create(l),t}return c.prototype=s,Rf(l,"constructor",s),Rf(s,"constructor",c),c.displayName="GeneratorFunction",Rf(s,o,"GeneratorFunction"),Rf(l),Rf(l,o,"Generator"),Rf(l,n,(function(){return this})),Rf(l,"toString",(function(){return"[object Generator]"})),(Cf=function(){return{w:i,m:p}})()}function Rf(t,e,r,n){var o=Object.defineProperty;try{o({},"",{})}catch(t){o=0}Rf=function(t,e,r,n){if(e)o?o(t,e,{value:r,enumerable:!n,configurable:!n,writable:!n}):t[e]=r;else{var i=function(e,r){Rf(t,e,(function(t){return this._invoke(e,r,t)}))};i("next",0),i("throw",1),i("return",2)}},Rf(t,e,r,n)}function Df(t,e,r,n,o,i,u){try{var a=t[i](u),c=a.value}catch(t){return void r(t)}a.done?e(c):Promise.resolve(c).then(n,o)}function Nf(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var Mf=function(r){var n,o,i=r.appLifecycle,u=r.config,a=r.dependenciesRequest,c=r.error,s=r.layoutRequest,f=r.layout,l=(n=(0,t.useState)(!1),o=2,function(t){if(Array.isArray(t))return t}(n)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,u,a=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(a.push(n.value),a.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(u=r.return(),Object(u)!==u))return}finally{if(s)throw o}}return a}}(n,o)||function(t,e){if(t){if("string"==typeof t)return Nf(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Nf(t,e):void 0}}(n,o)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),p=l[0],y=l[1],d=(0,t.useRef)(null);d.current||(d.current=new jn);var h,v=(0,t.useRef)(!1);return(0,t.useEffect)(Uf.bind(null,r,d,y)),(0,t.useEffect)((function(){var t;v.current&&(t=Cf().m((function t(){return Cf().w((function(t){for(;;)switch(t.n){case 0:return v.current=!1,t.n=1,_s(0);case 1:d.current.emit("rendered");case 2:return t.a(2)}}),t)})),function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function u(t){Df(i,n,o,u,a,"next",t)}function a(t){Df(i,n,o,u,a,"throw",t)}u(void 0)}))})()})),(0,t.useEffect)((function(){u.serve_locally?window._dashPlotlyJSURL="".concat(u.requests_pathname_prefix,"_dash-component-suites/plotly/package_data/plotly.min.js"):window._dashPlotlyJSURL=u.plotlyjs_url}),[]),s.status&&!Gt(s.status,[si,"loading"])?h=u.ui?e().createElement("div",{dangerouslySetInnerHTML:{__html:s.content}}):e().createElement("div",{className:"_dash-error"},"Error loading layout"):p||a.status&&!Gt(a.status,[si,"loading"])?h=u.ui?e().createElement("div",{dangerouslySetInnerHTML:{__html:a.content}}):e().createElement("div",{className:"_dash-error"},"Error loading dependencies"):i===To("HYDRATED")?(v.current=!0,h=e().createElement(e().Fragment,null,Array.isArray(f.components)?f.components.map((function(t,r){return Zs(t)?t:e().createElement(If,{_dashprivate_error:c,componentPath:["components",r],key:r})})):e().createElement(If,{_dashprivate_error:c,componentPath:["components"]}))):h=e().createElement("div",{className:"_dash-loading"},"Loading..."),u&&!0===u.ui?e().createElement(Qs,null,h):h};function Uf(t,e,r){var o=t.appLifecycle,i=t.dependenciesRequest,u=t.dispatch,a=t.error,c=t.graphs,s=t.hooks,f=t.layout,l=t.layoutRequest;(0,n.unstable_batchedUpdates)((function(){if(ae(l))"function"==typeof s.layout_pre&&s.layout_pre(),u(Ea("_dash-layout","GET","layoutRequest"));else if(l.status===si&&ae(f.components)){"function"==typeof s.layout_post&&s.layout_post(l.content);var t=Nu(l.content,u);u(Vu(En(t,["components"],null,e.current))),u(Wu(t))}if(ae(i)?u(Ea("_dash-dependencies","GET","dependenciesRequest")):i.status===si&&(ae(c)||c.reset)&&u(Ju(Xn(i.content,Zu(u)))),i.status===si&&!ae(c)&&l.status===si&&!ae(f.components)&&o===To("STARTED")){var n=!1;try{u((Zu(u),function(t,e){!function(t,e){var r,n,o=t.config,i=t.graphs,u=t.layout,a=t.paths,c=!o.suppress_callback_exceptions;c&&o.validation_layout?(r=o.validation_layout,n=En(r,[],null,a.events)):(r=u,n=a);var s=i.outputMap,f=i.inputMap,l=i.outputPatterns,p=i.inputPatterns;function y(t){return"This ID was used in the callback(s) for Output(s):\n  "+t.map((function(t){return t.outputs.map(mo).join(", ")})).join("\n  ")}function d(t,r,n){e("ID not found in layout",["Attempting to connect a callback ".concat(r," item to component:"),'  "'.concat(Jn(t),'"'),"but no components with that id exist in the layout.","","If you are assigning callbacks to components that are","generated by other callbacks (and therefore not in the","initial layout), you can suppress this exception by setting","`suppress_callback_exceptions=True`.",y(n)])}function h(t,n,o,i,u){var a=o.split("@")[0],c=ne(n,r),s=An(c);if(s&&s.propTypes&&!s.propTypes[a]){for(var f in s.propTypes){var l=f.length-1;if("*"===f.charAt(l)&&a.substr(0,l)===f.substr(0,l))return}var p=c.type,d=c.namespace;e("Invalid prop for this component",['Property "'.concat(a,'" was used with component ID:'),"  ".concat(JSON.stringify(t)),"in one of the ".concat(i," items of a callback."),"This ID is assigned to a ".concat(d,".").concat(p," component"),"in the layout, which does not support this property.",y(u)])}}function v(t,e,r,o){Ao()(n)({id:t,property:e}).forEach((function(t){h(t.id,t.path,e,r,o)}))}var b={};function m(t){var e=t.state,r=t.output;if(!b[r]){b[r]=1;var o="State";e.forEach((function(e){var r=e.id,i=e.property;if("string"==typeof r){var u=Pn(n,r);u?h(r,u,i,o,[t]):c&&d(r,o,[t])}else Cr([Un,Ln],Rr(r)).length||v(r,i,o,[t])}))}}function g(t,e,r){var o=function(o){var i=t[o],u=Ee(Rr(i));if(ir((function(t){return t.allow_optional}),Ee(u.map((function(t){return tr(t.outputs,t.inputs,t.states)}))).filter((function(t){return t.id===o}))))return 1;var a=Pn(n,o);if(a)for(var s in i){var f=i[s];h(o,a,s,e,f),r&&f.forEach(m)}else c&&d(o,e,u)};for(var i in t)o(i)}function O(t,e,r){for(var n in t){var o=t[n],i=function(t){o[t].forEach((function(n){var o=n.keys,i=n.values,u=n.callbacks;v(gr(o,i),t,e,u),r&&u.forEach(m)}))};for(var u in o)i(u)}}g(s,"Output",!0),g(f,"Input"),O(l,"Output",!0),O(p,"Input")}(e(),Zu(t)),function(t,e){var r=e(),n=r.graphs,o=r.paths,i=r.layout;try{n.MultiGraph.overallOrder()}catch(e){t(Gu({type:"backEnd",error:{message:"Circular Dependencies",html:e.toString()}}))}t(tc(So(n,o,i.components,{outputsOnly:!0})))}(t,e),t(Fu(To("HYDRATED")))}))}catch(t){a.frontEnd.length||a.backEnd.length||u(Gu({type:"backEnd",error:t})),n=!0}finally{r(n)}}}))}Mf.propTypes={appLifecycle:u().oneOf([To("STARTED"),To("HYDRATED"),To("DESTROYED")]),dispatch:u().func,dependenciesRequest:u().object,graphs:u().object,hooks:u().object,layoutRequest:u().object,layout:u().any,history:u().any,error:u().object,config:u().object};var Lf=z((function(t){return{appLifecycle:t.appLifecycle,dependenciesRequest:t.dependenciesRequest,hooks:t.hooks,layoutRequest:t.layoutRequest,layout:t.layout,graphs:t.graphs,history:t.history,error:t.error,config:t.config}}),(function(t){return{dispatch:t}}))(Mf);function qf(t){return t.isLoading?e().createElement("div",{className:"_dash-loading-callback"}):null}qf.propTypes={isLoading:u().bool.isRequired};var Bf=z((function(t){return{isLoading:t.isLoading}}))(qf),Gf=r(72),Ff=r.n(Gf),Hf=r(825),$f=r.n(Hf),Jf=r(659),zf=r.n(Jf),Wf=r(56),Vf=r.n(Wf),Kf=r(540),Yf=r.n(Kf),Qf=r(113),Xf=r.n(Qf),Zf=r(217),tl={};function el(t){var r=t.dispatch,n=t.history,o=e().createElement("span",{key:"undoLink",className:"_dash-undo-redo-link",onClick:function(){return r(na)}},e().createElement("div",{className:"_dash-icon-undo"},"↺"),e().createElement("div",{className:"_dash-undo-redo-label"},"undo")),i=e().createElement("span",{key:"redoLink",className:"_dash-undo-redo-link",onClick:function(){return r(ra)}},e().createElement("div",{className:"_dash-icon-redo"},"↻"),e().createElement("div",{className:"_dash-undo-redo-label"},"redo"));return e().createElement("div",{className:"_dash-undo-redo"},e().createElement("div",null,n.past.length>0?o:null,n.future.length>0?i:null))}tl.styleTagTransform=Xf(),tl.setAttributes=Vf(),tl.insert=zf().bind(null,"head"),tl.domAPI=$f(),tl.insertStyleElement=Yf(),Ff()(Zf.A,tl),Zf.A&&Zf.A.locals&&Zf.A.locals,el.propTypes={history:u().object,dispatch:u().func};var rl=z((function(t){return{history:t.history}}),(function(t){return{dispatch:t}}))(el),nl=nt((function(t){return function(e,r){return t(e,r)?-1:t(r,e)?1:0}})),ol=Ot((function(t,e){return t<e}));function il(t){return il="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},il(t)}function ul(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function al(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,cl(n.key),n)}}function cl(t){var e=function(t){if("object"!=il(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=il(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==il(e)?e:e+""}function sl(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(sl=function(){return!!t})()}function fl(t){return fl=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},fl(t)}function ll(t,e){return ll=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},ll(t,e)}var pl=function(t){function e(t){var r;if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),r=function(t,e,r){return e=fl(e),function(t,e){if(e&&("object"==il(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,sl()?Reflect.construct(e,r||[],fl(t).constructor):e.apply(t,r))}(this,e,[t]),t.config.hot_reload){var n=t.config.hot_reload,o=n.interval,i=n.max_retry;r.state={interval:o,disabled:!1,intervalId:null,packages:null,max_retry:i}}else r.state={disabled:!0};return r._retry=0,r._head=document.querySelector("head"),r.clearInterval=r.clearInterval.bind(r),r}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ll(t,e)}(e,t),r=e,n=[{key:"clearInterval",value:function(){window.clearInterval(this.state.intervalId),this.setState({intervalId:null})}},{key:"componentDidUpdate",value:function(t,e){var r=this.state.reloadRequest,n=this.props.dispatch;if(r&&pn("reloadRequest",e))if(200===r.status&&ne(["content","reloadHash"],r)!==ne(["reloadRequest","content","reloadHash"],e))if(!r.content.hard&&Ut(r.content.packages.length,Vt([],["reloadRequest","content","packages"],e).length)&&Ut(Xc(nl(ol),r.content.packages),Xc(nl(ol),Vt([],["reloadRequest","content","packages"],e))))n({type:"RELOAD"});else{var o,i=!1,u=function(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return ul(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ul(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,u=!0,a=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return u=t.done,t},e:function(t){a=!0,i=t},f:function(){try{u||null==r.return||r.return()}finally{if(a)throw i}}}}(r.content.files);try{for(u.s();!(o=u.n()).done;){var a=o.value;if(!a.is_css){i=!1;break}i=!0;for(var c=[],s=document.evaluate('//link[contains(@href, "'.concat(a.url,'")]'),this._head),f=s.iterateNext();f;)c.push(f),f=s.iterateNext();if(re((function(t){return t.setAttribute("disabled","disabled")}),c),a.modified>0){var l=document.createElement("link");l.href="".concat(a.url,"?m=").concat(a.modified),l.type="text/css",l.rel="stylesheet",this._head.appendChild(l)}}}catch(t){u.e(t)}finally{u.f()}i||window.location.reload()}else null!==this.state.intervalId&&500===r.status&&(this._retry>this.state.max_retry&&(this.clearInterval(),window.alert("Hot reloading is disabled after failing ".concat(this._retry," times. ")+"Please check your application for errors, then refresh the page.")),this._retry++)}},{key:"componentDidMount",value:function(){var t=this.props,e=t.dispatch,r=t.reloadRequest,n=this.state,o=n.disabled,i=n.interval;if(!o&&!this.state.intervalId){var u=window.setInterval((function(){"loading"!==r.status&&e(Ea("_reload-hash","GET","reloadRequest"))}),i);this.setState({intervalId:u})}}},{key:"componentWillUnmount",value:function(){!this.state.disabled&&this.state.intervalId&&this.clearInterval()}},{key:"render",value:function(){return null}}],o=[{key:"getDerivedStateFromProps",value:function(t){return ae(t.reloadRequest)||"loading"===t.reloadRequest.status?null:{reloadRequest:t.reloadRequest}}}],n&&al(r.prototype,n),o&&al(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}(e().Component);pl.defaultProps={},pl.propTypes={id:u().string,config:u().object,reloadRequest:u().object,dispatch:u().func,interval:u().number};var yl=z((function(t){return{config:t.config,reloadRequest:t.reloadRequest}}),(function(t){return{dispatch:t}}))(pl),dl=Ot((function(t,e){var r={};return tt(e.length,(function(){var n=t.apply(this,arguments);return jt(n,r)||(r[n]=e.apply(this,arguments)),r[n]}))})),hl=dl;function vl(t){return vl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},vl(t)}function bl(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ml(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?bl(Object(r),!0).forEach((function(e){gl(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):bl(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function gl(t,e,r){return(e=wl(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Ol(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,wl(n.key),n)}}function wl(t){var e=function(t){if("object"!=vl(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=vl(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==vl(e)?e:e+""}function Sl(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Sl=function(){return!!t})()}function jl(t){return jl=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},jl(t)}function _l(t,e){return _l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},_l(t,e)}var El=function(t){function r(t){var e;if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,r),e=function(t,e,r){return e=jl(e),function(t,e){if(e&&("object"==vl(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Sl()?Reflect.construct(e,r||[],jl(t).constructor):e.apply(t,r))}(this,r,[t]),null!==t.hooks.layout_pre||null!==t.hooks.layout_post||null!==t.hooks.request_pre||null!==t.hooks.request_post||null!==t.hooks.callback_resolved||null!==t.hooks.request_refresh_jwt){var n=t.hooks;n.request_refresh_jwt&&(n=ml(ml({},n),{},{request_refresh_jwt:hl(Ar,n.request_refresh_jwt)})),t.dispatch(zu(n))}return e}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&_l(t,e)}(r,t),n=r,o=[{key:"UNSAFE_componentWillMount",value:function(){var t,e=this.props.dispatch,r=(t=document.getElementById("_dash-config"),JSON.parse(null!=t&&t.textContent?null==t?void 0:t.textContent:"{}"));r.fetch={credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}},e(Hu(r))}},{key:"render",value:function(){var t=this.props.config;if("Null"===Rt(t))return e().createElement("div",{className:"_dash-loading"},"Loading...");var r=t.show_undo_redo;return e().createElement(e().Fragment,null,r?e().createElement(rl,null):null,e().createElement(Lf,null),e().createElement(Bf,null),e().createElement(yl,null))}}],o&&Ol(n.prototype,o),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o}(e().Component);El.propTypes={hooks:u().object,dispatch:u().func,config:u().object};var Pl=z((function(t){return{history:t.history,config:t.config}}),(function(t){return{dispatch:t}}))(El);function Al(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var kl=function(r){var n,o,i=r.hooks,u=void 0===i?{layout_pre:null,layout_post:null,request_pre:null,request_post:null,callback_resolved:null,request_refresh_jwt:null}:i,a=(n=(0,t.useState)((function(){return new Hs})),o=1,function(t){if(Array.isArray(t))return t}(n)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,u,a=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(a.push(n.value),a.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(u=r.return(),Object(u)!==u))return}finally{if(s)throw o}}return a}}(n,o)||function(t,e){if(t){if("string"==typeof t)return Al(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Al(t,e):void 0}}(n,o)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())[0].store;return e().createElement(W,{store:a},e().createElement(Pl,{hooks:u}))};kl.propTypes={hooks:u().shape({layout_pre:u().func,layout_post:u().func,request_pre:u().func,request_post:u().func,callback_resolved:u().func,request_refresh_jwt:u().func})};var xl=kl;function Tl(){return window.dash_stores=window.dash_stores||[]}function Il(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function Cl(t){return Cl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cl(t)}function Rl(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Nl(n.key),n)}}function Dl(t,e,r){return e&&Rl(t.prototype,e),r&&Rl(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function Nl(t){var e=function(t){if("object"!=Cl(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=Cl(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Cl(e)?e:e+""}window.dash_component_api={ExternalWrapper:function(r){var o,i,u=r.component,a=r.componentPath,c=r.temp,s=void 0!==c&&c,f=Z(),l=(o=(0,t.useState)(!1),i=2,function(t){if(Array.isArray(t))return t}(o)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,u,a=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(a.push(n.value),a.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(u=r.return(),Object(u)!==u))return}finally{if(s)throw o}}return a}}(o,i)||function(t,e){if(t){if("string"==typeof t)return Il(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Il(t,e):void 0}}(o,i)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),p=l[0],y=l[1];return(0,t.useEffect)((function(){var t;return f((t={component:u,componentPath:a},function(e,r){var n=r().paths;e(Ku(t)),e(Vu(En(t.component,t.componentPath,n)))})),y(!0),function(){s&&f(Yu({componentPath:a}))}}),[]),(0,t.useEffect)((function(){(0,n.unstable_batchedUpdates)((function(){f(Xu({itempath:a,props:u.props})),u.props.id&&f(ua({id:u.props.id,props:u.props}))}))}),[u.props]),p?e().createElement(If,{componentPath:a}):null},DashContext:Of,useDashContext:function(){var e=(0,t.useContext)(Of);return e||console.error("Dash Context was not found, component was rendered without a wrapper. Use `window.dash_component_api.ExternalWrapper` to make sure the component is properly connected."),e||{}},getLayout:function(t){for(var e=Tl(),r=0;r<e.length;r++){var n,o=e[r].getState(),i=o.paths,u=o.layout;n=Array.isArray(t)?t:Pn(i,t);var a=ne(n,u);if(void 0!==a)return a}},stringifyId:Jn};var Ml=Dl((function t(r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t);var n=document.getElementById("react-entry-point");o().createRoot?o().createRoot(n).render(e().createElement(xl,{hooks:r})):o().render(e().createElement(xl,{hooks:r}),n)})),Ul=/^([^\w]*)(javascript|vbscript)/im,Ll=/&(tab|newline);/gi,ql=/[\u0000-\u001F\u007F-\u009F\u2000-\u200D\uFEFF]/gim,Bl=/&#(\w+)(^\w|;)?/g,Gl=window.dash_clientside=window.dash_clientside||{};Gl.set_props=function(t,e){for(var r=Tl(),n=0;n<r.length;n++){var o=r[n],i=o.dispatch,u=(0,o.getState)().paths;i(Xu({props:e,itempath:Array.isArray(t)?t:Pn(u,t),renderType:"clientsideApi"})),i(ua({id:t,props:e}))}},Gl.clean_url=void 0===Gl.clean_url?function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"about:blank";if(""===t)return t;var r=t.replace(Ll,"").replace(ql,"").replace(Bl,(function(t,e){return String.fromCharCode(e)})).trim();return Ul.test(r)?e:t}:Gl.clean_url,window.DashRenderer=Ml}(),window.dash_renderer={}}();