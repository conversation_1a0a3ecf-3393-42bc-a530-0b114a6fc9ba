{"name": "dash-table", "version": "6.0.4", "description": "Dash table", "repository": {"type": "git", "url": "**************:plotly/dash.git"}, "bugs": {"url": "https://github.com/plotly/dash/issues"}, "homepage": "https://github.com/plotly/dash", "main": "dash_table/bundle.js", "scripts": {"preprivate::test.server": "run-s private::wait_dash*", "private::build": "node --max_old_space_size=4096 node_modules/webpack/bin/webpack --bail", "private::build:js": "run-s \"private::build -- --mode production\"", "private::build:js-test": "run-s \"private::build -- --mode development --config webpack.test.config.js\"", "private::build:js-test-watch": "run-s \"private::build -- --mode development --config webpack.test.config.js --watch\"", "private::build:backends": "dash-generate-components src/dash-table/dash/DataTable.js dash_table -p package-info.json && cp dash_table_base/** dash_table/ && dash-generate-components src/dash-table/dash/DataTable.js dash_table -p package-info.json -k DataTable --r-prefix 'dash' --r-suggests 'dash' --jl-prefix 'dash' && black dash_table", "private::format.ts": "npm run private::lint.ts -- --fix", "private::format.prettier": "prettier --config .prettierrc --write \"{src,tests,demo}/**/*.{js,ts,tsx}\"", "private::format.black": "black dash_table_base tests", "private::lint.ts": "eslint ./src ./tests", "private::lint.flake": "flake8 dash_table_base tests", "private::lint.black": "black --check dash_table_base tests", "private::lint.prettier": "prettier --config .prettierrc \"{src,tests,demo}/**/*.{js,ts,tsx}\" --list-different", "private::test.python": "python -m unittest tests/unit/format_test.py", "private::test.unit": "karma start karma.conf.js --single-run", "build.watch": "webpack serve --disable-host-check --content-base dash_table --mode development --config webpack.dev.config.js", "build": "run-s private::build:js private::build:backends", "postbuild": "es-check es2015 dash_table/bundle.js dash_table/async-*.js", "format": "run-s private::format.*", "lint": "run-s private::lint.*", "test.server": "pytest --nopercyfinalize tests/selenium", "test.unit": "run-s private::test.python private::test.unit", "test.visual": "npm install --package-lock-only=false --no-save @percy/storybook@^3.3.1 @storybook/builder-webpack5@^6.5.13 @storybook/cli@^6.5.13 @storybook/manager-webpack5@^6.5.16 @storybook/react@^6.5.13 @storybook/semver@^7.3.2 && build-storybook && percy-storybook --widths=1280", "test.visual-local": "npm install --package-lock-only=false --no-save @percy/storybook@^3.3.1 @storybook/builder-webpack5@^6.5.13 @storybook/cli@^6.5.13 @storybook/manager-webpack5@^6.5.16 @storybook/react@^6.5.13 @storybook/semver@^7.3.2 && build-storybook"}, "author": "<PERSON> <<EMAIL>>", "maintainer": "<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@babel/cli": "^7.28.0", "@babel/core": "^7.28.0", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-regenerator": "^7.28.1", "@babel/polyfill": "^7.12.1", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@fortawesome/fontawesome-svg-core": "1.2.36", "@fortawesome/free-regular-svg-icons": "^5.15.4", "@fortawesome/free-solid-svg-icons": "^5.15.4", "@fortawesome/react-fontawesome": "^0.2.2", "@plotly/dash-component-plugins": "^1.2.3", "@plotly/webpack-dash-dynamic-import": "^1.3.0", "@types/chai": "^4.3.5", "@types/d3-format": "^3.0.1", "@types/mocha": "^10.0.1", "@types/papaparse": "^5.3.7", "@types/ramda": "0.30.2", "@types/react": "^16.14.8", "@types/react-dom": "^16.9.13", "@types/react-select": "^4.0.16", "@typescript-eslint/eslint-plugin": "^5.59.7", "@typescript-eslint/parser": "^5.59.7", "babel-loader": "^9.2.1", "chai": "^4.3.7", "css-loader": "^6.8.1", "css.escape": "^1.5.1", "d3-format": "^3.1.0", "es-check": "^7.1.1", "eslint": "^8.41.0", "eslint-config-prettier": "^8.8.0", "fast-isnumeric": "^1.1.4", "file-loader": "^6.2.0", "highlight.js": "^11.8.0", "karma": "^6.4.2", "karma-chrome-launcher": "^3.2.0", "karma-mocha": "^2.0.1", "karma-typescript": "^5.5.4", "karma-webpack": "^5.0.0", "less": "^4.1.3", "less-loader": "^11.1.0", "mocha": "^10.2.0", "npm-run-all": "^4.1.5", "papaparse": "^5.4.1", "prettier": "^2.8.8", "ramda": "^0.30.1", "raw-loader": "^4.0.2", "react": "^16.14.0", "react-docgen": "^5.4.3", "react-dom": "^16.14.0", "react-select": "^1.3.0", "regenerator-runtime": "^0.13.11", "remarkable": "^2.0.1", "sheetclip": "^0.3.0", "style-loader": "^3.3.3", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "webpack": "^5.101.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "5.2.2", "webpack-preprocessor": "^0.1.12", "xlsx": "^0.17.5", "rimraf": "^5.0.5"}, "files": ["/dash_table/async-*{.js,.map}", "/dash_table/bundle*{.js,.map}"], "peerDependencies": {"prop-types": "^15.7.2", "react": ">=16", "react-dom": ">=16"}, "engines": {"node": ">=12.0.0", "npm": ">=6.1.0"}, "browserslist": ["last 9 years and not dead"]}