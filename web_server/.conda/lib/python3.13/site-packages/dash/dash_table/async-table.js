/*! For license information please see async-table.js.LICENSE.txt */
(self.webpackChunkdash_table=self.webpackChunkdash_table||[]).push([[214],{371:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(5564);function o(e,t){return function(){var n=arguments.length;if(0===n)return t();var o=arguments[n-1];return(0,r.A)(o)||"function"!=typeof o[e]?t.apply(this,arguments):o[e].apply(o,Array.prototype.slice.call(arguments,0,n-1))}}},726:(e,t,n)=>{"use strict";function r(e,t){return e===t||o(Object.values(e),Object.values(t))}function o(e,t){if(!e)return!1;var n=e.length;if(n!==t.length)return!1;for(var r=0;r<n;++r)if(e[r]!==t[r])return!1;return!0}n.d(t,{n:()=>r,y:()=>o})},794:(e,t,n)=>{"use strict";var r,o;n.d(t,{D:()=>r,F:()=>o}),function(e){e.Text="text",e.Markdown="markdown"}(r||(r={})),function(e){e.Both="both",e.Data="data",e.Header="header"}(o||(o={}))},954:(e,t,n)=>{"use strict";var r=(0,n(2173).A)(function(e,t,n){var r=Array.prototype.slice.call(n,0);return r.splice(e,t),r});t.A=r},1322:(e,t,n)=>{"use strict";var r=(0,n(3579).A)(function(e){return null===e?"Null":void 0===e?"Undefined":Object.prototype.toString.call(e).slice(8,-1)});t.A=r},1467:(e,t,n)=>{"use strict";var r=n(1354),o=n.n(r),i=n(6314),a=n.n(i)()(o());a.push([e.id,"/**\n * React Select\n * ============\n * Created by Jed Watson and Joss Mackison for KeystoneJS, http://www.keystonejs.com/\n * https://twitter.com/jedwatson https://twitter.com/jossmackison https://twitter.com/keystonejs\n * MIT License: https://github.com/JedWatson/react-select\n*/\n.Select {\n  position: relative;\n}\n.Select input::-webkit-contacts-auto-fill-button,\n.Select input::-webkit-credentials-auto-fill-button {\n  display: none !important;\n}\n.Select input::-ms-clear {\n  display: none !important;\n}\n.Select input::-ms-reveal {\n  display: none !important;\n}\n.Select,\n.Select div,\n.Select input,\n.Select span {\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n}\n.Select.is-disabled .Select-arrow-zone {\n  cursor: default;\n  pointer-events: none;\n  opacity: 0.35;\n}\n.Select.is-disabled > .Select-control {\n  background-color: #f9f9f9;\n}\n.Select.is-disabled > .Select-control:hover {\n  box-shadow: none;\n}\n.Select.is-open > .Select-control {\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 0;\n  background: #fff;\n  border-color: #b3b3b3 #ccc #d9d9d9;\n}\n.Select.is-open > .Select-control .Select-arrow {\n  top: -2px;\n  border-color: transparent transparent #999;\n  border-width: 0 5px 5px;\n}\n.Select.is-searchable.is-open > .Select-control {\n  cursor: text;\n}\n.Select.is-searchable.is-focused:not(.is-open) > .Select-control {\n  cursor: text;\n}\n.Select.is-focused > .Select-control {\n  background: #fff;\n}\n.Select.is-focused:not(.is-open) > .Select-control {\n  border-color: #007eff;\n  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 3px rgba(0, 126, 255, 0.1);\n  background: #fff;\n}\n.Select.has-value.is-clearable.Select--single > .Select-control .Select-value {\n  padding-right: 42px;\n}\n.Select.has-value.Select--single > .Select-control .Select-value .Select-value-label,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value .Select-value-label {\n  color: #333;\n}\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label {\n  cursor: pointer;\n  text-decoration: none;\n}\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label:hover,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label:hover,\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label:focus,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label:focus {\n  color: #007eff;\n  outline: none;\n  text-decoration: underline;\n}\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label:focus,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label:focus {\n  background: #fff;\n}\n.Select.has-value.is-pseudo-focused .Select-input {\n  opacity: 0;\n}\n.Select.is-open .Select-arrow,\n.Select .Select-arrow-zone:hover > .Select-arrow {\n  border-top-color: #666;\n}\n.Select.Select--rtl {\n  direction: rtl;\n  text-align: right;\n}\n.Select-control {\n  background-color: #fff;\n  border-color: #d9d9d9 #ccc #b3b3b3;\n  border-radius: 4px;\n  border: 1px solid #ccc;\n  color: #333;\n  cursor: default;\n  display: table;\n  border-spacing: 0;\n  border-collapse: separate;\n  height: 36px;\n  outline: none;\n  overflow: hidden;\n  position: relative;\n  width: 100%;\n}\n.Select-control:hover {\n  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.06);\n}\n.Select-control .Select-input:focus {\n  outline: none;\n  background: #fff;\n}\n.Select-placeholder,\n.Select--single > .Select-control .Select-value {\n  bottom: 0;\n  color: #aaa;\n  left: 0;\n  line-height: 34px;\n  padding-left: 10px;\n  padding-right: 10px;\n  position: absolute;\n  right: 0;\n  top: 0;\n  max-width: 100%;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.Select-input {\n  height: 34px;\n  padding-left: 10px;\n  padding-right: 10px;\n  vertical-align: middle;\n}\n.Select-input > input {\n  width: 100%;\n  background: none transparent;\n  border: 0 none;\n  box-shadow: none;\n  cursor: default;\n  display: inline-block;\n  font-family: inherit;\n  font-size: inherit;\n  margin: 0;\n  outline: none;\n  line-height: 17px;\n  /* For IE 8 compatibility */\n  padding: 8px 0 12px;\n  /* For IE 8 compatibility */\n  -webkit-appearance: none;\n}\n.is-focused .Select-input > input {\n  cursor: text;\n}\n.has-value.is-pseudo-focused .Select-input {\n  opacity: 0;\n}\n.Select-control:not(.is-searchable) > .Select-input {\n  outline: none;\n}\n.Select-loading-zone {\n  cursor: pointer;\n  display: table-cell;\n  position: relative;\n  text-align: center;\n  vertical-align: middle;\n  width: 16px;\n}\n.Select-loading {\n  -webkit-animation: Select-animation-spin 400ms infinite linear;\n  -o-animation: Select-animation-spin 400ms infinite linear;\n  animation: Select-animation-spin 400ms infinite linear;\n  width: 16px;\n  height: 16px;\n  box-sizing: border-box;\n  border-radius: 50%;\n  border: 2px solid #ccc;\n  border-right-color: #333;\n  display: inline-block;\n  position: relative;\n  vertical-align: middle;\n}\n.Select-clear-zone {\n  -webkit-animation: Select-animation-fadeIn 200ms;\n  -o-animation: Select-animation-fadeIn 200ms;\n  animation: Select-animation-fadeIn 200ms;\n  color: #999;\n  cursor: pointer;\n  display: table-cell;\n  position: relative;\n  text-align: center;\n  vertical-align: middle;\n  width: 17px;\n}\n.Select-clear-zone:hover {\n  color: #D0021B;\n}\n.Select-clear {\n  display: inline-block;\n  font-size: 18px;\n  line-height: 1;\n}\n.Select--multi .Select-clear-zone {\n  width: 17px;\n}\n.Select-arrow-zone {\n  cursor: pointer;\n  display: table-cell;\n  position: relative;\n  text-align: center;\n  vertical-align: middle;\n  width: 25px;\n  padding-right: 5px;\n}\n.Select--rtl .Select-arrow-zone {\n  padding-right: 0;\n  padding-left: 5px;\n}\n.Select-arrow {\n  border-color: #999 transparent transparent;\n  border-style: solid;\n  border-width: 5px 5px 2.5px;\n  display: inline-block;\n  height: 0;\n  width: 0;\n  position: relative;\n}\n.Select-control > *:last-child {\n  padding-right: 5px;\n}\n.Select--multi .Select-multi-value-wrapper {\n  display: inline-block;\n}\n.Select .Select-aria-only {\n  position: absolute;\n  display: inline-block;\n  height: 1px;\n  width: 1px;\n  margin: -1px;\n  clip: rect(0, 0, 0, 0);\n  overflow: hidden;\n  float: left;\n}\n@-webkit-keyframes Select-animation-fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n@keyframes Select-animation-fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n.Select-menu-outer {\n  border-bottom-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n  background-color: #fff;\n  border: 1px solid #ccc;\n  border-top-color: #e6e6e6;\n  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.06);\n  box-sizing: border-box;\n  margin-top: -1px;\n  max-height: 200px;\n  position: absolute;\n  left: 0;\n  top: 100%;\n  width: 100%;\n  z-index: 1;\n  -webkit-overflow-scrolling: touch;\n}\n.Select-menu {\n  max-height: 198px;\n  overflow-y: auto;\n}\n.Select-option {\n  box-sizing: border-box;\n  background-color: #fff;\n  color: #666666;\n  cursor: pointer;\n  display: block;\n  padding: 8px 10px;\n}\n.Select-option:last-child {\n  border-bottom-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n}\n.Select-option.is-selected {\n  background-color: #f5faff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.04);\n  color: #333;\n}\n.Select-option.is-focused {\n  background-color: #ebf5ff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.08);\n  color: #333;\n}\n.Select-option.is-disabled {\n  color: #cccccc;\n  cursor: default;\n}\n.Select-noresults {\n  box-sizing: border-box;\n  color: #999999;\n  cursor: default;\n  display: block;\n  padding: 8px 10px;\n}\n.Select--multi .Select-input {\n  vertical-align: middle;\n  margin-left: 10px;\n  padding: 0;\n}\n.Select--multi.Select--rtl .Select-input {\n  margin-left: 0;\n  margin-right: 10px;\n}\n.Select--multi.has-value .Select-input {\n  margin-left: 5px;\n}\n.Select--multi .Select-value {\n  background-color: #ebf5ff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.08);\n  border-radius: 2px;\n  border: 1px solid #c2e0ff;\n  /* Fallback color for IE 8 */\n  border: 1px solid rgba(0, 126, 255, 0.24);\n  color: #007eff;\n  display: inline-block;\n  font-size: 0.9em;\n  line-height: 1.4;\n  margin-left: 5px;\n  margin-top: 5px;\n  vertical-align: top;\n}\n.Select--multi .Select-value-icon,\n.Select--multi .Select-value-label {\n  display: inline-block;\n  vertical-align: middle;\n}\n.Select--multi .Select-value-label {\n  border-bottom-right-radius: 2px;\n  border-top-right-radius: 2px;\n  cursor: default;\n  padding: 2px 5px;\n}\n.Select--multi a.Select-value-label {\n  color: #007eff;\n  cursor: pointer;\n  text-decoration: none;\n}\n.Select--multi a.Select-value-label:hover {\n  text-decoration: underline;\n}\n.Select--multi .Select-value-icon {\n  cursor: pointer;\n  border-bottom-left-radius: 2px;\n  border-top-left-radius: 2px;\n  border-right: 1px solid #c2e0ff;\n  /* Fallback color for IE 8 */\n  border-right: 1px solid rgba(0, 126, 255, 0.24);\n  padding: 1px 5px 3px;\n}\n.Select--multi .Select-value-icon:hover,\n.Select--multi .Select-value-icon:focus {\n  background-color: #d8eafd;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 113, 230, 0.08);\n  color: #0071e6;\n}\n.Select--multi .Select-value-icon:active {\n  background-color: #c2e0ff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.24);\n}\n.Select--multi.Select--rtl .Select-value {\n  margin-left: 0;\n  margin-right: 5px;\n}\n.Select--multi.Select--rtl .Select-value-icon {\n  border-right: none;\n  border-left: 1px solid #c2e0ff;\n  /* Fallback color for IE 8 */\n  border-left: 1px solid rgba(0, 126, 255, 0.24);\n}\n.Select--multi.is-disabled .Select-value {\n  background-color: #fcfcfc;\n  border: 1px solid #e3e3e3;\n  color: #333;\n}\n.Select--multi.is-disabled .Select-value-icon {\n  cursor: not-allowed;\n  border-right: 1px solid #e3e3e3;\n}\n.Select--multi.is-disabled .Select-value-icon:hover,\n.Select--multi.is-disabled .Select-value-icon:focus,\n.Select--multi.is-disabled .Select-value-icon:active {\n  background-color: #fcfcfc;\n}\n@keyframes Select-animation-spin {\n  to {\n    transform: rotate(1turn);\n  }\n}\n@-webkit-keyframes Select-animation-spin {\n  to {\n    -webkit-transform: rotate(1turn);\n  }\n}\n","",{version:3,sources:["webpack://./node_modules/react-select/dist/react-select.css"],names:[],mappings:"AAAA;;;;;;CAMC;AACD;EACE,kBAAkB;AACpB;AACA;;EAEE,wBAAwB;AAC1B;AACA;EACE,wBAAwB;AAC1B;AACA;EACE,wBAAwB;AAC1B;AACA;;;;EAIE,8BAA8B;EAC9B,2BAA2B;EAC3B,sBAAsB;AACxB;AACA;EACE,eAAe;EACf,oBAAoB;EACpB,aAAa;AACf;AACA;EACE,yBAAyB;AAC3B;AACA;EACE,gBAAgB;AAClB;AACA;EACE,6BAA6B;EAC7B,4BAA4B;EAC5B,gBAAgB;EAChB,kCAAkC;AACpC;AACA;EACE,SAAS;EACT,0CAA0C;EAC1C,uBAAuB;AACzB;AACA;EACE,YAAY;AACd;AACA;EACE,YAAY;AACd;AACA;EACE,gBAAgB;AAClB;AACA;EACE,qBAAqB;EACrB,kFAAkF;EAClF,gBAAgB;AAClB;AACA;EACE,mBAAmB;AACrB;AACA;;EAEE,WAAW;AACb;AACA;;EAEE,eAAe;EACf,qBAAqB;AACvB;AACA;;;;EAIE,cAAc;EACd,aAAa;EACb,0BAA0B;AAC5B;AACA;;EAEE,gBAAgB;AAClB;AACA;EACE,UAAU;AACZ;AACA;;EAEE,sBAAsB;AACxB;AACA;EACE,cAAc;EACd,iBAAiB;AACnB;AACA;EACE,sBAAsB;EACtB,kCAAkC;EAClC,kBAAkB;EAClB,sBAAsB;EACtB,WAAW;EACX,eAAe;EACf,cAAc;EACd,iBAAiB;EACjB,yBAAyB;EACzB,YAAY;EACZ,aAAa;EACb,gBAAgB;EAChB,kBAAkB;EAClB,WAAW;AACb;AACA;EACE,uCAAuC;AACzC;AACA;EACE,aAAa;EACb,gBAAgB;AAClB;AACA;;EAEE,SAAS;EACT,WAAW;EACX,OAAO;EACP,iBAAiB;EACjB,kBAAkB;EAClB,mBAAmB;EACnB,kBAAkB;EAClB,QAAQ;EACR,MAAM;EACN,eAAe;EACf,gBAAgB;EAChB,uBAAuB;EACvB,mBAAmB;AACrB;AACA;EACE,YAAY;EACZ,kBAAkB;EAClB,mBAAmB;EACnB,sBAAsB;AACxB;AACA;EACE,WAAW;EACX,4BAA4B;EAC5B,cAAc;EACd,gBAAgB;EAChB,eAAe;EACf,qBAAqB;EACrB,oBAAoB;EACpB,kBAAkB;EAClB,SAAS;EACT,aAAa;EACb,iBAAiB;EACjB,2BAA2B;EAC3B,mBAAmB;EACnB,2BAA2B;EAC3B,wBAAwB;AAC1B;AACA;EACE,YAAY;AACd;AACA;EACE,UAAU;AACZ;AACA;EACE,aAAa;AACf;AACA;EACE,eAAe;EACf,mBAAmB;EACnB,kBAAkB;EAClB,kBAAkB;EAClB,sBAAsB;EACtB,WAAW;AACb;AACA;EACE,8DAA8D;EAC9D,yDAAyD;EACzD,sDAAsD;EACtD,WAAW;EACX,YAAY;EACZ,sBAAsB;EACtB,kBAAkB;EAClB,sBAAsB;EACtB,wBAAwB;EACxB,qBAAqB;EACrB,kBAAkB;EAClB,sBAAsB;AACxB;AACA;EACE,gDAAgD;EAChD,2CAA2C;EAC3C,wCAAwC;EACxC,WAAW;EACX,eAAe;EACf,mBAAmB;EACnB,kBAAkB;EAClB,kBAAkB;EAClB,sBAAsB;EACtB,WAAW;AACb;AACA;EACE,cAAc;AAChB;AACA;EACE,qBAAqB;EACrB,eAAe;EACf,cAAc;AAChB;AACA;EACE,WAAW;AACb;AACA;EACE,eAAe;EACf,mBAAmB;EACnB,kBAAkB;EAClB,kBAAkB;EAClB,sBAAsB;EACtB,WAAW;EACX,kBAAkB;AACpB;AACA;EACE,gBAAgB;EAChB,iBAAiB;AACnB;AACA;EACE,0CAA0C;EAC1C,mBAAmB;EACnB,2BAA2B;EAC3B,qBAAqB;EACrB,SAAS;EACT,QAAQ;EACR,kBAAkB;AACpB;AACA;EACE,kBAAkB;AACpB;AACA;EACE,qBAAqB;AACvB;AACA;EACE,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,UAAU;EACV,YAAY;EACZ,sBAAsB;EACtB,gBAAgB;EAChB,WAAW;AACb;AACA;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;AACA;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;AACA;EACE,+BAA+B;EAC/B,8BAA8B;EAC9B,sBAAsB;EACtB,sBAAsB;EACtB,yBAAyB;EACzB,uCAAuC;EACvC,sBAAsB;EACtB,gBAAgB;EAChB,iBAAiB;EACjB,kBAAkB;EAClB,OAAO;EACP,SAAS;EACT,WAAW;EACX,UAAU;EACV,iCAAiC;AACnC;AACA;EACE,iBAAiB;EACjB,gBAAgB;AAClB;AACA;EACE,sBAAsB;EACtB,sBAAsB;EACtB,cAAc;EACd,eAAe;EACf,cAAc;EACd,iBAAiB;AACnB;AACA;EACE,+BAA+B;EAC/B,8BAA8B;AAChC;AACA;EACE,yBAAyB;EACzB,4BAA4B;EAC5B,yCAAyC;EACzC,WAAW;AACb;AACA;EACE,yBAAyB;EACzB,4BAA4B;EAC5B,yCAAyC;EACzC,WAAW;AACb;AACA;EACE,cAAc;EACd,eAAe;AACjB;AACA;EACE,sBAAsB;EACtB,cAAc;EACd,eAAe;EACf,cAAc;EACd,iBAAiB;AACnB;AACA;EACE,sBAAsB;EACtB,iBAAiB;EACjB,UAAU;AACZ;AACA;EACE,cAAc;EACd,kBAAkB;AACpB;AACA;EACE,gBAAgB;AAClB;AACA;EACE,yBAAyB;EACzB,4BAA4B;EAC5B,yCAAyC;EACzC,kBAAkB;EAClB,yBAAyB;EACzB,4BAA4B;EAC5B,yCAAyC;EACzC,cAAc;EACd,qBAAqB;EACrB,gBAAgB;EAChB,gBAAgB;EAChB,gBAAgB;EAChB,eAAe;EACf,mBAAmB;AACrB;AACA;;EAEE,qBAAqB;EACrB,sBAAsB;AACxB;AACA;EACE,+BAA+B;EAC/B,4BAA4B;EAC5B,eAAe;EACf,gBAAgB;AAClB;AACA;EACE,cAAc;EACd,eAAe;EACf,qBAAqB;AACvB;AACA;EACE,0BAA0B;AAC5B;AACA;EACE,eAAe;EACf,8BAA8B;EAC9B,2BAA2B;EAC3B,+BAA+B;EAC/B,4BAA4B;EAC5B,+CAA+C;EAC/C,oBAAoB;AACtB;AACA;;EAEE,yBAAyB;EACzB,4BAA4B;EAC5B,yCAAyC;EACzC,cAAc;AAChB;AACA;EACE,yBAAyB;EACzB,4BAA4B;EAC5B,yCAAyC;AAC3C;AACA;EACE,cAAc;EACd,iBAAiB;AACnB;AACA;EACE,kBAAkB;EAClB,8BAA8B;EAC9B,4BAA4B;EAC5B,8CAA8C;AAChD;AACA;EACE,yBAAyB;EACzB,yBAAyB;EACzB,WAAW;AACb;AACA;EACE,mBAAmB;EACnB,+BAA+B;AACjC;AACA;;;EAGE,yBAAyB;AAC3B;AACA;EACE;IACE,wBAAwB;EAC1B;AACF;AACA;EACE;IACE,gCAAgC;EAClC;AACF",sourcesContent:["/**\n * React Select\n * ============\n * Created by Jed Watson and Joss Mackison for KeystoneJS, http://www.keystonejs.com/\n * https://twitter.com/jedwatson https://twitter.com/jossmackison https://twitter.com/keystonejs\n * MIT License: https://github.com/JedWatson/react-select\n*/\n.Select {\n  position: relative;\n}\n.Select input::-webkit-contacts-auto-fill-button,\n.Select input::-webkit-credentials-auto-fill-button {\n  display: none !important;\n}\n.Select input::-ms-clear {\n  display: none !important;\n}\n.Select input::-ms-reveal {\n  display: none !important;\n}\n.Select,\n.Select div,\n.Select input,\n.Select span {\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n}\n.Select.is-disabled .Select-arrow-zone {\n  cursor: default;\n  pointer-events: none;\n  opacity: 0.35;\n}\n.Select.is-disabled > .Select-control {\n  background-color: #f9f9f9;\n}\n.Select.is-disabled > .Select-control:hover {\n  box-shadow: none;\n}\n.Select.is-open > .Select-control {\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 0;\n  background: #fff;\n  border-color: #b3b3b3 #ccc #d9d9d9;\n}\n.Select.is-open > .Select-control .Select-arrow {\n  top: -2px;\n  border-color: transparent transparent #999;\n  border-width: 0 5px 5px;\n}\n.Select.is-searchable.is-open > .Select-control {\n  cursor: text;\n}\n.Select.is-searchable.is-focused:not(.is-open) > .Select-control {\n  cursor: text;\n}\n.Select.is-focused > .Select-control {\n  background: #fff;\n}\n.Select.is-focused:not(.is-open) > .Select-control {\n  border-color: #007eff;\n  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 3px rgba(0, 126, 255, 0.1);\n  background: #fff;\n}\n.Select.has-value.is-clearable.Select--single > .Select-control .Select-value {\n  padding-right: 42px;\n}\n.Select.has-value.Select--single > .Select-control .Select-value .Select-value-label,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value .Select-value-label {\n  color: #333;\n}\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label {\n  cursor: pointer;\n  text-decoration: none;\n}\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label:hover,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label:hover,\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label:focus,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label:focus {\n  color: #007eff;\n  outline: none;\n  text-decoration: underline;\n}\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label:focus,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label:focus {\n  background: #fff;\n}\n.Select.has-value.is-pseudo-focused .Select-input {\n  opacity: 0;\n}\n.Select.is-open .Select-arrow,\n.Select .Select-arrow-zone:hover > .Select-arrow {\n  border-top-color: #666;\n}\n.Select.Select--rtl {\n  direction: rtl;\n  text-align: right;\n}\n.Select-control {\n  background-color: #fff;\n  border-color: #d9d9d9 #ccc #b3b3b3;\n  border-radius: 4px;\n  border: 1px solid #ccc;\n  color: #333;\n  cursor: default;\n  display: table;\n  border-spacing: 0;\n  border-collapse: separate;\n  height: 36px;\n  outline: none;\n  overflow: hidden;\n  position: relative;\n  width: 100%;\n}\n.Select-control:hover {\n  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.06);\n}\n.Select-control .Select-input:focus {\n  outline: none;\n  background: #fff;\n}\n.Select-placeholder,\n.Select--single > .Select-control .Select-value {\n  bottom: 0;\n  color: #aaa;\n  left: 0;\n  line-height: 34px;\n  padding-left: 10px;\n  padding-right: 10px;\n  position: absolute;\n  right: 0;\n  top: 0;\n  max-width: 100%;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.Select-input {\n  height: 34px;\n  padding-left: 10px;\n  padding-right: 10px;\n  vertical-align: middle;\n}\n.Select-input > input {\n  width: 100%;\n  background: none transparent;\n  border: 0 none;\n  box-shadow: none;\n  cursor: default;\n  display: inline-block;\n  font-family: inherit;\n  font-size: inherit;\n  margin: 0;\n  outline: none;\n  line-height: 17px;\n  /* For IE 8 compatibility */\n  padding: 8px 0 12px;\n  /* For IE 8 compatibility */\n  -webkit-appearance: none;\n}\n.is-focused .Select-input > input {\n  cursor: text;\n}\n.has-value.is-pseudo-focused .Select-input {\n  opacity: 0;\n}\n.Select-control:not(.is-searchable) > .Select-input {\n  outline: none;\n}\n.Select-loading-zone {\n  cursor: pointer;\n  display: table-cell;\n  position: relative;\n  text-align: center;\n  vertical-align: middle;\n  width: 16px;\n}\n.Select-loading {\n  -webkit-animation: Select-animation-spin 400ms infinite linear;\n  -o-animation: Select-animation-spin 400ms infinite linear;\n  animation: Select-animation-spin 400ms infinite linear;\n  width: 16px;\n  height: 16px;\n  box-sizing: border-box;\n  border-radius: 50%;\n  border: 2px solid #ccc;\n  border-right-color: #333;\n  display: inline-block;\n  position: relative;\n  vertical-align: middle;\n}\n.Select-clear-zone {\n  -webkit-animation: Select-animation-fadeIn 200ms;\n  -o-animation: Select-animation-fadeIn 200ms;\n  animation: Select-animation-fadeIn 200ms;\n  color: #999;\n  cursor: pointer;\n  display: table-cell;\n  position: relative;\n  text-align: center;\n  vertical-align: middle;\n  width: 17px;\n}\n.Select-clear-zone:hover {\n  color: #D0021B;\n}\n.Select-clear {\n  display: inline-block;\n  font-size: 18px;\n  line-height: 1;\n}\n.Select--multi .Select-clear-zone {\n  width: 17px;\n}\n.Select-arrow-zone {\n  cursor: pointer;\n  display: table-cell;\n  position: relative;\n  text-align: center;\n  vertical-align: middle;\n  width: 25px;\n  padding-right: 5px;\n}\n.Select--rtl .Select-arrow-zone {\n  padding-right: 0;\n  padding-left: 5px;\n}\n.Select-arrow {\n  border-color: #999 transparent transparent;\n  border-style: solid;\n  border-width: 5px 5px 2.5px;\n  display: inline-block;\n  height: 0;\n  width: 0;\n  position: relative;\n}\n.Select-control > *:last-child {\n  padding-right: 5px;\n}\n.Select--multi .Select-multi-value-wrapper {\n  display: inline-block;\n}\n.Select .Select-aria-only {\n  position: absolute;\n  display: inline-block;\n  height: 1px;\n  width: 1px;\n  margin: -1px;\n  clip: rect(0, 0, 0, 0);\n  overflow: hidden;\n  float: left;\n}\n@-webkit-keyframes Select-animation-fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n@keyframes Select-animation-fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n.Select-menu-outer {\n  border-bottom-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n  background-color: #fff;\n  border: 1px solid #ccc;\n  border-top-color: #e6e6e6;\n  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.06);\n  box-sizing: border-box;\n  margin-top: -1px;\n  max-height: 200px;\n  position: absolute;\n  left: 0;\n  top: 100%;\n  width: 100%;\n  z-index: 1;\n  -webkit-overflow-scrolling: touch;\n}\n.Select-menu {\n  max-height: 198px;\n  overflow-y: auto;\n}\n.Select-option {\n  box-sizing: border-box;\n  background-color: #fff;\n  color: #666666;\n  cursor: pointer;\n  display: block;\n  padding: 8px 10px;\n}\n.Select-option:last-child {\n  border-bottom-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n}\n.Select-option.is-selected {\n  background-color: #f5faff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.04);\n  color: #333;\n}\n.Select-option.is-focused {\n  background-color: #ebf5ff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.08);\n  color: #333;\n}\n.Select-option.is-disabled {\n  color: #cccccc;\n  cursor: default;\n}\n.Select-noresults {\n  box-sizing: border-box;\n  color: #999999;\n  cursor: default;\n  display: block;\n  padding: 8px 10px;\n}\n.Select--multi .Select-input {\n  vertical-align: middle;\n  margin-left: 10px;\n  padding: 0;\n}\n.Select--multi.Select--rtl .Select-input {\n  margin-left: 0;\n  margin-right: 10px;\n}\n.Select--multi.has-value .Select-input {\n  margin-left: 5px;\n}\n.Select--multi .Select-value {\n  background-color: #ebf5ff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.08);\n  border-radius: 2px;\n  border: 1px solid #c2e0ff;\n  /* Fallback color for IE 8 */\n  border: 1px solid rgba(0, 126, 255, 0.24);\n  color: #007eff;\n  display: inline-block;\n  font-size: 0.9em;\n  line-height: 1.4;\n  margin-left: 5px;\n  margin-top: 5px;\n  vertical-align: top;\n}\n.Select--multi .Select-value-icon,\n.Select--multi .Select-value-label {\n  display: inline-block;\n  vertical-align: middle;\n}\n.Select--multi .Select-value-label {\n  border-bottom-right-radius: 2px;\n  border-top-right-radius: 2px;\n  cursor: default;\n  padding: 2px 5px;\n}\n.Select--multi a.Select-value-label {\n  color: #007eff;\n  cursor: pointer;\n  text-decoration: none;\n}\n.Select--multi a.Select-value-label:hover {\n  text-decoration: underline;\n}\n.Select--multi .Select-value-icon {\n  cursor: pointer;\n  border-bottom-left-radius: 2px;\n  border-top-left-radius: 2px;\n  border-right: 1px solid #c2e0ff;\n  /* Fallback color for IE 8 */\n  border-right: 1px solid rgba(0, 126, 255, 0.24);\n  padding: 1px 5px 3px;\n}\n.Select--multi .Select-value-icon:hover,\n.Select--multi .Select-value-icon:focus {\n  background-color: #d8eafd;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 113, 230, 0.08);\n  color: #0071e6;\n}\n.Select--multi .Select-value-icon:active {\n  background-color: #c2e0ff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.24);\n}\n.Select--multi.Select--rtl .Select-value {\n  margin-left: 0;\n  margin-right: 5px;\n}\n.Select--multi.Select--rtl .Select-value-icon {\n  border-right: none;\n  border-left: 1px solid #c2e0ff;\n  /* Fallback color for IE 8 */\n  border-left: 1px solid rgba(0, 126, 255, 0.24);\n}\n.Select--multi.is-disabled .Select-value {\n  background-color: #fcfcfc;\n  border: 1px solid #e3e3e3;\n  color: #333;\n}\n.Select--multi.is-disabled .Select-value-icon {\n  cursor: not-allowed;\n  border-right: 1px solid #e3e3e3;\n}\n.Select--multi.is-disabled .Select-value-icon:hover,\n.Select--multi.is-disabled .Select-value-icon:focus,\n.Select--multi.is-disabled .Select-value-icon:active {\n  background-color: #fcfcfc;\n}\n@keyframes Select-animation-spin {\n  to {\n    transform: rotate(1turn);\n  }\n}\n@-webkit-keyframes Select-animation-spin {\n  to {\n    -webkit-transform: rotate(1turn);\n  }\n}\n"],sourceRoot:""}]),t.A=a},1487:(e,t,n)=>{"use strict";var r=n(3112),o=(0,n(2254).A)(r.A);t.A=o},1608:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(1322);function o(e,t,n){if(n||(n=new i),function(e){var t=typeof e;return null==e||"object"!=t&&"function"!=t}(e))return e;var a,s=function(r){var i=n.get(e);if(i)return i;for(var a in n.set(e,r),e)Object.prototype.hasOwnProperty.call(e,a)&&(r[a]=t?o(e[a],!0,n):e[a]);return r};switch((0,r.A)(e)){case"Object":return s(Object.create(Object.getPrototypeOf(e)));case"Array":return s(Array(e.length));case"Date":return new Date(e.valueOf());case"RegExp":return a=e,new RegExp(a.source,a.flags?a.flags:(a.global?"g":"")+(a.ignoreCase?"i":"")+(a.multiline?"m":"")+(a.sticky?"y":"")+(a.unicode?"u":"")+(a.dotAll?"s":""));case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":return e.slice();default:return e}}var i=function(){function e(){this.map={},this.length=0}return e.prototype.set=function(e,t){var n=this.hash(e),r=this.map[n];r||(this.map[n]=r=[]),r.push([e,t]),this.length+=1},e.prototype.hash=function(e){var t=[];for(var n in e)t.push(Object.prototype.toString.call(e[n]));return t.join()},e.prototype.get=function(e){if(this.length<=180){for(var t in this.map)for(var n=this.map[t],r=0;r<n.length;r+=1)if((i=n[r])[0]===e)return i[1]}else{var o=this.hash(e);if(n=this.map[o])for(r=0;r<n.length;r+=1){var i;if((i=n[r])[0]===e)return i[1]}}},e}(),a=(0,n(3579).A)(function(e){return null!=e&&"function"==typeof e.clone?e.clone():o(e,!0)})},1831:function(e){!function(t){"use strict";function n(){}function r(e){return e.split('"').length-1}n.prototype=Object.create(Object.prototype,{parse:{value:function(e){var t,n,o,i,a,s,l,c=[],u=0;for((o=e.split("\n")).length>1&&""===o[o.length-1]&&o.pop(),t=0,n=o.length;t<n;t+=1){for(o[t]=o[t].split("\t"),i=0,a=o[t].length;i<a;i+=1)c[u]||(c[u]=[]),s&&0===i?(l=c[u].length-1,c[u][l]=c[u][l]+"\n"+o[t][0],s&&1&r(o[t][0])&&(s=!1,c[u][l]=c[u][l].substring(0,c[u][l].length-1).replace(/""/g,'"'))):i===a-1&&0===o[t][i].indexOf('"')&&1&r(o[t][i])?(c[u].push(o[t][i].substring(1).replace(/""/g,'"')),s=!0):(c[u].push(o[t][i].replace(/""/g,'"')),s=!1);s||(u+=1)}return c},enumerable:!0,configurable:!1,writable:!1},stringify:{value:function(e){var t,n,r,o,i,a="";for(t=0,n=e.length;t<n;t+=1){for(r=0,o=e[t].length;r<o;r+=1)r>0&&(a+="\t"),"string"==typeof(i=e[t][r])?i.indexOf("\n")>-1?a+='"'+i.replace(/"/g,'""')+'"':a+=i:a+=null==i?"":i;a+="\n"}return a},enumerable:!0,configurable:!1,writable:!1}}),e.exports?e.exports=n:t.SheetClip=n}(this)},2537:(e,t,n)=>{"use strict";function r(e){return"[object Number]"===Object.prototype.toString.call(e)}n.d(t,{A:()=>o});var o=(0,n(2254).A)(function(e,t){if(!r(e)||!r(t))throw new TypeError("Both arguments to range must be numbers");for(var n=Array(e<t?t-e:0),o=e<0?t+Math.abs(e):t-e,i=0;i<o;)n[i]=i+e,i+=1;return n})},2870:(e,t,n)=>{"use strict";function r(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var o,i,a,s,l,c,u,d,p,h,f;n.d(t,{$C:()=>o,AV:()=>a,Ie:()=>c,Jm:()=>f,VP:()=>A,Xw:()=>p,iV:()=>d,ru:()=>s,se:()=>i,vb:()=>h,vh:()=>u,ze:()=>l}),function(e){e.Any="any",e.Numeric="numeric",e.Text="text",e.Datetime="datetime"}(o||(o={})),function(e){e.All="all",e.Visible="visible"}(i||(i={})),function(e){e.Csv="csv",e.Xlsx="xlsx",e.None="none"}(a||(a={})),function(e){e.Ids="ids",e.Names="names",e.None="none",e.Display="display"}(s||(s={})),function(e){e.Insensitive="insensitive",e.Sensitive="sensitive"}(l||(l={})),function(e){e.Single="single",e.Multi="multi"}(c||(c={})),function(e){e.Custom="custom",e.Native="native",e.None="none"}(u||(u={})),function(e){e.And="and",e.Or="or"}(d||(d={}));class A{constructor(e){r(this,"clearable",void 0),r(this,"deletable",void 0),r(this,"editable",!1),r(this,"filter_options",void 0),r(this,"hideable",void 0),r(this,"renamable",void 0),r(this,"selectable",void 0),r(this,"sort_as_null",[]),r(this,"id",void 0),r(this,"name",[]),Object.keys(e).includes("name")&&(this.name=e.name),Object.keys(e).includes("id")&&(this.id=e.id)}}!function(e){e.Coerce="coerce",e.None="none",e.Validate="validate"}(p||(p={})),function(e){e.Default="default",e.Accept="accept",e.Reject="reject"}(h||(h={})),function(e){e.Dropdown="dropdown",e.Input="input",e.Markdown="markdown"}(f||(f={}))},3111:(e,t,n)=>{"use strict";n.d(t,{A:()=>h});var r=n(2254);function o(e){for(var t,n=[];!(t=e.next()).done;)n.push(t.value);return n}function i(e,t,n){for(var r=0,o=n.length;r<o;){if(e(t,n[r]))return!0;r+=1}return!1}var a=n(1069),s="function"==typeof Object.is?Object.is:function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t},l=n(2959),c=n(1322);function u(e,t,n,r){var a=o(e);function s(e,t){return d(e,t,n.slice(),r.slice())}return!i(function(e,t){return!i(s,t,e)},o(t),a)}function d(e,t,n,r){if(s(e,t))return!0;var o,i,p=(0,c.A)(e);if(p!==(0,c.A)(t))return!1;if("function"==typeof e["fantasy-land/equals"]||"function"==typeof t["fantasy-land/equals"])return"function"==typeof e["fantasy-land/equals"]&&e["fantasy-land/equals"](t)&&"function"==typeof t["fantasy-land/equals"]&&t["fantasy-land/equals"](e);if("function"==typeof e.equals||"function"==typeof t.equals)return"function"==typeof e.equals&&e.equals(t)&&"function"==typeof t.equals&&t.equals(e);switch(p){case"Arguments":case"Array":case"Object":if("function"==typeof e.constructor&&"Promise"===(o=e.constructor,null==(i=String(o).match(/^function (\w*)/))?"":i[1]))return e===t;break;case"Boolean":case"Number":case"String":if(typeof e!=typeof t||!s(e.valueOf(),t.valueOf()))return!1;break;case"Date":if(!s(e.valueOf(),t.valueOf()))return!1;break;case"Error":return e.name===t.name&&e.message===t.message;case"RegExp":if(e.source!==t.source||e.global!==t.global||e.ignoreCase!==t.ignoreCase||e.multiline!==t.multiline||e.sticky!==t.sticky||e.unicode!==t.unicode)return!1}for(var h=n.length-1;h>=0;){if(n[h]===e)return r[h]===t;h-=1}switch(p){case"Map":return e.size===t.size&&u(e.entries(),t.entries(),n.concat([e]),r.concat([t]));case"Set":return e.size===t.size&&u(e.values(),t.values(),n.concat([e]),r.concat([t]));case"Arguments":case"Array":case"Object":case"Boolean":case"Number":case"String":case"Date":case"Error":case"RegExp":case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"ArrayBuffer":break;default:return!1}var f=(0,l.A)(e);if(f.length!==(0,l.A)(t).length)return!1;var A=n.concat([e]),v=r.concat([t]);for(h=f.length-1;h>=0;){var b=f[h];if(!(0,a.A)(b,t)||!d(t[b],e[b],A,v))return!1;h-=1}return!0}var p=(0,r.A)(function(e,t){return d(e,t,[],[])});function h(e,t,n){var r,o;if("function"==typeof e.indexOf)switch(typeof t){case"number":if(0===t){for(r=1/t;n<e.length;){if(0===(o=e[n])&&1/o===r)return n;n+=1}return-1}if(t!=t){for(;n<e.length;){if("number"==typeof(o=e[n])&&o!=o)return n;n+=1}return-1}return e.indexOf(t,n);case"string":case"boolean":case"function":case"undefined":return e.indexOf(t,n);case"object":if(null===t)return e.indexOf(t,n)}for(;n<e.length;){if(p(e[n],t))return n;n+=1}return-1}},3112:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(3111);function o(e,t){return(0,r.A)(t,e,0)>=0}},3650:(e,t,n)=>{"use strict";var r=n(1354),o=n.n(r),i=n(6314),a=n.n(i)()(o());a.push([e.id,".dash-spreadsheet .Select,\n.dash-spreadsheet .Select-control {\n    border: none;\n    cursor: pointer;\n    height: 30px;  /* matches the td height and line-height */\n    border: none;\n}\n\n.dash-spreadsheet .Select-placeholder,\n.dash-spreadsheet .Select--single > .Select-control .Select-value {\n    height: 100%;\n    line-height: inherit;\n}\n\n.dash-spreadsheet .Select.has-value.Select--single > .Select-control .Select-value .Select-value-label,\n.dash-spreadsheet .Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value .Select-value-label {\n    color: var(--text-color);\n}\n\n.dash-spreadsheet .Select:hover,\n.dash-spreadsheet .Select-control:hover {\n    border: none;\n    box-shadow: none;\n    cursor: pointer;\n}\n\n.dash-spreadsheet .Select-menu-outer {\n    z-index: 100;\n}\n\n.dash-spreadsheet .Select-arrow {\n    border-top-color: var(--faded-dropdown);\n    border-left-color: transparent;\n    border-right-color: transparent;\n    border-bottom-color: transparent;\n}\n\n.dash-spreadsheet .Select-control:hover .Select-arrow {\n    border-top-color: var(--accent);\n    border-left-color: transparent;\n    border-right-color: transparent;\n    border-bottom-color: transparent;\n}\n\n.dash-spreadsheet .is-open > .Select-control .Select-arrow {\n    border-bottom-color: var(--background-color-8-react-select);\n    border-left-color: transparent;\n    border-right-color: transparent;\n    border-top-color: transparent;\n}\n\n.dash-spreadsheet .Select--multi .Select-value,\n.dash-spreadsheet .Select--multi a.Select-value-label {\n    color: var(--color-8-react-select);\n}\n\n.dash-spreadsheet .Select {\n    border-radius: 0;\n}\n\n.dash-spreadsheet .Select.is-disabled > .Select-control {\n    border: none;\n}\n\n.dash-spreadsheet .Select--multi .Select-value-icon:hover,\n.dash-spreadsheet .Select--multi .Select-value-icon:focus {\n    font-weight: bold;\n}\n\n.dash-spreadsheet .Select.is-disabled .Select-control {\n    cursor: not-allowed;\n}\n\n.dash-spreadsheet .Select-menu-outer {\n    background-color: white;\n    border: none;\n    border-bottom: thin var(--border) solid;\n    border-radius: 0;\n    border-top: none;\n}\n\n.dash-spreadsheet .Select-option {\n    background-color: inherit;\n    color: var(--accent);\n}\n\n.dash-spreadsheet .Select-option:hover {\n    font-weight: bold;\n    color: var(--accent);\n    background-color: var(--hover);\n}\n\n.dash-spreadsheet .Select.is-focused:not(.is-open) > .Select-control {\n    border: none;\n    -webkit-box-shadow: none;\n    box-shadow: none;\n    background: #fff;\n}\n\n.dash-spreadsheet .Select-option.is-focused {\n    background-color: white;\n    color: var(--accent);\n    font-weight: bold;\n}\n\n.dash-spreadsheet .Select-clear {\n    color: transparent;\n}\n.dash-spreadsheet .Select:hover .Select-clear {\n    color: var(--accent);\n}\n\n.dash-spreadsheet .Select-control {\n    padding-left: 2px;\n}\n","",{version:3,sources:["webpack://./src/dash-table/components/Table/Dropdown.css"],names:[],mappings:"AAAA;;IAEI,YAAY;IACZ,eAAe;IACf,YAAY,GAAG,0CAA0C;IACzD,YAAY;AAChB;;AAEA;;IAEI,YAAY;IACZ,oBAAoB;AACxB;;AAEA;;IAEI,wBAAwB;AAC5B;;AAEA;;IAEI,YAAY;IACZ,gBAAgB;IAChB,eAAe;AACnB;;AAEA;IACI,YAAY;AAChB;;AAEA;IACI,uCAAuC;IACvC,8BAA8B;IAC9B,+BAA+B;IAC/B,gCAAgC;AACpC;;AAEA;IACI,+BAA+B;IAC/B,8BAA8B;IAC9B,+BAA+B;IAC/B,gCAAgC;AACpC;;AAEA;IACI,2DAA2D;IAC3D,8BAA8B;IAC9B,+BAA+B;IAC/B,6BAA6B;AACjC;;AAEA;;IAEI,kCAAkC;AACtC;;AAEA;IACI,gBAAgB;AACpB;;AAEA;IACI,YAAY;AAChB;;AAEA;;IAEI,iBAAiB;AACrB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,uBAAuB;IACvB,YAAY;IACZ,uCAAuC;IACvC,gBAAgB;IAChB,gBAAgB;AACpB;;AAEA;IACI,yBAAyB;IACzB,oBAAoB;AACxB;;AAEA;IACI,iBAAiB;IACjB,oBAAoB;IACpB,8BAA8B;AAClC;;AAEA;IACI,YAAY;IACZ,wBAAwB;IACxB,gBAAgB;IAChB,gBAAgB;AACpB;;AAEA;IACI,uBAAuB;IACvB,oBAAoB;IACpB,iBAAiB;AACrB;;AAEA;IACI,kBAAkB;AACtB;AACA;IACI,oBAAoB;AACxB;;AAEA;IACI,iBAAiB;AACrB",sourcesContent:[".dash-spreadsheet .Select,\n.dash-spreadsheet .Select-control {\n    border: none;\n    cursor: pointer;\n    height: 30px;  /* matches the td height and line-height */\n    border: none;\n}\n\n.dash-spreadsheet .Select-placeholder,\n.dash-spreadsheet .Select--single > .Select-control .Select-value {\n    height: 100%;\n    line-height: inherit;\n}\n\n.dash-spreadsheet .Select.has-value.Select--single > .Select-control .Select-value .Select-value-label,\n.dash-spreadsheet .Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value .Select-value-label {\n    color: var(--text-color);\n}\n\n.dash-spreadsheet .Select:hover,\n.dash-spreadsheet .Select-control:hover {\n    border: none;\n    box-shadow: none;\n    cursor: pointer;\n}\n\n.dash-spreadsheet .Select-menu-outer {\n    z-index: 100;\n}\n\n.dash-spreadsheet .Select-arrow {\n    border-top-color: var(--faded-dropdown);\n    border-left-color: transparent;\n    border-right-color: transparent;\n    border-bottom-color: transparent;\n}\n\n.dash-spreadsheet .Select-control:hover .Select-arrow {\n    border-top-color: var(--accent);\n    border-left-color: transparent;\n    border-right-color: transparent;\n    border-bottom-color: transparent;\n}\n\n.dash-spreadsheet .is-open > .Select-control .Select-arrow {\n    border-bottom-color: var(--background-color-8-react-select);\n    border-left-color: transparent;\n    border-right-color: transparent;\n    border-top-color: transparent;\n}\n\n.dash-spreadsheet .Select--multi .Select-value,\n.dash-spreadsheet .Select--multi a.Select-value-label {\n    color: var(--color-8-react-select);\n}\n\n.dash-spreadsheet .Select {\n    border-radius: 0;\n}\n\n.dash-spreadsheet .Select.is-disabled > .Select-control {\n    border: none;\n}\n\n.dash-spreadsheet .Select--multi .Select-value-icon:hover,\n.dash-spreadsheet .Select--multi .Select-value-icon:focus {\n    font-weight: bold;\n}\n\n.dash-spreadsheet .Select.is-disabled .Select-control {\n    cursor: not-allowed;\n}\n\n.dash-spreadsheet .Select-menu-outer {\n    background-color: white;\n    border: none;\n    border-bottom: thin var(--border) solid;\n    border-radius: 0;\n    border-top: none;\n}\n\n.dash-spreadsheet .Select-option {\n    background-color: inherit;\n    color: var(--accent);\n}\n\n.dash-spreadsheet .Select-option:hover {\n    font-weight: bold;\n    color: var(--accent);\n    background-color: var(--hover);\n}\n\n.dash-spreadsheet .Select.is-focused:not(.is-open) > .Select-control {\n    border: none;\n    -webkit-box-shadow: none;\n    box-shadow: none;\n    background: #fff;\n}\n\n.dash-spreadsheet .Select-option.is-focused {\n    background-color: white;\n    color: var(--accent);\n    font-weight: bold;\n}\n\n.dash-spreadsheet .Select-clear {\n    color: transparent;\n}\n.dash-spreadsheet .Select:hover .Select-clear {\n    color: var(--accent);\n}\n\n.dash-spreadsheet .Select-control {\n    padding-left: 2px;\n}\n"],sourceRoot:""}]),t.A=a},3700:(e,t,n)=>{"use strict";var r=n(4239),o=(0,n(2254).A)(function(e,t){return(0,r.A)({},e,t)});t.A=o},3838:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>wh});var r=n(1609),o=n.n(r),i=n(2254),a=n(1069),s=n(1647),l=(0,i.A)(function(e,t){if(0===e.length||(0,s.A)(t))return!1;for(var n=t,r=0;r<e.length;){if((0,s.A)(n)||!(0,a.A)(e[r],n))return!1;n=n[e[r]],r+=1}return!0}),c=(0,i.A)(function(e,t){return l([e],t)}),u=c,d=n(3579),p=(0,d.A)(function(e){var t,n=[];for(t in e)n[n.length]=t;return n}),h=p,f=n(7667),A=n(4239),v=(0,d.A)(function(e){return A.A.apply(null,[{}].concat(e))}),b=n(2039),g=n(2598),m=n(2173),y=(0,d.A)(function(e){return function(){return e}}),w=function(e){return{value:e,map:function(t){return w(t(e))}}},E=(0,m.A)(function(e,t,n){return e(function(e){return w(t(e))})(n).value}),C=E,x=(0,m.A)(function(e,t,n){return C(e,y(t),n)}),k=x,S=n(2270),O=(0,i.A)(function(e,t){return function(n){return function(r){return(0,g.A)(function(e){return t(e,r)},n(e(r)))}}}),B=n(4279),_=n(6359),j=(0,d.A)(function(e){return O(function(t){return function(e,t){for(var n=t,r=0;r<e.length;r+=1){if(null==n)return;var o=e[r];n=(0,B.A)(o)?(0,_.A)(o,n):n[o]}return n}(e,t)},(0,S.A)(e))}),P=j,D=n(3112),F=n(8267);function I(e){return'"'+e.replace(/\\/g,"\\\\").replace(/[\b]/g,"\\b").replace(/\f/g,"\\f").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\t/g,"\\t").replace(/\v/g,"\\v").replace(/\0/g,"\\0").replace(/"/g,'\\"')+'"'}var T=function(e){return(e<10?"0":"")+e},M="function"==typeof Date.prototype.toISOString?function(e){return e.toISOString()}:function(e){return e.getUTCFullYear()+"-"+T(e.getUTCMonth()+1)+"-"+T(e.getUTCDate())+"T"+T(e.getUTCHours())+":"+T(e.getUTCMinutes())+":"+T(e.getUTCSeconds())+"."+(e.getUTCMilliseconds()/1e3).toFixed(3).slice(2,5)+"Z"},R=n(2959),z=n(3430),N=n(845);function L(e,t){for(var n=0,r=t.length,o=[];n<r;)e(t[n])&&(o[o.length]=t[n]),n+=1;return o}var q=n(1878),W=function(){function e(e,t){this.xf=t,this.f=e}return e.prototype["@@transducer/init"]=q.A.init,e.prototype["@@transducer/result"]=q.A.result,e.prototype["@@transducer/step"]=function(e,t){return this.f(t)?this.xf["@@transducer/step"](e,t):e},e}();function V(e){return function(t){return new W(e,t)}}var U=(0,i.A)((0,N.A)(["fantasy-land/filter","filter"],V,function(e,t){return n=t,"[object Object]"===Object.prototype.toString.call(n)?(0,z.A)(function(n,r){return e(t[r])&&(n[r]=t[r]),n},{},(0,R.A)(t)):L(e,t);var n})),Y=(0,i.A)(function(e,t){return U((n=e,function(){return!n.apply(this,arguments)}),t);var n});function H(e,t){var n=function(n){var r=t.concat([e]);return(0,D.A)(n,r)?"<Circular>":H(n,r)},r=function(e,t){return(0,F.A)(function(t){return I(t)+": "+n(e[t])},t.slice().sort())};switch(Object.prototype.toString.call(e)){case"[object Arguments]":return"(function() { return arguments; }("+(0,F.A)(n,e).join(", ")+"))";case"[object Array]":return"["+(0,F.A)(n,e).concat(r(e,Y(function(e){return/^\d+$/.test(e)},(0,R.A)(e)))).join(", ")+"]";case"[object Boolean]":return"object"==typeof e?"new Boolean("+n(e.valueOf())+")":e.toString();case"[object Date]":return"new Date("+(isNaN(e.valueOf())?n(NaN):I(M(e)))+")";case"[object Map]":return"new Map("+n(Array.from(e))+")";case"[object Null]":return"null";case"[object Number]":return"object"==typeof e?"new Number("+n(e.valueOf())+")":1/e==-1/0?"-0":e.toString(10);case"[object Set]":return"new Set("+n(Array.from(e).sort())+")";case"[object String]":return"object"==typeof e?"new String("+n(e.valueOf())+")":I(e);case"[object Undefined]":return"undefined";default:if("function"==typeof e.toString){var o=e.toString();if("[object Object]"!==o)return o}return"{"+r(e,(0,R.A)(e)).join(", ")+"}"}}var K=(0,d.A)(function(e){return H(e,[])}),$=(0,i.A)(function(e,t){if(e===t)return t;function n(e,t){if(e>t!=t>e)return t>e?t:e}var r=n(e,t);if(void 0!==r)return r;var o=n(typeof e,typeof t);if(void 0!==o)return o===typeof e?e:t;var i=K(e),a=n(i,K(t));return void 0!==a&&a===i?e:t}),G=(0,i.A)(function(e,t){if(e===t)return e;function n(e,t){if(e<t!=t<e)return t<e?t:e}var r=n(e,t);if(void 0!==r)return r;var o=n(typeof e,typeof t);if(void 0!==o)return o===typeof e?e:t;var i=K(e),a=n(i,K(t));return void 0!==a?a===i?e:t:e}),Z=n(9614),Q=n(1487),X=(0,d.A)(function(e){for(var t=(0,R.A)(e),n=t.length,r=[],o=0;o<n;)r[o]=e[t[o]],o+=1;return r}),J=X;function ee(e){return e}var te=n(5564),ne=n(8228),re=(0,d.A)(function(e){return!!(0,te.A)(e)||!!e&&"object"==typeof e&&!(0,ne.A)(e)&&(0===e.length||e.length>0&&e.hasOwnProperty(0)&&e.hasOwnProperty(e.length-1))});function oe(e,t,n){for(var r=0,o=n.length;r<o;){if((t=e["@@transducer/step"](t,n[r]))&&t["@@transducer/reduced"]){t=t["@@transducer/value"];break}r+=1}return e["@@transducer/result"](t)}var ie="undefined"!=typeof Symbol?Symbol.iterator:"@@iterator";function ae(e,t,n){return function(r,o,i){if(re(i))return e(r,o,i);if(null==i)return o;if("function"==typeof i["fantasy-land/reduce"])return t(r,o,i,"fantasy-land/reduce");if(null!=i[ie])return n(r,o,i[ie]());if("function"==typeof i.next)return n(r,o,i);if("function"==typeof i.reduce)return t(r,o,i,"reduce");throw new TypeError("reduce: list must be array or iterable")}}var se=n(7660),le=(0,i.A)(function(e,t){return(0,se.A)(e.length,function(){return e.apply(t,arguments)})});function ce(e,t,n){for(var r=n.next();!r.done;){if((t=e["@@transducer/step"](t,r.value))&&t["@@transducer/reduced"]){t=t["@@transducer/value"];break}r=n.next()}return e["@@transducer/result"](t)}function ue(e,t,n,r){return e["@@transducer/result"](n[r](le(e["@@transducer/step"],e),t))}var de=ae(oe,ue,ce),pe="@@transducer/init",he="@@transducer/step",fe="@@transducer/result",Ae=function(){function e(e){this.xf=e}return e.prototype[pe]=q.A.init,e.prototype[fe]=q.A.result,e.prototype[he]=function(e,t){var n=this.xf[he](e,t);return n["@@transducer/reduced"]?{"@@transducer/value":n,"@@transducer/reduced":!0}:n},e}(),ve=function(){function e(e){this.xf=new Ae(e)}return e.prototype[pe]=q.A.init,e.prototype[fe]=q.A.result,e.prototype[he]=function(e,t){return re(t)?de(this.xf,e,t):oe(this.xf,e,[t])},e}(),be=n(9607);function ge(e){return function(t){return(0,be.A)(e)(function(e){return new ve(e)}(t))}}var me=(0,i.A)((0,N.A)(["fantasy-land/chain","chain"],ge,function(e,t){return"function"==typeof t?function(n){return e(t(n))(n)}:function(e){for(var t,n,r,o=[],i=0,a=e.length;i<a;){if(re(e[i]))for(r=0,n=(t=e[i]).length;r<n;)o[o.length]=t[r],r+=1;else o[o.length]=e[i];i+=1}return o}((0,g.A)(e,t))}))(ee);function ye(e){return e&&e["@@transducer/reduced"]?e:{"@@transducer/value":e,"@@transducer/reduced":!0}}var we=function(){function e(e,t){this.xf=t,this.f=e,this.any=!1}return e.prototype["@@transducer/init"]=q.A.init,e.prototype["@@transducer/result"]=function(e){return this.any||(e=this.xf["@@transducer/step"](e,!1)),this.xf["@@transducer/result"](e)},e.prototype["@@transducer/step"]=function(e,t){return this.f(t)&&(this.any=!0,e=ye(this.xf["@@transducer/step"](e,!0))),e},e}();function Ee(e){return function(t){return new we(e,t)}}var Ce=(0,i.A)((0,N.A)(["any"],Ee,function(e,t){for(var n=0;n<t.length;){if(e(t[n]))return!0;n+=1}return!1})),xe=n(2314);function ke(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Se{constructor(e){ke(this,"name",void 0),ke(this,"__stylesheet",void 0),this.name=e}get rules(){var e=this.sheet;return Array.from(e.rules||e.cssRules)}addRule(e,t){this.sheet.addRule?this.sheet.addRule(e,t):this.sheet.insertRule("".concat(e," { ").concat(t," }"),0)}deleteRule(e){this.sheet.deleteRule(e)}findRule(e){var t=this.rules,n=t.findIndex(t=>t.selectorText===e);return-1===n?null:{rule:t[n],index:n}}get sheet(){return(this.__stylesheet=this.__stylesheet||(()=>{var e=document.createElement("style");return e.type="text/css",e.id=this.name,document.getElementsByTagName("head")[0].appendChild(e),e})()).sheet}}class Oe{constructor(e){ke(this,"prefix",void 0),ke(this,"stylesheet",void 0),this.prefix=e,this.stylesheet=new Se("".concat(e,"-dynamic-inline.css"))}deleteRule(e){e="".concat(this.prefix," ").concat(e);var t=this.stylesheet.findRule(e);t&&this.stylesheet.deleteRule(t.index)}setRule(e,t){e="".concat(this.prefix," ").concat(e);var n=this.stylesheet.findRule(e);if(n){if(n.rule.cssText===t||n.rule.cssText==="".concat(e," { ").concat(t," }"))return;this.stylesheet.deleteRule(n.index)}this.stylesheet.addRule(e,t),xe.Ay.trace("stylesheet",e,t)}}var Be={MOUSE_LEFT:1,MOUSE_RIGHT:3,MOUSE_MIDDLE:2,BACKSPACE:8,COMMA:188,INSERT:45,DELETE:46,END:35,ENTER:13,ESCAPE:27,CONTROL:17,COMMAND_LEFT:91,COMMAND_RIGHT:93,COMMAND_FIREFOX:224,ALT:18,HOME:36,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,SPACE:32,SHIFT:16,CAPS_LOCK:20,TAB:9,ARROW_RIGHT:39,ARROW_LEFT:37,ARROW_UP:38,ARROW_DOWN:40,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,A:65,X:88,C:67,V:86},_e=[...(Be.ARROW_DOWN,Be.ARROW_UP,Be.ARROW_LEFT,Be.ARROW_RIGHT,Be.HOME,Be.END,Be.DELETE,Be.BACKSPACE,Be.F1,Be.F2,Be.F3,Be.F4,Be.F5,Be.F6,Be.F7,Be.F8,Be.F9,Be.F10,Be.F11,Be.F12,Be.TAB,Be.PAGE_DOWN,Be.PAGE_UP,Be.ENTER,Be.ESCAPE,Be.SHIFT,Be.CAPS_LOCK,Be.ALT,[Be.ARROW_DOWN,Be.ARROW_UP,Be.ARROW_LEFT,Be.ARROW_RIGHT]),Be.TAB,Be.ENTER];function je(e){return-1!==_e.indexOf(e)}var Pe=n(371),De=(0,m.A)((0,Pe.A)("slice",function(e,t,n){return Array.prototype.slice.call(n,e,t)})),Fe=n(9849),Ie=(0,i.A)(function(e,t){for(var n={},r={},o=0,i=e.length;o<i;)r[e[o]]=1,o+=1;for(var a in t)r.hasOwnProperty(a)||(n[a]=t[a]);return n}),Te=Ie,Me=n(3700),Re=n(2537),ze=n(1322),Ne=e=>Array.isArray(e.name)?e.name.length:1,Le=e=>Math.max(...e.map(Ne));function qe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function We(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ve(e,t,n,r,o){var i=arguments.length>5&&void 0!==arguments[5]&&arguments[5];if(!e.name||Array.isArray(e.name)&&e.name.length<n||!r)return{groupIndexFirst:o,groupIndexLast:o};if(i)for(var a=o;a>=0;--a){var s=t[a];if(!(s.name&&Array.isArray(s.name)&&s.name.length>n&&s.name[n]===e.name[n]))break;o=a}for(var l=o,c=o;c<t.length;++c){var u=t[c];if(!(u.name&&Array.isArray(u.name)&&u.name.length>n&&u.name[n]===e.name[n]))break;l=c}return{groupIndexFirst:o,groupIndexLast:l}}function Ue(e,t,n,r){var o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],i=Ve(e,t,n,r,t.indexOf(e),o),a=i.groupIndexFirst,s=i.groupIndexLast;return De(a,s+1,Fe.A("id",t))}function Ye(e,t,n,r,o,i){return{data:He(e,t,n,r,o,i).data}}function He(e,t,n,r,o,i){var a=Ue(e,n,r,o);return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?qe(Object(n),!0).forEach(function(t){We(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):qe(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({columns:U(e=>-1===a.indexOf(e.id),t),data:g.A(Te(a),i)},$e)}function Ke(e,t,n,r){var o=Ve(e,t,n,r,t.indexOf(e)),i=o.groupIndexFirst,a=o.groupIndexLast;return g.A(e=>e.id,t.slice(i,a+1))}var $e={active_cell:void 0,start_cell:void 0,end_cell:void 0,selected_cells:[]};var Ge=n(2870);function Ze(e,t){var n;t=t||[];var r=(e=e||[]).length,o=t.length,i=[];for(n=0;n<r;)i[i.length]=e[n],n+=1;for(n=0;n<o;)i[i.length]=t[n],n+=1;return i}var Qe=n(5845),Xe=(0,d.A)(function(e){return(0,Qe.A)(e.length,function(){var t=0,n=arguments[0],r=arguments[arguments.length-1],o=Array.prototype.slice.call(arguments,0);return o[0]=function(){var e=n.apply(this,Ze(arguments,[t,r]));return t+=1,e},e.apply(this,o)})});function Je(e){var t=Object.prototype.toString.call(e);return"[object Function]"===t||"[object AsyncFunction]"===t||"[object GeneratorFunction]"===t||"[object AsyncGeneratorFunction]"===t}var et=(0,i.A)(function(e,t){if((0,te.A)(e)){if((0,te.A)(t))return e.concat(t);throw new TypeError(K(t)+" is not an array")}if((0,ne.A)(e)){if((0,ne.A)(t))return e+t;throw new TypeError(K(t)+" is not a string")}if(null!=e&&Je(e["fantasy-land/concat"]))return e["fantasy-land/concat"](t);if(null!=e&&Je(e.concat))return e.concat(t);throw new TypeError(K(e)+' does not have a method named "concat" or "fantasy-land/concat"')}),tt=(0,i.A)(function(e,t){for(var n={},r=0;r<e.length;)e[r]in t&&(n[e[r]]=t[e[r]]),r+=1;return n}),nt=(0,d.A)(function(e){for(var t=0,n=[];t<e.length;){for(var r=e[t],o=0;o<r.length;)void 0===n[o]&&(n[o]=[]),n[o].push(r[o]),o+=1;t+=1}return n}),rt=nt,ot=n(9734);function it(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var l=r&&r.prototype instanceof s?r:s,c=Object.create(l.prototype);return at(c,"_invoke",function(n,r,o){var i,s,l,c=0,u=o||[],d=!1,p={p:0,n:0,v:e,a:h,f:h.bind(e,4),d:function(t,n){return i=t,s=0,l=e,p.n=n,a}};function h(n,r){for(s=n,l=r,t=0;!d&&c&&!o&&t<u.length;t++){var o,i=u[t],h=p.p,f=i[2];n>3?(o=f===r)&&(l=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=h&&((o=n<2&&h<i[1])?(s=0,p.v=r,p.n=i[1]):h<f&&(o=n<3||i[0]>r||r>f)&&(i[4]=n,i[5]=r,p.n=f,s=0))}if(o||n>1)return a;throw d=!0,r}return function(o,u,f){if(c>1)throw TypeError("Generator is already running");for(d&&1===u&&h(u,f),s=u,l=f;(t=s<2?e:l)||!d;){i||(s?s<3?(s>1&&(p.n=-1),h(s,l)):p.n=l:p.v=l);try{if(c=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(l=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(d=p.n<0)?l:n.call(r,p))!==a)break}catch(t){i=e,s=1,l=t}finally{c=1}}return{value:t,done:d}}}(n,o,i),!0),c}var a={};function s(){}function l(){}function c(){}t=Object.getPrototypeOf;var u=[][r]?t(t([][r]())):(at(t={},r,function(){return this}),t),d=c.prototype=s.prototype=Object.create(u);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,c):(e.__proto__=c,at(e,o,"GeneratorFunction")),e.prototype=Object.create(d),e}return l.prototype=c,at(d,"constructor",c),at(c,"constructor",l),l.displayName="GeneratorFunction",at(c,o,"GeneratorFunction"),at(d),at(d,o,"Generator"),at(d,r,function(){return this}),at(d,"toString",function(){return"[object Generator]"}),(it=function(){return{w:i,m:p}})()}function at(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}at=function(e,t,n,r){if(t)o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{function i(t,n){at(e,t,function(e){return this._invoke(t,n,e)})}i("next",0),i("throw",1),i("return",2)}},at(e,t,n,r)}function st(e,t,n,r,o,i,a){try{var s=e[i](a),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function lt(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){st(i,r,o,a,s,"next",e)}function s(e){st(i,r,o,a,s,"throw",e)}a(void 0)})}}function ct(e){var t=[],n=Xe(Z.A),r=Xe(Z.A);return n((e,n)=>{var o={};r((e,r)=>{o[e]?r===o[e].e.c+1?o[e].e={r:n,c:r}:(t.push(o[e]),o[e]={s:{r:n,c:r},e:{r:n,c:r}}):o[e]={s:{r:n,c:r},e:{r:n,c:r}}},e);var i=Object.values(o);t=et(t,i)},e),U(e=>e.s.c!==e.e.c||e.s.r!==e.e.r,t)}function ut(e,t,n,r,o){return dt.apply(this,arguments)}function dt(){return(dt=lt(it().m(function e(t,n,r,o,i){var a,s,l,c;return it().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,ot.A.xlsx;case 1:return a=e.v,s=a.utils.aoa_to_sheet([]),n=g.A(tt(r))(n),o===Ge.ru.Display||o===Ge.ru.Names||o===Ge.ru.None?(a.utils.sheet_add_json(s,t,{skipHeader:!0}),l=t.length>0?{header:r,skipHeader:!0,origin:t.length}:{skipHeader:!0},a.utils.sheet_add_json(s,n,l),o===Ge.ru.Display&&i&&(s["!merges"]=ct(t))):o===Ge.ru.Ids&&a.utils.sheet_add_json(s,n,{header:r}),c=a.utils.book_new(),a.utils.book_append_sheet(c,s,"SheetJS"),e.a(2,c)}},e)}))).apply(this,arguments)}function pt(e,t){return ht.apply(this,arguments)}function ht(){return(ht=lt(it().m(function e(t,n){var r;return it().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,ot.A.xlsx;case 1:r=e.v,"xlsx"===n?r.writeFile(t,"Data.xlsx",{bookType:"xlsx",type:"buffer"}):"csv"===n&&r.writeFile(t,"Data.csv",{bookType:"csv",type:"buffer"});case 2:return e.a(2)}},e)}))).apply(this,arguments)}function ft(e,t){var n=function(e,t){return e.map(e=>e instanceof Array&&e.length<t?e.concat(Array(t-e.length).fill("")):0===t||1===t?[e]:e instanceof String||"string"==typeof e?Array(t).fill(e):e)}(e,t);return rt(n)}function At(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var l=r&&r.prototype instanceof s?r:s,c=Object.create(l.prototype);return vt(c,"_invoke",function(n,r,o){var i,s,l,c=0,u=o||[],d=!1,p={p:0,n:0,v:e,a:h,f:h.bind(e,4),d:function(t,n){return i=t,s=0,l=e,p.n=n,a}};function h(n,r){for(s=n,l=r,t=0;!d&&c&&!o&&t<u.length;t++){var o,i=u[t],h=p.p,f=i[2];n>3?(o=f===r)&&(l=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=h&&((o=n<2&&h<i[1])?(s=0,p.v=r,p.n=i[1]):h<f&&(o=n<3||i[0]>r||r>f)&&(i[4]=n,i[5]=r,p.n=f,s=0))}if(o||n>1)return a;throw d=!0,r}return function(o,u,f){if(c>1)throw TypeError("Generator is already running");for(d&&1===u&&h(u,f),s=u,l=f;(t=s<2?e:l)||!d;){i||(s?s<3?(s>1&&(p.n=-1),h(s,l)):p.n=l:p.v=l);try{if(c=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(l=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(d=p.n<0)?l:n.call(r,p))!==a)break}catch(t){i=e,s=1,l=t}finally{c=1}}return{value:t,done:d}}}(n,o,i),!0),c}var a={};function s(){}function l(){}function c(){}t=Object.getPrototypeOf;var u=[][r]?t(t([][r]())):(vt(t={},r,function(){return this}),t),d=c.prototype=s.prototype=Object.create(u);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,c):(e.__proto__=c,vt(e,o,"GeneratorFunction")),e.prototype=Object.create(d),e}return l.prototype=c,vt(d,"constructor",c),vt(c,"constructor",l),l.displayName="GeneratorFunction",vt(c,o,"GeneratorFunction"),vt(d),vt(d,o,"Generator"),vt(d,r,function(){return this}),vt(d,"toString",function(){return"[object Generator]"}),(At=function(){return{w:i,m:p}})()}function vt(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}vt=function(e,t,n,r){if(t)o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{function i(t,n){vt(e,t,function(e){return this._invoke(t,n,e)})}i("next",0),i("throw",1),i("return",2)}},vt(e,t,n,r)}function bt(e,t,n,r,o,i,a){try{var s=e[i](a),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}var gt=o().memo(e=>{var t=e.columns,n=e.export_columns,r=e.export_format,i=e.virtual_data,a=e.export_headers,s=e.visibleColumns,l=e.merge_duplicate_headers,c=r===Ge.AV.Csv||r===Ge.AV.Xlsx,u=n===Ge.se.Visible?s:t,d=function(){var e,n=(e=At().m(function e(){var n,o,s,c,d;return At().w(function(e){for(;;)switch(e.n){case 0:return n=u.map(e=>e.id),o=u.map(e=>e.name),s=Le(t),c=a!==Ge.ru.None?ft(o,s):[],e.n=1,ut(c,i.data,n,a,l);case 1:return d=e.v,e.n=2,pt(d,r);case 2:return e.a(2)}},e)}),function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){bt(i,r,o,a,s,"next",e)}function s(e){bt(i,r,o,a,s,"throw",e)}a(void 0)})});return function(){return n.apply(this,arguments)}}();return o().createElement("div",null,c?o().createElement("button",{className:"export",onClick:d},"Export"):null)}),mt=function(){function e(e){this.f=e}return e.prototype["@@transducer/init"]=function(){throw new Error("init not implemented on XWrap")},e.prototype["@@transducer/result"]=function(e){return e},e.prototype["@@transducer/step"]=function(e,t){return this.f(e,t)},e}();function yt(e){return new mt(e)}var wt=(0,m.A)(function(e,t,n){return de("function"==typeof e?yt(e):e,t,n)});function Et(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function Ct(e){var t=Fe.A("row",e),n=Fe.A("column",e);return{minRow:wt(G,1/0,t),minCol:wt(G,1/0,n),maxRow:wt($,0,t),maxCol:wt($,0,n)}}function xt(e,t){var n,r,o=Ct(t),i=o.minRow,a=o.minCol,s=o.maxRow,l=o.maxCol,c=(r=2,function(e){if(Array.isArray(e))return e}(n=e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(n,r)||function(e,t){if(e){if("string"==typeof e)return Et(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Et(e,t):void 0}}(n,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),u=c[0],d=c[1],p=[u,d];return u>s&&(p[0]=i,p[1]=d+1>l?a:d+1),u<i&&(p[0]=s,p[1]=d-1<a?l:d-1),d>l&&(p[1]=a,p[0]=u+1>s?i:u+1),d<a&&(p[1]=l,p[0]=u-1<a?s:u-1),p}var kt=(0,i.A)(function(e,t){for(var n,r=0,o=e.length,i=t.length,a=Array(o*i);r<o;){for(n=0;n<i;)a[r*i+n]=[e[r],t[n]],n+=1;r+=1}return a}),St=kt;function Ot(e,t,n,r){var o={row:e,column:t,column_id:n[t].id},i=r.data[e].id;return void 0!==i&&(o.row_id=i),o}function Bt(e,t,n){var r=e.minRow,o=e.maxRow,i=e.minCol,a=e.maxCol;return(0,g.A)(e=>Ot(e[0],e[1],t,n),St((0,Re.A)(r,o+1),(0,Re.A)(i,a+1)))}var _t=e=>{var t=document.createElement("div");t.style.position="absolute",t.style.visibility="hidden",t.style.width="100px",t.style.height="100px",t.style.overflow="scroll";var n=document.createElement("div");return n.style.width="100px",n.style.height="100px",t.appendChild(n),e.appendChild(t),new Promise(r=>{setTimeout(()=>{var o=n.clientWidth-t.clientWidth;e.removeChild(t),r(o)},0)})};function jt(e,t,n){for(var r=e.length,o=new Array(r),i=0;i<r;++i)o[i]=n(e[i],t[i],i);return o}var Pt=function(){function e(e,t){this.xf=t,this.f=e,this.found=!1}return e.prototype["@@transducer/init"]=q.A.init,e.prototype["@@transducer/result"]=function(e){return this.found||(e=this.xf["@@transducer/step"](e,void 0)),this.xf["@@transducer/result"](e)},e.prototype["@@transducer/step"]=function(e,t){return this.f(t)&&(this.found=!0,e=ye(this.xf["@@transducer/step"](e,t))),e},e}();function Dt(e){return function(t){return new Pt(e,t)}}var Ft=(0,i.A)((0,N.A)(["find"],Dt,function(e,t){for(var n=0,r=t.length;n<r;){if(e(t[n]))return t[n];n+=1}}));function It(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function Tt(e,t){for(var n,r=[],o=function(){var n;t=t.replace(/^\s+/,"");var o=r.slice(-1)[0],i=o?o.lexeme:null,a=e.filter(e=>e.if&&(Array.isArray(e.if)?i?e.if&&-1!==e.if.indexOf(i.type):e.if&&-1!==e.if.indexOf(void 0):e.if(r,o))),s=Ft(e=>e.regexp.test(t),a);if(!s)return{v:{lexemes:r,valid:!1,error:t}};var l=null!==(n=t.match(s.regexp))&&void 0!==n?n:[],c=l[s.regexpMatch||0],u=l[s.regexpFlags||-1];r.push({lexeme:s,flags:u,value:c}),t=t.substring(c.length)};t.length;)if(n=o())return n.v;var i,a,s=(i=[void 0,void 0,...r].slice(-2),a=2,function(e){if(Array.isArray(e))return e}(i)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(i,a)||function(e,t){if(e){if("string"==typeof e)return It(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?It(e,t):void 0}}(i,a)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),l=s[0],c=s[1],u=!c||("function"==typeof c.lexeme.terminal?c.lexeme.terminal(r,l):c.lexeme.terminal);return{lexemes:r,valid:u}}var Mt=(0,d.A)(ee);function Rt(e,t,n){var r,o=typeof e;switch(o){case"string":case"number":return 0===e&&1/e==-1/0?!!n._items["-0"]||(t&&(n._items["-0"]=!0),!1):null!==n._nativeSet?t?(r=n._nativeSet.size,n._nativeSet.add(e),n._nativeSet.size===r):n._nativeSet.has(e):o in n._items?e in n._items[o]||(t&&(n._items[o][e]=!0),!1):(t&&(n._items[o]={},n._items[o][e]=!0),!1);case"boolean":if(o in n._items){var i=e?1:0;return!!n._items[o][i]||(t&&(n._items[o][i]=!0),!1)}return t&&(n._items[o]=e?[!1,!0]:[!0,!1]),!1;case"function":return null!==n._nativeSet?t?(r=n._nativeSet.size,n._nativeSet.add(e),n._nativeSet.size===r):n._nativeSet.has(e):o in n._items?!!(0,D.A)(e,n._items[o])||(t&&n._items[o].push(e),!1):(t&&(n._items[o]=[e]),!1);case"undefined":return!!n._items[o]||(t&&(n._items[o]=!0),!1);case"object":if(null===e)return!!n._items.null||(t&&(n._items.null=!0),!1);default:return(o=Object.prototype.toString.call(e))in n._items?!!(0,D.A)(e,n._items[o])||(t&&n._items[o].push(e),!1):(t&&(n._items[o]=[e]),!1)}}var zt=function(){function e(){this._nativeSet="function"==typeof Set?new Set:null,this._items={}}return e.prototype.add=function(e){return!Rt(e,!0,this)},e.prototype.has=function(e){return Rt(e,!1,this)},e}(),Nt=function(){function e(e,t){this.xf=t,this.f=e,this.set=new zt}return e.prototype["@@transducer/init"]=q.A.init,e.prototype["@@transducer/result"]=q.A.result,e.prototype["@@transducer/step"]=function(e,t){return this.set.add(this.f(t))?this.xf["@@transducer/step"](e,t):e},e}();function Lt(e){return function(t){return new Nt(e,t)}}var qt=(0,i.A)((0,N.A)([],Lt,function(e,t){for(var n,r,o=new zt,i=[],a=0;a<t.length;)n=e(r=t[a]),o.add(n)&&i.push(r),a+=1;return i})),Wt=qt(Mt),Vt=(0,d.A)(function(e){return(0,_.A)(0,e)}),Ut=(0,d.A)(function(e){return(0,_.A)(-1,e)}),Yt=n(5987),Ht=(0,i.A)(function(e,t){return e.map(function(e){return(0,Yt.A)(e,t)})}),Kt=n(1831),$t=n.n(Kt);class Gt{static set(e,t){e.clipboardData.setData("text/plain",t),e.preventDefault()}static get(e){return e.clipboardData?e.clipboardData.getData("text/plain"):void 0}}var Zt=n(1608),Qt=(0,m.A)(function(e,t,n){return e=e<n.length&&e>=0?e:n.length,[].concat(Array.prototype.slice.call(n,0,e),t,Array.prototype.slice.call(n,e))}),Xt=n(3111),Jt=(0,i.A)(function(e,t){return"function"!=typeof t.indexOf||(0,te.A)(t)?(0,Xt.A)(t,e,0):t.indexOf(e)}),en=(0,i.A)(function(e,t){var n=Number(t);if(n<0||isNaN(n))throw new RangeError("n must be a non-negative number");for(var r=0,o=Array(n);r<n;)o[r]=e(r),r+=1;return o}),tn=(0,i.A)(function(e,t){return en(y(e),t)}),nn=(e,t)=>({success:!0,value:e});function rn(e,t){if((n=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var n,r=e.slice(0,n);return[r.length>1?r[0]+r.slice(2):r,+e.slice(n+1)]}var on,an=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function sn(e){if(!(t=an.exec(e)))throw new Error("invalid format: "+e);var t;return new ln({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function ln(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function cn(e,t){var n=rn(e,t);if(!n)return e+"";var r=n[0],o=n[1];return o<0?"0."+new Array(-o).join("0")+r:r.length>o+1?r.slice(0,o+1)+"."+r.slice(o+1):r+new Array(o-r.length+2).join("0")}sn.prototype=ln.prototype,ln.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};var un={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>cn(100*e,t),r:cn,s:function(e,t){var n=rn(e,t);if(!n)return e+"";var r=n[0],o=n[1],i=o-(on=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=r.length;return i===a?r:i>a?r+new Array(i-a+1).join("0"):i>0?r.slice(0,i)+"."+r.slice(i):"0."+new Array(1-i).join("0")+rn(e,Math.max(0,t+i-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function dn(e){return e}var pn=Array.prototype.map,hn=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];var fn=n(7365),An=n.n(fn),vn=(e,t)=>{var n=Boolean(t&&t.validation&&t.validation.allow_null),r=bn(e);return{success:r&&n,value:r?null:e}},bn=e=>null==e||"number"==typeof e&&(isNaN(e)||!isFinite(e)),gn=["group","symbol"];function mn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function yn(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function wn(e,t){return An()(e)?{success:!0,value:+e}:vn(e,t)}function En(e,t){return"number"!=typeof e||bn(e)?vn(e,t):{success:!0,value:e}}function Cn(e,t){return bn(e)?vn(e,t):"string"==typeof e?{success:!0,value:e}:{success:!0,value:JSON.stringify(e)}}function xn(e,t){return"string"==typeof e?{success:!0,value:e}:vn(e,t)}var kn=/^\s*(-?\d{4}|\d{2})(-(\d{1,2})(-(\d{1,2})([ Tt]([01]?\d|2[0-3])(:([0-5]\d)(:([0-5]\d(\.\d+)?))?(Z|z|[+\-]\d{2}:?\d{2})?)?)?)?)?\s*$/m,Sn=(new Date).getFullYear()-70;function On(e,t){if("string"!=typeof e)return null;var n=e.match(kn);if(!n)return null;var r=n[1],o=2===r.length;if(o&&(!t||!t.allow_YY))return null;var i=o?(Number(r)+2e3-Sn)%100+Sn:Number(r),a=i<0,s=n[3],l=Number(s||"1")-1,c=n[5],u=Number(c||1),d=n[7],p=Number(d||0),h=n[9],f=Number(h||0),A=n[11],v=new Date(Date.UTC(2e3,l,u,p,f));if(v.setUTCFullYear(i),v.getUTCMonth()!==l||v.getUTCDate()!==u)return null;var b=A?29:h?16:d?13:c?10:s?7:4;return(a?"-":"")+(v.toISOString().substr(a?3:0,17).replace("T"," ")+(A||"")).substr(0,b)}function Bn(e,t){var n=On(e,t&&t.validation);return null!==n?{success:!0,value:n}:vn(e,t)}function _n(e,t){return"string"==typeof e&&null!==On(e,t&&t.validation)?{success:!0,value:e.trim()}:vn(e,t)}function jn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Pn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?jn(Object(n),!0).forEach(function(t){Dn(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):jn(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Dn(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Fn=(e,t)=>{var n=function(e,t){var n=t&&t.on_change&&t.on_change.action||Ge.Xw.Coerce;switch(n){case Ge.Xw.Coerce:return Pn({action:n},function(e){switch(e.type){case Ge.$C.Numeric:return wn;case Ge.$C.Text:return Cn;case Ge.$C.Datetime:return Bn;case Ge.$C.Any:default:return nn}}(t)(e,t));case Ge.Xw.None:return{success:!0,value:e,action:n};case Ge.Xw.Validate:return Pn({action:n},function(e){switch(e.type){case Ge.$C.Numeric:return En;case Ge.$C.Text:return xn;case Ge.$C.Datetime:return _n;case Ge.$C.Any:default:return nn}}(t)(e,t))}}(e,t);return n.success?n:function(e,t){var n=t&&t.on_change&&t.on_change.failure||Ge.vb.Reject;if(e.failure=n,n===Ge.vb.Default){var r=t&&t.validation&&t.validation.default,o=s.A(r)?null:r;e.success=!0,e.value=o}else n===Ge.vb.Accept&&(e.success=!0);return e}(n,t)};function In(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(e,t)||Mn(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Tn(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=Mn(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,i=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw i}}}}function Mn(e,t){if(e){if("string"==typeof e)return Rn(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Rn(e,t):void 0}}function Rn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function zn(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Nn{static toClipboard(e,t,n,r,o,i){var a=Wt(Fe.A("row",t).sort((e,t)=>e-t)),s=Wt(Fe.A("column",t).sort((e,t)=>e-t)),l=De(Vt(a),Ut(a)+1,o).map(e=>Ht(s,Ht(Fe.A("id",r),e))),c=$t().prototype.stringify(l);if(Nn.lastLocalCopy=l,i){var u=ft(Fe.A("name",r),Le(n)),d=g.A(e=>g.A(t=>e[t],s),u).concat(l);c=$t().prototype.stringify(d),Nn.lastLocalCopy=d,Nn.localCopyWithoutHeaders=l}xe.Ay.trace("TableClipboard -- set clipboard data: ",c),Gt.set(e,c)}static clearClipboard(){Nn.lastLocalCopy=[],Nn.localCopyWithoutHeaders=[]}static fromClipboard(e,t,n,r,o,i){var a=!(arguments.length>6&&void 0!==arguments[6])||arguments[6],s=!(arguments.length>7&&void 0!==arguments[7])||arguments[7],l=arguments.length>8?arguments[8]:void 0,c=Gt.get(e);if(xe.Ay.trace("TableClipboard -- get clipboard data: ",c),c){var u=$t().prototype.stringify(Nn.lastLocalCopy),d=l?Nn.localCopyWithoutHeaders:Nn.lastLocalCopy;return function(e,t,n,r,o,i){var a=!(arguments.length>6&&void 0!==arguments[6])||arguments[6],s=!(arguments.length>7&&void 0!==arguments[7])||arguments[7];s||xe.Ay.debug("Clipboard -- Sorting or filtering active, do not create new rows"),a||xe.Ay.debug("Clipboard -- Do not create new columns");var l=Zt.A(i),c=r.slice(0),u=o.slice(0);if(a&&e[0].length+t.column>=o.length){for(var d=[],p=function(e){d.push({id:"Column ".concat(e+1),name:"Column ".concat(e+1),type:Ge.$C.Any,sort_as_null:[]}),l.forEach(t=>t["Column ".concat(e)]="")},h=o.length;h<e[0].length+t.column;h++)p(h);c=Qt(Jt(Ut(o),r)+1,d,c),u=et(u,d)}var f=n[t.row];if(s&&e.length+f>=i.length){var A={};o.forEach(e=>A[e.id]=""),l=et(l,tn(A,e.length+f-i.length))}var v,b=n.slice(-1)[0]||0,g=n.length,m=Tn(e.entries());try{for(m.s();!(v=m.n()).done;){var y,w=In(v.value,2),E=w[0],C=Tn(w[1].entries());try{for(C.s();!(y=C.n()).done;){var x=In(y.value,2),S=x[0],O=x[1],B=t.row+E,_=g>B?n[B]:s?b+(B-g+1):void 0;if(void 0!==_){var j=u[t.column+S];if(j&&j.editable){var D=Fn(O,j);D.success&&(l=k(P([_,j.id]),D.value,l))}}}}catch(e){C.e(e)}finally{C.f()}}}catch(e){m.e(e)}finally{m.f()}return{data:l,columns:c}}(u===c?d:Nn.parse(c),t,n,r,o,i,a,s)}}static parse(e){var t,n,r,o,i,a,s=0,l=[[]],c=e.split("\n");for(c.length>1&&""===c[c.length-1]&&c.pop(),l=[],t=0,n=c.length;t<n;t+=1){var u=c[t].split("\t");for(r=0,o=u.length;r<o;r+=1)l[s]||(l[s]=[]),i&&0===r?(a=l[s].length-1,l[s][a]=l[s][a]+"\n"+u[0].replace(/""/g,'"'),i&&1&Nn.countQuotes(u[0])&&(i=!1,l[s][a]=l[s][a].substring(0,l[s][a].length-1))):r===o-1&&0===u[r].indexOf('"')&&1&Nn.countQuotes(u[r])?(l[s].push(u[r].substring(1).replace(/""/g,'"')),i=!0):(l[s].push(u[r]),i=!1);i||(s+=1)}return l}static countQuotes(e){return e.split('"').length-1}}zn(Nn,"lastLocalCopy",[[]]),zn(Nn,"localCopyWithoutHeaders",[[]]);var Ln=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(t=t||(()=>{for(t=e;t&&"td"!==t.nodeName.toLowerCase();)t=t.parentElement;return t})(),e&&t){for(var n=e;"relative"!==getComputedStyle(n).position&&"sticky"!==getComputedStyle(n).position&&n.parentElement;)n=n.parentElement;for(var r=e;"relative"!==getComputedStyle(r).position&&r.parentElement;)r=r.parentElement;var o=n.getBoundingClientRect(),i=r.getBoundingClientRect(),a=t.getBoundingClientRect(),s=a.left-o.left+n.scrollLeft,l=a.top-o.top+n.scrollTop+a.height;e.style.width="".concat(a.width,"px"),e.style.top="".concat(l,"px"),e.style.left="".concat(s,"px"),e.style.position="absolute",a.top+a.height/2>i.bottom||a.top-a.height/2<i.top||a.left<i.left||a.left+.25*a.width>i.left+i.width?e.style.display="none":e.style.display="block"}},qn=(e,t,n)=>"last"===n?e===t:"first"===n?0===e:"boolean"==typeof n?n:!!n&&n[e],Wn=(0,i.A)(function(e,t){for(var n=Math.min(e.length,t.length),r=Array(n),o=0;o<n;)r[o]=[e[o],t[o]],o+=1;return r}),Vn=(0,f.ty)((e,t,n)=>{var r=((e,t)=>g.A(n=>e.map(e=>s.A(e.name)&&n===t-1?e.id:((e,t)=>Array.isArray(e.name)?e.name[t]:e.name)(e,n)),Re.A(0,t)))(t,Le(e)),o=((e,t,n)=>g.A(t=>{if(n){var r=[0],o=0;return t.forEach((e,n)=>{e!==t[o]&&(r.push(n),o=n)}),r}return Re.A(0,e.length)},t))(t,r,n);return Wn(r,o)});function Un(e){for(var t=e.length,n=new Array(t),r=0;r<t;++r)n[r]=e[r].slice(0);return n}function Yn(e,t,n){for(var r=e.length,o=t.length,i=0;i<r;++i)for(var a=0;a<o;++a)n(e[i],t[a],i,a)}function Hn(e,t,n){for(var r=e.length,o=t.length,i=new Array(r),a=0;a<r;++a){for(var s=new Array(o),l=0;l<o;++l)s[l]=n(e[a],t[l],a,l);i[a]=s}return i}function Kn(e,t,n){for(var r=e.length,o=new Array(r),i=0;i<r;++i){for(var a=e[i].length,s=new Array(a),l=0;l<a;++l)s[l]=n(e[i][l],t?t[i][l]:void 0,i,l);o[i]=s}return o}function $n(e,t,n,r){for(var o=e.length,i=new Array(o),a=0;a<o;++a){for(var s=e[a].length,l=new Array(s),c=0;c<s;++c)l[c]=r(e[a][c],t?t[a][c]:void 0,n?n[a][c]:void 0,a,c);i[a]=l}return i}function Gn(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var o=n.slice(0,-1);return wt((e,t)=>e.get(t)||e.set(t,new Map).get(t),e,o)}var Zn=()=>e=>{var t=new Map;return{get:function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];var i=r.slice(-1)[0],a=Gn(t,...r);return a.get(i)||a.set(i,(0,f.B4)(e)).get(i)}}};function Qn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Xn(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Jn=/^children$/;class er extends r.Component{constructor(e){super(e)}get propsWithDefaults(){return this.props}render(){var e=this.propsWithDefaults,t=e.attributes,n=e.className,r=e.onClick,i=e.onDoubleClick,a=e.onMouseEnter,s=e.onMouseLeave,l=e.onMouseMove,c=e.style;return o().createElement("td",function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Qn(Object(n),!0).forEach(function(t){Xn(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Qn(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({children:this.props.children,tabIndex:-1,className:n,onClick:r,onDoubleClick:i,onMouseEnter:a,onMouseLeave:s,onMouseMove:l,onMouseOver:l,style:c},t))}shouldComponentUpdate(e){var t=this.props,n=this.getChildProps(t),r=this.getChildProps(e);return Ce(n=>!Jn.test(n)&&t[n]!==e[n],h(t))||Ce(e=>n[e]!==r[e],h(n))}getChildProps(e){return e&&e.children&&e.children[0]&&e.children[0].props}}var tr,nr=(e,t,n)=>!!e&&e.row===t&&e.column===n,rr=(e,t,n,r)=>{var o=e(),i=o.cell_selectable,a=o.selected_cells,s=o.active_cell,l=o.setProps,c=o.viewport,u=o.virtualized,d=o.visibleColumns,p=n+u.offset.columns,h=Ot(t,p,d,c);if((!s||t!==s.row||p!==s.column)&&(d[p].presentation!==Ge.Jm.Markdown&&r.preventDefault(),i)){var f=window.getSelection();f&&f.removeAllRanges();var A=((e,t,n)=>Ce(e=>e.row===t&&e.column===n,e))(a,t,p);if(!A||r.shiftKey){var v={is_focused:!1,end_cell:h};r.shiftKey&&s?v.selected_cells=Bt({minRow:G(t,s.row),maxRow:$(t,s.row),minCol:G(p,s.column),maxCol:$(p,s.column)},d,c):(v.active_cell=h,v.start_cell=h,v.selected_cells=[h]),l(v)}else l({is_focused:!1,active_cell:h})}},or=(e,t,n,r)=>{var o=e(),i=o.is_focused,a=o.setProps,s=o.viewport,l=o.virtualized,c=o.visibleColumns;if(c[n].editable){var u=Ot(t,n+l.offset.columns,c,s);i||(r.preventDefault(),a({selected_cells:[u],active_cell:u,start_cell:u,end_cell:u,is_focused:!0}))}},ir=(e,t,n,r)=>{var o=e(),i=o.data,a=o.setProps,s=o.virtualized,l=o.visibleColumns[n],c=s.indices[t-s.offset.rows];if(l.editable){var u=Fn(r,l);u.success&&a({data:k(P([c,l.id]),u.value,i)})}},ar=(e,t,n)=>{var r=e(),o=r.setState,i=r.virtualized;o({currentTooltip:{header:!1,id:r.visibleColumns[n].id,row:i.indices[t-i.offset.rows]}})},sr=(e,t,n)=>{var r=e();(0,r.setState)({currentTooltip:{header:!0,id:r.visibleColumns[n].id,row:t}})},lr=(e,t,n)=>{(0,e().setState)({currentTooltip:void 0})},cr=(e,t,n)=>{var r=e(),o=r.currentTooltip,i=r.setState,a=r.virtualized,s=r.visibleColumns[n],l=a.indices[t-a.offset.rows];o&&o.id===s.id&&o.row===l&&!o.header||i({currentTooltip:{header:!1,id:s.id,row:l}})},ur=(e,t,n)=>{var r=e(),o=r.currentTooltip,i=r.setState,a=r.visibleColumns[n];o&&o.id===a.id&&o.row===t&&o.header||i({currentTooltip:{header:!0,id:a.id,row:t}})},dr=(e,t,n,r)=>{var o=e(),i=o.active_cell,a=o.is_focused,s=nr(i,t,n);if(!a&&s){r.preventDefault();var l=r.target;l.setSelectionRange(0,l.value?l.value.length:0)}},pr=(e,t,n,r)=>{r.preventDefault()};function hr(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}!function(e){e.Change="change",e.Click="click",e.DoubleClick="doubleclick",e.Enter="enter",e.EnterHeader="enterheader",e.Leave="leave",e.Move="move",e.MoveHeader="moveheader",e.MouseUp="mouseup",e.Paste="paste"}(tr||(tr={}));var fr=e=>new Ar(e).get;class Ar{constructor(e){var t,n;hr(this,"propsFn",void 0),hr(this,"cache",(t=(e,t,n)=>{switch(e){case tr.Change:return ir.bind(void 0,this.propsFn,t,n);case tr.Click:return rr.bind(void 0,this.propsFn,t,n);case tr.DoubleClick:return or.bind(void 0,this.propsFn,t,n);case tr.Enter:return ar.bind(void 0,this.propsFn,t,n);case tr.EnterHeader:return sr.bind(void 0,this.propsFn,t,n);case tr.Leave:return lr.bind(void 0,this.propsFn,t,n);case tr.Move:return cr.bind(void 0,this.propsFn,t,n);case tr.MoveHeader:return ur.bind(void 0,this.propsFn,t,n);case tr.MouseUp:return dr.bind(void 0,this.propsFn,t,n);case tr.Paste:return pr.bind(void 0,this.propsFn,t,n);default:throw new Error("unexpected handler ".concat(e))}},n=new Map,{get:function(){for(var e=arguments.length,r=new Array(e),o=0;o<e;o++)r[o]=arguments[o];var i=r.slice(-1)[0],a=Gn(n,...r);return a.has(i)?a.get(i):a.set(i,t(...r)).get(i)}})),hr(this,"get",(e,t,n)=>this.cache.get(e,t,n)),this.propsFn=e}}function vr(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class br{constructor(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:fr(e);vr(this,"handlers",void 0),vr(this,"partialGet",(0,f.B4)((e,t,n)=>t.map((t,r)=>e.map((e,t)=>this.getWrapper(!1,!1,r+n.rows,t,e),e)))),vr(this,"get",(0,f.B4)((e,t,n,r)=>(e=Un(e),(r.length?r:n?[n]:[]).forEach(r=>{var i=r.row,a=r.column;if(i-=t.rows,a-=t.columns,!(i<0||a<0||e.length<=i||e[i].length<=a)){var s=e[i][a],l=nr(n,i+t.rows,a+t.columns);e[i][a]=o().cloneElement(s,{className:s.props.className+" cell--selected"+(l?" focused":"")})}}),e))),vr(this,"wrapper",Zn()((e,t,n,r,i,a,s,l,c,u)=>o().createElement(er,{active:e,attributes:{"data-dash-column":r,"data-dash-row":i},className:t,key:"column-".concat(n),onClick:c,onDoubleClick:u,onMouseEnter:a,onMouseLeave:s,onMouseMove:l}))),this.handlers=t}getWrapper(e,t,n,r,o){var i=o.presentation===Ge.Jm.Dropdown,a="dash-cell"+" column-".concat(r)+(e?" focused":"")+(t?" cell--selected":"")+(i?" dropdown":"");return this.wrapper.get(n,r)(e,a,r,o.id,n,this.handlers(tr.Enter,n,r),this.handlers(tr.Leave,n,r),this.handlers(tr.Move,n,r),this.handlers(tr.Click,n,r),this.handlers(tr.DoubleClick,n,r))}}function gr(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class mr extends r.PureComponent{constructor(e){super(e),gr(this,"textInputRef",void 0),gr(this,"propagateChange",()=>{this.state.value!==this.props.value&&(0,this.props.onChange)(this.state.value)}),gr(this,"handleChange",e=>{this.setState({value:e.target.value})}),gr(this,"handleKeyDown",e=>{var t=this.props.focused;t&&e.keyCode!==Be.TAB&&e.keyCode!==Be.ENTER||(t||je(e.keyCode))&&this.propagateChange()}),this.state={value:e.value},this.textInputRef=(0,r.createRef)()}render(){var e=this.props,t=e.className,n=e.onMouseUp,r=e.onPaste,i=e.value,a=null===this.state.value?void 0:this.state.value;return o().createElement("div",{className:"dash-input-cell-value-container dash-cell-value-container"},o().createElement("div",{className:"input-cell-value-shadow cell-value-shadow"},i),o().createElement("input",{ref:this.textInputRef,type:"text",className:t,onBlur:this.propagateChange,onChange:this.handleChange,onKeyDown:this.handleKeyDown,onMouseUp:n,onPaste:r,value:a}))}UNSAFE_componentWillReceiveProps(e){var t=e.value;this.state.value!==t&&this.setState({value:t})}componentDidUpdate(){this.setFocus()}componentDidMount(){this.setFocus()}setFocus(){var e=this.props,t=e.active,n=e.applyFocus;if(t){var r=this.textInputRef.current;n&&r&&document.activeElement!==r&&(r.focus(),r.setSelectionRange(0,r.value?r.value.length:0))}}}class yr extends r.PureComponent{constructor(e){var t,n,o;super(e),t=this,o=void 0,(n=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(n="elRef"))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o,this.elRef=(0,r.createRef)()}render(){var e=this.props,t=e.className,n=e.value;return o().createElement("div",{ref:this.elRef,className:t,tabIndex:-1},"boolean"==typeof n?n.toString():n)}componentDidUpdate(){this.setFocus()}componentDidMount(){this.setFocus()}setFocus(){var e=this.props,t=e.active,n=e.applyFocus;if(t){var r,o=this.elRef.current;n&&o&&document.activeElement!==o&&(null===(r=window.getSelection())||void 0===r||r.selectAllChildren(o),o.focus())}}}var wr=n(9132),Er=n(6942),Cr=n.n(Er),xr=n(6120),kr=n.n(xr),Sr=n(5795),Or=function(e){var t=e.onMouseDown;return o().createElement("span",{className:"Select-arrow",onMouseDown:t})};Or.propTypes={onMouseDown:kr().func};var Br=[{base:"A",letters:/[\u0041\u24B6\uFF21\u00C0\u00C1\u00C2\u1EA6\u1EA4\u1EAA\u1EA8\u00C3\u0100\u0102\u1EB0\u1EAE\u1EB4\u1EB2\u0226\u01E0\u00C4\u01DE\u1EA2\u00C5\u01FA\u01CD\u0200\u0202\u1EA0\u1EAC\u1EB6\u1E00\u0104\u023A\u2C6F]/g},{base:"AA",letters:/[\uA732]/g},{base:"AE",letters:/[\u00C6\u01FC\u01E2]/g},{base:"AO",letters:/[\uA734]/g},{base:"AU",letters:/[\uA736]/g},{base:"AV",letters:/[\uA738\uA73A]/g},{base:"AY",letters:/[\uA73C]/g},{base:"B",letters:/[\u0042\u24B7\uFF22\u1E02\u1E04\u1E06\u0243\u0182\u0181]/g},{base:"C",letters:/[\u0043\u24B8\uFF23\u0106\u0108\u010A\u010C\u00C7\u1E08\u0187\u023B\uA73E]/g},{base:"D",letters:/[\u0044\u24B9\uFF24\u1E0A\u010E\u1E0C\u1E10\u1E12\u1E0E\u0110\u018B\u018A\u0189\uA779]/g},{base:"DZ",letters:/[\u01F1\u01C4]/g},{base:"Dz",letters:/[\u01F2\u01C5]/g},{base:"E",letters:/[\u0045\u24BA\uFF25\u00C8\u00C9\u00CA\u1EC0\u1EBE\u1EC4\u1EC2\u1EBC\u0112\u1E14\u1E16\u0114\u0116\u00CB\u1EBA\u011A\u0204\u0206\u1EB8\u1EC6\u0228\u1E1C\u0118\u1E18\u1E1A\u0190\u018E]/g},{base:"F",letters:/[\u0046\u24BB\uFF26\u1E1E\u0191\uA77B]/g},{base:"G",letters:/[\u0047\u24BC\uFF27\u01F4\u011C\u1E20\u011E\u0120\u01E6\u0122\u01E4\u0193\uA7A0\uA77D\uA77E]/g},{base:"H",letters:/[\u0048\u24BD\uFF28\u0124\u1E22\u1E26\u021E\u1E24\u1E28\u1E2A\u0126\u2C67\u2C75\uA78D]/g},{base:"I",letters:/[\u0049\u24BE\uFF29\u00CC\u00CD\u00CE\u0128\u012A\u012C\u0130\u00CF\u1E2E\u1EC8\u01CF\u0208\u020A\u1ECA\u012E\u1E2C\u0197]/g},{base:"J",letters:/[\u004A\u24BF\uFF2A\u0134\u0248]/g},{base:"K",letters:/[\u004B\u24C0\uFF2B\u1E30\u01E8\u1E32\u0136\u1E34\u0198\u2C69\uA740\uA742\uA744\uA7A2]/g},{base:"L",letters:/[\u004C\u24C1\uFF2C\u013F\u0139\u013D\u1E36\u1E38\u013B\u1E3C\u1E3A\u0141\u023D\u2C62\u2C60\uA748\uA746\uA780]/g},{base:"LJ",letters:/[\u01C7]/g},{base:"Lj",letters:/[\u01C8]/g},{base:"M",letters:/[\u004D\u24C2\uFF2D\u1E3E\u1E40\u1E42\u2C6E\u019C]/g},{base:"N",letters:/[\u004E\u24C3\uFF2E\u01F8\u0143\u00D1\u1E44\u0147\u1E46\u0145\u1E4A\u1E48\u0220\u019D\uA790\uA7A4]/g},{base:"NJ",letters:/[\u01CA]/g},{base:"Nj",letters:/[\u01CB]/g},{base:"O",letters:/[\u004F\u24C4\uFF2F\u00D2\u00D3\u00D4\u1ED2\u1ED0\u1ED6\u1ED4\u00D5\u1E4C\u022C\u1E4E\u014C\u1E50\u1E52\u014E\u022E\u0230\u00D6\u022A\u1ECE\u0150\u01D1\u020C\u020E\u01A0\u1EDC\u1EDA\u1EE0\u1EDE\u1EE2\u1ECC\u1ED8\u01EA\u01EC\u00D8\u01FE\u0186\u019F\uA74A\uA74C]/g},{base:"OI",letters:/[\u01A2]/g},{base:"OO",letters:/[\uA74E]/g},{base:"OU",letters:/[\u0222]/g},{base:"P",letters:/[\u0050\u24C5\uFF30\u1E54\u1E56\u01A4\u2C63\uA750\uA752\uA754]/g},{base:"Q",letters:/[\u0051\u24C6\uFF31\uA756\uA758\u024A]/g},{base:"R",letters:/[\u0052\u24C7\uFF32\u0154\u1E58\u0158\u0210\u0212\u1E5A\u1E5C\u0156\u1E5E\u024C\u2C64\uA75A\uA7A6\uA782]/g},{base:"S",letters:/[\u0053\u24C8\uFF33\u1E9E\u015A\u1E64\u015C\u1E60\u0160\u1E66\u1E62\u1E68\u0218\u015E\u2C7E\uA7A8\uA784]/g},{base:"T",letters:/[\u0054\u24C9\uFF34\u1E6A\u0164\u1E6C\u021A\u0162\u1E70\u1E6E\u0166\u01AC\u01AE\u023E\uA786]/g},{base:"TZ",letters:/[\uA728]/g},{base:"U",letters:/[\u0055\u24CA\uFF35\u00D9\u00DA\u00DB\u0168\u1E78\u016A\u1E7A\u016C\u00DC\u01DB\u01D7\u01D5\u01D9\u1EE6\u016E\u0170\u01D3\u0214\u0216\u01AF\u1EEA\u1EE8\u1EEE\u1EEC\u1EF0\u1EE4\u1E72\u0172\u1E76\u1E74\u0244]/g},{base:"V",letters:/[\u0056\u24CB\uFF36\u1E7C\u1E7E\u01B2\uA75E\u0245]/g},{base:"VY",letters:/[\uA760]/g},{base:"W",letters:/[\u0057\u24CC\uFF37\u1E80\u1E82\u0174\u1E86\u1E84\u1E88\u2C72]/g},{base:"X",letters:/[\u0058\u24CD\uFF38\u1E8A\u1E8C]/g},{base:"Y",letters:/[\u0059\u24CE\uFF39\u1EF2\u00DD\u0176\u1EF8\u0232\u1E8E\u0178\u1EF6\u1EF4\u01B3\u024E\u1EFE]/g},{base:"Z",letters:/[\u005A\u24CF\uFF3A\u0179\u1E90\u017B\u017D\u1E92\u1E94\u01B5\u0224\u2C7F\u2C6B\uA762]/g},{base:"a",letters:/[\u0061\u24D0\uFF41\u1E9A\u00E0\u00E1\u00E2\u1EA7\u1EA5\u1EAB\u1EA9\u00E3\u0101\u0103\u1EB1\u1EAF\u1EB5\u1EB3\u0227\u01E1\u00E4\u01DF\u1EA3\u00E5\u01FB\u01CE\u0201\u0203\u1EA1\u1EAD\u1EB7\u1E01\u0105\u2C65\u0250]/g},{base:"aa",letters:/[\uA733]/g},{base:"ae",letters:/[\u00E6\u01FD\u01E3]/g},{base:"ao",letters:/[\uA735]/g},{base:"au",letters:/[\uA737]/g},{base:"av",letters:/[\uA739\uA73B]/g},{base:"ay",letters:/[\uA73D]/g},{base:"b",letters:/[\u0062\u24D1\uFF42\u1E03\u1E05\u1E07\u0180\u0183\u0253]/g},{base:"c",letters:/[\u0063\u24D2\uFF43\u0107\u0109\u010B\u010D\u00E7\u1E09\u0188\u023C\uA73F\u2184]/g},{base:"d",letters:/[\u0064\u24D3\uFF44\u1E0B\u010F\u1E0D\u1E11\u1E13\u1E0F\u0111\u018C\u0256\u0257\uA77A]/g},{base:"dz",letters:/[\u01F3\u01C6]/g},{base:"e",letters:/[\u0065\u24D4\uFF45\u00E8\u00E9\u00EA\u1EC1\u1EBF\u1EC5\u1EC3\u1EBD\u0113\u1E15\u1E17\u0115\u0117\u00EB\u1EBB\u011B\u0205\u0207\u1EB9\u1EC7\u0229\u1E1D\u0119\u1E19\u1E1B\u0247\u025B\u01DD]/g},{base:"f",letters:/[\u0066\u24D5\uFF46\u1E1F\u0192\uA77C]/g},{base:"g",letters:/[\u0067\u24D6\uFF47\u01F5\u011D\u1E21\u011F\u0121\u01E7\u0123\u01E5\u0260\uA7A1\u1D79\uA77F]/g},{base:"h",letters:/[\u0068\u24D7\uFF48\u0125\u1E23\u1E27\u021F\u1E25\u1E29\u1E2B\u1E96\u0127\u2C68\u2C76\u0265]/g},{base:"hv",letters:/[\u0195]/g},{base:"i",letters:/[\u0069\u24D8\uFF49\u00EC\u00ED\u00EE\u0129\u012B\u012D\u00EF\u1E2F\u1EC9\u01D0\u0209\u020B\u1ECB\u012F\u1E2D\u0268\u0131]/g},{base:"j",letters:/[\u006A\u24D9\uFF4A\u0135\u01F0\u0249]/g},{base:"k",letters:/[\u006B\u24DA\uFF4B\u1E31\u01E9\u1E33\u0137\u1E35\u0199\u2C6A\uA741\uA743\uA745\uA7A3]/g},{base:"l",letters:/[\u006C\u24DB\uFF4C\u0140\u013A\u013E\u1E37\u1E39\u013C\u1E3D\u1E3B\u017F\u0142\u019A\u026B\u2C61\uA749\uA781\uA747]/g},{base:"lj",letters:/[\u01C9]/g},{base:"m",letters:/[\u006D\u24DC\uFF4D\u1E3F\u1E41\u1E43\u0271\u026F]/g},{base:"n",letters:/[\u006E\u24DD\uFF4E\u01F9\u0144\u00F1\u1E45\u0148\u1E47\u0146\u1E4B\u1E49\u019E\u0272\u0149\uA791\uA7A5]/g},{base:"nj",letters:/[\u01CC]/g},{base:"o",letters:/[\u006F\u24DE\uFF4F\u00F2\u00F3\u00F4\u1ED3\u1ED1\u1ED7\u1ED5\u00F5\u1E4D\u022D\u1E4F\u014D\u1E51\u1E53\u014F\u022F\u0231\u00F6\u022B\u1ECF\u0151\u01D2\u020D\u020F\u01A1\u1EDD\u1EDB\u1EE1\u1EDF\u1EE3\u1ECD\u1ED9\u01EB\u01ED\u00F8\u01FF\u0254\uA74B\uA74D\u0275]/g},{base:"oi",letters:/[\u01A3]/g},{base:"ou",letters:/[\u0223]/g},{base:"oo",letters:/[\uA74F]/g},{base:"p",letters:/[\u0070\u24DF\uFF50\u1E55\u1E57\u01A5\u1D7D\uA751\uA753\uA755]/g},{base:"q",letters:/[\u0071\u24E0\uFF51\u024B\uA757\uA759]/g},{base:"r",letters:/[\u0072\u24E1\uFF52\u0155\u1E59\u0159\u0211\u0213\u1E5B\u1E5D\u0157\u1E5F\u024D\u027D\uA75B\uA7A7\uA783]/g},{base:"s",letters:/[\u0073\u24E2\uFF53\u00DF\u015B\u1E65\u015D\u1E61\u0161\u1E67\u1E63\u1E69\u0219\u015F\u023F\uA7A9\uA785\u1E9B]/g},{base:"t",letters:/[\u0074\u24E3\uFF54\u1E6B\u1E97\u0165\u1E6D\u021B\u0163\u1E71\u1E6F\u0167\u01AD\u0288\u2C66\uA787]/g},{base:"tz",letters:/[\uA729]/g},{base:"u",letters:/[\u0075\u24E4\uFF55\u00F9\u00FA\u00FB\u0169\u1E79\u016B\u1E7B\u016D\u00FC\u01DC\u01D8\u01D6\u01DA\u1EE7\u016F\u0171\u01D4\u0215\u0217\u01B0\u1EEB\u1EE9\u1EEF\u1EED\u1EF1\u1EE5\u1E73\u0173\u1E77\u1E75\u0289]/g},{base:"v",letters:/[\u0076\u24E5\uFF56\u1E7D\u1E7F\u028B\uA75F\u028C]/g},{base:"vy",letters:/[\uA761]/g},{base:"w",letters:/[\u0077\u24E6\uFF57\u1E81\u1E83\u0175\u1E87\u1E85\u1E98\u1E89\u2C73]/g},{base:"x",letters:/[\u0078\u24E7\uFF58\u1E8B\u1E8D]/g},{base:"y",letters:/[\u0079\u24E8\uFF59\u1EF3\u00FD\u0177\u1EF9\u0233\u1E8F\u00FF\u1EF7\u1E99\u1EF5\u01B4\u024F\u1EFF]/g},{base:"z",letters:/[\u007A\u24E9\uFF5A\u017A\u1E91\u017C\u017E\u1E93\u1E95\u01B6\u0225\u0240\u2C6C\uA763]/g}],_r=function(e){for(var t=0;t<Br.length;t++)e=e.replace(Br[t].letters,Br[t].base);return e},jr=function(e){return null!=e&&""!==e},Pr=function(e,t,n,r){return r.ignoreAccents&&(t=_r(t)),r.ignoreCase&&(t=t.toLowerCase()),r.trimFilter&&(t=t.replace(/^\s+|\s+$/g,"")),n&&(n=n.map(function(e){return e[r.valueKey]})),e.filter(function(e){if(n&&n.indexOf(e[r.valueKey])>-1)return!1;if(r.filterOption)return r.filterOption.call(void 0,e,t);if(!t)return!0;var o=e[r.valueKey],i=e[r.labelKey],a=jr(o),s=jr(i);if(!a&&!s)return!1;var l=a?String(o):null,c=s?String(i):null;return r.ignoreAccents&&(l&&"label"!==r.matchProp&&(l=_r(l)),c&&"value"!==r.matchProp&&(c=_r(c))),r.ignoreCase&&(l&&"label"!==r.matchProp&&(l=l.toLowerCase()),c&&"value"!==r.matchProp&&(c=c.toLowerCase())),"start"===r.matchPos?l&&"label"!==r.matchProp&&l.substr(0,t.length)===t||c&&"value"!==r.matchProp&&c.substr(0,t.length)===t:l&&"label"!==r.matchProp&&l.indexOf(t)>=0||c&&"value"!==r.matchProp&&c.indexOf(t)>=0})},Dr=function(e){var t=e.focusedOption,n=e.focusOption,r=e.inputValue,i=e.instancePrefix,a=e.onFocus,s=e.onOptionRef,l=e.onSelect,c=e.optionClassName,u=e.optionComponent,d=e.optionRenderer,p=e.options,h=e.removeValue,f=e.selectValue,A=e.valueArray,v=e.valueKey,b=u;return p.map(function(e,u){var p=A&&A.some(function(t){return t[v]===e[v]}),g=e===t,m=Cr()(c,{"Select-option":!0,"is-selected":p,"is-focused":g,"is-disabled":e.disabled});return o().createElement(b,{className:m,focusOption:n,inputValue:r,instancePrefix:i,isDisabled:e.disabled,isFocused:g,isSelected:p,key:"option-"+u+"-"+e[v],onFocus:a,onSelect:l,option:e,optionIndex:u,ref:function(e){s(e,g)},removeValue:h,selectValue:f},d(e,u,r))})};Dr.propTypes={focusOption:kr().func,focusedOption:kr().object,inputValue:kr().string,instancePrefix:kr().string,onFocus:kr().func,onOptionRef:kr().func,onSelect:kr().func,optionClassName:kr().string,optionComponent:kr().func,optionRenderer:kr().func,options:kr().array,removeValue:kr().func,selectValue:kr().func,valueArray:kr().array,valueKey:kr().string};var Fr=function(e){e.preventDefault(),e.stopPropagation(),"A"===e.target.tagName&&"href"in e.target&&(e.target.target?window.open(e.target.href,e.target.target):window.location.href=e.target.href)},Ir="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Tr=(function(){function e(e){this.value=e}function t(t){var n,r;function o(n,r){try{var a=t[n](r),s=a.value;s instanceof e?Promise.resolve(s.value).then(function(e){o("next",e)},function(e){o("throw",e)}):i(a.done?"return":"normal",a.value)}catch(e){i("throw",e)}}function i(e,t){switch(e){case"return":n.resolve({value:t,done:!0});break;case"throw":n.reject(t);break;default:n.resolve({value:t,done:!1})}(n=n.next)?o(n.key,n.arg):r=null}this._invoke=function(e,t){return new Promise(function(i,a){var s={key:e,arg:t,resolve:i,reject:a,next:null};r?r=r.next=s:(n=r=s,o(e,t))})},"function"!=typeof t.return&&(this.return=void 0)}"function"==typeof Symbol&&Symbol.asyncIterator&&(t.prototype[Symbol.asyncIterator]=function(){return this}),t.prototype.next=function(e){return this._invoke("next",e)},t.prototype.throw=function(e){return this._invoke("throw",e)},t.prototype.return=function(e){return this._invoke("return",e)}}(),function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}),Mr=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),Rr=function(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},zr=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Nr=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)},Lr=function(e,t){var n={};for(var r in e)t.indexOf(r)>=0||Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n},qr=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t},Wr=function(e){function t(e){Tr(this,t);var n=qr(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.handleMouseDown=n.handleMouseDown.bind(n),n.handleMouseEnter=n.handleMouseEnter.bind(n),n.handleMouseMove=n.handleMouseMove.bind(n),n.handleTouchStart=n.handleTouchStart.bind(n),n.handleTouchEnd=n.handleTouchEnd.bind(n),n.handleTouchMove=n.handleTouchMove.bind(n),n.onFocus=n.onFocus.bind(n),n}return Nr(t,e),Mr(t,[{key:"handleMouseDown",value:function(e){e.preventDefault(),e.stopPropagation(),this.props.onSelect(this.props.option,e)}},{key:"handleMouseEnter",value:function(e){this.onFocus(e)}},{key:"handleMouseMove",value:function(e){this.onFocus(e)}},{key:"handleTouchEnd",value:function(e){this.dragging||this.handleMouseDown(e)}},{key:"handleTouchMove",value:function(){this.dragging=!0}},{key:"handleTouchStart",value:function(){this.dragging=!1}},{key:"onFocus",value:function(e){this.props.isFocused||this.props.onFocus(this.props.option,e)}},{key:"render",value:function(){var e=this.props,t=e.option,n=e.instancePrefix,r=e.optionIndex,i=Cr()(this.props.className,t.className);return t.disabled?o().createElement("div",{className:i,onMouseDown:Fr,onClick:Fr},this.props.children):o().createElement("div",{className:i,style:t.style,role:"option","aria-label":t.label,onMouseDown:this.handleMouseDown,onMouseEnter:this.handleMouseEnter,onMouseMove:this.handleMouseMove,onTouchStart:this.handleTouchStart,onTouchMove:this.handleTouchMove,onTouchEnd:this.handleTouchEnd,id:n+"-option-"+r,title:t.title},this.props.children)}}]),t}(o().Component);Wr.propTypes={children:kr().node,className:kr().string,instancePrefix:kr().string.isRequired,isDisabled:kr().bool,isFocused:kr().bool,isSelected:kr().bool,onFocus:kr().func,onSelect:kr().func,onUnfocus:kr().func,option:kr().object.isRequired,optionIndex:kr().number};var Vr=function(e){function t(e){Tr(this,t);var n=qr(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.handleMouseDown=n.handleMouseDown.bind(n),n.onRemove=n.onRemove.bind(n),n.handleTouchEndRemove=n.handleTouchEndRemove.bind(n),n.handleTouchMove=n.handleTouchMove.bind(n),n.handleTouchStart=n.handleTouchStart.bind(n),n}return Nr(t,e),Mr(t,[{key:"handleMouseDown",value:function(e){if("mousedown"!==e.type||0===e.button)return this.props.onClick?(e.stopPropagation(),void this.props.onClick(this.props.value,e)):void(this.props.value.href&&e.stopPropagation())}},{key:"onRemove",value:function(e){e.preventDefault(),e.stopPropagation(),this.props.onRemove(this.props.value)}},{key:"handleTouchEndRemove",value:function(e){this.dragging||this.onRemove(e)}},{key:"handleTouchMove",value:function(){this.dragging=!0}},{key:"handleTouchStart",value:function(){this.dragging=!1}},{key:"renderRemoveIcon",value:function(){if(!this.props.disabled&&this.props.onRemove)return o().createElement("span",{className:"Select-value-icon","aria-hidden":"true",onMouseDown:this.onRemove,onTouchEnd:this.handleTouchEndRemove,onTouchStart:this.handleTouchStart,onTouchMove:this.handleTouchMove},"×")}},{key:"renderLabel",value:function(){var e="Select-value-label";return this.props.onClick||this.props.value.href?o().createElement("a",{className:e,href:this.props.value.href,target:this.props.value.target,onMouseDown:this.handleMouseDown,onTouchEnd:this.handleMouseDown},this.props.children):o().createElement("span",{className:e,role:"option","aria-selected":"true",id:this.props.id},this.props.children)}},{key:"render",value:function(){return o().createElement("div",{className:Cr()("Select-value",this.props.value.disabled?"Select-value-disabled":"",this.props.value.className),style:this.props.value.style,title:this.props.value.title},this.renderRemoveIcon(),this.renderLabel())}}]),t}(o().Component);Vr.propTypes={children:kr().node,disabled:kr().bool,id:kr().string,onClick:kr().func,onRemove:kr().func,value:kr().object.isRequired};var Ur=function(e){return"string"==typeof e?e:null!==e&&JSON.stringify(e)||""},Yr=kr().oneOfType([kr().string,kr().node]),Hr=kr().oneOfType([kr().string,kr().number]),Kr=1,$r=function(e,t){var n=void 0===e?"undefined":Ir(e);if("string"!==n&&"number"!==n&&"boolean"!==n)return e;var r=t.options,o=t.valueKey;if(r)for(var i=0;i<r.length;i++)if(String(r[i][o])===String(e))return r[i]},Gr=function(e,t){return!e||(t?0===e.length:0===Object.keys(e).length)},Zr=function(e){function t(e){Tr(this,t);var n=qr(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return["clearValue","focusOption","getOptionLabel","handleInputBlur","handleInputChange","handleInputFocus","handleInputValueChange","handleKeyDown","handleMenuScroll","handleMouseDown","handleMouseDownOnArrow","handleMouseDownOnMenu","handleTouchEnd","handleTouchEndClearValue","handleTouchMove","handleTouchOutside","handleTouchStart","handleValueClick","onOptionRef","removeValue","selectValue"].forEach(function(e){return n[e]=n[e].bind(n)}),n.state={inputValue:"",isFocused:!1,isOpen:!1,isPseudoFocused:!1,required:!1},n}return Nr(t,e),Mr(t,[{key:"componentWillMount",value:function(){this._instancePrefix="react-select-"+(this.props.instanceId||++Kr)+"-";var e=this.getValueArray(this.props.value);this.props.required&&this.setState({required:Gr(e[0],this.props.multi)})}},{key:"componentDidMount",value:function(){void 0!==this.props.autofocus&&"undefined"!=typeof console&&console.warn("Warning: The autofocus prop has changed to autoFocus, support will be removed after react-select@1.0"),(this.props.autoFocus||this.props.autofocus)&&this.focus()}},{key:"componentWillReceiveProps",value:function(e){var t=this.getValueArray(e.value,e);e.required?this.setState({required:Gr(t[0],e.multi)}):this.props.required&&this.setState({required:!1}),this.state.inputValue&&this.props.value!==e.value&&e.onSelectResetsInput&&this.setState({inputValue:this.handleInputValueChange("")})}},{key:"componentDidUpdate",value:function(e,t){if(this.menu&&this.focused&&this.state.isOpen&&!this.hasScrolledToOption){var n=(0,Sr.findDOMNode)(this.focused),r=(0,Sr.findDOMNode)(this.menu),o=r.scrollTop,i=o+r.offsetHeight,a=n.offsetTop,s=a+n.offsetHeight;(o>a||i<s)&&(r.scrollTop=n.offsetTop),this.hasScrolledToOption=!0}else this.state.isOpen||(this.hasScrolledToOption=!1);if(this._scrollToFocusedOptionOnUpdate&&this.focused&&this.menu){this._scrollToFocusedOptionOnUpdate=!1;var l=(0,Sr.findDOMNode)(this.focused),c=(0,Sr.findDOMNode)(this.menu),u=l.getBoundingClientRect(),d=c.getBoundingClientRect();u.bottom>d.bottom?c.scrollTop=l.offsetTop+l.clientHeight-c.offsetHeight:u.top<d.top&&(c.scrollTop=l.offsetTop)}if(this.props.scrollMenuIntoView&&this.menuContainer){var p=this.menuContainer.getBoundingClientRect();window.innerHeight<p.bottom+this.props.menuBuffer&&window.scrollBy(0,p.bottom+this.props.menuBuffer-window.innerHeight)}if(e.disabled!==this.props.disabled&&(this.setState({isFocused:!1}),this.closeMenu()),t.isOpen!==this.state.isOpen){this.toggleTouchOutsideEvent(this.state.isOpen);var h=this.state.isOpen?this.props.onOpen:this.props.onClose;h&&h()}}},{key:"componentWillUnmount",value:function(){this.toggleTouchOutsideEvent(!1)}},{key:"toggleTouchOutsideEvent",value:function(e){var t=e?document.addEventListener?"addEventListener":"attachEvent":document.removeEventListener?"removeEventListener":"detachEvent",n=document.addEventListener?"":"on";document[t](n+"touchstart",this.handleTouchOutside),document[t](n+"mousedown",this.handleTouchOutside)}},{key:"handleTouchOutside",value:function(e){this.wrapper&&!this.wrapper.contains(e.target)&&this.closeMenu()}},{key:"focus",value:function(){this.input&&this.input.focus()}},{key:"blurInput",value:function(){this.input&&this.input.blur()}},{key:"handleTouchMove",value:function(){this.dragging=!0}},{key:"handleTouchStart",value:function(){this.dragging=!1}},{key:"handleTouchEnd",value:function(e){this.dragging||this.handleMouseDown(e)}},{key:"handleTouchEndClearValue",value:function(e){this.dragging||this.clearValue(e)}},{key:"handleMouseDown",value:function(e){if(!(this.props.disabled||"mousedown"===e.type&&0!==e.button))if("INPUT"!==e.target.tagName){if(e.preventDefault(),!this.props.searchable)return this.focus(),this.setState({isOpen:!this.state.isOpen,focusedOption:null});if(this.state.isFocused){this.focus();var t=this.input,n=!0;"function"==typeof t.getInput&&(t=t.getInput()),t.value="",this._focusAfterClear&&(n=!1,this._focusAfterClear=!1),this.setState({isOpen:n,isPseudoFocused:!1,focusedOption:null})}else this._openAfterFocus=this.props.openOnClick,this.focus(),this.setState({focusedOption:null})}else this.state.isFocused?this.state.isOpen||this.setState({isOpen:!0,isPseudoFocused:!1,focusedOption:null}):(this._openAfterFocus=this.props.openOnClick,this.focus())}},{key:"handleMouseDownOnArrow",value:function(e){this.props.disabled||"mousedown"===e.type&&0!==e.button||(this.state.isOpen?(e.stopPropagation(),e.preventDefault(),this.closeMenu()):this.setState({isOpen:!0}))}},{key:"handleMouseDownOnMenu",value:function(e){this.props.disabled||"mousedown"===e.type&&0!==e.button||(e.stopPropagation(),e.preventDefault(),this._openAfterFocus=!0,this.focus())}},{key:"closeMenu",value:function(){this.props.onCloseResetsInput?this.setState({inputValue:this.handleInputValueChange(""),isOpen:!1,isPseudoFocused:this.state.isFocused&&!this.props.multi}):this.setState({isOpen:!1,isPseudoFocused:this.state.isFocused&&!this.props.multi}),this.hasScrolledToOption=!1}},{key:"handleInputFocus",value:function(e){if(!this.props.disabled){var t=this.state.isOpen||this._openAfterFocus||this.props.openOnFocus;t=!this._focusAfterClear&&t,this.props.onFocus&&this.props.onFocus(e),this.setState({isFocused:!0,isOpen:!!t}),this._focusAfterClear=!1,this._openAfterFocus=!1}}},{key:"handleInputBlur",value:function(e){if(!this.menu||this.menu!==document.activeElement&&!this.menu.contains(document.activeElement)){this.props.onBlur&&this.props.onBlur(e);var t={isFocused:!1,isOpen:!1,isPseudoFocused:!1};this.props.onBlurResetsInput&&(t.inputValue=this.handleInputValueChange("")),this.setState(t)}else this.focus()}},{key:"handleInputChange",value:function(e){var t=e.target.value;this.state.inputValue!==e.target.value&&(t=this.handleInputValueChange(t)),this.setState({inputValue:t,isOpen:!0,isPseudoFocused:!1})}},{key:"setInputValue",value:function(e){if(this.props.onInputChange){var t=this.props.onInputChange(e);null!=t&&"object"!==(void 0===t?"undefined":Ir(t))&&(e=""+t)}this.setState({inputValue:e})}},{key:"handleInputValueChange",value:function(e){if(this.props.onInputChange){var t=this.props.onInputChange(e);null!=t&&"object"!==(void 0===t?"undefined":Ir(t))&&(e=""+t)}return e}},{key:"handleKeyDown",value:function(e){if(!(this.props.disabled||"function"==typeof this.props.onInputKeyDown&&(this.props.onInputKeyDown(e),e.defaultPrevented)))switch(e.keyCode){case 8:!this.state.inputValue&&this.props.backspaceRemoves&&(e.preventDefault(),this.popValue());break;case 9:if(e.shiftKey||!this.state.isOpen||!this.props.tabSelectsValue)break;e.preventDefault(),this.selectFocusedOption();break;case 13:e.preventDefault(),e.stopPropagation(),this.state.isOpen?this.selectFocusedOption():this.focusNextOption();break;case 27:e.preventDefault(),this.state.isOpen?(this.closeMenu(),e.stopPropagation()):this.props.clearable&&this.props.escapeClearsValue&&(this.clearValue(e),e.stopPropagation());break;case 32:if(this.props.searchable)break;if(e.preventDefault(),!this.state.isOpen){this.focusNextOption();break}e.stopPropagation(),this.selectFocusedOption();break;case 38:e.preventDefault(),this.focusPreviousOption();break;case 40:e.preventDefault(),this.focusNextOption();break;case 33:e.preventDefault(),this.focusPageUpOption();break;case 34:e.preventDefault(),this.focusPageDownOption();break;case 35:if(e.shiftKey)break;e.preventDefault(),this.focusEndOption();break;case 36:if(e.shiftKey)break;e.preventDefault(),this.focusStartOption();break;case 46:!this.state.inputValue&&this.props.deleteRemoves&&(e.preventDefault(),this.popValue())}}},{key:"handleValueClick",value:function(e,t){this.props.onValueClick&&this.props.onValueClick(e,t)}},{key:"handleMenuScroll",value:function(e){if(this.props.onMenuScrollToBottom){var t=e.target;t.scrollHeight>t.offsetHeight&&t.scrollHeight-t.offsetHeight-t.scrollTop<=0&&this.props.onMenuScrollToBottom()}}},{key:"getOptionLabel",value:function(e){return e[this.props.labelKey]}},{key:"getValueArray",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,n="object"===(void 0===t?"undefined":Ir(t))?t:this.props;if(n.multi){if("string"==typeof e&&(e=e.split(n.delimiter)),!Array.isArray(e)){if(null==e)return[];e=[e]}return e.map(function(e){return $r(e,n)}).filter(function(e){return e})}var r=$r(e,n);return r?[r]:[]}},{key:"setValue",value:function(e){var t=this;if(this.props.autoBlur&&this.blurInput(),this.props.required){var n=Gr(e,this.props.multi);this.setState({required:n})}this.props.simpleValue&&e&&(e=this.props.multi?e.map(function(e){return e[t.props.valueKey]}).join(this.props.delimiter):e[this.props.valueKey]),this.props.onChange&&this.props.onChange(e)}},{key:"selectValue",value:function(e){var t=this;this.props.closeOnSelect&&(this.hasScrolledToOption=!1);var n=this.props.onSelectResetsInput?"":this.state.inputValue;this.props.multi?this.setState({focusedIndex:null,inputValue:this.handleInputValueChange(n),isOpen:!this.props.closeOnSelect},function(){t.getValueArray(t.props.value).some(function(n){return n[t.props.valueKey]===e[t.props.valueKey]})?t.removeValue(e):t.addValue(e)}):this.setState({inputValue:this.handleInputValueChange(n),isOpen:!this.props.closeOnSelect,isPseudoFocused:this.state.isFocused},function(){t.setValue(e)})}},{key:"addValue",value:function(e){var t=this.getValueArray(this.props.value),n=this._visibleOptions.filter(function(e){return!e.disabled}),r=n.indexOf(e);this.setValue(t.concat(e)),this.props.closeOnSelect&&(n.length-1===r?this.focusOption(n[r-1]):n.length>r&&this.focusOption(n[r+1]))}},{key:"popValue",value:function(){var e=this.getValueArray(this.props.value);e.length&&!1!==e[e.length-1].clearableValue&&this.setValue(this.props.multi?e.slice(0,e.length-1):null)}},{key:"removeValue",value:function(e){var t=this,n=this.getValueArray(this.props.value);this.setValue(n.filter(function(n){return n[t.props.valueKey]!==e[t.props.valueKey]})),this.focus()}},{key:"clearValue",value:function(e){e&&"mousedown"===e.type&&0!==e.button||(e.preventDefault(),this.setValue(this.getResetValue()),this.setState({inputValue:this.handleInputValueChange(""),isOpen:!1},this.focus),this._focusAfterClear=!0)}},{key:"getResetValue",value:function(){return void 0!==this.props.resetValue?this.props.resetValue:this.props.multi?[]:null}},{key:"focusOption",value:function(e){this.setState({focusedOption:e})}},{key:"focusNextOption",value:function(){this.focusAdjacentOption("next")}},{key:"focusPreviousOption",value:function(){this.focusAdjacentOption("previous")}},{key:"focusPageUpOption",value:function(){this.focusAdjacentOption("page_up")}},{key:"focusPageDownOption",value:function(){this.focusAdjacentOption("page_down")}},{key:"focusStartOption",value:function(){this.focusAdjacentOption("start")}},{key:"focusEndOption",value:function(){this.focusAdjacentOption("end")}},{key:"focusAdjacentOption",value:function(e){var t=this._visibleOptions.map(function(e,t){return{option:e,index:t}}).filter(function(e){return!e.option.disabled});if(this._scrollToFocusedOptionOnUpdate=!0,!this.state.isOpen){var n={focusedOption:this._focusedOption||(t.length?t["next"===e?0:t.length-1].option:null),isOpen:!0};return this.props.onSelectResetsInput&&(n.inputValue=""),void this.setState(n)}if(t.length){for(var r=-1,o=0;o<t.length;o++)if(this._focusedOption===t[o].option){r=o;break}if("next"===e&&-1!==r)r=(r+1)%t.length;else if("previous"===e)r>0?r-=1:r=t.length-1;else if("start"===e)r=0;else if("end"===e)r=t.length-1;else if("page_up"===e){var i=r-this.props.pageSize;r=i<0?0:i}else if("page_down"===e){var a=r+this.props.pageSize;r=a>t.length-1?t.length-1:a}-1===r&&(r=0),this.setState({focusedIndex:t[r].index,focusedOption:t[r].option})}}},{key:"getFocusedOption",value:function(){return this._focusedOption}},{key:"selectFocusedOption",value:function(){if(this._focusedOption)return this.selectValue(this._focusedOption)}},{key:"renderLoading",value:function(){if(this.props.isLoading)return o().createElement("span",{className:"Select-loading-zone","aria-hidden":"true"},o().createElement("span",{className:"Select-loading"}))}},{key:"renderValue",value:function(e,t){var n=this,r=this.props.valueRenderer||this.getOptionLabel,i=this.props.valueComponent;if(!e.length){var a=function(e,t,n){var r=e.inputValue,o=e.isPseudoFocused,i=e.isFocused,a=t.onSelectResetsInput;return!r||!a&&!n&&!o&&!i}(this.state,this.props,t);return a?o().createElement("div",{className:"Select-placeholder"},this.props.placeholder):null}var s=this.props.onValueClick?this.handleValueClick:null;return this.props.multi?e.map(function(t,a){return o().createElement(i,{disabled:n.props.disabled||!1===t.clearableValue,id:n._instancePrefix+"-value-"+a,instancePrefix:n._instancePrefix,key:"value-"+a+"-"+t[n.props.valueKey],onClick:s,onRemove:n.removeValue,placeholder:n.props.placeholder,value:t,values:e},r(t,a),o().createElement("span",{className:"Select-aria-only"}," "))}):function(e,t){var n=e.inputValue,r=e.isPseudoFocused,o=e.isFocused,i=t.onSelectResetsInput;return!n||!i&&!(!o&&r||o&&!r)}(this.state,this.props)?(t&&(s=null),o().createElement(i,{disabled:this.props.disabled,id:this._instancePrefix+"-value-item",instancePrefix:this._instancePrefix,onClick:s,placeholder:this.props.placeholder,value:e[0]},r(e[0]))):void 0}},{key:"renderInput",value:function(e,t){var n,r=this,i=Cr()("Select-input",this.props.inputProps.className),a=this.state.isOpen,s=Cr()((Rr(n={},this._instancePrefix+"-list",a),Rr(n,this._instancePrefix+"-backspace-remove-message",this.props.multi&&!this.props.disabled&&this.state.isFocused&&!this.state.inputValue),n)),l=this.state.inputValue;!l||this.props.onSelectResetsInput||this.state.isFocused||(l="");var c=zr({},this.props.inputProps,{"aria-activedescendant":a?this._instancePrefix+"-option-"+t:this._instancePrefix+"-value","aria-describedby":this.props["aria-describedby"],"aria-expanded":""+a,"aria-haspopup":""+a,"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],"aria-owns":s,onBlur:this.handleInputBlur,onChange:this.handleInputChange,onFocus:this.handleInputFocus,ref:function(e){return r.input=e},role:"combobox",required:this.state.required,tabIndex:this.props.tabIndex,value:l});if(this.props.inputRenderer)return this.props.inputRenderer(c);if(this.props.disabled||!this.props.searchable){var u=Lr(this.props.inputProps,[]),d=Cr()(Rr({},this._instancePrefix+"-list",a));return o().createElement("div",zr({},u,{"aria-expanded":a,"aria-owns":d,"aria-activedescendant":a?this._instancePrefix+"-option-"+t:this._instancePrefix+"-value","aria-disabled":""+this.props.disabled,"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],className:i,onBlur:this.handleInputBlur,onFocus:this.handleInputFocus,ref:function(e){return r.input=e},role:"combobox",style:{border:0,width:1,display:"inline-block"},tabIndex:this.props.tabIndex||0}))}return this.props.autosize?o().createElement(wr.A,zr({id:this.props.id},c,{className:i,minWidth:"5"})):o().createElement("div",{className:i,key:"input-wrap",style:{display:"inline-block"}},o().createElement("input",zr({id:this.props.id},c)))}},{key:"renderClear",value:function(){var e=this.getValueArray(this.props.value);if(this.props.clearable&&e.length&&!this.props.disabled&&!this.props.isLoading){var t=this.props.multi?this.props.clearAllText:this.props.clearValueText,n=this.props.clearRenderer();return o().createElement("span",{"aria-label":t,className:"Select-clear-zone",onMouseDown:this.clearValue,onTouchEnd:this.handleTouchEndClearValue,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,title:t},n)}}},{key:"renderArrow",value:function(){if(this.props.arrowRenderer){var e=this.handleMouseDownOnArrow,t=this.state.isOpen,n=this.props.arrowRenderer({onMouseDown:e,isOpen:t});return n?o().createElement("span",{className:"Select-arrow-zone",onMouseDown:e},n):null}}},{key:"filterOptions",value:function(e){var t=this.state.inputValue,n=this.props.options||[];return this.props.filterOptions?("function"==typeof this.props.filterOptions?this.props.filterOptions:Pr)(n,t,e,{filterOption:this.props.filterOption,ignoreAccents:this.props.ignoreAccents,ignoreCase:this.props.ignoreCase,labelKey:this.props.labelKey,matchPos:this.props.matchPos,matchProp:this.props.matchProp,trimFilter:this.props.trimFilter,valueKey:this.props.valueKey}):n}},{key:"onOptionRef",value:function(e,t){t&&(this.focused=e)}},{key:"renderMenu",value:function(e,t,n){return e&&e.length?this.props.menuRenderer({focusedOption:n,focusOption:this.focusOption,inputValue:this.state.inputValue,instancePrefix:this._instancePrefix,labelKey:this.props.labelKey,onFocus:this.focusOption,onOptionRef:this.onOptionRef,onSelect:this.selectValue,optionClassName:this.props.optionClassName,optionComponent:this.props.optionComponent,optionRenderer:this.props.optionRenderer||this.getOptionLabel,options:e,removeValue:this.removeValue,selectValue:this.selectValue,valueArray:t,valueKey:this.props.valueKey}):this.props.noResultsText?o().createElement("div",{className:"Select-noresults"},this.props.noResultsText):null}},{key:"renderHiddenField",value:function(e){var t=this;if(this.props.name){if(this.props.joinValues){var n=e.map(function(e){return Ur(e[t.props.valueKey])}).join(this.props.delimiter);return o().createElement("input",{disabled:this.props.disabled,name:this.props.name,ref:function(e){return t.value=e},type:"hidden",value:n})}return e.map(function(e,n){return o().createElement("input",{disabled:t.props.disabled,key:"hidden."+n,name:t.props.name,ref:"value"+n,type:"hidden",value:Ur(e[t.props.valueKey])})})}}},{key:"getFocusableOptionIndex",value:function(e){var t=this._visibleOptions;if(!t.length)return null;var n=this.props.valueKey,r=this.state.focusedOption||e;if(r&&!r.disabled){var o=-1;if(t.some(function(e,t){var i=e[n]===r[n];return i&&(o=t),i}),-1!==o)return o}for(var i=0;i<t.length;i++)if(!t[i].disabled)return i;return null}},{key:"renderOuter",value:function(e,t,n){var r=this,i=this.renderMenu(e,t,n);return i?o().createElement("div",{ref:function(e){return r.menuContainer=e},className:"Select-menu-outer",style:this.props.menuContainerStyle},o().createElement("div",{className:"Select-menu",id:this._instancePrefix+"-list",onMouseDown:this.handleMouseDownOnMenu,onScroll:this.handleMenuScroll,ref:function(e){return r.menu=e},role:"listbox",style:this.props.menuStyle,tabIndex:-1},i)):null}},{key:"render",value:function(){var e=this,t=this.getValueArray(this.props.value),n=this._visibleOptions=this.filterOptions(this.props.multi&&this.props.removeSelected?t:null),r=this.state.isOpen;this.props.multi&&!n.length&&t.length&&!this.state.inputValue&&(r=!1);var i,a=this.getFocusableOptionIndex(t[0]);i=this._focusedOption=null!==a?n[a]:null;var s=Cr()("Select",this.props.className,{"has-value":t.length,"is-clearable":this.props.clearable,"is-disabled":this.props.disabled,"is-focused":this.state.isFocused,"is-loading":this.props.isLoading,"is-open":r,"is-pseudo-focused":this.state.isPseudoFocused,"is-searchable":this.props.searchable,"Select--multi":this.props.multi,"Select--rtl":this.props.rtl,"Select--single":!this.props.multi}),l=null;return this.props.multi&&!this.props.disabled&&t.length&&!this.state.inputValue&&this.state.isFocused&&this.props.backspaceRemoves&&(l=o().createElement("span",{id:this._instancePrefix+"-backspace-remove-message",className:"Select-aria-only","aria-live":"assertive"},this.props.backspaceToRemoveMessage.replace("{label}",t[t.length-1][this.props.labelKey]))),o().createElement("div",{ref:function(t){return e.wrapper=t},className:s,style:this.props.wrapperStyle},this.renderHiddenField(t),o().createElement("div",{ref:function(t){return e.control=t},className:"Select-control",onKeyDown:this.handleKeyDown,onMouseDown:this.handleMouseDown,onTouchEnd:this.handleTouchEnd,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,style:this.props.style},o().createElement("div",{className:"Select-multi-value-wrapper",id:this._instancePrefix+"-value"},this.renderValue(t,r),this.renderInput(t,a)),l,this.renderLoading(),this.renderClear(),this.renderArrow()),r?this.renderOuter(n,t,i):null)}}]),t}(o().Component);Zr.propTypes={"aria-describedby":kr().string,"aria-label":kr().string,"aria-labelledby":kr().string,arrowRenderer:kr().func,autoBlur:kr().bool,autoFocus:kr().bool,autofocus:kr().bool,autosize:kr().bool,backspaceRemoves:kr().bool,backspaceToRemoveMessage:kr().string,className:kr().string,clearAllText:Yr,clearRenderer:kr().func,clearValueText:Yr,clearable:kr().bool,closeOnSelect:kr().bool,deleteRemoves:kr().bool,delimiter:kr().string,disabled:kr().bool,escapeClearsValue:kr().bool,filterOption:kr().func,filterOptions:kr().any,id:kr().string,ignoreAccents:kr().bool,ignoreCase:kr().bool,inputProps:kr().object,inputRenderer:kr().func,instanceId:kr().string,isLoading:kr().bool,joinValues:kr().bool,labelKey:kr().string,matchPos:kr().string,matchProp:kr().string,menuBuffer:kr().number,menuContainerStyle:kr().object,menuRenderer:kr().func,menuStyle:kr().object,multi:kr().bool,name:kr().string,noResultsText:Yr,onBlur:kr().func,onBlurResetsInput:kr().bool,onChange:kr().func,onClose:kr().func,onCloseResetsInput:kr().bool,onFocus:kr().func,onInputChange:kr().func,onInputKeyDown:kr().func,onMenuScrollToBottom:kr().func,onOpen:kr().func,onSelectResetsInput:kr().bool,onValueClick:kr().func,openOnClick:kr().bool,openOnFocus:kr().bool,optionClassName:kr().string,optionComponent:kr().func,optionRenderer:kr().func,options:kr().array,pageSize:kr().number,placeholder:Yr,removeSelected:kr().bool,required:kr().bool,resetValue:kr().any,rtl:kr().bool,scrollMenuIntoView:kr().bool,searchable:kr().bool,simpleValue:kr().bool,style:kr().object,tabIndex:Hr,tabSelectsValue:kr().bool,trimFilter:kr().bool,value:kr().any,valueComponent:kr().func,valueKey:kr().string,valueRenderer:kr().func,wrapperStyle:kr().object},Zr.defaultProps={arrowRenderer:Or,autosize:!0,backspaceRemoves:!0,backspaceToRemoveMessage:"Press backspace to remove {label}",clearable:!0,clearAllText:"Clear all",clearRenderer:function(){return o().createElement("span",{className:"Select-clear",dangerouslySetInnerHTML:{__html:"&times;"}})},clearValueText:"Clear value",closeOnSelect:!0,deleteRemoves:!0,delimiter:",",disabled:!1,escapeClearsValue:!0,filterOptions:Pr,ignoreAccents:!0,ignoreCase:!0,inputProps:{},isLoading:!1,joinValues:!1,labelKey:"label",matchPos:"any",matchProp:"any",menuBuffer:0,menuRenderer:Dr,multi:!1,noResultsText:"No results found",onBlurResetsInput:!0,onCloseResetsInput:!0,onSelectResetsInput:!0,openOnClick:!0,optionComponent:Wr,pageSize:5,placeholder:"Select...",removeSelected:!0,required:!1,rtl:!1,scrollMenuIntoView:!0,searchable:!0,simpleValue:!1,tabSelectsValue:!0,trimFilter:!0,valueComponent:Vr,valueKey:"value"};var Qr={autoload:kr().bool.isRequired,cache:kr().any,children:kr().func.isRequired,ignoreAccents:kr().bool,ignoreCase:kr().bool,loadOptions:kr().func.isRequired,loadingPlaceholder:kr().oneOfType([kr().string,kr().node]),multi:kr().bool,noResultsText:kr().oneOfType([kr().string,kr().node]),onChange:kr().func,onInputChange:kr().func,options:kr().array.isRequired,placeholder:kr().oneOfType([kr().string,kr().node]),searchPromptText:kr().oneOfType([kr().string,kr().node]),value:kr().any},Xr={},Jr={autoload:!0,cache:Xr,children:function(e){return o().createElement(Zr,e)},ignoreAccents:!0,ignoreCase:!0,loadingPlaceholder:"Loading...",options:[],searchPromptText:"Type to search"},eo=function(e){function t(e,n){Tr(this,t);var r=qr(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,n));return r._cache=e.cache===Xr?{}:e.cache,r.state={inputValue:"",isLoading:!1,options:e.options},r.onInputChange=r.onInputChange.bind(r),r}return Nr(t,e),Mr(t,[{key:"componentDidMount",value:function(){this.props.autoload&&this.loadOptions("")}},{key:"componentWillReceiveProps",value:function(e){e.options!==this.props.options&&this.setState({options:e.options})}},{key:"componentWillUnmount",value:function(){this._callback=null}},{key:"loadOptions",value:function(e){var t=this,n=this.props.loadOptions,r=this._cache;if(r&&Object.prototype.hasOwnProperty.call(r,e))return this._callback=null,void this.setState({isLoading:!1,options:r[e]});var o=function n(o,i){var a=i&&i.options||[];r&&(r[e]=a),n===t._callback&&(t._callback=null,t.setState({isLoading:!1,options:a}))};this._callback=o;var i=n(e,o);i&&i.then(function(e){return o(0,e)},function(e){return o()}),this._callback&&!this.state.isLoading&&this.setState({isLoading:!0})}},{key:"onInputChange",value:function(e){var t=this.props,n=t.ignoreAccents,r=t.ignoreCase,o=t.onInputChange,i=e;if(o){var a=o(i);null!=a&&"object"!==(void 0===a?"undefined":Ir(a))&&(i=""+a)}var s=i;return n&&(s=_r(s)),r&&(s=s.toLowerCase()),this.setState({inputValue:i}),this.loadOptions(s),i}},{key:"noResultsText",value:function(){var e=this.props,t=e.loadingPlaceholder,n=e.noResultsText,r=e.searchPromptText,o=this.state,i=o.inputValue;return o.isLoading?t:i&&n?n:r}},{key:"focus",value:function(){this.select.focus()}},{key:"render",value:function(){var e=this,t=this.props,n=t.children,r=t.loadingPlaceholder,o=t.placeholder,i=this.state,a=i.isLoading,s=i.options,l={noResultsText:this.noResultsText(),placeholder:a?r:o,options:a&&r?[]:s,ref:function(t){return e.select=t}};return n(zr({},this.props,l,{isLoading:a,onInputChange:this.onInputChange}))}}]),t}(r.Component);eo.propTypes=Qr,eo.defaultProps=Jr;var to=function(e){function t(e,n){Tr(this,t);var r=qr(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,n));return r.filterOptions=r.filterOptions.bind(r),r.menuRenderer=r.menuRenderer.bind(r),r.onInputKeyDown=r.onInputKeyDown.bind(r),r.onInputChange=r.onInputChange.bind(r),r.onOptionSelect=r.onOptionSelect.bind(r),r}return Nr(t,e),Mr(t,[{key:"createNewOption",value:function(){var e=this.props,t=e.isValidNewOption,n=e.newOptionCreator,r=e.onNewOptionClick,o=e.options,i=void 0===o?[]:o;if(t({label:this.inputValue})){var a=n({label:this.inputValue,labelKey:this.labelKey,valueKey:this.valueKey});this.isOptionUnique({option:a,options:i})&&(r?r(a):(i.unshift(a),this.select.selectValue(a)))}}},{key:"filterOptions",value:function(){var e=this.props,t=e.filterOptions,n=e.isValidNewOption,r=e.promptTextCreator,o=e.showNewOptionAtTop,i=(arguments.length<=2?void 0:arguments[2])||[],a=t.apply(void 0,arguments)||[];if(n({label:this.inputValue})){var s=this.props.newOptionCreator,l=s({label:this.inputValue,labelKey:this.labelKey,valueKey:this.valueKey});if(this.isOptionUnique({option:l,options:i.concat(a)})){var c=r(this.inputValue);this._createPlaceholderOption=s({label:c,labelKey:this.labelKey,valueKey:this.valueKey}),o?a.unshift(this._createPlaceholderOption):a.push(this._createPlaceholderOption)}}return a}},{key:"isOptionUnique",value:function(e){var t=e.option,n=e.options,r=this.props.isOptionUnique;return n=n||this.props.options,r({labelKey:this.labelKey,option:t,options:n,valueKey:this.valueKey})}},{key:"menuRenderer",value:function(e){return(0,this.props.menuRenderer)(zr({},e,{onSelect:this.onOptionSelect,selectValue:this.onOptionSelect}))}},{key:"onInputChange",value:function(e){var t=this.props.onInputChange;return this.inputValue=e,t&&(this.inputValue=t(e)),this.inputValue}},{key:"onInputKeyDown",value:function(e){var t=this.props,n=t.shouldKeyDownEventCreateNewOption,r=t.onInputKeyDown,o=this.select.getFocusedOption();o&&o===this._createPlaceholderOption&&n(e)?(this.createNewOption(),e.preventDefault()):r&&r(e)}},{key:"onOptionSelect",value:function(e){e===this._createPlaceholderOption?this.createNewOption():this.select.selectValue(e)}},{key:"focus",value:function(){this.select.focus()}},{key:"render",value:function(){var e=this,t=this.props,n=t.ref,r=Lr(t,["ref"]),o=this.props.children;return o||(o=no),o(zr({},r,{allowCreate:!0,filterOptions:this.filterOptions,menuRenderer:this.menuRenderer,onInputChange:this.onInputChange,onInputKeyDown:this.onInputKeyDown,ref:function(t){e.select=t,t&&(e.labelKey=t.props.labelKey,e.valueKey=t.props.valueKey),n&&n(t)}}))}}]),t}(o().Component),no=function(e){return o().createElement(Zr,e)},ro=function(e){var t=e.option,n=e.options,r=e.labelKey,o=e.valueKey;return!n||!n.length||0===n.filter(function(e){return e[r]===t[r]||e[o]===t[o]}).length},oo=function(e){return!!e.label},io=function(e){var t=e.label,n=e.labelKey,r={};return r[e.valueKey]=t,r[n]=t,r.className="Select-create-option-placeholder",r},ao=function(e){return'Create option "'+e+'"'},so=function(e){switch(e.keyCode){case 9:case 13:case 188:return!0;default:return!1}};to.isOptionUnique=ro,to.isValidNewOption=oo,to.newOptionCreator=io,to.promptTextCreator=ao,to.shouldKeyDownEventCreateNewOption=so,to.defaultProps={filterOptions:Pr,isOptionUnique:ro,isValidNewOption:oo,menuRenderer:Dr,newOptionCreator:io,promptTextCreator:ao,shouldKeyDownEventCreateNewOption:so,showNewOptionAtTop:!0},to.propTypes={children:kr().func,filterOptions:kr().any,isOptionUnique:kr().func,isValidNewOption:kr().func,menuRenderer:kr().any,newOptionCreator:kr().func,onInputChange:kr().func,onInputKeyDown:kr().func,onNewOptionClick:kr().func,options:kr().array,promptTextCreator:kr().func,ref:kr().func,shouldKeyDownEventCreateNewOption:kr().func,showNewOptionAtTop:kr().bool};var lo=function(e){function t(){return Tr(this,t),qr(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return Nr(t,e),Mr(t,[{key:"focus",value:function(){this.select.focus()}},{key:"render",value:function(){var e=this;return o().createElement(eo,this.props,function(t){var n=t.ref,r=Lr(t,["ref"]),i=n;return o().createElement(to,r,function(t){var n=t.ref,r=Lr(t,["ref"]),o=n;return e.props.children(zr({},r,{ref:function(t){o(t),i(t),e.select=t}}))})})}}]),t}(o().Component);lo.propTypes={children:kr().func.isRequired},lo.defaultProps={children:function(e){return o().createElement(Zr,e)}},Zr.Async=eo,Zr.AsyncCreatable=lo,Zr.Creatable=to,Zr.Value=Vr,Zr.Option=Wr;var co=Zr;class uo{static getFirstParentOfType(e,t){t=t.toUpperCase();for(var n=e;n;){if(n.tagName.toUpperCase()===t)return n;if(null===n.parentElement)return;n=n.parentElement}}static getParentById(e,t){for(var n=e;n;){if(n.id===t)return n;if(null===n.parentElement)return;n=n.parentElement}}}function po(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class ho extends r.Component{constructor(e){super(e),po(this,"dropdownRef",void 0),po(this,"handleOpenDropdown",()=>{Ln(this.dropdownRef.current.wrapper.querySelector(".Select-menu-outer"))}),this.dropdownRef=(0,r.createRef)()}render(){var e=this.props,t=e.clearable,n=e.dropdown,r=e.onChange,i=e.value,a=e.disabled;return o().createElement("div",{className:"dash-dropdown-cell-value-container dash-cell-value-container",onClick:this.handleClick},o().createElement("div",{className:"dropdown-cell-value-shadow cell-value-shadow"},(n&&n.find(e=>e.value===i)||{label:void 0}).label),o().createElement(co,{ref:this.dropdownRef,clearable:t,onChange:e=>{r(e?e.value:e)},scrollMenuIntoView:!1,onOpen:this.handleOpenDropdown,options:n,placeholder:"",value:i,disabled:a}))}componentDidUpdate(){this.setFocus()}componentDidMount(){this.setFocus()}handleClick(e){e.stopPropagation()}setFocus(){var e=this.props,t=e.active,n=e.applyFocus;if(t){var r=this.dropdownRef.current;if(n&&r&&document.activeElement!==r){var o=uo.getFirstParentOfType(r.wrapper,"td");o&&-1===o.className.indexOf("phantom-cell")&&o.focus()}}}}var fo,Ao=e=>e,vo=e=>{var t;return e.type===Ge.$C.Numeric&&(t=function(e){if(!e)return e=>e;var t,n,r,o,i=function(e){var t,n,r=void 0===e.grouping||void 0===e.thousands?dn:(t=pn.call(e.grouping,Number),n=e.thousands+"",function(e,r){for(var o=e.length,i=[],a=0,s=t[0],l=0;o>0&&s>0&&(l+s+1>r&&(s=Math.max(1,r-l)),i.push(e.substring(o-=s,o+s)),!((l+=s+1)>r));)s=t[a=(a+1)%t.length];return i.reverse().join(n)}),o=void 0===e.currency?"":e.currency[0]+"",i=void 0===e.currency?"":e.currency[1]+"",a=void 0===e.decimal?".":e.decimal+"",s=void 0===e.numerals?dn:function(e){return function(t){return t.replace(/[0-9]/g,function(t){return e[+t]})}}(pn.call(e.numerals,String)),l=void 0===e.percent?"%":e.percent+"",c=void 0===e.minus?"−":e.minus+"",u=void 0===e.nan?"NaN":e.nan+"";function d(e){var t=(e=sn(e)).fill,n=e.align,d=e.sign,p=e.symbol,h=e.zero,f=e.width,A=e.comma,v=e.precision,b=e.trim,g=e.type;"n"===g?(A=!0,g="g"):un[g]||(void 0===v&&(v=12),b=!0,g="g"),(h||"0"===t&&"="===n)&&(h=!0,t="0",n="=");var m="$"===p?o:"#"===p&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",y="$"===p?i:/[%p]/.test(g)?l:"",w=un[g],E=/[defgprs%]/.test(g);function C(e){var o,i,l,p=m,C=y;if("c"===g)C=w(e)+C,e="";else{var x=(e=+e)<0||1/e<0;if(e=isNaN(e)?u:w(Math.abs(e),v),b&&(e=function(e){e:for(var t,n=e.length,r=1,o=-1;r<n;++r)switch(e[r]){case".":o=t=r;break;case"0":0===o&&(o=r),t=r;break;default:if(!+e[r])break e;o>0&&(o=0)}return o>0?e.slice(0,o)+e.slice(t+1):e}(e)),x&&0===+e&&"+"!==d&&(x=!1),p=(x?"("===d?d:c:"-"===d||"("===d?"":d)+p,C=("s"===g?hn[8+on/3]:"")+C+(x&&"("===d?")":""),E)for(o=-1,i=e.length;++o<i;)if(48>(l=e.charCodeAt(o))||l>57){C=(46===l?a+e.slice(o+1):e.slice(o))+C,e=e.slice(0,o);break}}A&&!h&&(e=r(e,1/0));var k=p.length+e.length+C.length,S=k<f?new Array(f-k+1).join(t):"";switch(A&&h&&(e=r(S+e,S.length?f-C.length:1/0),S=""),n){case"<":e=p+e+C+S;break;case"=":e=p+S+e+C;break;case"^":e=S.slice(0,k=S.length>>1)+p+e+C+S.slice(k);break;default:e=S+p+e+C}return s(e)}return v=void 0===v?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),C.toString=function(){return e+""},C}return{format:d,formatPrefix:function(e,t){var n,r=d(((e=sn(e)).type="f",e)),o=3*Math.max(-8,Math.min(8,Math.floor((n=t,((n=rn(Math.abs(n)))?n[1]:NaN)/3)))),i=Math.pow(10,-o),a=hn[8+o/3];return function(e){return r(i*e)+a}}}}((t=e.locale,n=t.group,r=t.symbol,o=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(t,gn),function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?mn(Object(n),!0).forEach(function(t){yn(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):mn(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({currency:r,thousands:n},Te(["separate_4digits","symbol"],o)))),a=e.prefix?i.formatPrefix(e.specifier,e.prefix):i.format(e.specifier),s=e.locale.separate_4digits?e.specifier:e.specifier.replace(/,/,""),l=e.prefix?i.formatPrefix(s,e.prefix):i.format(s);return t=>"number"!=typeof(t=bn(t)?e.nully:t)?t:Math.abs(t)<1e4?l(t):a(t)}(e.format)),t||Ao};function bo(e){return(fo=fo||document.createElement("textarea")).innerHTML="&"+e+";",fo.value}var go=Object.prototype.hasOwnProperty;function mo(e){return[].slice.call(arguments,1).forEach(function(t){if(t){if("object"!=typeof t)throw new TypeError(t+"must be object");Object.keys(t).forEach(function(n){e[n]=t[n]})}}),e}var yo=/\\([\\!"#$%&'()*+,.\/:;<=>?@[\]^_`{|}~-])/g;function wo(e){return e.indexOf("\\")<0?e:e.replace(yo,"$1")}function Eo(e){return!(e>=55296&&e<=57343||e>=64976&&e<=65007||!(65535&~e&&65534!=(65535&e))||e>=0&&e<=8||11===e||e>=14&&e<=31||e>=127&&e<=159||e>1114111)}function Co(e){if(e>65535){var t=55296+((e-=65536)>>10),n=56320+(1023&e);return String.fromCharCode(t,n)}return String.fromCharCode(e)}var xo=/&([a-z#][a-z0-9]{1,31});/gi,ko=/^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))/i;function So(e,t){var n=0,r=bo(t);return t!==r?r:35===t.charCodeAt(0)&&ko.test(t)&&Eo(n="x"===t[1].toLowerCase()?parseInt(t.slice(2),16):parseInt(t.slice(1),10))?Co(n):e}function Oo(e){return e.indexOf("&")<0?e:e.replace(xo,So)}var Bo=/[&<>"]/,_o=/[&<>"]/g,jo={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};function Po(e){return jo[e]}function Do(e){return Bo.test(e)?e.replace(_o,Po):e}var Fo={};function Io(e,t){return++t>=e.length-2?t:"paragraph_open"===e[t].type&&e[t].tight&&"inline"===e[t+1].type&&0===e[t+1].content.length&&"paragraph_close"===e[t+2].type&&e[t+2].tight?Io(e,t+2):t}Fo.blockquote_open=function(){return"<blockquote>\n"},Fo.blockquote_close=function(e,t){return"</blockquote>"+To(e,t)},Fo.code=function(e,t){return e[t].block?"<pre><code>"+Do(e[t].content)+"</code></pre>"+To(e,t):"<code>"+Do(e[t].content)+"</code>"},Fo.fence=function(e,t,n,r,o){var i,a,s,l,c=e[t],u="",d=n.langPrefix;if(c.params){if(a=(i=c.params.split(/\s+/g)).join(" "),s=o.rules.fence_custom,l=i[0],s&&go.call(s,l))return o.rules.fence_custom[i[0]](e,t,n,r,o);u=' class="'+d+Do(Oo(wo(a)))+'"'}return"<pre><code"+u+">"+(n.highlight&&n.highlight.apply(n.highlight,[c.content].concat(i))||Do(c.content))+"</code></pre>"+To(e,t)},Fo.fence_custom={},Fo.heading_open=function(e,t){return"<h"+e[t].hLevel+">"},Fo.heading_close=function(e,t){return"</h"+e[t].hLevel+">\n"},Fo.hr=function(e,t,n){return(n.xhtmlOut?"<hr />":"<hr>")+To(e,t)},Fo.bullet_list_open=function(){return"<ul>\n"},Fo.bullet_list_close=function(e,t){return"</ul>"+To(e,t)},Fo.list_item_open=function(){return"<li>"},Fo.list_item_close=function(){return"</li>\n"},Fo.ordered_list_open=function(e,t){var n=e[t];return"<ol"+(n.order>1?' start="'+n.order+'"':"")+">\n"},Fo.ordered_list_close=function(e,t){return"</ol>"+To(e,t)},Fo.paragraph_open=function(e,t){return e[t].tight?"":"<p>"},Fo.paragraph_close=function(e,t){var n=!(e[t].tight&&t&&"inline"===e[t-1].type&&!e[t-1].content);return(e[t].tight?"":"</p>")+(n?To(e,t):"")},Fo.link_open=function(e,t,n){var r=e[t].title?' title="'+Do(Oo(e[t].title))+'"':"",o=n.linkTarget?' target="'+n.linkTarget+'"':"";return'<a href="'+Do(e[t].href)+'"'+r+o+">"},Fo.link_close=function(){return"</a>"},Fo.image=function(e,t,n){var r=' src="'+Do(e[t].src)+'"',o=e[t].title?' title="'+Do(Oo(e[t].title))+'"':"";return"<img"+r+' alt="'+(e[t].alt?Do(Oo(wo(e[t].alt))):"")+'"'+o+(n.xhtmlOut?" /":"")+">"},Fo.table_open=function(){return"<table>\n"},Fo.table_close=function(){return"</table>\n"},Fo.thead_open=function(){return"<thead>\n"},Fo.thead_close=function(){return"</thead>\n"},Fo.tbody_open=function(){return"<tbody>\n"},Fo.tbody_close=function(){return"</tbody>\n"},Fo.tr_open=function(){return"<tr>"},Fo.tr_close=function(){return"</tr>\n"},Fo.th_open=function(e,t){var n=e[t];return"<th"+(n.align?' style="text-align:'+n.align+'"':"")+">"},Fo.th_close=function(){return"</th>"},Fo.td_open=function(e,t){var n=e[t];return"<td"+(n.align?' style="text-align:'+n.align+'"':"")+">"},Fo.td_close=function(){return"</td>"},Fo.strong_open=function(){return"<strong>"},Fo.strong_close=function(){return"</strong>"},Fo.em_open=function(){return"<em>"},Fo.em_close=function(){return"</em>"},Fo.del_open=function(){return"<del>"},Fo.del_close=function(){return"</del>"},Fo.ins_open=function(){return"<ins>"},Fo.ins_close=function(){return"</ins>"},Fo.mark_open=function(){return"<mark>"},Fo.mark_close=function(){return"</mark>"},Fo.sub=function(e,t){return"<sub>"+Do(e[t].content)+"</sub>"},Fo.sup=function(e,t){return"<sup>"+Do(e[t].content)+"</sup>"},Fo.hardbreak=function(e,t,n){return n.xhtmlOut?"<br />\n":"<br>\n"},Fo.softbreak=function(e,t,n){return n.breaks?n.xhtmlOut?"<br />\n":"<br>\n":"\n"},Fo.text=function(e,t){return Do(e[t].content)},Fo.htmlblock=function(e,t){return e[t].content},Fo.htmltag=function(e,t){return e[t].content},Fo.abbr_open=function(e,t){return'<abbr title="'+Do(Oo(e[t].title))+'">'},Fo.abbr_close=function(){return"</abbr>"},Fo.footnote_ref=function(e,t){var n=Number(e[t].id+1).toString(),r="fnref"+n;return e[t].subId>0&&(r+=":"+e[t].subId),'<sup class="footnote-ref"><a href="#fn'+n+'" id="'+r+'">['+n+"]</a></sup>"},Fo.footnote_block_open=function(e,t,n){return(n.xhtmlOut?'<hr class="footnotes-sep" />\n':'<hr class="footnotes-sep">\n')+'<section class="footnotes">\n<ol class="footnotes-list">\n'},Fo.footnote_block_close=function(){return"</ol>\n</section>\n"},Fo.footnote_open=function(e,t){return'<li id="fn'+Number(e[t].id+1).toString()+'"  class="footnote-item">'},Fo.footnote_close=function(){return"</li>\n"},Fo.footnote_anchor=function(e,t){var n="fnref"+Number(e[t].id+1).toString();return e[t].subId>0&&(n+=":"+e[t].subId),' <a href="#'+n+'" class="footnote-backref">↩</a>'},Fo.dl_open=function(){return"<dl>\n"},Fo.dt_open=function(){return"<dt>"},Fo.dd_open=function(){return"<dd>"},Fo.dl_close=function(){return"</dl>\n"},Fo.dt_close=function(){return"</dt>\n"},Fo.dd_close=function(){return"</dd>\n"};var To=Fo.getBreak=function(e,t){return(t=Io(e,t))<e.length&&"list_item_close"===e[t].type?"":"\n"};function Mo(){this.rules=mo({},Fo),this.getBreak=Fo.getBreak}function Ro(){this.__rules__=[],this.__cache__=null}function zo(e,t,n,r,o){this.src=e,this.env=r,this.options=n,this.parser=t,this.tokens=o,this.pos=0,this.posMax=this.src.length,this.level=0,this.pending="",this.pendingLevel=0,this.cache=[],this.isInLabel=!1,this.linkLevel=0,this.linkContent="",this.labelUnmatchedScopes=0}function No(e,t){var n,r,o,i=-1,a=e.posMax,s=e.pos,l=e.isInLabel;if(e.isInLabel)return-1;if(e.labelUnmatchedScopes)return e.labelUnmatchedScopes--,-1;for(e.pos=t+1,e.isInLabel=!0,n=1;e.pos<a;){if(91===(o=e.src.charCodeAt(e.pos)))n++;else if(93===o&&0===--n){r=!0;break}e.parser.skipToken(e)}return r?(i=e.pos,e.labelUnmatchedScopes=0):e.labelUnmatchedScopes=n-1,e.pos=s,e.isInLabel=l,i}function Lo(e,t,n,r){var o,i,a,s,l,c;if(42!==e.charCodeAt(0))return-1;if(91!==e.charCodeAt(1))return-1;if(-1===e.indexOf("]:"))return-1;if((i=No(o=new zo(e,t,n,r,[]),1))<0||58!==e.charCodeAt(i+1))return-1;for(s=o.posMax,a=i+2;a<s&&10!==o.src.charCodeAt(a);a++);return l=e.slice(2,i),0===(c=e.slice(i+2,a).trim()).length?-1:(r.abbreviations||(r.abbreviations={}),void 0===r.abbreviations[":"+l]&&(r.abbreviations[":"+l]=c),a)}function qo(e){var t=Oo(e);try{t=decodeURI(t)}catch(e){}return encodeURI(t)}function Wo(e,t){var n,r,o,i=t,a=e.posMax;if(60===e.src.charCodeAt(t)){for(t++;t<a;){if(10===(n=e.src.charCodeAt(t)))return!1;if(62===n)return o=qo(wo(e.src.slice(i+1,t))),!!e.parser.validateLink(o)&&(e.pos=t+1,e.linkContent=o,!0);92===n&&t+1<a?t+=2:t++}return!1}for(r=0;t<a&&32!==(n=e.src.charCodeAt(t))&&!(n<32||127===n);)if(92===n&&t+1<a)t+=2;else{if(40===n&&++r>1)break;if(41===n&&--r<0)break;t++}return i!==t&&(o=wo(e.src.slice(i,t)),!!e.parser.validateLink(o)&&(e.linkContent=o,e.pos=t,!0))}function Vo(e,t){var n,r=t,o=e.posMax,i=e.src.charCodeAt(t);if(34!==i&&39!==i&&40!==i)return!1;for(t++,40===i&&(i=41);t<o;){if((n=e.src.charCodeAt(t))===i)return e.pos=t+1,e.linkContent=wo(e.src.slice(r+1,t)),!0;92===n&&t+1<o?t+=2:t++}return!1}function Uo(e){return e.trim().replace(/\s+/g," ").toUpperCase()}function Yo(e,t,n,r){var o,i,a,s,l,c,u,d,p;if(91!==e.charCodeAt(0))return-1;if(-1===e.indexOf("]:"))return-1;if((i=No(o=new zo(e,t,n,r,[]),0))<0||58!==e.charCodeAt(i+1))return-1;for(s=o.posMax,a=i+2;a<s&&(32===(l=o.src.charCodeAt(a))||10===l);a++);if(!Wo(o,a))return-1;for(u=o.linkContent,c=a=o.pos,a+=1;a<s&&(32===(l=o.src.charCodeAt(a))||10===l);a++);for(a<s&&c!==a&&Vo(o,a)?(d=o.linkContent,a=o.pos):(d="",a=c);a<s&&32===o.src.charCodeAt(a);)a++;return a<s&&10!==o.src.charCodeAt(a)?-1:(p=Uo(e.slice(1,i)),void 0===r.references[p]&&(r.references[p]={title:d,href:u}),a)}Mo.prototype.renderInline=function(e,t,n){for(var r=this.rules,o=e.length,i=0,a="";o--;)a+=r[e[i].type](e,i++,t,n,this);return a},Mo.prototype.render=function(e,t,n){for(var r=this.rules,o=e.length,i=-1,a="";++i<o;)"inline"===e[i].type?a+=this.renderInline(e[i].children,t,n):a+=r[e[i].type](e,i,t,n,this);return a},Ro.prototype.__find__=function(e){for(var t=this.__rules__.length,n=-1;t--;)if(this.__rules__[++n].name===e)return n;return-1},Ro.prototype.__compile__=function(){var e=this,t=[""];e.__rules__.forEach(function(e){e.enabled&&e.alt.forEach(function(e){t.indexOf(e)<0&&t.push(e)})}),e.__cache__={},t.forEach(function(t){e.__cache__[t]=[],e.__rules__.forEach(function(n){n.enabled&&(t&&n.alt.indexOf(t)<0||e.__cache__[t].push(n.fn))})})},Ro.prototype.at=function(e,t,n){var r=this.__find__(e),o=n||{};if(-1===r)throw new Error("Parser rule not found: "+e);this.__rules__[r].fn=t,this.__rules__[r].alt=o.alt||[],this.__cache__=null},Ro.prototype.before=function(e,t,n,r){var o=this.__find__(e),i=r||{};if(-1===o)throw new Error("Parser rule not found: "+e);this.__rules__.splice(o,0,{name:t,enabled:!0,fn:n,alt:i.alt||[]}),this.__cache__=null},Ro.prototype.after=function(e,t,n,r){var o=this.__find__(e),i=r||{};if(-1===o)throw new Error("Parser rule not found: "+e);this.__rules__.splice(o+1,0,{name:t,enabled:!0,fn:n,alt:i.alt||[]}),this.__cache__=null},Ro.prototype.push=function(e,t,n){var r=n||{};this.__rules__.push({name:e,enabled:!0,fn:t,alt:r.alt||[]}),this.__cache__=null},Ro.prototype.enable=function(e,t){e=Array.isArray(e)?e:[e],t&&this.__rules__.forEach(function(e){e.enabled=!1}),e.forEach(function(e){var t=this.__find__(e);if(t<0)throw new Error("Rules manager: invalid rule name "+e);this.__rules__[t].enabled=!0},this),this.__cache__=null},Ro.prototype.disable=function(e){(e=Array.isArray(e)?e:[e]).forEach(function(e){var t=this.__find__(e);if(t<0)throw new Error("Rules manager: invalid rule name "+e);this.__rules__[t].enabled=!1},this),this.__cache__=null},Ro.prototype.getRules=function(e){return null===this.__cache__&&this.__compile__(),this.__cache__[e]||[]},zo.prototype.pushPending=function(){this.tokens.push({type:"text",content:this.pending,level:this.pendingLevel}),this.pending=""},zo.prototype.push=function(e){this.pending&&this.pushPending(),this.tokens.push(e),this.pendingLevel=this.level},zo.prototype.cacheSet=function(e,t){for(var n=this.cache.length;n<=e;n++)this.cache.push(0);this.cache[e]=t},zo.prototype.cacheGet=function(e){return e<this.cache.length?this.cache[e]:0};var Ho=" \n()[]'\".,!?-";function Ko(e){return e.replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1")}var $o=/\+-|\.\.|\?\?\?\?|!!!!|,,|--/,Go=/\((c|tm|r|p)\)/gi,Zo={c:"©",r:"®",p:"§",tm:"™"};function Qo(e){return e.indexOf("(")<0?e:e.replace(Go,function(e,t){return Zo[t.toLowerCase()]})}var Xo=/['"]/,Jo=/['"]/g,ei=/[-\s()\[\]]/;function ti(e,t){return!(t<0||t>=e.length||ei.test(e[t]))}function ni(e,t,n){return e.substr(0,t)+n+e.substr(t+1)}var ri=[["block",function(e){e.inlineMode?e.tokens.push({type:"inline",content:e.src.replace(/\n/g," ").trim(),level:0,lines:[0,1],children:[]}):e.block.parse(e.src,e.options,e.env,e.tokens)}],["abbr",function(e){var t,n,r,o,i=e.tokens;if(!e.inlineMode)for(t=1,n=i.length-1;t<n;t++)if("paragraph_open"===i[t-1].type&&"inline"===i[t].type&&"paragraph_close"===i[t+1].type){for(r=i[t].content;r.length&&!((o=Lo(r,e.inline,e.options,e.env))<0);)r=r.slice(o).trim();i[t].content=r,r.length||(i[t-1].tight=!0,i[t+1].tight=!0)}}],["references",function(e){var t,n,r,o,i=e.tokens;if(e.env.references=e.env.references||{},!e.inlineMode)for(t=1,n=i.length-1;t<n;t++)if("inline"===i[t].type&&"paragraph_open"===i[t-1].type&&"paragraph_close"===i[t+1].type){for(r=i[t].content;r.length&&!((o=Yo(r,e.inline,e.options,e.env))<0);)r=r.slice(o).trim();i[t].content=r,r.length||(i[t-1].tight=!0,i[t+1].tight=!0)}}],["inline",function(e){var t,n,r,o=e.tokens;for(n=0,r=o.length;n<r;n++)"inline"===(t=o[n]).type&&e.inline.parse(t.content,e.options,e.env,t.children)}],["footnote_tail",function(e){var t,n,r,o,i,a,s,l,c,u=0,d=!1,p={};if(e.env.footnotes&&(e.tokens=e.tokens.filter(function(e){return"footnote_reference_open"===e.type?(d=!0,l=[],c=e.label,!1):"footnote_reference_close"===e.type?(d=!1,p[":"+c]=l,!1):(d&&l.push(e),!d)}),e.env.footnotes.list)){for(a=e.env.footnotes.list,e.tokens.push({type:"footnote_block_open",level:u++}),t=0,n=a.length;t<n;t++){for(e.tokens.push({type:"footnote_open",id:t,level:u++}),a[t].tokens?((s=[]).push({type:"paragraph_open",tight:!1,level:u++}),s.push({type:"inline",content:"",level:u,children:a[t].tokens}),s.push({type:"paragraph_close",tight:!1,level:--u})):a[t].label&&(s=p[":"+a[t].label]),e.tokens=e.tokens.concat(s),i="paragraph_close"===e.tokens[e.tokens.length-1].type?e.tokens.pop():null,o=a[t].count>0?a[t].count:1,r=0;r<o;r++)e.tokens.push({type:"footnote_anchor",id:t,subId:r,level:u});i&&e.tokens.push(i),e.tokens.push({type:"footnote_close",level:--u})}e.tokens.push({type:"footnote_block_close",level:--u})}}],["abbr2",function(e){var t,n,r,o,i,a,s,l,c,u,d,p,h=e.tokens;if(e.env.abbreviations)for(e.env.abbrRegExp||(p="(^|["+Ho.split("").map(Ko).join("")+"])("+Object.keys(e.env.abbreviations).map(function(e){return e.substr(1)}).sort(function(e,t){return t.length-e.length}).map(Ko).join("|")+")($|["+Ho.split("").map(Ko).join("")+"])",e.env.abbrRegExp=new RegExp(p,"g")),u=e.env.abbrRegExp,n=0,r=h.length;n<r;n++)if("inline"===h[n].type)for(t=(o=h[n].children).length-1;t>=0;t--)if("text"===(i=o[t]).type){for(l=0,a=i.content,u.lastIndex=0,c=i.level,s=[];d=u.exec(a);)u.lastIndex>l&&s.push({type:"text",content:a.slice(l,d.index+d[1].length),level:c}),s.push({type:"abbr_open",title:e.env.abbreviations[":"+d[2]],level:c++}),s.push({type:"text",content:d[2],level:c}),s.push({type:"abbr_close",level:--c}),l=u.lastIndex-d[3].length;s.length&&(l<a.length&&s.push({type:"text",content:a.slice(l),level:c}),h[n].children=o=[].concat(o.slice(0,t),s,o.slice(t+1)))}}],["replacements",function(e){var t,n,r,o,i;if(e.options.typographer)for(i=e.tokens.length-1;i>=0;i--)if("inline"===e.tokens[i].type)for(t=(o=e.tokens[i].children).length-1;t>=0;t--)"text"===(n=o[t]).type&&(r=Qo(r=n.content),$o.test(r)&&(r=r.replace(/\+-/g,"±").replace(/\.{2,}/g,"…").replace(/([?!])…/g,"$1..").replace(/([?!]){4,}/g,"$1$1$1").replace(/,{2,}/g,",").replace(/(^|[^-])---([^-]|$)/gm,"$1—$2").replace(/(^|\s)--(\s|$)/gm,"$1–$2").replace(/(^|[^-\s])--([^-\s]|$)/gm,"$1–$2")),n.content=r)}],["smartquotes",function(e){var t,n,r,o,i,a,s,l,c,u,d,p,h,f,A,v,b;if(e.options.typographer)for(b=[],A=e.tokens.length-1;A>=0;A--)if("inline"===e.tokens[A].type)for(v=e.tokens[A].children,b.length=0,t=0;t<v.length;t++)if("text"===(n=v[t]).type&&!Xo.test(n.text)){for(s=v[t].level,h=b.length-1;h>=0&&!(b[h].level<=s);h--);b.length=h+1,i=0,a=(r=n.content).length;e:for(;i<a&&(Jo.lastIndex=i,o=Jo.exec(r));)if(l=!ti(r,o.index-1),i=o.index+1,f="'"===o[0],(c=!ti(r,i))||l){if(d=!c,p=!l)for(h=b.length-1;h>=0&&(u=b[h],!(b[h].level<s));h--)if(u.single===f&&b[h].level===s){u=b[h],f?(v[u.token].content=ni(v[u.token].content,u.pos,e.options.quotes[2]),n.content=ni(n.content,o.index,e.options.quotes[3])):(v[u.token].content=ni(v[u.token].content,u.pos,e.options.quotes[0]),n.content=ni(n.content,o.index,e.options.quotes[1])),b.length=h;continue e}d?b.push({token:t,pos:o.index,single:f,level:s}):p&&f&&(n.content=ni(n.content,o.index,"’"))}else f&&(n.content=ni(n.content,o.index,"’"))}}]];function oi(){this.options={},this.ruler=new Ro;for(var e=0;e<ri.length;e++)this.ruler.push(ri[e][0],ri[e][1])}function ii(e,t,n,r,o){var i,a,s,l,c,u,d;for(this.src=e,this.parser=t,this.options=n,this.env=r,this.tokens=o,this.bMarks=[],this.eMarks=[],this.tShift=[],this.blkIndent=0,this.line=0,this.lineMax=0,this.tight=!1,this.parentType="root",this.ddIndent=-1,this.level=0,this.result="",u=0,d=!1,s=l=u=0,c=(a=this.src).length;l<c;l++){if(i=a.charCodeAt(l),!d){if(32===i){u++;continue}d=!0}10!==i&&l!==c-1||(10!==i&&l++,this.bMarks.push(s),this.eMarks.push(l),this.tShift.push(u),d=!1,u=0,s=l+1)}this.bMarks.push(a.length),this.eMarks.push(a.length),this.tShift.push(0),this.lineMax=this.bMarks.length-1}function ai(e,t){var n,r,o;return(r=e.bMarks[t]+e.tShift[t])>=(o=e.eMarks[t])||42!==(n=e.src.charCodeAt(r++))&&45!==n&&43!==n||r<o&&32!==e.src.charCodeAt(r)?-1:r}function si(e,t){var n,r=e.bMarks[t]+e.tShift[t],o=e.eMarks[t];if(r+1>=o)return-1;if((n=e.src.charCodeAt(r++))<48||n>57)return-1;for(;;){if(r>=o)return-1;if(!((n=e.src.charCodeAt(r++))>=48&&n<=57)){if(41===n||46===n)break;return-1}}return r<o&&32!==e.src.charCodeAt(r)?-1:r}oi.prototype.process=function(e){var t,n,r;for(t=0,n=(r=this.ruler.getRules("")).length;t<n;t++)r[t](e)},ii.prototype.isEmpty=function(e){return this.bMarks[e]+this.tShift[e]>=this.eMarks[e]},ii.prototype.skipEmptyLines=function(e){for(var t=this.lineMax;e<t&&!(this.bMarks[e]+this.tShift[e]<this.eMarks[e]);e++);return e},ii.prototype.skipSpaces=function(e){for(var t=this.src.length;e<t&&32===this.src.charCodeAt(e);e++);return e},ii.prototype.skipChars=function(e,t){for(var n=this.src.length;e<n&&this.src.charCodeAt(e)===t;e++);return e},ii.prototype.skipCharsBack=function(e,t,n){if(e<=n)return e;for(;e>n;)if(t!==this.src.charCodeAt(--e))return e+1;return e},ii.prototype.getLines=function(e,t,n,r){var o,i,a,s,l,c=e;if(e>=t)return"";if(c+1===t)return i=this.bMarks[c]+Math.min(this.tShift[c],n),a=r?this.eMarks[c]+1:this.eMarks[c],this.src.slice(i,a);for(s=new Array(t-e),o=0;c<t;c++,o++)(l=this.tShift[c])>n&&(l=n),l<0&&(l=0),i=this.bMarks[c]+l,a=c+1<t||r?this.eMarks[c]+1:this.eMarks[c],s[o]=this.src.slice(i,a);return s.join("")};var li={};["article","aside","button","blockquote","body","canvas","caption","col","colgroup","dd","div","dl","dt","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","iframe","li","map","object","ol","output","p","pre","progress","script","section","style","table","tbody","td","textarea","tfoot","th","tr","thead","ul","video"].forEach(function(e){li[e]=!0});var ci=/^<([a-zA-Z]{1,15})[\s\/>]/,ui=/^<\/([a-zA-Z]{1,15})[\s>]/;function di(e,t){var n=e.bMarks[t]+e.blkIndent,r=e.eMarks[t];return e.src.substr(n,r-n)}function pi(e,t){var n,r,o=e.bMarks[t]+e.tShift[t],i=e.eMarks[t];return o>=i||126!==(r=e.src.charCodeAt(o++))&&58!==r||o===(n=e.skipSpaces(o))||n>=i?-1:n}var hi=[["code",function(e,t,n){var r,o;if(e.tShift[t]-e.blkIndent<4)return!1;for(o=r=t+1;r<n;)if(e.isEmpty(r))r++;else{if(!(e.tShift[r]-e.blkIndent>=4))break;o=++r}return e.line=r,e.tokens.push({type:"code",content:e.getLines(t,o,4+e.blkIndent,!0),block:!0,lines:[t,e.line],level:e.level}),!0}],["fences",function(e,t,n,r){var o,i,a,s,l,c=!1,u=e.bMarks[t]+e.tShift[t],d=e.eMarks[t];if(u+3>d)return!1;if(126!==(o=e.src.charCodeAt(u))&&96!==o)return!1;if(l=u,(i=(u=e.skipChars(u,o))-l)<3)return!1;if((a=e.src.slice(u,d).trim()).indexOf("`")>=0)return!1;if(r)return!0;for(s=t;!(++s>=n||(u=l=e.bMarks[s]+e.tShift[s])<(d=e.eMarks[s])&&e.tShift[s]<e.blkIndent);)if(e.src.charCodeAt(u)===o&&!(e.tShift[s]-e.blkIndent>=4||(u=e.skipChars(u,o))-l<i||(u=e.skipSpaces(u))<d)){c=!0;break}return i=e.tShift[t],e.line=s+(c?1:0),e.tokens.push({type:"fence",params:a,content:e.getLines(t+1,s,i,!0),lines:[t,e.line],level:e.level}),!0},["paragraph","blockquote","list"]],["blockquote",function(e,t,n,r){var o,i,a,s,l,c,u,d,p,h,f,A=e.bMarks[t]+e.tShift[t],v=e.eMarks[t];if(A>v)return!1;if(62!==e.src.charCodeAt(A++))return!1;if(e.level>=e.options.maxNesting)return!1;if(r)return!0;for(32===e.src.charCodeAt(A)&&A++,l=e.blkIndent,e.blkIndent=0,s=[e.bMarks[t]],e.bMarks[t]=A,i=(A=A<v?e.skipSpaces(A):A)>=v,a=[e.tShift[t]],e.tShift[t]=A-e.bMarks[t],d=e.parser.ruler.getRules("blockquote"),o=t+1;o<n&&!((A=e.bMarks[o]+e.tShift[o])>=(v=e.eMarks[o]));o++)if(62!==e.src.charCodeAt(A++)){if(i)break;for(f=!1,p=0,h=d.length;p<h;p++)if(d[p](e,o,n,!0)){f=!0;break}if(f)break;s.push(e.bMarks[o]),a.push(e.tShift[o]),e.tShift[o]=-1337}else 32===e.src.charCodeAt(A)&&A++,s.push(e.bMarks[o]),e.bMarks[o]=A,i=(A=A<v?e.skipSpaces(A):A)>=v,a.push(e.tShift[o]),e.tShift[o]=A-e.bMarks[o];for(c=e.parentType,e.parentType="blockquote",e.tokens.push({type:"blockquote_open",lines:u=[t,0],level:e.level++}),e.parser.tokenize(e,t,o),e.tokens.push({type:"blockquote_close",level:--e.level}),e.parentType=c,u[1]=e.line,p=0;p<a.length;p++)e.bMarks[p+t]=s[p],e.tShift[p+t]=a[p];return e.blkIndent=l,!0},["paragraph","blockquote","list"]],["hr",function(e,t,n,r){var o,i,a,s=e.bMarks[t],l=e.eMarks[t];if((s+=e.tShift[t])>l)return!1;if(42!==(o=e.src.charCodeAt(s++))&&45!==o&&95!==o)return!1;for(i=1;s<l;){if((a=e.src.charCodeAt(s++))!==o&&32!==a)return!1;a===o&&i++}return!(i<3||(r||(e.line=t+1,e.tokens.push({type:"hr",lines:[t,e.line],level:e.level})),0))},["paragraph","blockquote","list"]],["list",function(e,t,n,r){var o,i,a,s,l,c,u,d,p,h,f,A,v,b,g,m,y,w,E,C,x,k=!0;if((d=si(e,t))>=0)A=!0;else{if(!((d=ai(e,t))>=0))return!1;A=!1}if(e.level>=e.options.maxNesting)return!1;if(f=e.src.charCodeAt(d-1),r)return!0;for(b=e.tokens.length,A?(u=e.bMarks[t]+e.tShift[t],h=Number(e.src.substr(u,d-u-1)),e.tokens.push({type:"ordered_list_open",order:h,lines:m=[t,0],level:e.level++})):e.tokens.push({type:"bullet_list_open",lines:m=[t,0],level:e.level++}),o=t,g=!1,w=e.parser.ruler.getRules("list");!(!(o<n)||((p=(v=e.skipSpaces(d))>=e.eMarks[o]?1:v-d)>4&&(p=1),p<1&&(p=1),i=d-e.bMarks[o]+p,e.tokens.push({type:"list_item_open",lines:y=[t,0],level:e.level++}),s=e.blkIndent,l=e.tight,a=e.tShift[t],c=e.parentType,e.tShift[t]=v-e.bMarks[t],e.blkIndent=i,e.tight=!0,e.parentType="list",e.parser.tokenize(e,t,n,!0),e.tight&&!g||(k=!1),g=e.line-t>1&&e.isEmpty(e.line-1),e.blkIndent=s,e.tShift[t]=a,e.tight=l,e.parentType=c,e.tokens.push({type:"list_item_close",level:--e.level}),o=t=e.line,y[1]=o,v=e.bMarks[t],o>=n)||e.isEmpty(o)||e.tShift[o]<e.blkIndent);){for(x=!1,E=0,C=w.length;E<C;E++)if(w[E](e,o,n,!0)){x=!0;break}if(x)break;if(A){if((d=si(e,o))<0)break}else if((d=ai(e,o))<0)break;if(f!==e.src.charCodeAt(d-1))break}return e.tokens.push({type:A?"ordered_list_close":"bullet_list_close",level:--e.level}),m[1]=o,e.line=o,k&&function(e,t){var n,r,o=e.level+2;for(n=t+2,r=e.tokens.length-2;n<r;n++)e.tokens[n].level===o&&"paragraph_open"===e.tokens[n].type&&(e.tokens[n+2].tight=!0,e.tokens[n].tight=!0,n+=2)}(e,b),!0},["paragraph","blockquote"]],["footnote",function(e,t,n,r){var o,i,a,s,l,c=e.bMarks[t]+e.tShift[t],u=e.eMarks[t];if(c+4>u)return!1;if(91!==e.src.charCodeAt(c))return!1;if(94!==e.src.charCodeAt(c+1))return!1;if(e.level>=e.options.maxNesting)return!1;for(s=c+2;s<u;s++){if(32===e.src.charCodeAt(s))return!1;if(93===e.src.charCodeAt(s))break}return!(s===c+2||s+1>=u||58!==e.src.charCodeAt(++s)||(r||(s++,e.env.footnotes||(e.env.footnotes={}),e.env.footnotes.refs||(e.env.footnotes.refs={}),l=e.src.slice(c+2,s-2),e.env.footnotes.refs[":"+l]=-1,e.tokens.push({type:"footnote_reference_open",label:l,level:e.level++}),o=e.bMarks[t],i=e.tShift[t],a=e.parentType,e.tShift[t]=e.skipSpaces(s)-s,e.bMarks[t]=s,e.blkIndent+=4,e.parentType="footnote",e.tShift[t]<e.blkIndent&&(e.tShift[t]+=e.blkIndent,e.bMarks[t]-=e.blkIndent),e.parser.tokenize(e,t,n,!0),e.parentType=a,e.blkIndent-=4,e.tShift[t]=i,e.bMarks[t]=o,e.tokens.push({type:"footnote_reference_close",level:--e.level})),0))},["paragraph"]],["heading",function(e,t,n,r){var o,i,a,s=e.bMarks[t]+e.tShift[t],l=e.eMarks[t];if(s>=l)return!1;if(35!==(o=e.src.charCodeAt(s))||s>=l)return!1;for(i=1,o=e.src.charCodeAt(++s);35===o&&s<l&&i<=6;)i++,o=e.src.charCodeAt(++s);return!(i>6||s<l&&32!==o||(r||(l=e.skipCharsBack(l,32,s),(a=e.skipCharsBack(l,35,s))>s&&32===e.src.charCodeAt(a-1)&&(l=a),e.line=t+1,e.tokens.push({type:"heading_open",hLevel:i,lines:[t,e.line],level:e.level}),s<l&&e.tokens.push({type:"inline",content:e.src.slice(s,l).trim(),level:e.level+1,lines:[t,e.line],children:[]}),e.tokens.push({type:"heading_close",hLevel:i,level:e.level})),0))},["paragraph","blockquote"]],["lheading",function(e,t,n){var r,o,i,a=t+1;return!(a>=n||e.tShift[a]<e.blkIndent||e.tShift[a]-e.blkIndent>3||(o=e.bMarks[a]+e.tShift[a])>=(i=e.eMarks[a])||45!==(r=e.src.charCodeAt(o))&&61!==r||(o=e.skipChars(o,r),(o=e.skipSpaces(o))<i||(o=e.bMarks[t]+e.tShift[t],e.line=a+1,e.tokens.push({type:"heading_open",hLevel:61===r?1:2,lines:[t,e.line],level:e.level}),e.tokens.push({type:"inline",content:e.src.slice(o,e.eMarks[t]).trim(),level:e.level+1,lines:[t,e.line-1],children:[]}),e.tokens.push({type:"heading_close",hLevel:61===r?1:2,level:e.level}),0)))}],["htmlblock",function(e,t,n,r){var o,i,a,s=e.bMarks[t],l=e.eMarks[t],c=e.tShift[t];if(s+=c,!e.options.html)return!1;if(c>3||s+2>=l)return!1;if(60!==e.src.charCodeAt(s))return!1;if(33===(o=e.src.charCodeAt(s+1))||63===o){if(r)return!0}else{if(47!==o&&!function(e){var t=32|e;return t>=97&&t<=122}(o))return!1;if(47===o){if(!(i=e.src.slice(s,l).match(ui)))return!1}else if(!(i=e.src.slice(s,l).match(ci)))return!1;if(!0!==li[i[1].toLowerCase()])return!1;if(r)return!0}for(a=t+1;a<e.lineMax&&!e.isEmpty(a);)a++;return e.line=a,e.tokens.push({type:"htmlblock",level:e.level,lines:[t,e.line],content:e.getLines(t,a,0,!0)}),!0},["paragraph","blockquote"]],["table",function(e,t,n,r){var o,i,a,s,l,c,u,d,p,h,f;if(t+2>n)return!1;if(l=t+1,e.tShift[l]<e.blkIndent)return!1;if((a=e.bMarks[l]+e.tShift[l])>=e.eMarks[l])return!1;if(124!==(o=e.src.charCodeAt(a))&&45!==o&&58!==o)return!1;if(i=di(e,t+1),!/^[-:| ]+$/.test(i))return!1;if((c=i.split("|"))<=2)return!1;for(d=[],s=0;s<c.length;s++){if(!(p=c[s].trim())){if(0===s||s===c.length-1)continue;return!1}if(!/^:?-+:?$/.test(p))return!1;58===p.charCodeAt(p.length-1)?d.push(58===p.charCodeAt(0)?"center":"right"):58===p.charCodeAt(0)?d.push("left"):d.push("")}if(-1===(i=di(e,t).trim()).indexOf("|"))return!1;if(c=i.replace(/^\||\|$/g,"").split("|"),d.length!==c.length)return!1;if(r)return!0;for(e.tokens.push({type:"table_open",lines:h=[t,0],level:e.level++}),e.tokens.push({type:"thead_open",lines:[t,t+1],level:e.level++}),e.tokens.push({type:"tr_open",lines:[t,t+1],level:e.level++}),s=0;s<c.length;s++)e.tokens.push({type:"th_open",align:d[s],lines:[t,t+1],level:e.level++}),e.tokens.push({type:"inline",content:c[s].trim(),lines:[t,t+1],level:e.level,children:[]}),e.tokens.push({type:"th_close",level:--e.level});for(e.tokens.push({type:"tr_close",level:--e.level}),e.tokens.push({type:"thead_close",level:--e.level}),e.tokens.push({type:"tbody_open",lines:f=[t+2,0],level:e.level++}),l=t+2;l<n&&!(e.tShift[l]<e.blkIndent)&&-1!==(i=di(e,l).trim()).indexOf("|");l++){for(c=i.replace(/^\||\|$/g,"").split("|"),e.tokens.push({type:"tr_open",level:e.level++}),s=0;s<c.length;s++)e.tokens.push({type:"td_open",align:d[s],level:e.level++}),u=c[s].substring(124===c[s].charCodeAt(0)?1:0,124===c[s].charCodeAt(c[s].length-1)?c[s].length-1:c[s].length).trim(),e.tokens.push({type:"inline",content:u,level:e.level,children:[]}),e.tokens.push({type:"td_close",level:--e.level});e.tokens.push({type:"tr_close",level:--e.level})}return e.tokens.push({type:"tbody_close",level:--e.level}),e.tokens.push({type:"table_close",level:--e.level}),h[1]=f[1]=l,e.line=l,!0},["paragraph"]],["deflist",function(e,t,n,r){var o,i,a,s,l,c,u,d,p,h,f,A,v,b;if(r)return!(e.ddIndent<0)&&pi(e,t)>=0;if(u=t+1,e.isEmpty(u)&&++u>n)return!1;if(e.tShift[u]<e.blkIndent)return!1;if((o=pi(e,u))<0)return!1;if(e.level>=e.options.maxNesting)return!1;c=e.tokens.length,e.tokens.push({type:"dl_open",lines:l=[t,0],level:e.level++}),a=t,i=u;e:for(;;){for(b=!0,v=!1,e.tokens.push({type:"dt_open",lines:[a,a],level:e.level++}),e.tokens.push({type:"inline",content:e.getLines(a,a+1,e.blkIndent,!1).trim(),level:e.level+1,lines:[a,a],children:[]}),e.tokens.push({type:"dt_close",level:--e.level});;){if(e.tokens.push({type:"dd_open",lines:s=[u,0],level:e.level++}),A=e.tight,p=e.ddIndent,d=e.blkIndent,f=e.tShift[i],h=e.parentType,e.blkIndent=e.ddIndent=e.tShift[i]+2,e.tShift[i]=o-e.bMarks[i],e.tight=!0,e.parentType="deflist",e.parser.tokenize(e,i,n,!0),e.tight&&!v||(b=!1),v=e.line-i>1&&e.isEmpty(e.line-1),e.tShift[i]=f,e.tight=A,e.parentType=h,e.blkIndent=d,e.ddIndent=p,e.tokens.push({type:"dd_close",level:--e.level}),s[1]=u=e.line,u>=n)break e;if(e.tShift[u]<e.blkIndent)break e;if((o=pi(e,u))<0)break;i=u}if(u>=n)break;if(a=u,e.isEmpty(a))break;if(e.tShift[a]<e.blkIndent)break;if((i=a+1)>=n)break;if(e.isEmpty(i)&&i++,i>=n)break;if(e.tShift[i]<e.blkIndent)break;if((o=pi(e,i))<0)break}return e.tokens.push({type:"dl_close",level:--e.level}),l[1]=u,e.line=u,b&&function(e,t){var n,r,o=e.level+2;for(n=t+2,r=e.tokens.length-2;n<r;n++)e.tokens[n].level===o&&"paragraph_open"===e.tokens[n].type&&(e.tokens[n+2].tight=!0,e.tokens[n].tight=!0,n+=2)}(e,c),!0},["paragraph"]],["paragraph",function(e,t){var n,r,o,i,a,s,l=t+1;if(l<(n=e.lineMax)&&!e.isEmpty(l))for(s=e.parser.ruler.getRules("paragraph");l<n&&!e.isEmpty(l);l++)if(!(e.tShift[l]-e.blkIndent>3)){for(o=!1,i=0,a=s.length;i<a;i++)if(s[i](e,l,n,!0)){o=!0;break}if(o)break}return r=e.getLines(t,l,e.blkIndent,!1).trim(),e.line=l,r.length&&(e.tokens.push({type:"paragraph_open",tight:!1,lines:[t,e.line],level:e.level}),e.tokens.push({type:"inline",content:r,level:e.level+1,lines:[t,e.line],children:[]}),e.tokens.push({type:"paragraph_close",tight:!1,level:e.level})),!0}]];function fi(){this.ruler=new Ro;for(var e=0;e<hi.length;e++)this.ruler.push(hi[e][0],hi[e][1],{alt:(hi[e][2]||[]).slice()})}fi.prototype.tokenize=function(e,t,n){for(var r,o=this.ruler.getRules(""),i=o.length,a=t,s=!1;a<n&&(e.line=a=e.skipEmptyLines(a),!(a>=n))&&!(e.tShift[a]<e.blkIndent);){for(r=0;r<i&&!o[r](e,a,n,!1);r++);if(e.tight=!s,e.isEmpty(e.line-1)&&(s=!0),(a=e.line)<n&&e.isEmpty(a)){if(s=!0,++a<n&&"list"===e.parentType&&e.isEmpty(a))break;e.line=a}}};var Ai=/[\n\t]/g,vi=/\r[\n\u0085]|[\u2424\u2028\u0085]/g,bi=/\u00a0/g;function gi(e){switch(e){case 10:case 92:case 96:case 42:case 95:case 94:case 91:case 93:case 33:case 38:case 60:case 62:case 123:case 125:case 36:case 37:case 64:case 126:case 43:case 61:case 58:return!0;default:return!1}}fi.prototype.parse=function(e,t,n,r){var o,i=0,a=0;if(!e)return[];(e=(e=e.replace(bi," ")).replace(vi,"\n")).indexOf("\t")>=0&&(e=e.replace(Ai,function(t,n){var r;return 10===e.charCodeAt(n)?(i=n+1,a=0,t):(r="    ".slice((n-i-a)%4),a=n-i+1,r)})),o=new ii(e,this,t,n,r),this.tokenize(o,o.line,o.lineMax)};for(var mi=[],yi=0;yi<256;yi++)mi.push(0);function wi(e){return e>=48&&e<=57||e>=65&&e<=90||e>=97&&e<=122}function Ei(e,t){var n,r,o,i=t,a=!0,s=!0,l=e.posMax,c=e.src.charCodeAt(t);for(n=t>0?e.src.charCodeAt(t-1):-1;i<l&&e.src.charCodeAt(i)===c;)i++;return i>=l&&(a=!1),(o=i-t)>=4?a=s=!1:(32!==(r=i<l?e.src.charCodeAt(i):-1)&&10!==r||(a=!1),32!==n&&10!==n||(s=!1),95===c&&(wi(n)&&(a=!1),wi(r)&&(s=!1))),{can_open:a,can_close:s,delims:o}}"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach(function(e){mi[e.charCodeAt(0)]=1});var Ci=/\\([ \\!"#$%&'()*+,.\/:;<=>?@[\]^_`{|}~-])/g,xi=/\\([ \\!"#$%&'()*+,.\/:;<=>?@[\]^_`{|}~-])/g,ki=["coap","doi","javascript","aaa","aaas","about","acap","cap","cid","crid","data","dav","dict","dns","file","ftp","geo","go","gopher","h323","http","https","iax","icap","im","imap","info","ipp","iris","iris.beep","iris.xpc","iris.xpcs","iris.lwz","ldap","mailto","mid","msrp","msrps","mtqp","mupdate","news","nfs","ni","nih","nntp","opaquelocktoken","pop","pres","rtsp","service","session","shttp","sieve","sip","sips","sms","snmp","soap.beep","soap.beeps","tag","tel","telnet","tftp","thismessage","tn3270","tip","tv","urn","vemmi","ws","wss","xcon","xcon-userid","xmlrpc.beep","xmlrpc.beeps","xmpp","z39.50r","z39.50s","adiumxtra","afp","afs","aim","apt","attachment","aw","beshare","bitcoin","bolo","callto","chrome","chrome-extension","com-eventbrite-attendee","content","cvs","dlna-playsingle","dlna-playcontainer","dtn","dvb","ed2k","facetime","feed","finger","fish","gg","git","gizmoproject","gtalk","hcp","icon","ipn","irc","irc6","ircs","itms","jar","jms","keyparc","lastfm","ldaps","magnet","maps","market","message","mms","ms-help","msnim","mumble","mvn","notes","oid","palm","paparazzi","platform","proxy","psyc","query","res","resource","rmi","rsync","rtmp","secondlife","sftp","sgn","skype","smb","soldat","spotify","ssh","steam","svn","teamspeak","things","udp","unreal","ut2004","ventrilo","view-source","webcal","wtai","wyciwyg","xfire","xri","ymsgr"],Si=/^<([a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)>/,Oi=/^<([a-zA-Z.\-]{1,25}):([^<>\x00-\x20]*)>/;function Bi(e,t){return e=e.source,t=t||"",function n(r,o){return r?(o=o.source||o,e=e.replace(r,o),n):new RegExp(e,t)}}var _i=Bi(/(?:unquoted|single_quoted|double_quoted)/)("unquoted",/[^"'=<>`\x00-\x20]+/)("single_quoted",/'[^']*'/)("double_quoted",/"[^"]*"/)(),ji=Bi(/(?:\s+attr_name(?:\s*=\s*attr_value)?)/)("attr_name",/[a-zA-Z_:][a-zA-Z0-9:._-]*/)("attr_value",_i)(),Pi=Bi(/<[A-Za-z][A-Za-z0-9]*attribute*\s*\/?>/)("attribute",ji)(),Di=Bi(/^(?:open_tag|close_tag|comment|processing|declaration|cdata)/)("open_tag",Pi)("close_tag",/<\/[A-Za-z][A-Za-z0-9]*\s*>/)("comment",/<!---->|<!--(?:-?[^>-])(?:-?[^-])*-->/)("processing",/<[?].*?[?]>/)("declaration",/<![A-Z]+\s+[^>]*>/)("cdata",/<!\[CDATA\[[\s\S]*?\]\]>/)(),Fi=/^&#((?:x[a-f0-9]{1,8}|[0-9]{1,8}));/i,Ii=/^&([a-z][a-z0-9]{1,31});/i,Ti=[["text",function(e,t){for(var n=e.pos;n<e.posMax&&!gi(e.src.charCodeAt(n));)n++;return n!==e.pos&&(t||(e.pending+=e.src.slice(e.pos,n)),e.pos=n,!0)}],["newline",function(e,t){var n,r,o=e.pos;if(10!==e.src.charCodeAt(o))return!1;if(n=e.pending.length-1,r=e.posMax,!t)if(n>=0&&32===e.pending.charCodeAt(n))if(n>=1&&32===e.pending.charCodeAt(n-1)){for(var i=n-2;i>=0;i--)if(32!==e.pending.charCodeAt(i)){e.pending=e.pending.substring(0,i+1);break}e.push({type:"hardbreak",level:e.level})}else e.pending=e.pending.slice(0,-1),e.push({type:"softbreak",level:e.level});else e.push({type:"softbreak",level:e.level});for(o++;o<r&&32===e.src.charCodeAt(o);)o++;return e.pos=o,!0}],["escape",function(e,t){var n,r=e.pos,o=e.posMax;if(92!==e.src.charCodeAt(r))return!1;if(++r<o){if((n=e.src.charCodeAt(r))<256&&0!==mi[n])return t||(e.pending+=e.src[r]),e.pos+=2,!0;if(10===n){for(t||e.push({type:"hardbreak",level:e.level}),r++;r<o&&32===e.src.charCodeAt(r);)r++;return e.pos=r,!0}}return t||(e.pending+="\\"),e.pos++,!0}],["backticks",function(e,t){var n,r,o,i,a,s=e.pos;if(96!==e.src.charCodeAt(s))return!1;for(n=s,s++,r=e.posMax;s<r&&96===e.src.charCodeAt(s);)s++;for(o=e.src.slice(n,s),i=a=s;-1!==(i=e.src.indexOf("`",a));){for(a=i+1;a<r&&96===e.src.charCodeAt(a);)a++;if(a-i===o.length)return t||e.push({type:"code",content:e.src.slice(s,i).replace(/[ \n]+/g," ").trim(),block:!1,level:e.level}),e.pos=a,!0}return t||(e.pending+=o),e.pos+=o.length,!0}],["del",function(e,t){var n,r,o,i,a,s=e.posMax,l=e.pos;if(126!==e.src.charCodeAt(l))return!1;if(t)return!1;if(l+4>=s)return!1;if(126!==e.src.charCodeAt(l+1))return!1;if(e.level>=e.options.maxNesting)return!1;if(i=l>0?e.src.charCodeAt(l-1):-1,a=e.src.charCodeAt(l+2),126===i)return!1;if(126===a)return!1;if(32===a||10===a)return!1;for(r=l+2;r<s&&126===e.src.charCodeAt(r);)r++;if(r>l+3)return e.pos+=r-l,t||(e.pending+=e.src.slice(l,r)),!0;for(e.pos=l+2,o=1;e.pos+1<s;){if(126===e.src.charCodeAt(e.pos)&&126===e.src.charCodeAt(e.pos+1)&&(i=e.src.charCodeAt(e.pos-1),126!==(a=e.pos+2<s?e.src.charCodeAt(e.pos+2):-1)&&126!==i&&(32!==i&&10!==i?o--:32!==a&&10!==a&&o++,o<=0))){n=!0;break}e.parser.skipToken(e)}return n?(e.posMax=e.pos,e.pos=l+2,t||(e.push({type:"del_open",level:e.level++}),e.parser.tokenize(e),e.push({type:"del_close",level:--e.level})),e.pos=e.posMax+2,e.posMax=s,!0):(e.pos=l,!1)}],["ins",function(e,t){var n,r,o,i,a,s=e.posMax,l=e.pos;if(43!==e.src.charCodeAt(l))return!1;if(t)return!1;if(l+4>=s)return!1;if(43!==e.src.charCodeAt(l+1))return!1;if(e.level>=e.options.maxNesting)return!1;if(i=l>0?e.src.charCodeAt(l-1):-1,a=e.src.charCodeAt(l+2),43===i)return!1;if(43===a)return!1;if(32===a||10===a)return!1;for(r=l+2;r<s&&43===e.src.charCodeAt(r);)r++;if(r!==l+2)return e.pos+=r-l,t||(e.pending+=e.src.slice(l,r)),!0;for(e.pos=l+2,o=1;e.pos+1<s;){if(43===e.src.charCodeAt(e.pos)&&43===e.src.charCodeAt(e.pos+1)&&(i=e.src.charCodeAt(e.pos-1),43!==(a=e.pos+2<s?e.src.charCodeAt(e.pos+2):-1)&&43!==i&&(32!==i&&10!==i?o--:32!==a&&10!==a&&o++,o<=0))){n=!0;break}e.parser.skipToken(e)}return n?(e.posMax=e.pos,e.pos=l+2,t||(e.push({type:"ins_open",level:e.level++}),e.parser.tokenize(e),e.push({type:"ins_close",level:--e.level})),e.pos=e.posMax+2,e.posMax=s,!0):(e.pos=l,!1)}],["mark",function(e,t){var n,r,o,i,a,s=e.posMax,l=e.pos;if(61!==e.src.charCodeAt(l))return!1;if(t)return!1;if(l+4>=s)return!1;if(61!==e.src.charCodeAt(l+1))return!1;if(e.level>=e.options.maxNesting)return!1;if(i=l>0?e.src.charCodeAt(l-1):-1,a=e.src.charCodeAt(l+2),61===i)return!1;if(61===a)return!1;if(32===a||10===a)return!1;for(r=l+2;r<s&&61===e.src.charCodeAt(r);)r++;if(r!==l+2)return e.pos+=r-l,t||(e.pending+=e.src.slice(l,r)),!0;for(e.pos=l+2,o=1;e.pos+1<s;){if(61===e.src.charCodeAt(e.pos)&&61===e.src.charCodeAt(e.pos+1)&&(i=e.src.charCodeAt(e.pos-1),61!==(a=e.pos+2<s?e.src.charCodeAt(e.pos+2):-1)&&61!==i&&(32!==i&&10!==i?o--:32!==a&&10!==a&&o++,o<=0))){n=!0;break}e.parser.skipToken(e)}return n?(e.posMax=e.pos,e.pos=l+2,t||(e.push({type:"mark_open",level:e.level++}),e.parser.tokenize(e),e.push({type:"mark_close",level:--e.level})),e.pos=e.posMax+2,e.posMax=s,!0):(e.pos=l,!1)}],["emphasis",function(e,t){var n,r,o,i,a,s,l,c=e.posMax,u=e.pos,d=e.src.charCodeAt(u);if(95!==d&&42!==d)return!1;if(t)return!1;if(n=(l=Ei(e,u)).delims,!l.can_open)return e.pos+=n,t||(e.pending+=e.src.slice(u,e.pos)),!0;if(e.level>=e.options.maxNesting)return!1;for(e.pos=u+n,s=[n];e.pos<c;)if(e.src.charCodeAt(e.pos)!==d)e.parser.skipToken(e);else{if(r=(l=Ei(e,e.pos)).delims,l.can_close){for(i=s.pop(),a=r;i!==a;){if(a<i){s.push(i-a);break}if(a-=i,0===s.length)break;e.pos+=i,i=s.pop()}if(0===s.length){n=i,o=!0;break}e.pos+=r;continue}l.can_open&&s.push(r),e.pos+=r}return o?(e.posMax=e.pos,e.pos=u+n,t||(2!==n&&3!==n||e.push({type:"strong_open",level:e.level++}),1!==n&&3!==n||e.push({type:"em_open",level:e.level++}),e.parser.tokenize(e),1!==n&&3!==n||e.push({type:"em_close",level:--e.level}),2!==n&&3!==n||e.push({type:"strong_close",level:--e.level})),e.pos=e.posMax+n,e.posMax=c,!0):(e.pos=u,!1)}],["sub",function(e,t){var n,r,o=e.posMax,i=e.pos;if(126!==e.src.charCodeAt(i))return!1;if(t)return!1;if(i+2>=o)return!1;if(e.level>=e.options.maxNesting)return!1;for(e.pos=i+1;e.pos<o;){if(126===e.src.charCodeAt(e.pos)){n=!0;break}e.parser.skipToken(e)}return n&&i+1!==e.pos?(r=e.src.slice(i+1,e.pos)).match(/(^|[^\\])(\\\\)*\s/)?(e.pos=i,!1):(e.posMax=e.pos,e.pos=i+1,t||e.push({type:"sub",level:e.level,content:r.replace(Ci,"$1")}),e.pos=e.posMax+1,e.posMax=o,!0):(e.pos=i,!1)}],["sup",function(e,t){var n,r,o=e.posMax,i=e.pos;if(94!==e.src.charCodeAt(i))return!1;if(t)return!1;if(i+2>=o)return!1;if(e.level>=e.options.maxNesting)return!1;for(e.pos=i+1;e.pos<o;){if(94===e.src.charCodeAt(e.pos)){n=!0;break}e.parser.skipToken(e)}return n&&i+1!==e.pos?(r=e.src.slice(i+1,e.pos)).match(/(^|[^\\])(\\\\)*\s/)?(e.pos=i,!1):(e.posMax=e.pos,e.pos=i+1,t||e.push({type:"sup",level:e.level,content:r.replace(xi,"$1")}),e.pos=e.posMax+1,e.posMax=o,!0):(e.pos=i,!1)}],["links",function(e,t){var n,r,o,i,a,s,l,c,u=!1,d=e.pos,p=e.posMax,h=e.pos,f=e.src.charCodeAt(h);if(33===f&&(u=!0,f=e.src.charCodeAt(++h)),91!==f)return!1;if(e.level>=e.options.maxNesting)return!1;if(n=h+1,(r=No(e,h))<0)return!1;if((s=r+1)<p&&40===e.src.charCodeAt(s)){for(s++;s<p&&(32===(c=e.src.charCodeAt(s))||10===c);s++);if(s>=p)return!1;for(h=s,Wo(e,s)?(i=e.linkContent,s=e.pos):i="",h=s;s<p&&(32===(c=e.src.charCodeAt(s))||10===c);s++);if(s<p&&h!==s&&Vo(e,s))for(a=e.linkContent,s=e.pos;s<p&&(32===(c=e.src.charCodeAt(s))||10===c);s++);else a="";if(s>=p||41!==e.src.charCodeAt(s))return e.pos=d,!1;s++}else{if(e.linkLevel>0)return!1;for(;s<p&&(32===(c=e.src.charCodeAt(s))||10===c);s++);if(s<p&&91===e.src.charCodeAt(s)&&(h=s+1,(s=No(e,s))>=0?o=e.src.slice(h,s++):s=h-1),o||(void 0===o&&(s=r+1),o=e.src.slice(n,r)),!(l=e.env.references[Uo(o)]))return e.pos=d,!1;i=l.href,a=l.title}return t||(e.pos=n,e.posMax=r,u?e.push({type:"image",src:i,title:a,alt:e.src.substr(n,r-n),level:e.level}):(e.push({type:"link_open",href:i,title:a,level:e.level++}),e.linkLevel++,e.parser.tokenize(e),e.linkLevel--,e.push({type:"link_close",level:--e.level}))),e.pos=s,e.posMax=p,!0}],["footnote_inline",function(e,t){var n,r,o,i,a=e.posMax,s=e.pos;return!(s+2>=a||94!==e.src.charCodeAt(s)||91!==e.src.charCodeAt(s+1)||e.level>=e.options.maxNesting||(n=s+2,(r=No(e,s+1))<0||(t||(e.env.footnotes||(e.env.footnotes={}),e.env.footnotes.list||(e.env.footnotes.list=[]),o=e.env.footnotes.list.length,e.pos=n,e.posMax=r,e.push({type:"footnote_ref",id:o,level:e.level}),e.linkLevel++,i=e.tokens.length,e.parser.tokenize(e),e.env.footnotes.list[o]={tokens:e.tokens.splice(i)},e.linkLevel--),e.pos=r+1,e.posMax=a,0)))}],["footnote_ref",function(e,t){var n,r,o,i,a=e.posMax,s=e.pos;if(s+3>a)return!1;if(!e.env.footnotes||!e.env.footnotes.refs)return!1;if(91!==e.src.charCodeAt(s))return!1;if(94!==e.src.charCodeAt(s+1))return!1;if(e.level>=e.options.maxNesting)return!1;for(r=s+2;r<a;r++){if(32===e.src.charCodeAt(r))return!1;if(10===e.src.charCodeAt(r))return!1;if(93===e.src.charCodeAt(r))break}return!(r===s+2||r>=a||(r++,n=e.src.slice(s+2,r-1),void 0===e.env.footnotes.refs[":"+n]||(t||(e.env.footnotes.list||(e.env.footnotes.list=[]),e.env.footnotes.refs[":"+n]<0?(o=e.env.footnotes.list.length,e.env.footnotes.list[o]={label:n,count:0},e.env.footnotes.refs[":"+n]=o):o=e.env.footnotes.refs[":"+n],i=e.env.footnotes.list[o].count,e.env.footnotes.list[o].count++,e.push({type:"footnote_ref",id:o,subId:i,level:e.level})),e.pos=r,e.posMax=a,0)))}],["autolink",function(e,t){var n,r,o,i,a,s=e.pos;return!(60!==e.src.charCodeAt(s)||(n=e.src.slice(s)).indexOf(">")<0||((r=n.match(Oi))?ki.indexOf(r[1].toLowerCase())<0||(a=qo(i=r[0].slice(1,-1)),!e.parser.validateLink(i)||(t||(e.push({type:"link_open",href:a,level:e.level}),e.push({type:"text",content:i,level:e.level+1}),e.push({type:"link_close",level:e.level})),e.pos+=r[0].length,0)):!(o=n.match(Si))||(a=qo("mailto:"+(i=o[0].slice(1,-1))),!e.parser.validateLink(a)||(t||(e.push({type:"link_open",href:a,level:e.level}),e.push({type:"text",content:i,level:e.level+1}),e.push({type:"link_close",level:e.level})),e.pos+=o[0].length,0))))}],["htmltag",function(e,t){var n,r,o,i=e.pos;return!(!e.options.html||(o=e.posMax,60!==e.src.charCodeAt(i)||i+2>=o||33!==(n=e.src.charCodeAt(i+1))&&63!==n&&47!==n&&!function(e){var t=32|e;return t>=97&&t<=122}(n)||!(r=e.src.slice(i).match(Di))||(t||e.push({type:"htmltag",content:e.src.slice(i,i+r[0].length),level:e.level}),e.pos+=r[0].length,0)))}],["entity",function(e,t){var n,r,o=e.pos,i=e.posMax;if(38!==e.src.charCodeAt(o))return!1;if(o+1<i)if(35===e.src.charCodeAt(o+1)){if(r=e.src.slice(o).match(Fi))return t||(n="x"===r[1][0].toLowerCase()?parseInt(r[1].slice(1),16):parseInt(r[1],10),e.pending+=Eo(n)?Co(n):Co(65533)),e.pos+=r[0].length,!0}else if(r=e.src.slice(o).match(Ii)){var a=bo(r[1]);if(r[1]!==a)return t||(e.pending+=a),e.pos+=r[0].length,!0}return t||(e.pending+="&"),e.pos++,!0}]];function Mi(){this.ruler=new Ro;for(var e=0;e<Ti.length;e++)this.ruler.push(Ti[e][0],Ti[e][1]);this.validateLink=Ri}function Ri(e){var t=e.trim().toLowerCase();return-1===(t=Oo(t)).indexOf(":")||-1===["vbscript","javascript","file","data"].indexOf(t.split(":")[0])}Mi.prototype.skipToken=function(e){var t,n,r=this.ruler.getRules(""),o=r.length,i=e.pos;if((n=e.cacheGet(i))>0)e.pos=n;else{for(t=0;t<o;t++)if(r[t](e,!0))return void e.cacheSet(i,e.pos);e.pos++,e.cacheSet(i,e.pos)}},Mi.prototype.tokenize=function(e){for(var t,n,r=this.ruler.getRules(""),o=r.length,i=e.posMax;e.pos<i;){for(n=0;n<o&&!(t=r[n](e,!1));n++);if(t){if(e.pos>=i)break}else e.pending+=e.src[e.pos++]}e.pending&&e.pushPending()},Mi.prototype.parse=function(e,t,n,r){var o=new zo(e,this,t,n,r);this.tokenize(o)};var zi={default:{options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkTarget:"",typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["block","inline","references","replacements","smartquotes","references","abbr2","footnote_tail"]},block:{rules:["blockquote","code","fences","footnote","heading","hr","htmlblock","lheading","list","paragraph","table"]},inline:{rules:["autolink","backticks","del","emphasis","entity","escape","footnote_ref","htmltag","links","newline","text"]}}},full:{options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkTarget:"",typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{},block:{},inline:{}}},commonmark:{options:{html:!0,xhtmlOut:!0,breaks:!1,langPrefix:"language-",linkTarget:"",typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["block","inline","references","abbr2"]},block:{rules:["blockquote","code","fences","heading","hr","htmlblock","lheading","list","paragraph"]},inline:{rules:["autolink","backticks","emphasis","entity","escape","htmltag","links","newline","text"]}}}};function Ni(e,t,n){this.src=t,this.env=n,this.options=e.options,this.tokens=[],this.inlineMode=!1,this.inline=e.inline,this.block=e.block,this.renderer=e.renderer,this.typographer=e.typographer}function Li(e,t){"string"!=typeof e&&(t=e,e="default"),t&&null!=t.linkify&&console.warn("linkify option is removed. Use linkify plugin instead:\n\nimport Remarkable from 'remarkable';\nimport linkify from 'remarkable/linkify';\nnew Remarkable().use(linkify)\n"),this.inline=new Mi,this.block=new fi,this.core=new oi,this.renderer=new Mo,this.ruler=new Ro,this.options={},this.configure(zi[e]),this.set(t||{})}Li.prototype.set=function(e){mo(this.options,e)},Li.prototype.configure=function(e){var t=this;if(!e)throw new Error("Wrong `remarkable` preset, check name/content");e.options&&t.set(e.options),e.components&&Object.keys(e.components).forEach(function(n){e.components[n].rules&&t[n].ruler.enable(e.components[n].rules,!0)})},Li.prototype.use=function(e,t){return e(this,t),this},Li.prototype.parse=function(e,t){var n=new Ni(this,e,t);return this.core.process(n),n.tokens},Li.prototype.render=function(e,t){return t=t||{},this.renderer.render(this.parse(e,t),this.options,t)},Li.prototype.parseInline=function(e,t){var n=new Ni(this,e,t);return n.inlineMode=!0,this.core.process(n),n.tokens},Li.prototype.renderInline=function(e,t){return t=t||{},this.renderer.render(this.parseInline(e,t),this.options,t)};var qi=n(3847),Wi=(0,d.A)(function(e){var t=[];for(var n in e)(0,a.A)(n,e)&&(t[t.length]=[n,e[n]]);return t}),Vi=Wi,Ui=e=>e.map((e,t)=>t?e.charAt(0).toUpperCase()+e.substring(1):e).join(""),Yi=e=>e.join("-"),Hi=e=>e.join("_"),Ki=[],$i=[];[["align","content"],["align","items"],["alignment","adjust"],["alignment","baseline"],["align","self"],["animation","delay"],["animation","direction"],["animation","iteration","count"],["animation","name"],["animation","play","state"],["appearance"],["backface","visibility"],["background"],["background","attachment"],["background","blend","mode"],["background","color"],["background","composite"],["background","image"],["background","origin"],["background","position"],["background","repeat"],["baseline","shift"],["behavior"],["border"],["border","bottom"],["border","bottom","color"],["border","bottom","left","radius"],["border","bottom","right","radius"],["border","bottom","style"],["border","bottom","width"],["border","collapse"],["border","color"],["border","corner","shape"],["border","image","source"],["border","image","width"],["border","left"],["border","left","color"],["border","left","style"],["border","left","width"],["border","radius"],["border","right"],["border","right","color"],["border","right","style"],["border","right","width"],["border","spacing"],["border","style"],["border","top"],["border","top","color"],["border","top","left","radius"],["border","top","right","radius"],["border","top","style"],["border","top","width"],["border","width"],["bottom"],["box","align"],["box","decoration","break"],["box","direction"],["box","flex"],["box","flex","group"],["box","line","progression"],["box","lines"],["box","ordinal","group"],["box","shadow"],["break","after"],["break","before"],["break","inside"],["clear"],["clip"],["clip","rule"],["color"],["column","count"],["column","fill"],["column","gap"],["column","rule"],["column","rule","color"],["column","rule","width"],["columns"],["column","span"],["column","width"],["counter","increment"],["counter","reset"],["cue"],["cue","after"],["cursor"],["direction"],["display"],["fill"],["fill","opacity"],["fill","rule"],["filter"],["flex"],["flex","align"],["flex","basis"],["flex","direction"],["flex","flow"],["flex","grow"],["flex","item","align"],["flex","line","pack"],["flex","order"],["flex","shrink"],["flex","wrap"],["float"],["flow","from"],["font"],["font","family"],["font","kerning"],["font","size"],["font","size","adjust"],["font","stretch"],["font","style"],["font","synthesis"],["font","variant"],["font","variant","alternates"],["font","weight"],["grid","area"],["grid","column"],["grid","column","end"],["grid","column","start"],["grid","row"],["grid","row","end"],["grid","row","position"],["grid","row","span"],["grid","template","areas"],["grid","template","columns"],["grid","template","rows"],["height"],["hyphenate","limit","chars"],["hyphenate","limit","lines"],["hyphenate","limit","zone"],["hyphens"],["ime","mode"],["justify","content"],["layout","grid"],["layout","grid","char"],["layout","grid","line"],["layout","grid","mode"],["layout","grid","type"],["left"],["letter","spacing"],["line","break"],["line","clamp"],["line","height"],["list","style"],["list","style","image"],["list","style","position"],["list","style","type"],["margin"],["margin","bottom"],["margin","left"],["margin","right"],["margin","top"],["marquee","direction"],["marquee","style"],["mask"],["mask","border"],["mask","border","repeat"],["mask","border","slice"],["mask","border","source"],["mask","border","width"],["mask","clip"],["mask","origin"],["max","font","size"],["max","height"],["max","width"],["min","height"],["min","width"],["opacity"],["order"],["orphans"],["outline"],["outline","color"],["outline","offset"],["overflow"],["overflow","style"],["overflow","x"],["overflow","y"],["padding"],["padding","bottom"],["padding","left"],["padding","right"],["padding","top"],["page","break","after"],["page","break","before"],["page","break","inside"],["pause"],["pause","after"],["pause","before"],["perspective"],["perspective","origin"],["pointer","events"],["position"],["punctuation","trim"],["quotes"],["region","fragment"],["rest","after"],["rest","before"],["right"],["ruby","align"],["ruby","position"],["shape","image","threshold"],["shape","inside"],["shape","margin"],["shape","outside"],["speak"],["speak","as"],["stroke","opacity"],["stroke","width"],["table","layout"],["tab","size"],["text","align"],["text","align","last"],["text","decoration"],["text","decoration","color"],["text","decoration","line"],["text","decoration","line","through"],["text","decoration","none"],["text","decoration","overline"],["text","decoration","skip"],["text","decoration","style"],["text","decoration","underline"],["text","emphasis"],["text","emphasis","color"],["text","emphasis","style"],["text","height"],["text","indent"],["text","justify","trim"],["text","kashida","space"],["text","line","through"],["text","line","through","color"],["text","line","through","mode"],["text","line","through","style"],["text","line","through","width"],["text","overflow"],["text","overline"],["text","overline","color"],["text","overline","mode"],["text","overline","style"],["text","overline","width"],["text","rendering"],["text","script"],["text","shadow"],["text","transform"],["text","underline","position"],["text","underline","style"],["top"],["touch","action"],["transform"],["transform","origin"],["transform","origin","z"],["transform","style"],["transition"],["transition","delay"],["transition","duration"],["transition","property"],["transition","timing","function"],["unicode","bidi"],["unicode","range"],["user","focus"],["user","input"],["vertical","align"],["visibility"],["voice","balance"],["voice","duration"],["voice","family"],["voice","pitch"],["voice","range"],["voice","rate"],["voice","stress"],["voice","volume"],["white","space"],["white","space","treatment"],["widows"],["width"],["word","break"],["word","spacing"],["word","wrap"],["wrap","flow"],["wrap","margin"],["wrap","option"],["writing","mode"],["z","index"],["zoom"]].forEach(e=>{var t=Ui(e);Ki.push(t),$i.push([t,t]),$i.push([Yi(e),t]),$i.push([Hi(e),t])});var Gi=new Map($i),Zi=Ki;function Qi(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var Xi,Ji=e=>null!==e&&"object"==typeof e?wt((e,t)=>{var n,r,o=(r=2,function(e){if(Array.isArray(e))return e}(n=t)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(n,r)||function(e,t){if(e){if("string"==typeof e)return Qi(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Qi(e,t):void 0}}(n,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),i=o[0],a=o[1];return(0,qi.A)(Ui(i.split("_")),Ji(a),e)},{},Vi(e)):Array.isArray(e)?e.map(Ji,e):e,ea=Ji;function ta(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var l=r&&r.prototype instanceof s?r:s,c=Object.create(l.prototype);return na(c,"_invoke",function(n,r,o){var i,s,l,c=0,u=o||[],d=!1,p={p:0,n:0,v:e,a:h,f:h.bind(e,4),d:function(t,n){return i=t,s=0,l=e,p.n=n,a}};function h(n,r){for(s=n,l=r,t=0;!d&&c&&!o&&t<u.length;t++){var o,i=u[t],h=p.p,f=i[2];n>3?(o=f===r)&&(l=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=h&&((o=n<2&&h<i[1])?(s=0,p.v=r,p.n=i[1]):h<f&&(o=n<3||i[0]>r||r>f)&&(i[4]=n,i[5]=r,p.n=f,s=0))}if(o||n>1)return a;throw d=!0,r}return function(o,u,f){if(c>1)throw TypeError("Generator is already running");for(d&&1===u&&h(u,f),s=u,l=f;(t=s<2?e:l)||!d;){i||(s?s<3?(s>1&&(p.n=-1),h(s,l)):p.n=l:p.v=l);try{if(c=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(l=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(d=p.n<0)?l:n.call(r,p))!==a)break}catch(t){i=e,s=1,l=t}finally{c=1}}return{value:t,done:d}}}(n,o,i),!0),c}var a={};function s(){}function l(){}function c(){}t=Object.getPrototypeOf;var u=[][r]?t(t([][r]())):(na(t={},r,function(){return this}),t),d=c.prototype=s.prototype=Object.create(u);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,c):(e.__proto__=c,na(e,o,"GeneratorFunction")),e.prototype=Object.create(d),e}return l.prototype=c,na(d,"constructor",c),na(c,"constructor",l),l.displayName="GeneratorFunction",na(c,o,"GeneratorFunction"),na(d),na(d,o,"Generator"),na(d,r,function(){return this}),na(d,"toString",function(){return"[object Generator]"}),(ta=function(){return{w:i,m:p}})()}function na(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}na=function(e,t,n,r){if(t)o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{function i(t,n){na(e,t,function(e){return this._invoke(t,n,e)})}i("next",0),i("throw",1),i("return",2)}},na(e,t,n,r)}function ra(e,t,n,r,o,i,a){try{var s=e[i](a),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function oa(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function ia(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class aa{constructor(e){ia(this,"options",void 0),ia(this,"md",void 0),ia(this,"render",e=>this.md.render(e)),this.options=e,this.md=new Li(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?oa(Object(n),!0).forEach(function(t){ia(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):oa(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({highlight:(e,t)=>{if(aa.hljs){if(t&&aa.hljs.getLanguage(t))try{return aa.hljs.highlight(t,e).value}catch(e){}try{return aa.hljs.highlightAuto(e).value}catch(e){}}else aa.loadhljs();return""}},ea(this.options)))}static get isReady(){return aa._isReady}static loadhljs(){return(e=ta().m(function e(){return ta().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,ot.A.hljs;case 1:aa.hljs=e.v,aa.hljsResolve(),aa._isReady=!0;case 2:return e.a(2)}},e)}),function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){ra(i,r,o,a,s,"next",e)}function s(e){ra(i,r,o,a,s,"throw",e)}a(void 0)})})();var e}}function sa(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function la(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}Xi=aa,ia(aa,"hljs",void 0),ia(aa,"hljsResolve",void 0),ia(aa,"_isReady",new Promise(e=>{Xi.hljsResolve=e}));class ca extends r.PureComponent{constructor(e){super(e),la(this,"getMarkdown",(0,f.B4)((e,t,n)=>({dangerouslySetInnerHTML:{__html:t.render(String(e))}}))),la(this,"elRef",void 0),!0!==aa.isReady&&aa.isReady.then(()=>{this.setState({})}),this.elRef=(0,r.createRef)()}componentDidUpdate(){this.setFocus()}componentDidMount(){this.setFocus()}render(){var e=this.props,t=e.className,n=e.markdown,r=e.value;return o().createElement("div",function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?sa(Object(n),!0).forEach(function(t){la(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):sa(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({ref:this.elRef,className:[t,"cell-markdown"].join(" ")},this.getMarkdown(r,n,aa.isReady)))}setFocus(){var e=this.props,t=e.active,n=e.applyFocus;if(t){var r=this.elRef.current;if(n&&r&&document.activeElement!==r){var o=uo.getFirstParentOfType(r,"td");o&&-1!==o.className.indexOf("phantom-cell")&&o.focus()}}}}function ua(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var da,pa=Xe(g.A),ha=Xe(g.A);!function(e){e[e.Dropdown=0]="Dropdown",e[e.DropdownLabel=1]="DropdownLabel",e[e.Input=2]="Input",e[e.Label=3]="Label",e[e.Markdown=4]="Markdown"}(da||(da={}));class fa{constructor(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:fr(e);ua(this,"handlers",void 0),ua(this,"cell_selectable",void 0),ua(this,"partialGet",(0,f.B4)((e,t,n,r,o,i,a)=>{var s=g.A(vo,e);return pa((t,n)=>ha((e,l)=>this.getContent(!1,!1,r,e,o&&o[n][l],l,n,t,s,i,a),e),t)})),ua(this,"get",(0,f.B4)((e,t,n,r,o,i,a,s,l,c)=>{if(!t)return e;var u=t.row,d=t.column,p=u-i.rows,h=d-i.columns;if(p<0||h<0||o.length<=p||r.length<=h)return e;var f=g.A(vo,r);return(e=Un(e))[p][h]=this.getContent(!0,n||!1,a,r[h],s&&s[p][h],d,u,o[p],f,l,c),e})),this.handlers=t,this.cell_selectable=e().cell_selectable}getContent(e,t,n,r,i,a,s,l,c,u,d){var p=[...e?["input-active"]:[],n?"focused":"unfocused",...this.cell_selectable?["selectable"]:[],"dash-cell-value"].join(" "),h=function(e,t,n,r,o){switch(r){case Ge.Jm.Input:return e&&t&&!o?da.Input:da.Label;case Ge.Jm.Dropdown:return n&&t?da.Dropdown:da.DropdownLabel;case Ge.Jm.Markdown:return da.Markdown;default:return e&&t&&!o?da.Input:da.Label}}(e,r.editable,i&&i.options,r.presentation,u);switch(h){case da.Dropdown:return o().createElement(ho,{key:"column-".concat(a),active:e,applyFocus:t,clearable:i&&i.clearable,dropdown:i&&i.options,onChange:this.handlers(tr.Change,s,a),value:l[r.id],disabled:u});case da.Input:return o().createElement(mr,{key:"column-".concat(a),active:e,applyFocus:t,className:p,focused:n,onChange:this.handlers(tr.Change,s,a),onMouseUp:this.handlers(tr.MouseUp,s,a),onPaste:this.handlers(tr.Paste,s,a),type:r.type,value:l[r.id]});case da.Markdown:return o().createElement(ca,{key:"column-".concat(a),active:e,applyFocus:t,className:p,markdown:d,value:l[r.id]});case da.DropdownLabel:case da.Label:default:var f=h===da.DropdownLabel?this.resolveDropdownLabel(i,l[r.id]):c[a](l[r.id]);return o().createElement(yr,{active:e,applyFocus:t,className:p,key:"column-".concat(a),value:f})}}resolveDropdownLabel(e,t){var n=e&&e.options&&e.options.find(e=>e.value===t);return n?n.label:t}}var Aa=n(954),va=(0,i.A)(function(e,t){return t instanceof e||null!=t&&(t.constructor===e||"Object"===e.name&&"object"==typeof t)}),ba=(0,i.A)(function(e,t){for(var n=new zt,r=0;r<e.length;r+=1)n.add(e[r]);return Y(n.has.bind(n),t)}),ga=ba,ma=(0,m.A)(function(e,t,n){return(0,Qe.A)(Math.max(e.length,t.length,n.length),function(){return e.apply(this,arguments)?t.apply(this,arguments):n.apply(this,arguments)})}),ya=(0,i.A)(function(e,t){return Ze(t,[e])});function wa(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Ea(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ca(e,t,n,r,i,a){return o().createElement("td",{key:"select",className:"dash-select-cell",style:{width:"30px",maxWidth:"30px",minWidth:"30px",textAlign:"center"}},o().createElement("input",{type:"single"===n?"radio":"checkbox",style:{verticalAlign:"middle"},name:"row-select-".concat(e),checked:Q.A(t,r),onChange:()=>{var e="single"===n?[t]:ma(Q.A(t),ga([t]),ya(t))(r);i({selected_rows:e,selected_row_ids:g.A(e=>a[e].id,e)})}}))}var xa,ka=(0,f.ty)((e,t,n,r,i,a,s,l)=>Xe(g.A)((n,c)=>{return[...a?[(u=()=>l(function(e,t,n){var r=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?wa(Object(n),!0).forEach(function(t){Ea(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):wa(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({data:Aa.A(e,1,t)},$e);return va(Array,n)&&Ce(t=>t>=e,n)&&(r.selected_rows=g.A(t=>t>e?t-1:t,ga([e],n)),r.selected_row_ids=g.A(e=>r.data[e].id,r.selected_rows)),r}(r[c],t,s)),o().createElement("td",{key:"delete",className:"dash-delete-cell",onClick:()=>u(),style:{width:"30px",maxWidth:"30px",minWidth:"30px"}},"×"))]:[],...i?[Ca(e,r[c],i,s,l,t)]:[]];var u},n));function Sa(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Oa(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Sa(Object(n),!0).forEach(function(t){Ba(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Sa(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Ba(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _a(e){return Oa(Oa({},e),{},{if:()=>!1,terminal:!1})}!function(e){e.BlockClose="close-block",e.BlockOpen="open-block",e.LogicalOperator="logical-operator",e.RelationalOperator="relational-operator",e.UnaryOperator="unary-operator",e.Expression="expression"}(xa||(xa={}));var ja=e=>{var t=0,n=e.map(e=>{var n=Object.assign({},e,{nesting:t});return t+=e.lexeme.nesting||0,n}),r=n.filter(e=>0===e.nesting&&"number"==typeof e.lexeme.priority).sort((e,t)=>(t.lexeme.priority||-1)-(e.lexeme.priority||-1))[0];xe.Ay.trace("parser -> pivot",r,e);var o=n.indexOf(r);if(r.lexeme.syntaxer){var i=r.lexeme.syntaxer(e,r,o);return Array.isArray(i.left)&&(i.left=ja(i.left)),Array.isArray(i.right)&&(i.right=ja(i.right)),Array.isArray(i.block)&&(i.block=ja(i.block)),i}throw new Error(r.lexeme.type)};function Pa(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Da(e){var t=e.block,n=e.left,r=e.lexeme,o=e.right,i=e.value,a={subType:r.subType,type:r.type,value:r.present?r.present(e):i};return t&&(a.block=Da(t)),n&&(a.left=Da(n)),o&&(a.right=Da(o)),a}class Fa{get isValid(){return this.syntaxerResult.valid}get tree(){return this.syntaxerResult.tree}constructor(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e=>e;Pa(this,"lexicon",void 0),Pa(this,"query",void 0),Pa(this,"lexerResult",void 0),Pa(this,"syntaxerResult",void 0),Pa(this,"evaluate",e=>{if(!this.isValid){var t="DataTable filtering syntax is invalid for query: ".concat(this.query);throw xe.Ay.error(t),new Error(t)}return!(this.tree&&this.tree.lexeme&&this.tree.lexeme.evaluate)||this.tree.lexeme.evaluate(e,this.tree)}),Pa(this,"filter",e=>e.filter(this.evaluate)),this.lexicon=e,this.query=t,this.lexerResult=n(Tt(this.lexicon,this.query)),this.syntaxerResult=(e=>{var t=e.lexemes;if(!e.valid)return{valid:!1,error:"lexer -- ".concat(e.error)};if(0===e.lexemes.length)return{valid:!0};try{return{tree:ja(t),valid:!0}}catch(e){return{valid:!1,error:String(e)}}})(this.lexerResult)}toQueryString(){return this.lexerResult.valid?g.A(e=>e.lexeme.transform?e.lexeme.transform(e.value):e.value,this.lexerResult.lexemes).join(" "):""}toStructure(){return this.isValid&&this.syntaxerResult.tree?Da(this.syntaxerResult.tree):null}}var Ia,Ta=/^{(([^{}\\]|\\.)+)}/,Ma=/^(('([^'\\]|\\.)*')|("([^"\\]|\\.)*")|(`([^`\\]|\\.)*`))/,Ra=e=>e.slice(1,e.length-1).replace(/\\(.)/g,"$1"),za={present:e=>Ra(e.value),resolve:(e,t)=>{if(Ta.test(t.value))return e[Ra(t.value)];throw new Error},regexp:Ta,subType:"field",type:xa.Expression},Na=e=>e.slice(1,e.length-1).replace(/\\(.)/g,"$1"),La={present:e=>Na(e.value),resolve:(e,t)=>{if(Ma.test(t.value))return Na(t.value);throw new Error},regexp:Ma,subType:"value",type:xa.Expression},qa=(e,t)=>{var n=(e=>t=>(t=t.match(e)[1],An()(t)?+t:t.replace(/\\(.)/g,"$1")))(e);return{present:e=>n(e.value),resolve:(t,r)=>{if(e.test(r.value))return n(r.value);throw new Error},regexp:e,regexpMatch:1,subType:"value",transform:t,type:xa.Expression}},Wa=qa(/^(([^\s'"`{}()\\]|\\.)+)(?:[\s)]|$)/),Va=qa(/^(([^'"`{}()\\]|\\.)+)$/,e=>"string"==typeof e&&-1!==e.indexOf(" ")?'"'.concat(e,'"'):e);!function(e){e.And="&&",e.Or="||"}(Ia||(Ia={}));var Ua,Ya={evaluate:(e,t)=>{xe.Ay.trace("evaluate -> &&",e,t);var n=t,r=n.left.lexeme.evaluate(e,n.left),o=n.right.lexeme.evaluate(e,n.right);return r&&o},type:xa.LogicalOperator,priority:2,regexp:/^(and\s|&&)/i,subType:Ia.And,syntaxer:(e,t,n)=>Object.assign({left:e.slice(0,n),right:e.slice(n+1)},t)},Ha={evaluate:(e,t)=>{xe.Ay.trace("evaluate -> ||",e,t);var n=t;return n.left.lexeme.evaluate(e,n.left)||n.right.lexeme.evaluate(e,n.right)},type:xa.LogicalOperator,subType:Ia.Or,priority:3,regexp:/^(or\s|\|\|)/i,syntaxer:(e,t,n)=>Object.assign({left:e.slice(0,n),right:e.slice(n+1)},t)};function Ka(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return $a(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?$a(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $a(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function Ga(e){return(t,n)=>e(function(e,t){xe.Ay.trace("evaluate -> relational",e,t);var n=t,r=n.left.lexeme.resolve(e,n.left),o=n.right.lexeme.resolve(e,n.right);return xe.Ay.trace("opValue: ".concat(r,", expValue: ").concat(o)),[r,o,t.value]}(t,n))}!function(e){e.Contains="contains",e.DateStartsWith="datestartswith",e.Equal="=",e.GreaterOrEqual=">=",e.GreaterThan=">",e.LessOrEqual="<=",e.LessThan="<",e.NotEqual="!="}(Ua||(Ua={}));var Za,Qa={priority:0,syntaxer:function(e){var t=Ka(e,3),n=t[0],r=t[1],o=t[2];return Object.assign({left:n,right:o},r)},type:xa.RelationalOperator},Xa=(e,t,n,r)=>"i"==r[0]?e(t.toString().toUpperCase(),n.toString().toUpperCase()):e(t,n),Ja=Me.A({evaluate:Ga(e=>{var t=Ka(e,3),n=t[0],r=t[1],o=t[2];return!s.A(r)&&!s.A(n)&&("String"===ze.A(r)||"String"===ze.A(n))&&((e,t,n)=>"i"==n[0]?-1!==e.toString().toUpperCase().indexOf(t.toString().toUpperCase()):-1!==e.toString().indexOf(t.toString()))(n,r,o)}),subType:Ua.Contains,regexp:/^((i|s)?contains)(?=\s|$)/i,regexpFlags:2,regexpMatch:1},Qa),es=Me.A({evaluate:Ga(e=>{var t,n,r,o=Ka(e,3);return t=o[0],n=o[1],r=o[2],An()(t)&&An()(n)?+t===+n:"i"==r[0]?t.toString().toUpperCase()===n.toString().toUpperCase():t===n}),subType:Ua.Equal,regexp:/^((i|s)?(=|(eq)(?=\s|$)))/i,regexpFlags:2,regexpMatch:1},Qa),ts=Me.A({evaluate:Ga(e=>{var t=Ka(e,3),n=t[0],r=t[1],o=t[2];return Xa((e,t)=>e>=t,n,r,o)}),subType:Ua.GreaterOrEqual,regexp:/^((i|s)?(>=|(ge)(?=\s|$)))/i,regexpFlags:2,regexpMatch:1},Qa),ns=Me.A({evaluate:Ga(e=>{var t=Ka(e,3),n=t[0],r=t[1],o=t[2];return Xa((e,t)=>e>t,n,r,o)}),subType:Ua.GreaterThan,regexp:/^((i|s)?(>|(gt)(?=\s|$)))/i,regexpFlags:2,regexpMatch:1},Qa),rs={allow_YY:!0},os=Me.A({evaluate:Ga(e=>{var t=Ka(e,2),n=t[0],r=t[1];n="number"==typeof n?n.toString():n,r="number"==typeof r?r.toString():r;var o=On(n,rs),i=On(r,rs);return!s.A(o)&&!s.A(i)&&0===o.indexOf(i)}),subType:Ua.DateStartsWith,regexp:/^((datestartswith)(?=\s|$))/i,regexpMatch:1},Qa),is=Me.A({evaluate:Ga(e=>{var t=Ka(e,3),n=t[0],r=t[1],o=t[2];return Xa((e,t)=>e<=t,n,r,o)}),subType:Ua.LessOrEqual,regexp:/^((i|s)?(<=|(le)(?=\s|$)))/i,regexpFlags:2,regexpMatch:1},Qa),as=Me.A({evaluate:Ga(e=>{var t=Ka(e,3),n=t[0],r=t[1],o=t[2];return Xa((e,t)=>e<t,n,r,o)}),subType:Ua.LessThan,regexp:/^((i|s)?(<|(lt)(?=\s|$)))/i,regexpFlags:2,regexpMatch:1},Qa),ss=Me.A({evaluate:Ga(e=>{var t=Ka(e,3),n=t[0],r=t[1],o=t[2];return Xa((e,t)=>e!==t,n,r,o)}),subType:Ua.NotEqual,regexp:/^((i|s)?(!=|(ne)(?=\s|$)))/i,regexpFlags:2,regexpMatch:1},Qa);function ls(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function cs(e){return(t,n)=>e(function(e,t){xe.Ay.trace("evaluate -> unary",e,t),xe.Ay.trace("evaluate -> unary",e,t);var n=t;return n.left.lexeme.resolve(e,n.left)}(t,n))}!function(e){e.Not="!"}(Za||(Za={}));var us={present:e=>e.value,priority:0,syntaxer:function(e){var t,n,r=(n=2,function(e){if(Array.isArray(e))return e}(t=e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(t,n)||function(e,t){if(e){if("string"==typeof e)return ls(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ls(e,t):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),o=r[0],i=r[1];return Object.assign({left:o},i)},type:xa.UnaryOperator},ds={evaluate:(e,t)=>{xe.Ay.trace("evaluate -> unary not",e,t);var n=t;return!n.right.lexeme.evaluate(e,n.right)},type:xa.UnaryOperator,subType:Za.Not,priority:1.5,regexp:/^!/,syntaxer:e=>Object.assign({right:e.slice(1,e.length)},e[0])},ps=Me.A({evaluate:cs(e=>"boolean"==typeof e),regexp:/^(is bool)/i},us),hs=Me.A({evaluate:cs(e=>"number"==typeof e&&e%2==0),regexp:/^(is even)/i},us),fs=Me.A({evaluate:cs(e=>null==e||""===e),regexp:/^(is blank)/i},us),As=Me.A({evaluate:cs(e=>null==e),regexp:/^(is nil)/i},us),vs=Me.A({evaluate:cs(e=>"number"==typeof e),regexp:/^(is num)/i},us),bs=Me.A({evaluate:cs(e=>null!==e&&"object"==typeof e),regexp:/^(is object)/i},us),gs=Me.A({evaluate:cs(e=>"number"==typeof e&&e%2==1),regexp:/^(is odd)/i},us),ms=Me.A({evaluate:cs(e=>"number"==typeof e&&(e=>{if(2===e)return!0;if(e<2||e%2==0)return!1;for(var t=3;t*t<=e;t+=2)if(e%t===0)return!1;return!0})(e)),regexp:/^(is prime)/i},us),ys=Me.A({evaluate:cs(e=>"string"==typeof e),regexp:/^(is str)/i},us),ws=wt((e,t)=>e+(t.lexeme.nesting||0)),Es=(e,t)=>0===ws(0,e),Cs=(e,t)=>Es(e)&&!!t&&Q.A(t.lexeme.type,[xa.RelationalOperator]),xs=(e,t)=>!t||Q.A(t.lexeme.type,[xa.BlockOpen,xa.LogicalOperator,xa.RelationalOperator]),ks=(e,t)=>!t,Ss=(e,t)=>!!t&&Q.A(t.lexeme.type,[xa.BlockClose,xa.Expression,xa.UnaryOperator]),Os=(e,t)=>!!t&&Q.A(t.lexeme.type,[xa.Expression]),Bs=Os;function _s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function js(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_s(Object(n),!0).forEach(function(t){Ps(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_s(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Ps(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Ds extends Fa{constructor(e,t){super(function(e){return[js(js({},e===Ge.iV.And?Ya:Ha),{},{if:Ss,terminal:!1}),...[Ja,os,es,ts,ns,is,as,ss].map(e=>js(js({},e),{},{if:Os,terminal:!1})),...[fs,ps,hs,As,vs,bs,gs,ms,ys].map(e=>js(js({},e),{},{if:Bs,terminal:!0})),...[za,La,Wa].map(e=>js(js({},e),{},{if:xs,terminal:Cs}))]}(t),e)}get isValid(){return super.isValid&&this.respectsBasicSyntax()}get statements(){if(this.syntaxerResult.tree){for(var e=[],t=[this.syntaxerResult.tree];t.length;){var n=t.pop();n&&(e.push(n),n.left&&t.push(n.left),n.block&&t.push(n.block),n.right&&t.push(n.right))}return e}}respectsBasicSyntax(){var e=g.A(e=>e.value,U(e=>e.lexeme.type===xa.Expression&&"field"===e.lexeme.subType,this.lexerResult.lexemes)),t=Wt(e);return e.length===t.length}}var Fs={nesting:-1,regexp:/^\)/,type:xa.BlockClose},Is={evaluate:(e,t)=>{xe.Ay.trace("evaluate -> ()",e,t);var n=t;return n.block.lexeme.evaluate(e,n.block)},type:xa.BlockOpen,nesting:1,subType:"()",priority:1,regexp:/^\(/,syntaxer:e=>Object.assign({block:e.slice(1,e.length-1)},e[0])};function Ts(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Ms(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ts(Object(n),!0).forEach(function(t){Rs(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ts(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Rs(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var zs=[...[Ya,Ha].map(e=>Ms(Ms({},e),{},{if:Ss,terminal:!1})),Ms(Ms({},Fs),{},{if:(e,t)=>!!t&&Q.A(t.lexeme.type,[xa.BlockClose,xa.BlockOpen,xa.Expression,xa.UnaryOperator])&&ws(0,e)>0,terminal:Es}),Ms(Ms({},Is),{},{if:(e,t)=>!t||Q.A(t.lexeme.type,[xa.BlockOpen,xa.LogicalOperator,xa.UnaryOperator]),terminal:!1}),...[Ja,os,es,ts,ns,is,as,ss].map(e=>Ms(Ms({},e),{},{if:Os,terminal:!1})),...[fs,ps,hs,As,vs,bs,gs,ms,ys].map(e=>Ms(Ms({},e),{},{if:Bs,terminal:Es})),Ms(Ms({},ds),{},{if:(e,t)=>!t||Q.A(t.lexeme.type,[xa.LogicalOperator,xa.UnaryOperator]),terminal:!1}),...[za,La,Wa].map(e=>Ms(Ms({},e),{},{if:xs,terminal:Cs}))];class Ns extends Fa{constructor(e){super(zs,e)}}function Ls(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function qs(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ls(Object(n),!0).forEach(function(t){Ws(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ls(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Ws(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Vs=[...[Ja,os,es,ts,ns,is,as,ss].map(e=>qs(qs({},e),{},{if:ks,terminal:!1})),...[fs,ps,hs,As,vs,bs,gs,ms,ys].map(e=>qs(qs({},e),{},{if:ks,terminal:!0})),...[za,Va,La].map(e=>qs(qs({},e),{},{if:xs,terminal:!0}))];function Us(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Ys(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Us(Object(n),!0).forEach(function(t){Hs(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Us(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Hs(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ks=[Ua.Contains,Ua.Equal,Ua.GreaterOrEqual,Ua.GreaterThan,Ua.LessOrEqual,Ua.LessThan,Ua.NotEqual];function $s(e,t){var n=s.A(e)?"":e.case===Ge.ze.Insensitive?"i":"s";return t.lexeme.type===xa.RelationalOperator&&t.lexeme.subType&&-1!==Ks.indexOf(t.lexeme.subType)&&t.value&&-1===["i","s"].indexOf(t.value[0])?Ys(Ys({},t),{},{value:"".concat(n).concat(t.value)}):t}function Gs(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Ge.$C.Any,n=s.A(e)?"":e.case===Ge.ze.Insensitive?"i":"s";switch(t){case Ge.$C.Any:case Ge.$C.Text:return{lexeme:_a(Ja),value:"".concat(n).concat(Ua.Contains)};case Ge.$C.Datetime:return{lexeme:_a(os),value:Ua.DateStartsWith};case Ge.$C.Numeric:return{lexeme:_a(es),value:"".concat(n).concat(Ua.Equal)}}}function Zs(e,t){return t.valid?(2===t.lexemes.length?t.lexemes=[{lexeme:_a(za),value:"{".concat(e.id,"}")},$s(e.filter_options,t.lexemes[0]),t.lexemes[1]]:1!==(n=t.lexemes).length||n[0].lexeme.type!==xa.UnaryOperator?function(e){return 1===e.length&&e[0].lexeme.type===xa.Expression}(t.lexemes)&&(t.lexemes=[{lexeme:_a(za),value:"{".concat(e.id,"}")},Gs(e.filter_options,e.type),...t.lexemes]):t.lexemes=[{lexeme:_a(za),value:"{".concat(e.id,"}")},...t.lexemes],t):t;var n}class Qs extends Fa{constructor(e,t){super(Vs,e,Zs.bind(void 0,t))}}function Xs(e,t){return!e||void 0===e.column_id||(Array.isArray(e.column_id)?Q.A(t,e.column_id):e.column_id===t)}function Js(e,t){if(!e||void 0===e.row_index)return!0;var n=e.row_index;return"string"==typeof n?t%2==("odd"===n?1:0):Array.isArray(n)?Q.A(t,n):t===n}var el=(e,t,n,r,o)=>U(i=>!i.checksHeaderRow()&&i.matchesActive(r)&&i.matchesSelected(o)&&i.matchesDataRow(t)&&i.matchesColumn(n)&&i.matchesFilter(e)),tl=e=>U(t=>!t.checksState()&&!t.checksDataRow()&&!t.checksHeaderRow()&&t.matchesColumn(e)),nl=(e,t)=>U(n=>!n.checksState()&&!n.checksDataRow()&&n.matchesHeaderRow(e)&&n.matchesColumn(t)),rl=(e,t)=>U(n=>!n.checksState()&&!n.checksColumn()&&!n.checksHeaderRow()&&n.matchesDataRow(t)&&n.matchesFilter(e)),ol=U(e=>!(e.checksState()||e.checksDataRow()||e.checksHeaderRow()||e.checksColumn())),il=e=>U(t=>!t.checksDataRow()&&!t.checksState()&&!t.checksColumn()&&t.matchesHeaderRow(e));function al(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var sl=["borderBottom","borderLeft","borderRight","borderTop"],ll=U(e=>0===e.indexOf("border"),Zi);class cl{constructor(e,t,n){if(al(this,"weights",void 0),al(this,"edges",void 0),al(this,"rows",void 0),al(this,"columns",void 0),al(this,"defaultEdge",void 0),al(this,"getEdge",(e,t)=>this.edges[e][t]),al(this,"getEdges",()=>this.edges),al(this,"getWeight",(e,t)=>this.weights[e][t]),al(this,"isDefault",(e,t)=>this.weights[e][t]===-1/0),al(this,"clone",()=>new cl(this)),"number"==typeof e&&void 0!==t){var r=e;this.rows=r,this.columns=t,this.defaultEdge=n,this.weights=g.A(()=>new Array(t).fill(-1/0),Re.A(0,r)),this.edges=g.A(()=>new Array(t).fill(n),Re.A(0,r))}else{var o=e;this.rows=o.rows,this.columns=o.columns,this.defaultEdge=o.defaultEdge,this.weights=Un(o.weights),this.edges=Un(o.edges)}}setEdge(e,t,n,r){var o=arguments.length>4&&void 0!==arguments[4]&&arguments[4];e<0||t<0||e>=this.rows||t>=this.columns||!o&&(s.A(n)||r<=this.weights[e][t])||(this.weights[e][t]=r,this.edges[e][t]=n)}}class ul{constructor(e,t,n,r,o){if(al(this,"horizontal",void 0),al(this,"vertical",void 0),al(this,"horizontalEdges",void 0),al(this,"verticalEdges",void 0),al(this,"rows",void 0),al(this,"columns",void 0),al(this,"defaultEdge",void 0),al(this,"getEdges",()=>({horizontal:this.horizontal.getEdges(),vertical:this.vertical.getEdges()})),al(this,"getMatrices",()=>({horizontal:this.horizontal,vertical:this.vertical})),al(this,"getStyle",(e,t)=>({borderBottom:this.horizontal.getEdge(e+1,t)||null,borderTop:this.horizontal.getEdge(e,t)||null,borderLeft:this.vertical.getEdge(e,t)||null,borderRight:this.vertical.getEdge(e,t+1)||null})),al(this,"clone",()=>new ul(this)),"number"==typeof e&&void 0!==t){var i=e;this.rows=i,this.columns=t,this.defaultEdge=n,this.horizontalEdges=s.A(r)||r,this.verticalEdges=s.A(o)||o,this.horizontal=new cl(i+1,t,this.horizontalEdges?n:void 0),this.vertical=new cl(i,t+1,this.verticalEdges?n:void 0)}else{var a=e;this.rows=a.rows,this.columns=a.columns,this.defaultEdge=a.defaultEdge,this.horizontal=a.horizontal.clone(),this.vertical=a.vertical.clone(),this.horizontalEdges=a.horizontalEdges,this.verticalEdges=a.verticalEdges}}setEdges(e,t,n){this.horizontalEdges&&(n.borderTop&&this.horizontal.setEdge(e,t,n.borderTop[0],n.borderTop[1]),n.borderBottom&&this.horizontal.setEdge(e+1,t,n.borderBottom[0],n.borderBottom[1])),this.verticalEdges&&(n.borderLeft&&this.vertical.setEdge(e,t,n.borderLeft[0],n.borderLeft[1]),n.borderRight&&this.vertical.setEdge(e,t+1,n.borderRight[0],n.borderRight[1]))}}function dl(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function pl(e){var t;return{checksColumn:()=>!(s.A(e.if)||s.A(e.if.column_id)&&s.A(e.if.column_type)&&s.A(e.if.column_editable)),checksFilter:()=>!s.A(e.if)&&!s.A(e.if.filter_query),checksDataRow:()=>!s.A(e.if)&&!s.A(e.if.row_index),checksHeaderRow:()=>!s.A(e.if)&&!s.A(e.if.header_index),checksState:()=>{var t;return!s.A(null===(t=e.if)||void 0===t?void 0:t.state)},checksStateActive:()=>{var t;return"active"===(null===(t=e.if)||void 0===t?void 0:t.state)},checksStateSelected:()=>{var t;return"selected"===(null===(t=e.if)||void 0===t?void 0:t.state)},matchesActive:t=>function(e,t){return"active"!==(null==e?void 0:e.state)||t}(e.if,t),matchesColumn:t=>{return!e.if||!s.A(t)&&Xs(e.if,t&&t.id)&&(n=e.if,r=t&&t.type,!n||void 0===n.column_type||n.column_type===(r||Ge.$C.Any))&&function(e,t){return!e||void 0===e.column_editable||t===e.column_editable}(e.if,t&&t.editable);var n,r},matchesFilter:n=>!e.if||void 0===e.if.filter_query||(t=t||new Ns(e.if.filter_query)).evaluate(n),matchesDataRow:t=>Js(e.if,t),matchesHeaderRow:t=>function(e,t){if(!e||void 0===e.header_index)return!0;var n=e.header_index;return"string"==typeof n?t%2==("odd"===n?1:0):Array.isArray(n)?Q.A(t,n):t===n}(e.if,t),matchesSelected:t=>function(e,t){return"selected"!==(null==e?void 0:e.state)||t}(e.if,t),style:hl(e)}}function hl(e){return wt((e,t)=>{var n,r,o=(r=2,function(e){if(Array.isArray(e))return e}(n=t)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(n,r)||function(e,t){if(e){if("string"==typeof e)return dl(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?dl(e,t):void 0}}(n,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),i=o[0],a=o[1];return Gi.has(i)&&(e[Gi.get(i)]=a),e},{},Vi(e))}var fl=(0,f.ty)((e,t,n,r)=>me([e?[pl(e)]:[],g.A(pl,n||[]),t?[pl(t)]:[],g.A(pl,r||[])])),Al=(0,f.ty)((e,t,n,r)=>me([e?[pl(e)]:[],g.A(pl,n||[]),t?[pl(t)]:[],g.A(pl,r||[])])),vl=(0,f.ty)((e,t,n,r)=>me([e?[pl(e)]:[],g.A(pl,n||[]),t?[pl(t)]:[],g.A(pl,r||[])])),bl=(0,f.ty)((e,t)=>[hl(e),hl(t)]);function gl(e){for(var t={},n=0;n<e.length;++n)Object.assign(t,e[n].style);return Te(ll,t)}var ml=(e,t,n,r,o)=>i=>gl(el(e,t,n,r,o)(i)),yl=n(7246);function wl(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function El(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?wl(Object(n),!0).forEach(function(t){Cl(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):wl(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Cl(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var xl={backgroundColor:yl.A.supportsCssVariables?"var(--selected-background)":"rgba(255, 65, 54, 0.2)"},kl=(0,f.ty)((e,t,n,r)=>Hn(n,e,(e,n,o)=>ml(e,o+r.rows,n,!1,!1)(t))),Sl=(0,f.ty)((e,t,n,r,o,i,a)=>{e=Un(e);var s=a.length?a:i?[i]:[],l=n.filter(e=>!e.checksState()),c=n.filter(e=>e.checksStateSelected()),u=n.filter(e=>e.checksStateActive());return s.forEach(n=>{var a=n.row,s=n.column,d=a-o.rows,p=s-o.columns;if(!(d<0||p<0||e.length<=d||e[d].length<=p)){var h=nr(i,a,s),f=El(El(El(El({},ml(r[a],a+o.rows,t[s],h,!0)(l)),xl),ml(r[a],a+o.rows,t[s],h,!0)(c)),ml(r[a],a+o.rows,t[s],h,!0)(u));e[d][p]=f}}),e}),Ol=(0,f.ty)((e,t,n,r)=>Hn(n,Re.A(0,e),(e,n,o)=>((e,t)=>n=>gl(rl(e,t)(n)))(e,o+r.rows)(t))),Bl=function(){function e(e,t){this.xf=t,this.f=e}return e.prototype["@@transducer/init"]=q.A.init,e.prototype["@@transducer/result"]=function(e){return this.xf["@@transducer/result"](this.xf["@@transducer/step"](e,this.last))},e.prototype["@@transducer/step"]=function(e,t){return this.f(t)&&(this.last=t),e},e}();function _l(e){return function(t){return new Bl(e,t)}}var jl=(0,i.A)((0,N.A)([],_l,function(e,t){for(var n=t.length-1;n>=0;){if(e(t[n]))return t[n];n-=1}}));function Pl(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function Dl(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Fl=Xe(g.A);class Il{constructor(){Dl(this,"get",(0,f.B4)((e,t,n,r,o,i)=>Fl((t,a)=>g.A(e=>{var s=n[a],l=i&&i.length>s&&i[s]&&i[s][e.id]||o[e.id];return this.dropdown.get(e.id,a)(l,r,e,t)},e),t))),Dl(this,"dropdown",Zn()((e,t,n,r)=>{var o=jl(e=>{var t,o,i=(o=2,function(e){if(Array.isArray(e))return e}(t=e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(t,o)||function(e,t){if(e){if("string"==typeof e)return Pl(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Pl(e,t):void 0}}(t,o)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),a=i[0],l=i[1];return Xs(a.if,n.id)&&(s.A(a.if)||s.A(a.if.filter_query)||this.evaluation.get(n.id,l)(this.ast.get(n.id,l)(a.if.filter_query),r))},Xe(g.A)((e,t)=>[e,t],t));return o&&o[0]||e||void 0})),Dl(this,"ast",Zn()(e=>new Ns(e))),Dl(this,"evaluation",Zn()((e,t)=>e.evaluate(t)))}}function Tl(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Ml{get props(){return this.propsFn()}constructor(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(e=>new fa(e))(e),n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:(new Il).get,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:ka(),i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:kl(),a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:Sl(),s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:Ol(),l=arguments.length>7&&void 0!==arguments[7]?arguments[7]:(e=>new br(e))(e),c=arguments.length>8&&void 0!==arguments[8]?arguments[8]:fl();Tl(this,"propsFn",void 0),Tl(this,"cellContents",void 0),Tl(this,"cellDropdowns",void 0),Tl(this,"cellOperations",void 0),Tl(this,"dataPartialStyles",void 0),Tl(this,"dataStyles",void 0),Tl(this,"dataOpStyles",void 0),Tl(this,"cellWrappers",void 0),Tl(this,"relevantStyles",void 0),Tl(this,"getMarkdown",(0,f.B4)(e=>new aa(e))),Tl(this,"getCells",(0,f.B4)((e,t)=>jt(e,t,(e,t)=>e.length?e.concat(t):t))),Tl(this,"getDataOpCell",Zn()((e,t,n,r,i,a)=>o().cloneElement(e,{style:v([{borderBottom:n,borderLeft:r,borderRight:i,borderTop:a},t,e.props.style])}))),Tl(this,"getDataOpCells",(0,f.B4)((e,t,n)=>Kn(e,t,(e,t,r,o)=>{var i=n&&n.getStyle(r,o);return this.getDataOpCell.get(r,o)(e,t,i&&i.borderBottom,i&&i.borderLeft,i&&i.borderRight,i&&i.borderTop)}))),Tl(this,"getDataCell",Zn()((e,t,n,r,i,a,s)=>o().cloneElement(e,{children:[t],style:Me.A(n||{},{borderBottom:r,borderLeft:i,borderRight:a,borderTop:s})}))),Tl(this,"getDataCells",(0,f.B4)((e,t,n,r)=>$n(e,n,t,(e,t,n,o,i)=>{var a=r&&r.getStyle(o,i);return this.getDataCell.get(o,i)(e,n,t,a&&a.borderBottom,a&&a.borderLeft,a&&a.borderRight,a&&a.borderTop)}))),this.propsFn=e,this.cellContents=t,this.cellDropdowns=n,this.cellOperations=r,this.dataPartialStyles=i,this.dataStyles=a,this.dataOpStyles=s,this.cellWrappers=l,this.relevantStyles=c}createCells(e,t){var n=this.props,r=n.active_cell,o=n.applyFocus,i=n.dropdown_conditional,a=n.dropdown,s=n.data,l=n.dropdown_data,c=n.id,u=n.is_focused,d=n.loading_state,p=n.markdown_options,h=n.row_deletable,f=n.row_selectable,A=n.selected_cells,v=n.selected_rows,b=n.setProps,g=n.style_cell,m=n.style_cell_conditional,y=n.style_data,w=n.style_data_conditional,E=n.virtualized,C=n.visibleColumns,x=this.relevantStyles(g,y,m,w),k=this.dataPartialStyles(C,x,E.data,E.offset),S=this.dataStyles(k,C,x,E.data,E.offset,r,A),O=this.dataOpStyles((f?1:0)+(h?1:0),x,E.data,E.offset),B=this.cellDropdowns(C,E.data,E.indices,i,a,l),_=this.cellOperations(c,s,E.data,E.indices,f,h,v,b),j=this.cellWrappers.partialGet(C,E.data,E.offset),P=this.cellWrappers.get(j,E.offset,r,A),D=this.getMarkdown(p),F=this.cellContents.partialGet(C,E.data,E.offset,!!u,B,d,D),I=this.cellContents.get(F,r,o||!1,C,E.data,E.offset,!!u,B,d,D),T=this.getDataOpCells(_,O,t),M=this.getDataCells(P,I,S,e);return this.getCells(T,M)}}function Rl(e,t){var n={};return Yn(e,sl,(e,r,o)=>{var i=e.style[r]||e.style.border;i&&(n[r]=[i,null!=t?t:o])}),n}var zl=(e,t,n,r,o,i)=>a=>Rl(el(e,t,n,r,o)(a),i);function Nl(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Ll(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Nl(Object(n),!0).forEach(function(t){ql(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Nl(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function ql(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Wl=Number.MAX_SAFE_INTEGER,Vl=Number.MAX_SAFE_INTEGER-1,Ul=(0,f.ty)((e,t,n,r,o)=>{if(0!==n.length&&0!==e.length){var i=new ul(n.length,e.length,yl.A.defaultEdge,!0,!o);return Yn(n,e,(e,n,o,a)=>i.setEdges(o,a,zl(e,o+r.rows,n,!1,!1)(t))),i}}),Yl=(0,f.ty)((e,t,n,r,o,i,a)=>{if(!e)return e;var s=e.clone(),l=a.length?a:i?[i]:[],c=n.filter(e=>!e.checksState()),u=n.filter(e=>e.checksStateSelected()),d=n.filter(e=>e.checksStateActive());return l.forEach(e=>{var n=e.row,a=e.column,l=n-o.rows,p=a-o.columns;if(!(l<0||p<0||r.length<=l)){var h=nr(i,n,a),f=h?Wl:Vl,A=h?yl.A.activeEdge:yl.A.defaultEdge,v=Ll(Ll(Ll({},zl(r[l],l,t[a],h,!0,f)(c)),{},{borderBottom:[A,f],borderLeft:[A,f],borderRight:[A,f],borderTop:[A,f]},zl(r[l],l,t[a],h,!0,f)(u)),zl(r[l],l,t[a],h,!0,f)(d));s.setEdges(l,a,v)}}),s}),Hl=(0,f.ty)((e,t,n,r,o)=>{if(0!==n.length&&0!==e){var i=new ul(n.length,e,yl.A.defaultEdge,!0,!o);return Yn(n,Re.A(0,e),(e,n,o,a)=>i.setEdges(o,a,((e,t)=>n=>Rl(rl(e,t)(n)))(e,o+r.rows)(t))),i}}),Kl=(0,f.ty)((e,t,n,r,o)=>{if(t&&0!==e.length){var i=new ul(1,e.length,yl.A.defaultEdge,!0,!o);return Yn(Re.A(0,1),e,(e,t,o,a)=>{i.setEdges(o,a,(e=>t=>Rl(tl(e)(t)))(t)(r));var s=n.get(t.id.toString());s&&!s.isValid&&i.setEdges(o,a,{borderBottom:[yl.A.activeEdge,1/0],borderLeft:[yl.A.activeEdge,1/0],borderRight:[yl.A.activeEdge,1/0],borderTop:[yl.A.activeEdge,1/0]})}),i}}),$l=(0,f.ty)((e,t,n,r)=>{if(t&&0!==e){var o=new ul(1,e,yl.A.defaultEdge,!0,!r);return Yn(Re.A(0,1),Re.A(0,e),(e,t)=>o.setEdges(e,t,(e=>Rl(ol(e)))(n))),o}}),Gl=(0,f.ty)((e,t,n,r)=>{if(0!==t&&0!==e.length){var o=new ul(t,e.length,yl.A.defaultEdge,!0,!r);return Yn(Re.A(0,t),e,(e,t,r,i)=>o.setEdges(r,i,((e,t)=>n=>Rl(nl(e,t)(n)))(r,t)(n))),o}}),Zl=(0,f.ty)((e,t,n,r)=>{if(0!==t&&0!==e){var o=new ul(t,e,yl.A.defaultEdge,!0,!r);return Yn(Re.A(0,t),Re.A(0,e),(e,t)=>o.setEdges(e,t,(e=>t=>Rl(il(e)(t)))(e)(n))),o}});function Ql(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Xl{static clone(e){return e&&e.clone()}static hasPrecedence(e,t,n){return(t<=n||e===1/0)&&t<=e}hOverride(e,t,n){if(e&&t)for(var r=e.getMatrices().horizontal,o=t.getMatrices().horizontal,i=r.rows-1,a=0;a<r.columns;a++)Xl.hasPrecedence(r.getWeight(i,a),o.getWeight(0,a),n)&&o.setEdge(0,a,r.getEdge(i,a),1/0,!0),r.setEdge(i,a,"none",-1/0,!0)}vOverride(e,t,n){if(e&&t)for(var r=e.getMatrices().vertical,o=t.getMatrices().vertical,i=r.columns-1,a=0;a<r.rows;a++)Xl.hasPrecedence(r.getWeight(a,i),o.getWeight(a,0),n)&&o.setEdge(a,0,r.getEdge(a,i),1/0,!0),r.setEdge(a,i,"none",-1/0,!0)}hReconcile(e,t,n){if(e&&t){var r=t.getMatrices().horizontal,o=e.getMatrices().horizontal,i=o.rows-1;if(isFinite(i))for(var a=0;a<o.columns;a++)Xl.hasPrecedence(o.getWeight(i,a),r.getWeight(0,a),n)||o.setEdge(i,a,"none",-1/0,!0)}}vReconcile(e,t,n){if(e&&t)for(var r=t.getMatrices().vertical,o=e.getMatrices().vertical,i=o.columns-1,a=0;a<o.rows;a++)Xl.hasPrecedence(o.getWeight(a,i),r.getWeight(a,0),n)||o.setEdge(a,i,"none",-1/0,!0)}get props(){return this.propsFn()}constructor(e){Ql(this,"propsFn",void 0),Ql(this,"dataStyles",fl()),Ql(this,"filterStyles",Al()),Ql(this,"headerStyles",vl()),Ql(this,"getPartialDataEdges",Ul()),Ql(this,"getDataEdges",Yl()),Ql(this,"getDataOpEdges",Hl()),Ql(this,"getFilterEdges",Kl()),Ql(this,"getFilterOpEdges",$l()),Ql(this,"getHeaderEdges",Gl()),Ql(this,"getHeaderOpEdges",Zl()),Ql(this,"memoizedCreateEdges",(0,f.B4)((e,t,n,r,o,i,a,s,l,c,u,d,p,h,f,A,v,b,g,m)=>{var y=this.dataStyles(u,p,d,h),w=this.filterStyles(u,f,d,A),E=this.headerStyles(u,v,d,b),C=Le(t),x=this.getPartialDataEdges(n,y,g,m,c),k=this.getDataEdges(x,n,y,g,m,e,l),S=this.getDataOpEdges(r,y,g,m,c),O=this.getFilterEdges(n,o,i,w,c),B=this.getFilterOpEdges(r,o,w,c),_=this.getHeaderEdges(n,C,E,c),j=this.getHeaderOpEdges(r,C,E,c),P=(u?1:0)+d.length-1;return _=Xl.clone(_),j=Xl.clone(j),O=Xl.clone(O),B=Xl.clone(B),k=Xl.clone(k),S=Xl.clone(S),this.hReconcile(_,O||k,P),this.hReconcile(j,B||S,P),this.hReconcile(O,k,P),this.hReconcile(B,S,P),this.vReconcile(j,_,P),this.vReconcile(B,O,P),this.vReconcile(S,k,P),s===C?o?(this.hOverride(_,O,P),this.hOverride(j,B,P)):(this.hOverride(_,k,P),this.hOverride(j,S,P)):o&&s===C+1&&(this.hOverride(O,k,P),this.hOverride(B,S,P)),a===r&&(this.vOverride(j,_,P),this.vOverride(B,O,P),this.vOverride(S,k,P)),{dataEdges:k,dataOpEdges:S,filterEdges:O,filterOpEdges:B,headerEdges:_,headerOpEdges:j}})),this.propsFn=e}createEdges(){var e=this.props,t=e.active_cell,n=e.columns,r=e.filter_action,o=e.workFilter,i=e.fixed_columns,a=e.fixed_rows,s=e.row_deletable,l=e.row_selectable,c=e.selected_cells,u=e.style_as_list_view,d=e.style_cell,p=e.style_cell_conditional,h=e.style_data,f=e.style_data_conditional,A=e.style_filter,v=e.style_filter_conditional,b=e.style_header,g=e.style_header_conditional,m=e.virtualized,y=e.visibleColumns;return this.memoizedCreateEdges(t,n,y,(s?1:0)+(l?1:0),r.type!==Ge.vh.None,o.map,i,a,c,u,d,p,h,f,A,v,b,g,m.data,m.offset)}}function Jl(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function ec(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class tc extends r.PureComponent{get propsWithDefaults(){return this.props}UNSAFE_componentWillReceiveProps(e){var t=this.props.value,n=e.value;t!==n&&this.setState({value:n})}constructor(e){super(e),ec(this,"handleKeyDown",e=>{var t=this.propsWithDefaults,n=t.stopPropagation,r=t.updateOnEnter;n&&e.stopPropagation(),r&&e.keyCode===Be.ENTER&&this.submit()}),ec(this,"handleChange",e=>{this.setState({value:e.target.value})}),ec(this,"submit",()=>this.state.value!==this.props.value&&this.props.submit(this.state.value)),this.state={value:e.value}}render(){var e=this.propsWithDefaults,t=e.onCopy,n=e.onPaste,r=e.placeholder,i=e.updateOnBlur,a=e.updateOnSubmit,s={onBlur:i?this.submit:void 0,onKeyDown:this.handleKeyDown,onSubmit:a?this.submit:void 0};return o().createElement("input",function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Jl(Object(n),!0).forEach(function(t){ec(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Jl(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({type:"text",value:this.state.value||"",onChange:this.handleChange,onCopy:t,onPaste:n,placeholder:r},s))}}ec(tc,"defaultProps",{stopPropagation:!1,updateOnEnter:!0,updateOnBlur:!0,updateOnSubmit:!0});var nc=e=>{var t=e.filterOptions,n=e.toggleFilterOptions;return o().createElement("input",{type:"button",className:"dash-filter--case ".concat(t.case===Ge.ze.Sensitive?"dash-filter--case--sensitive":"dash-filter--case--insensitive"),onClick:n,title:"Toggle filter case sensitivity",value:"Aa"})};class rc extends r.PureComponent{constructor(e){var t,n,r;super(e),t=this,r=e=>{(0,this.props.setFilter)({target:{value:e}})},(n=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(n="submit"))in t?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r,this.state={value:e.value}}render(){var e=this.props,t=e.className,n=e.columnId,r=e.filterOptions,i=e.isValid,a=e.style,s=e.toggleFilterOptions,l=e.value;return o().createElement("th",{className:t+(i?"":" invalid"),"data-dash-column":n,style:a},o().createElement("div",null,o().createElement(tc,{onCopy:e=>{e.stopPropagation(),Nn.clearClipboard()},onPaste:e=>{e.stopPropagation()},value:l,placeholder:r.placeholder_text,stopPropagation:!0,submit:this.submit}),o().createElement(nc,{filterOptions:r,toggleFilterOptions:s})))}}var oc=(0,f.ty)((e,t)=>g.A(e=>(e=>t=>gl(tl(e)(t)))(e)(t),e)),ic=(0,f.ty)((e,t,n)=>Hn(Re.A(0,e),Re.A(0,t),()=>(e=>gl(ol(e)))(n))),ac=(0,f.ty)((e,t,n)=>Xe(g.A)(()=>[...n?[o().createElement("th",{key:"delete",className:"expanded-row--empty-cell dash-delete-header",style:{width:"30px",maxWidth:"30px",minWidth:"30px"}})]:[],...t?[o().createElement("th",{key:"select",className:"expanded-row--empty-cell dash-select-header",style:{width:"30px",maxWidth:"30px",minWidth:"30px"}})]:[]],Re.A(0,e))),sc=(e,t)=>e===t?new Map(t):e,lc=(0,f.ty)((e,t,n,r)=>{var o=((e,t)=>{if(e.isValid){var n=new Map,r=e.statements;return r?(r.forEach(e=>{if(e.lexeme.type===xa.UnaryOperator&&e.left){var r=e.left.lexeme.present?e.left.lexeme.present(e.left):e.left.value,o=Ft(e=>e.id.toString()===r,t);if(!o)throw new Error("column ".concat(r," not found"));n.set(r,new Qs(e.value,o))}else if(e.lexeme.type===xa.RelationalOperator&&e.left&&e.right){var i=e.left.lexeme.present?e.left.lexeme.present(e.left):e.left.value,a=Ft(e=>e.id.toString()===i,t);if(!a)throw new Error("column ".concat(i," not found"));e.lexeme.present&&e.lexeme.present(e)===Ua.Equal?n.set(i,new Qs("".concat(e.right.value),a)):n.set(i,new Qs("".concat(e.value," ").concat(e.right.value),a))}}),n):n}})(new Ds(n,t),r);if(!o)return e;var i=e;return Wt(et(Array.from(e.keys()),Array.from(o.keys()))).forEach(t=>{var n=e.get(t),r=o.get(t);s.A(r)?(i=sc(i,e)).delete(t):(s.A(n)||r.toQueryString()!==n.toQueryString())&&(i=sc(i,e)).set(t,r)}),i});function cc(e,t,n){var r=t.id.toString(),o=new Map(e);return n&&n.length?o.set(r,new Qs(n,t)):o.delete(r),o}function uc(e,t,n){var r=Array.from(e.values()),o=((e,t)=>g.A(e=>e.toQueryString(),U(e=>Boolean(null==e?void 0:e.query)&&e.isValid)(e)).join(" ".concat(t===Ge.iV.And?"&&":"||"," ")))(r,t);n(o,g.A(e=>e.query,U(e=>Boolean(null==e?void 0:e.query))(r)).join(t===Ge.iV.And?" && ":" || "),e)}var dc=(e,t,n,r,o)=>{uc(e=cc(e,t,r),n,o)};function pc(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var hc=[];class fc{get props(){return this.propsFn()}constructor(e){pc(this,"propsFn",void 0),pc(this,"filterStyles",oc()),pc(this,"filterOpStyles",ic()),pc(this,"relevantStyles",Al()),pc(this,"headerOperations",ac()),pc(this,"onChange",(e,t,n,r,o)=>{xe.Ay.debug("Filter -- onChange",e.id,o.target.value&&o.target.value.trim());var i=o.target.value.trim();dc(t,e,n,i,r)}),pc(this,"onToggleChange",(e,t,n,r,o,i)=>{var a=o(e);dc(t,a,n,i,r)}),pc(this,"filter",Zn()((e,t,n,r,i,a)=>{var s=n.get(e.id.toString());return o().createElement(rc,{key:"column-".concat(t),className:"dash-filter column-".concat(t),columnId:e.id,filterOptions:e.filter_options,isValid:!s||s.isValid,setFilter:this.onChange.bind(this,e,n,r,i),toggleFilterOptions:this.onToggleChange.bind(this,e,n,r,i).bind(this,a,s&&s.query),value:s&&s.query})})),pc(this,"wrapperStyles",(0,f.B4)((e,t)=>function(e,t){for(var n=e.length,r=new Array(n),o=0;o<n;++o)r[o]=t(e[o],o);return r}(e,(e,n)=>Me.A(e,t&&t.getStyle(0,n)||{})))),pc(this,"getCells",(0,f.B4)((e,t)=>[e.concat(t)])),pc(this,"getFilterCells",(0,f.B4)((e,t,n)=>jt(e,t,(e,t,r)=>o().cloneElement(e,{style:v([n&&n.getStyle(0,r),t,e.props.style])})))),pc(this,"getOpFilterCells",(0,f.B4)((e,t,n)=>jt(e,t,(e,t,r)=>o().cloneElement(e,{style:v([n&&n.getStyle(0,r),t,e.props.style])})))),this.propsFn=e}createFilters(e,t){var n=this.props,r=n.filter_action,o=n.map,i=n.row_deletable,a=n.row_selectable,s=n.setFilter,l=n.style_cell,c=n.style_cell_conditional,u=n.style_filter,d=n.style_filter_conditional,p=n.toggleFilterOptions,h=n.visibleColumns;if(r.type===Ge.vh.None)return hc;var f=this.relevantStyles(l,u,c,d),A=this.wrapperStyles(this.filterStyles(h,f),e),v=this.filterOpStyles(1,(a?1:0)+(i?1:0),f)[0],b=Xe(g.A)((e,t)=>this.filter.get(e.id,t)(e,t,o,r.operator,s,p),h),m=this.getFilterCells(b,A,e),y=this.headerOperations(1,a,i)[0],w=this.getOpFilterCells(y,v,t);return this.getCells(w,m)}}var Ac=(0,i.A)(function(e,t){for(var n=new zt,r=0;r<e.length;r+=1)n.add(e[r]);return Wt(L(n.has.bind(n),t))}),vc=Ac;function bc(e,t){return function(){return t.call(this,e.apply(this,arguments))}}var gc=(0,d.A)((0,Pe.A)("tail",De(1,1/0)));function mc(){if(0===arguments.length)throw new Error("pipe requires at least one argument");return(0,se.A)(arguments[0].length,wt(bc,arguments[0],gc(arguments)))}var yc=(0,d.A)(function(e){return(0,ne.A)(e)?e.split("").reverse().join(""):Array.prototype.slice.call(e,0).reverse()});function wc(){if(0===arguments.length)throw new Error("compose requires at least one argument");return mc.apply(this,yc(arguments))}var Ec=(0,i.A)(wc(Wt,Ze)),Cc=function(){function e(e,t){this.xf=t,this.f=e,this.all=!0}return e.prototype["@@transducer/init"]=q.A.init,e.prototype["@@transducer/result"]=function(e){return this.all&&(e=this.xf["@@transducer/step"](e,!0)),this.xf["@@transducer/result"](e)},e.prototype["@@transducer/step"]=function(e,t){return this.f(t)||(this.all=!1,e=ye(this.xf["@@transducer/step"](e,!1))),e},e}();function xc(e){return function(t){return new Cc(e,t)}}var kc,Sc=(0,i.A)((0,N.A)(["all"],xc,function(e,t){for(var n=0;n<t.length;){if(!e(t[n]))return!1;n+=1}return!0})),Oc=(0,i.A)(function(e,t){return Array.prototype.slice.call(t,0).sort(function(t,n){for(var r=0,o=0;0===r&&o<e.length;)r=e[o](t,n),o+=1;return r})}),Bc=Oc,_c=(0,d.A)(function(e){return function(t,n){return e(t,n)?-1:e(n,t)?1:0}});!function(e){e.Ascending="asc",e.Descending="desc",e.None="none"}(kc||(kc={}));var jc=(e,t)=>s.A(e),Pc=function(){function e(e,t){this.xf=t,this.f=e,this.idx=-1,this.found=!1}return e.prototype["@@transducer/init"]=q.A.init,e.prototype["@@transducer/result"]=function(e){return this.found||(e=this.xf["@@transducer/step"](e,-1)),this.xf["@@transducer/result"](e)},e.prototype["@@transducer/step"]=function(e,t){return this.idx+=1,this.f(t)&&(this.found=!0,e=ye(this.xf["@@transducer/step"](e,this.idx))),e},e}();function Dc(e){return function(t){return new Pc(e,t)}}var Fc=(0,i.A)((0,N.A)([],Dc,function(e,t){for(var n=0,r=t.length;n<r;){if(e(t[n]))return n;n+=1}return-1})),Ic=(e,t)=>{if(xe.Ay.trace("multi - update sortBy",e,t),e=Zt.A(e),t.direction===kc.None){var n=Fc(e=>e.column_id===t.column_id,e);-1!==n&&e.splice(n,1)}else{var r=Ft(e=>e.column_id===t.column_id,e);r?r.direction=t.direction:e.push(t)}return e},Tc=(e,t)=>(xe.Ay.trace("single - update sortBy",e,t),t.direction===kc.None?[]:[t]);function Mc(e){return Mc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Mc(e)}function Rc(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function zc(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Nc(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){zc(e,t,n[t])})}return e}function Lc(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=[],r=!0,o=!1,i=void 0;try{for(var a,s=e[Symbol.iterator]();!(r=(a=s.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(e){o=!0,i=e}finally{try{r||null==s.return||s.return()}finally{if(o)throw i}}return n}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}var qc=function(){},Wc={},Vc={},Uc={mark:qc,measure:qc};try{"undefined"!=typeof window&&(Wc=window),"undefined"!=typeof document&&(Vc=document),"undefined"!=typeof MutationObserver&&MutationObserver,"undefined"!=typeof performance&&(Uc=performance)}catch(e){}var Yc=(Wc.navigator||{}).userAgent,Hc=void 0===Yc?"":Yc,Kc=Wc,$c=Vc,Gc=Uc,Zc=(Kc.document,!!$c.documentElement&&!!$c.head&&"function"==typeof $c.addEventListener&&"function"==typeof $c.createElement),Qc=(~Hc.indexOf("MSIE")||Hc.indexOf("Trident/"),"___FONT_AWESOME___"),Xc="svg-inline--fa",Jc=(function(){try{return!0}catch(e){return!1}}(),[1,2,3,4,5,6,7,8,9,10]),eu=Jc.concat([11,12,13,14,15,16,17,18,19,20]),tu={GROUP:"group",SWAP_OPACITY:"swap-opacity",PRIMARY:"primary",SECONDARY:"secondary"},nu=(["xs","sm","lg","fw","ul","li","border","pull-left","pull-right","spin","pulse","rotate-90","rotate-180","rotate-270","flip-horizontal","flip-vertical","flip-both","stack","stack-1x","stack-2x","inverse","layers","layers-text","layers-counter",tu.GROUP,tu.SWAP_OPACITY,tu.PRIMARY,tu.SECONDARY].concat(Jc.map(function(e){return"".concat(e,"x")})).concat(eu.map(function(e){return"w-".concat(e)})),Kc.FontAwesomeConfig||{});$c&&"function"==typeof $c.querySelector&&[["data-family-prefix","familyPrefix"],["data-replacement-class","replacementClass"],["data-auto-replace-svg","autoReplaceSvg"],["data-auto-add-css","autoAddCss"],["data-auto-a11y","autoA11y"],["data-search-pseudo-elements","searchPseudoElements"],["data-observe-mutations","observeMutations"],["data-mutate-approach","mutateApproach"],["data-keep-original-source","keepOriginalSource"],["data-measure-performance","measurePerformance"],["data-show-missing-icons","showMissingIcons"]].forEach(function(e){var t=Lc(e,2),n=t[0],r=t[1],o=function(e){return""===e||"false"!==e&&("true"===e||e)}(function(e){var t=$c.querySelector("script["+e+"]");if(t)return t.getAttribute(e)}(n));null!=o&&(nu[r]=o)});var ru=Nc({},{familyPrefix:"fa",replacementClass:Xc,autoReplaceSvg:!0,autoAddCss:!0,autoA11y:!0,searchPseudoElements:!1,observeMutations:!0,mutateApproach:"async",keepOriginalSource:!0,measurePerformance:!1,showMissingIcons:!0},nu);ru.autoReplaceSvg||(ru.observeMutations=!1);var ou=Nc({},ru);Kc.FontAwesomeConfig=ou;var iu=Kc||{};iu[Qc]||(iu[Qc]={}),iu[Qc].styles||(iu[Qc].styles={}),iu[Qc].hooks||(iu[Qc].hooks={}),iu[Qc].shims||(iu[Qc].shims=[]);var au=iu[Qc],su=[];Zc&&(($c.documentElement.doScroll?/^loaded|^c/:/^loaded|^i|^c/).test($c.readyState)||$c.addEventListener("DOMContentLoaded",function e(){$c.removeEventListener("DOMContentLoaded",e),su.map(function(e){return e()})}));var lu,cu="pending",uu="settled",du="fulfilled",pu="rejected",hu=function(){},fu=void 0!==n.g&&void 0!==n.g.process&&"function"==typeof n.g.process.emit,Au="undefined"==typeof setImmediate?setTimeout:setImmediate,vu=[];function bu(){for(var e=0;e<vu.length;e++)vu[e][0](vu[e][1]);vu=[],lu=!1}function gu(e,t){vu.push([e,t]),lu||(lu=!0,Au(bu,0))}function mu(e){var t=e.owner,n=t._state,r=t._data,o=e[n],i=e.then;if("function"==typeof o){n=du;try{r=o(r)}catch(e){Cu(i,e)}}yu(i,r)||(n===du&&wu(i,r),n===pu&&Cu(i,r))}function yu(e,t){var n;try{if(e===t)throw new TypeError("A promises callback cannot return that same promise.");if(t&&("function"==typeof t||"object"===Mc(t))){var r=t.then;if("function"==typeof r)return r.call(t,function(r){n||(n=!0,t===r?Eu(e,r):wu(e,r))},function(t){n||(n=!0,Cu(e,t))}),!0}}catch(t){return n||Cu(e,t),!0}return!1}function wu(e,t){e!==t&&yu(e,t)||Eu(e,t)}function Eu(e,t){e._state===cu&&(e._state=uu,e._data=t,gu(ku,e))}function Cu(e,t){e._state===cu&&(e._state=uu,e._data=t,gu(Su,e))}function xu(e){e._then=e._then.forEach(mu)}function ku(e){e._state=du,xu(e)}function Su(e){e._state=pu,xu(e),!e._handled&&fu&&n.g.process.emit("unhandledRejection",e._data,e)}function Ou(e){n.g.process.emit("rejectionHandled",e)}function Bu(e){if("function"!=typeof e)throw new TypeError("Promise resolver "+e+" is not a function");if(this instanceof Bu==0)throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.");this._then=[],function(e,t){function n(e){Cu(t,e)}try{e(function(e){wu(t,e)},n)}catch(e){n(e)}}(e,this)}Bu.prototype={constructor:Bu,_state:cu,_then:null,_data:void 0,_handled:!1,then:function(e,t){var n={owner:this,then:new this.constructor(hu),fulfilled:e,rejected:t};return!t&&!e||this._handled||(this._handled=!0,this._state===pu&&fu&&gu(Ou,this)),this._state===du||this._state===pu?gu(mu,n):this._then.push(n),n.then},catch:function(e){return this.then(null,e)}},Bu.all=function(e){if(!Array.isArray(e))throw new TypeError("You must pass an array to Promise.all().");return new Bu(function(t,n){var r=[],o=0;function i(e){return o++,function(n){r[e]=n,--o||t(r)}}for(var a,s=0;s<e.length;s++)(a=e[s])&&"function"==typeof a.then?a.then(i(s),n):r[s]=a;o||t(r)})},Bu.race=function(e){if(!Array.isArray(e))throw new TypeError("You must pass an array to Promise.race().");return new Bu(function(t,n){for(var r,o=0;o<e.length;o++)(r=e[o])&&"function"==typeof r.then?r.then(t,n):t(r)})},Bu.resolve=function(e){return e&&"object"===Mc(e)&&e.constructor===Bu?e:new Bu(function(t){t(e)})},Bu.reject=function(e){return new Bu(function(t,n){n(e)})};var _u={size:16,x:0,y:0,rotate:0,flipX:!1,flipY:!1};function ju(){for(var e=12,t="";e-- >0;)t+="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"[62*Math.random()|0];return t}function Pu(e){return"".concat(e).replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function Du(e){return Object.keys(e||{}).reduce(function(t,n){return t+"".concat(n,": ").concat(e[n],";")},"")}function Fu(e){return e.size!==_u.size||e.x!==_u.x||e.y!==_u.y||e.rotate!==_u.rotate||e.flipX||e.flipY}function Iu(e){var t=e.transform,n=e.containerWidth,r=e.iconWidth,o={transform:"translate(".concat(n/2," 256)")},i="translate(".concat(32*t.x,", ").concat(32*t.y,") "),a="scale(".concat(t.size/16*(t.flipX?-1:1),", ").concat(t.size/16*(t.flipY?-1:1),") "),s="rotate(".concat(t.rotate," 0 0)");return{outer:o,inner:{transform:"".concat(i," ").concat(a," ").concat(s)},path:{transform:"translate(".concat(r/2*-1," -256)")}}}var Tu={x:0,y:0,width:"100%",height:"100%"};function Mu(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return e.attributes&&(e.attributes.fill||t)&&(e.attributes.fill="black"),e}var Ru=(ou.measurePerformance&&Gc&&Gc.mark&&Gc.measure,function(e,t,n,r){var o,i,a,s=Object.keys(e),l=s.length,c=void 0!==r?function(e,t){return function(n,r,o,i){return e.call(t,n,r,o,i)}}(t,r):t;for(void 0===n?(o=1,a=e[s[0]]):(o=0,a=n);o<l;o++)a=c(a,e[i=s[o]],i,e);return a});function zu(e,t){var n=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}).skipHooks,r=void 0!==n&&n,o=Object.keys(t).reduce(function(e,n){var r=t[n];return r.icon?e[r.iconName]=r.icon:e[n]=r,e},{});"function"!=typeof au.hooks.addPack||r?au.styles[e]=Nc({},au.styles[e]||{},o):au.hooks.addPack(e,o),"fas"===e&&zu("fa",t)}var Nu=au.styles,Lu=au.shims,qu=function(){var e=function(e){return Ru(Nu,function(t,n,r){return t[r]=Ru(n,e,{}),t},{})};e(function(e,t,n){return t[3]&&(e[t[3]]=n),e}),e(function(e,t,n){var r=t[2];return e[n]=n,r.forEach(function(t){e[t]=n}),e});var t="far"in Nu;Ru(Lu,function(e,n){var r=n[0],o=n[1],i=n[2];return"far"!==o||t||(o="fas"),e[r]={prefix:o,iconName:i},e},{})};function Wu(e,t,n){if(e&&e[t]&&e[t][n])return{prefix:t,iconName:n,icon:e[t][n]}}function Vu(e){var t=e.tag,n=e.attributes,r=void 0===n?{}:n,o=e.children,i=void 0===o?[]:o;return"string"==typeof e?Pu(e):"<".concat(t," ").concat(function(e){return Object.keys(e||{}).reduce(function(t,n){return t+"".concat(n,'="').concat(Pu(e[n]),'" ')},"").trim()}(r),">").concat(i.map(Vu).join(""),"</").concat(t,">")}qu(),au.styles;function Uu(e){this.name="MissingIcon",this.message=e||"Icon unavailable",this.stack=(new Error).stack}Uu.prototype=Object.create(Error.prototype),Uu.prototype.constructor=Uu;var Yu={fill:"currentColor"},Hu={attributeType:"XML",repeatCount:"indefinite",dur:"2s"},Ku=(Nc({},Yu,{d:"M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z"}),Nc({},Hu,{attributeName:"opacity"}));function $u(e){var t=e[0],n=e[1],r=Lc(e.slice(4),1)[0];return{found:!0,width:t,height:n,icon:Array.isArray(r)?{tag:"g",attributes:{class:"".concat(ou.familyPrefix,"-").concat(tu.GROUP)},children:[{tag:"path",attributes:{class:"".concat(ou.familyPrefix,"-").concat(tu.SECONDARY),fill:"currentColor",d:r[0]}},{tag:"path",attributes:{class:"".concat(ou.familyPrefix,"-").concat(tu.PRIMARY),fill:"currentColor",d:r[1]}}]}:{tag:"path",attributes:{fill:"currentColor",d:r}}}}Nc({},Yu,{cx:"256",cy:"364",r:"28"}),Nc({},Hu,{attributeName:"r",values:"28;14;28;28;14;28;"}),Nc({},Ku,{values:"1;0;1;1;0;1;"}),Nc({},Yu,{opacity:"1",d:"M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z"}),Nc({},Ku,{values:"1;0;0;0;0;1;"}),Nc({},Yu,{opacity:"0",d:"M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z"}),Nc({},Ku,{values:"0;0;1;1;0;0;"}),au.styles,au.styles;var Gu=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.definitions={}}var t,n;return t=e,n=[{key:"add",value:function(){for(var e=this,t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];var o=n.reduce(this._pullDefinitions,{});Object.keys(o).forEach(function(t){e.definitions[t]=Nc({},e.definitions[t]||{},o[t]),zu(t,o[t]),qu()})}},{key:"reset",value:function(){this.definitions={}}},{key:"_pullDefinitions",value:function(e,t){var n=t.prefix&&t.iconName&&t.icon?{0:t}:t;return Object.keys(n).map(function(t){var r=n[t],o=r.prefix,i=r.iconName,a=r.icon;e[o]||(e[o]={}),e[o][i]=a}),e}}],n&&Rc(t.prototype,n),e}();function Zu(){ou.autoAddCss&&!ed&&(function(e){if(e&&Zc){var t=$c.createElement("style");t.setAttribute("type","text/css"),t.innerHTML=e;for(var n=$c.head.childNodes,r=null,o=n.length-1;o>-1;o--){var i=n[o],a=(i.tagName||"").toUpperCase();["STYLE","LINK"].indexOf(a)>-1&&(r=i)}$c.head.insertBefore(t,r)}}(function(){var e="fa",t=Xc,n=ou.familyPrefix,r=ou.replacementClass,o='svg:not(:root).svg-inline--fa {\n  overflow: visible;\n}\n\n.svg-inline--fa {\n  display: inline-block;\n  font-size: inherit;\n  height: 1em;\n  overflow: visible;\n  vertical-align: -0.125em;\n}\n.svg-inline--fa.fa-lg {\n  vertical-align: -0.225em;\n}\n.svg-inline--fa.fa-w-1 {\n  width: 0.0625em;\n}\n.svg-inline--fa.fa-w-2 {\n  width: 0.125em;\n}\n.svg-inline--fa.fa-w-3 {\n  width: 0.1875em;\n}\n.svg-inline--fa.fa-w-4 {\n  width: 0.25em;\n}\n.svg-inline--fa.fa-w-5 {\n  width: 0.3125em;\n}\n.svg-inline--fa.fa-w-6 {\n  width: 0.375em;\n}\n.svg-inline--fa.fa-w-7 {\n  width: 0.4375em;\n}\n.svg-inline--fa.fa-w-8 {\n  width: 0.5em;\n}\n.svg-inline--fa.fa-w-9 {\n  width: 0.5625em;\n}\n.svg-inline--fa.fa-w-10 {\n  width: 0.625em;\n}\n.svg-inline--fa.fa-w-11 {\n  width: 0.6875em;\n}\n.svg-inline--fa.fa-w-12 {\n  width: 0.75em;\n}\n.svg-inline--fa.fa-w-13 {\n  width: 0.8125em;\n}\n.svg-inline--fa.fa-w-14 {\n  width: 0.875em;\n}\n.svg-inline--fa.fa-w-15 {\n  width: 0.9375em;\n}\n.svg-inline--fa.fa-w-16 {\n  width: 1em;\n}\n.svg-inline--fa.fa-w-17 {\n  width: 1.0625em;\n}\n.svg-inline--fa.fa-w-18 {\n  width: 1.125em;\n}\n.svg-inline--fa.fa-w-19 {\n  width: 1.1875em;\n}\n.svg-inline--fa.fa-w-20 {\n  width: 1.25em;\n}\n.svg-inline--fa.fa-pull-left {\n  margin-right: 0.3em;\n  width: auto;\n}\n.svg-inline--fa.fa-pull-right {\n  margin-left: 0.3em;\n  width: auto;\n}\n.svg-inline--fa.fa-border {\n  height: 1.5em;\n}\n.svg-inline--fa.fa-li {\n  width: 2em;\n}\n.svg-inline--fa.fa-fw {\n  width: 1.25em;\n}\n\n.fa-layers svg.svg-inline--fa {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0;\n}\n\n.fa-layers {\n  display: inline-block;\n  height: 1em;\n  position: relative;\n  text-align: center;\n  vertical-align: -0.125em;\n  width: 1em;\n}\n.fa-layers svg.svg-inline--fa {\n  -webkit-transform-origin: center center;\n          transform-origin: center center;\n}\n\n.fa-layers-counter, .fa-layers-text {\n  display: inline-block;\n  position: absolute;\n  text-align: center;\n}\n\n.fa-layers-text {\n  left: 50%;\n  top: 50%;\n  -webkit-transform: translate(-50%, -50%);\n          transform: translate(-50%, -50%);\n  -webkit-transform-origin: center center;\n          transform-origin: center center;\n}\n\n.fa-layers-counter {\n  background-color: #ff253a;\n  border-radius: 1em;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  color: #fff;\n  height: 1.5em;\n  line-height: 1;\n  max-width: 5em;\n  min-width: 1.5em;\n  overflow: hidden;\n  padding: 0.25em;\n  right: 0;\n  text-overflow: ellipsis;\n  top: 0;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: top right;\n          transform-origin: top right;\n}\n\n.fa-layers-bottom-right {\n  bottom: 0;\n  right: 0;\n  top: auto;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: bottom right;\n          transform-origin: bottom right;\n}\n\n.fa-layers-bottom-left {\n  bottom: 0;\n  left: 0;\n  right: auto;\n  top: auto;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: bottom left;\n          transform-origin: bottom left;\n}\n\n.fa-layers-top-right {\n  right: 0;\n  top: 0;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: top right;\n          transform-origin: top right;\n}\n\n.fa-layers-top-left {\n  left: 0;\n  right: auto;\n  top: 0;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: top left;\n          transform-origin: top left;\n}\n\n.fa-lg {\n  font-size: 1.3333333333em;\n  line-height: 0.75em;\n  vertical-align: -0.0667em;\n}\n\n.fa-xs {\n  font-size: 0.75em;\n}\n\n.fa-sm {\n  font-size: 0.875em;\n}\n\n.fa-1x {\n  font-size: 1em;\n}\n\n.fa-2x {\n  font-size: 2em;\n}\n\n.fa-3x {\n  font-size: 3em;\n}\n\n.fa-4x {\n  font-size: 4em;\n}\n\n.fa-5x {\n  font-size: 5em;\n}\n\n.fa-6x {\n  font-size: 6em;\n}\n\n.fa-7x {\n  font-size: 7em;\n}\n\n.fa-8x {\n  font-size: 8em;\n}\n\n.fa-9x {\n  font-size: 9em;\n}\n\n.fa-10x {\n  font-size: 10em;\n}\n\n.fa-fw {\n  text-align: center;\n  width: 1.25em;\n}\n\n.fa-ul {\n  list-style-type: none;\n  margin-left: 2.5em;\n  padding-left: 0;\n}\n.fa-ul > li {\n  position: relative;\n}\n\n.fa-li {\n  left: -2em;\n  position: absolute;\n  text-align: center;\n  width: 2em;\n  line-height: inherit;\n}\n\n.fa-border {\n  border: solid 0.08em #eee;\n  border-radius: 0.1em;\n  padding: 0.2em 0.25em 0.15em;\n}\n\n.fa-pull-left {\n  float: left;\n}\n\n.fa-pull-right {\n  float: right;\n}\n\n.fa.fa-pull-left,\n.fas.fa-pull-left,\n.far.fa-pull-left,\n.fal.fa-pull-left,\n.fab.fa-pull-left {\n  margin-right: 0.3em;\n}\n.fa.fa-pull-right,\n.fas.fa-pull-right,\n.far.fa-pull-right,\n.fal.fa-pull-right,\n.fab.fa-pull-right {\n  margin-left: 0.3em;\n}\n\n.fa-spin {\n  -webkit-animation: fa-spin 2s infinite linear;\n          animation: fa-spin 2s infinite linear;\n}\n\n.fa-pulse {\n  -webkit-animation: fa-spin 1s infinite steps(8);\n          animation: fa-spin 1s infinite steps(8);\n}\n\n@-webkit-keyframes fa-spin {\n  0% {\n    -webkit-transform: rotate(0deg);\n            transform: rotate(0deg);\n  }\n  100% {\n    -webkit-transform: rotate(360deg);\n            transform: rotate(360deg);\n  }\n}\n\n@keyframes fa-spin {\n  0% {\n    -webkit-transform: rotate(0deg);\n            transform: rotate(0deg);\n  }\n  100% {\n    -webkit-transform: rotate(360deg);\n            transform: rotate(360deg);\n  }\n}\n.fa-rotate-90 {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";\n  -webkit-transform: rotate(90deg);\n          transform: rotate(90deg);\n}\n\n.fa-rotate-180 {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";\n  -webkit-transform: rotate(180deg);\n          transform: rotate(180deg);\n}\n\n.fa-rotate-270 {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";\n  -webkit-transform: rotate(270deg);\n          transform: rotate(270deg);\n}\n\n.fa-flip-horizontal {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";\n  -webkit-transform: scale(-1, 1);\n          transform: scale(-1, 1);\n}\n\n.fa-flip-vertical {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";\n  -webkit-transform: scale(1, -1);\n          transform: scale(1, -1);\n}\n\n.fa-flip-both, .fa-flip-horizontal.fa-flip-vertical {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";\n  -webkit-transform: scale(-1, -1);\n          transform: scale(-1, -1);\n}\n\n:root .fa-rotate-90,\n:root .fa-rotate-180,\n:root .fa-rotate-270,\n:root .fa-flip-horizontal,\n:root .fa-flip-vertical,\n:root .fa-flip-both {\n  -webkit-filter: none;\n          filter: none;\n}\n\n.fa-stack {\n  display: inline-block;\n  height: 2em;\n  position: relative;\n  width: 2.5em;\n}\n\n.fa-stack-1x,\n.fa-stack-2x {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0;\n}\n\n.svg-inline--fa.fa-stack-1x {\n  height: 1em;\n  width: 1.25em;\n}\n.svg-inline--fa.fa-stack-2x {\n  height: 2em;\n  width: 2.5em;\n}\n\n.fa-inverse {\n  color: #fff;\n}\n\n.sr-only {\n  border: 0;\n  clip: rect(0, 0, 0, 0);\n  height: 1px;\n  margin: -1px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  width: 1px;\n}\n\n.sr-only-focusable:active, .sr-only-focusable:focus {\n  clip: auto;\n  height: auto;\n  margin: 0;\n  overflow: visible;\n  position: static;\n  width: auto;\n}\n\n.svg-inline--fa .fa-primary {\n  fill: var(--fa-primary-color, currentColor);\n  opacity: 1;\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa .fa-secondary {\n  fill: var(--fa-secondary-color, currentColor);\n  opacity: 0.4;\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-primary {\n  opacity: 0.4;\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-secondary {\n  opacity: 1;\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa mask .fa-primary,\n.svg-inline--fa mask .fa-secondary {\n  fill: black;\n}\n\n.fad.fa-inverse {\n  color: #fff;\n}';if(n!==e||r!==t){var i=new RegExp("\\.".concat(e,"\\-"),"g"),a=new RegExp("\\--".concat(e,"\\-"),"g"),s=new RegExp("\\.".concat(t),"g");o=o.replace(i,".".concat(n,"-")).replace(a,"--".concat(n,"-")).replace(s,".".concat(r))}return o}()),ed=!0)}function Qu(e){var t=e.prefix,n=void 0===t?"fa":t,r=e.iconName;if(r)return Wu(Ju.definitions,n,r)||Wu(au.styles,n,r)}var Xu,Ju=new Gu,ed=!1,td={transform:function(e){return function(e){var t={size:16,x:0,y:0,flipX:!1,flipY:!1,rotate:0};return e?e.toLowerCase().split(" ").reduce(function(e,t){var n=t.toLowerCase().split("-"),r=n[0],o=n.slice(1).join("-");if(r&&"h"===o)return e.flipX=!0,e;if(r&&"v"===o)return e.flipY=!0,e;if(o=parseFloat(o),isNaN(o))return e;switch(r){case"grow":e.size=e.size+o;break;case"shrink":e.size=e.size-o;break;case"left":e.x=e.x-o;break;case"right":e.x=e.x+o;break;case"up":e.y=e.y-o;break;case"down":e.y=e.y+o;break;case"rotate":e.rotate=e.rotate+o}return e},t):t}(e)}},nd=(Xu=function(e){var t,n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=r.transform,i=void 0===o?_u:o,a=r.symbol,s=void 0!==a&&a,l=r.mask,c=void 0===l?null:l,u=r.maskId,d=void 0===u?null:u,p=r.title,h=void 0===p?null:p,f=r.titleId,A=void 0===f?null:f,v=r.classes,b=void 0===v?[]:v,g=r.attributes,m=void 0===g?{}:g,y=r.styles,w=void 0===y?{}:y;if(e){var E=e.prefix,C=e.iconName,x=e.icon;return t=Nc({type:"icon"},e),n=function(){return Zu(),ou.autoA11y&&(h?m["aria-labelledby"]="".concat(ou.replacementClass,"-title-").concat(A||ju()):(m["aria-hidden"]="true",m.focusable="false")),function(e){var t=e.icons,n=t.main,r=t.mask,o=e.prefix,i=e.iconName,a=e.transform,s=e.symbol,l=e.title,c=e.maskId,u=e.titleId,d=e.extra,p=e.watchable,h=void 0!==p&&p,f=r.found?r:n,A=f.width,v=f.height,b="fak"===o,g=b?"":"fa-w-".concat(Math.ceil(A/v*16)),m=[ou.replacementClass,i?"".concat(ou.familyPrefix,"-").concat(i):"",g].filter(function(e){return-1===d.classes.indexOf(e)}).filter(function(e){return""!==e||!!e}).concat(d.classes).join(" "),y={children:[],attributes:Nc({},d.attributes,{"data-prefix":o,"data-icon":i,class:m,role:d.attributes.role||"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 ".concat(A," ").concat(v)})},w=b&&!~d.classes.indexOf("fa-fw")?{width:"".concat(A/v*16*.0625,"em")}:{};h&&(y.attributes["data-fa-i2svg"]=""),l&&y.children.push({tag:"title",attributes:{id:y.attributes["aria-labelledby"]||"title-".concat(u||ju())},children:[l]});var E=Nc({},y,{prefix:o,iconName:i,main:n,mask:r,maskId:c,transform:a,symbol:s,styles:Nc({},w,d.styles)}),C=r.found&&n.found?function(e){var t,n=e.children,r=e.attributes,o=e.main,i=e.mask,a=e.maskId,s=e.transform,l=o.width,c=o.icon,u=i.width,d=i.icon,p=Iu({transform:s,containerWidth:u,iconWidth:l}),h={tag:"rect",attributes:Nc({},Tu,{fill:"white"})},f=c.children?{children:c.children.map(Mu)}:{},A={tag:"g",attributes:Nc({},p.inner),children:[Mu(Nc({tag:c.tag,attributes:Nc({},c.attributes,p.path)},f))]},v={tag:"g",attributes:Nc({},p.outer),children:[A]},b="mask-".concat(a||ju()),g="clip-".concat(a||ju()),m={tag:"mask",attributes:Nc({},Tu,{id:b,maskUnits:"userSpaceOnUse",maskContentUnits:"userSpaceOnUse"}),children:[h,v]},y={tag:"defs",children:[{tag:"clipPath",attributes:{id:g},children:(t=d,"g"===t.tag?t.children:[t])},m]};return n.push(y,{tag:"rect",attributes:Nc({fill:"currentColor","clip-path":"url(#".concat(g,")"),mask:"url(#".concat(b,")")},Tu)}),{children:n,attributes:r}}(E):function(e){var t=e.children,n=e.attributes,r=e.main,o=e.transform,i=Du(e.styles);if(i.length>0&&(n.style=i),Fu(o)){var a=Iu({transform:o,containerWidth:r.width,iconWidth:r.width});t.push({tag:"g",attributes:Nc({},a.outer),children:[{tag:"g",attributes:Nc({},a.inner),children:[{tag:r.icon.tag,children:r.icon.children,attributes:Nc({},r.icon.attributes,a.path)}]}]})}else t.push(r.icon);return{children:t,attributes:n}}(E),x=C.children,k=C.attributes;return E.children=x,E.attributes=k,s?function(e){var t=e.prefix,n=e.iconName,r=e.children,o=e.attributes,i=e.symbol;return[{tag:"svg",attributes:{style:"display: none;"},children:[{tag:"symbol",attributes:Nc({},o,{id:!0===i?"".concat(t,"-").concat(ou.familyPrefix,"-").concat(n):i}),children:r}]}]}(E):function(e){var t=e.children,n=e.main,r=e.mask,o=e.attributes,i=e.styles,a=e.transform;if(Fu(a)&&n.found&&!r.found){var s={x:n.width/n.height/2,y:.5};o.style=Du(Nc({},i,{"transform-origin":"".concat(s.x+a.x/16,"em ").concat(s.y+a.y/16,"em")}))}return[{tag:"svg",attributes:o,children:t}]}(E)}({icons:{main:$u(x),mask:c?$u(c.icon):{found:!1,width:null,height:null,icon:{}}},prefix:E,iconName:C,transform:Nc({},_u,i),symbol:s,title:h,maskId:d,titleId:A,extra:{attributes:m,styles:w,classes:b}})},Object.defineProperty(t,"abstract",{get:n}),Object.defineProperty(t,"html",{get:function(){return t.abstract.map(function(e){return Vu(e)})}}),Object.defineProperty(t,"node",{get:function(){if(Zc){var e=$c.createElement("div");return e.innerHTML=t.html,e.children}}}),t}},function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=(e||{}).icon?e:Qu(e||{}),r=t.mask;return r&&(r=(r||{}).icon?r:Qu(r||{})),Xu(n,Nc({},t,{mask:r}))});function rd(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function od(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?rd(Object(n),!0).forEach(function(t){ad(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):rd(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function id(e){return id="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},id(e)}function ad(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function sd(e){return function(e){if(Array.isArray(e))return ld(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return ld(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ld(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ld(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function cd(e){return t=e,(t-=0)==t?e:(e=e.replace(/[\-_\s]+(.)?/g,function(e,t){return t?t.toUpperCase():""})).substr(0,1).toLowerCase()+e.substr(1);var t}var ud=["style"],dd=!1;try{dd=!0}catch(e){}function pd(e){return e&&"object"===id(e)&&e.prefix&&e.iconName&&e.icon?e:td.icon?td.icon(e):null===e?null:e&&"object"===id(e)&&e.prefix&&e.iconName?e:Array.isArray(e)&&2===e.length?{prefix:e[0],iconName:e[1]}:"string"==typeof e?{prefix:"fas",iconName:e}:void 0}function hd(e,t){return Array.isArray(t)&&t.length>0||!Array.isArray(t)&&t?ad({},e,t):{}}var fd={border:!1,className:"",mask:null,maskId:null,fixedWidth:!1,inverse:!1,flip:!1,icon:null,listItem:!1,pull:null,pulse:!1,rotation:null,size:null,spin:!1,spinPulse:!1,spinReverse:!1,beat:!1,fade:!1,beatFade:!1,bounce:!1,shake:!1,symbol:!1,title:"",titleId:null,transform:null,swapOpacity:!1},Ad=o().forwardRef(function(e,t){var n=od(od({},fd),e),r=n.icon,o=n.mask,i=n.symbol,a=n.className,s=n.title,l=n.titleId,c=n.maskId,u=pd(r),d=hd("classes",[].concat(sd(function(e){var t,n=e.beat,r=e.fade,o=e.beatFade,i=e.bounce,a=e.shake,s=e.flash,l=e.spin,c=e.spinPulse,u=e.spinReverse,d=e.pulse,p=e.fixedWidth,h=e.inverse,f=e.border,A=e.listItem,v=e.flip,b=e.size,g=e.rotation,m=e.pull,y=(ad(t={"fa-beat":n,"fa-fade":r,"fa-beat-fade":o,"fa-bounce":i,"fa-shake":a,"fa-flash":s,"fa-spin":l,"fa-spin-reverse":u,"fa-spin-pulse":c,"fa-pulse":d,"fa-fw":p,"fa-inverse":h,"fa-border":f,"fa-li":A,"fa-flip":!0===v,"fa-flip-horizontal":"horizontal"===v||"both"===v,"fa-flip-vertical":"vertical"===v||"both"===v},"fa-".concat(b),null!=b),ad(t,"fa-rotate-".concat(g),null!=g&&0!==g),ad(t,"fa-pull-".concat(m),null!=m),ad(t,"fa-swap-opacity",e.swapOpacity),t);return Object.keys(y).map(function(e){return y[e]?e:null}).filter(function(e){return e})}(n)),sd((a||"").split(" ")))),p=hd("transform","string"==typeof n.transform?td.transform(n.transform):n.transform),h=hd("mask",pd(o)),f=nd(u,od(od(od(od({},d),p),h),{},{symbol:i,title:s,titleId:l,maskId:c}));if(!f)return function(){var e;!dd&&console&&"function"==typeof console.error&&(e=console).error.apply(e,arguments)}("Could not find icon",u),null;var A=f.abstract,v={ref:t};return Object.keys(n).forEach(function(e){fd.hasOwnProperty(e)||(v[e]=n[e])}),vd(A[0],v)});Ad.displayName="FontAwesomeIcon",Ad.propTypes={beat:kr().bool,border:kr().bool,beatFade:kr().bool,bounce:kr().bool,className:kr().string,fade:kr().bool,flash:kr().bool,mask:kr().oneOfType([kr().object,kr().array,kr().string]),maskId:kr().string,fixedWidth:kr().bool,inverse:kr().bool,flip:kr().oneOf([!0,!1,"horizontal","vertical","both"]),icon:kr().oneOfType([kr().object,kr().array,kr().string]),listItem:kr().bool,pull:kr().oneOf(["right","left"]),pulse:kr().bool,rotation:kr().oneOf([0,90,180,270]),shake:kr().bool,size:kr().oneOf(["2xs","xs","sm","lg","xl","2xl","1x","2x","3x","4x","5x","6x","7x","8x","9x","10x"]),spin:kr().bool,spinPulse:kr().bool,spinReverse:kr().bool,symbol:kr().oneOfType([kr().bool,kr().string]),title:kr().string,titleId:kr().string,transform:kr().oneOfType([kr().string,kr().object]),swapOpacity:kr().bool};var vd=function e(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("string"==typeof n)return n;var o=(n.children||[]).map(function(n){return e(t,n)}),i=Object.keys(n.attributes||{}).reduce(function(e,t){var r=n.attributes[t];switch(t){case"class":e.attrs.className=r,delete n.attributes.class;break;case"style":e.attrs.style=r.split(";").map(function(e){return e.trim()}).filter(function(e){return e}).reduce(function(e,t){var n,r=t.indexOf(":"),o=cd(t.slice(0,r)),i=t.slice(r+1).trim();return o.startsWith("webkit")?e[(n=o,n.charAt(0).toUpperCase()+n.slice(1))]=i:e[o]=i,e},{});break;default:0===t.indexOf("aria-")||0===t.indexOf("data-")?e.attrs[t.toLowerCase()]=r:e.attrs[cd(t)]=r}return e},{attrs:{}}),a=r.style,s=void 0===a?{}:a,l=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(r,ud);return i.attrs.style=od(od({},i.attrs.style),s),t.apply(void 0,[n.tag,od(od({},i.attrs),l)].concat(sd(o)))}.bind(null,o().createElement);function bd(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function gd(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function md(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var yd=(e,t,n,r,o,i,a,s,l,c,u,d)=>()=>{var p=e(n,r,i,a,s,d),h=Ue(n,r,a,s);e===He&&vc(t,h).length>0&&(p.selected_columns=ga(h,t)),c(p);var f=[];Z.A(e=>{var t=r.find(t=>t.id===e);t&&f.push(t)},h),((e,t,n,r)=>{t.forEach(t=>{e=cc(e,t,"")}),uc(e,n,r)})(u,f,o,l)};function wd(e,t,n,r){return()=>{var o;switch(xd(e,t)){case kc.Descending:o=kc.None;break;case kc.Ascending:o=kc.Descending;break;case kc.None:default:o=kc.Ascending}var i=n===Ge.Ie.Single?Tc:Ic;r(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?gd(Object(n),!0).forEach(function(t){md(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):gd(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({sort_by:i(t,{column_id:e,direction:o})},$e))}}function Ed(e,t,n,r,o){return()=>{var i=function(e,t,n,r){var o=window.prompt("Enter a new column name");return null===o?null:function(e,t,n,r,o){var i=t,a=Le(i),s=i.findIndex(t=>t.id===e.id);if("string"==typeof e.name&&a>1){var l=Array(a).fill(e.name),c=Me.A(e,{name:l});(i=i.slice(0))[s]=c}var u=Ve(e,i,n,r,s,!0),d=u.groupIndexFirst,p=u.groupIndexLast;return Re.A(d,p+1).map(e=>{var t=[e,"name"];"Array"===ze.A(i[e].name)&&t.push(n),i=k(P(t),o,i)}),{columns:i}}(e,t,n,r,o)}(e,t,n,o);i&&r(i)}}function Cd(e,t,n,r,o,i,a,s){if(a&&!s)return()=>{};var l=Ue(t,n,r,i,!0);return a?()=>o({selected_columns:l}):s?()=>o({selected_columns:Ec(e,l)}):()=>o({selected_columns:ga(l,e)})}function xd(e,t){var n=Ft(t=>t.column_id===e,t);return n?n.direction:kc.None}function kd(e,t){switch(xd(e,t)){case kc.Descending:return"sort-down";case kc.Ascending:return"sort-up";case kc.None:default:return"sort"}}var Sd=(0,f.ty)(function(e,t,n,r,i,a,s,l,c,u,d,p,h,f,A,v,b){return Xe(g.A)((m,y)=>{var w,E,C=(E=2,function(e){if(Array.isArray(e))return e}(w=m)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(w,E)||function(e,t){if(e){if("string"==typeof e)return bd(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?bd(e,t):void 0}}(w,E)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),x=C[0],k=C[1],S=a.length-1,O=y===S;return Xe(g.A)((a,g)=>{var m,w=t[a];m=b?a===Ut(k)?x.length-a:k[g+1]-a:1;var E=f!==Ge.vh.Custom&&qn(y,S,w.clearable),C=f!==Ge.vh.Custom&&qn(y,S,w.deletable),B=qn(y,S,w.hideable),_=qn(y,S,w.renamable),j=qn(y,S,w.selectable),P=t.length===m,D=Ue(w,n,y,b,!0),F=j&&("single"!==l||c.length===D.length)&&Sc(e=>-1!==c.indexOf(e),D);return o().createElement("div",{key:a},o().createElement("div",{className:"column-actions"},l&&j?o().createElement("span",{className:"column-header--select"},o().createElement("input",{checked:F,onChange:Cd(c,w,n,y,v,b,"single"===l,!F),name:"column-select-".concat(e),type:"single"===l?"radio":"checkbox"})):null,u!==Ge.vh.None&&O?o().createElement("span",{className:"column-header--sort",onClick:wd(w.id,p,d,v)},o().createElement(Ad,{icon:kd(w.id,p)})):null,_?o().createElement("span",{className:"column-header--edit",onClick:Ed(w,n,y,v,b)},o().createElement(Ad,{icon:"pencil-alt"})):null,E?o().createElement("span",{className:"column-header--clear",onClick:yd(Ye,c,w,n,h,t,y,b,A,v,s,i)},o().createElement(Ad,{icon:"eraser"})):null,C?o().createElement("span",{className:"column-header--delete"+(P?" disabled":""),onClick:P?void 0:yd(He,c,w,n,h,t,y,b,A,v,s,i)},o().createElement(Ad,{icon:["far","trash-alt"]})):null,B?o().createElement("span",{className:"column-header--hide"+(P?" disabled":""),onClick:P?void 0:()=>{var e=Ke(w,t,y,b),n=r?Ec(r,e):e;v({hidden_columns:n})}},o().createElement(Ad,{icon:["far","eye-slash"]})):null),o().createElement("span",{className:"column-header-name"},x[a]))},k)},a)});function Od(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function Bd(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class _d{constructor(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:fr(e);Bd(this,"handlers",void 0),Bd(this,"get",(e,t,n)=>t.map((t,r)=>{var o,i,a=(i=2,function(e){if(Array.isArray(e))return e}(o=t)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(o,i)||function(e,t){if(e){if("string"==typeof e)return Od(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Od(e,t):void 0}}(o,i)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),s=a[0],l=a[1];return l.map((t,o)=>{var i,a=e[t];return i=n?t===Ut(l)?s.length-t:l[o+1]-t:1,this.wrapper.get(r,t)(t,a.id,i,t===e.length-1||t===Ut(l),this.handlers(tr.EnterHeader,r,t),this.handlers(tr.Leave,r,t),this.handlers(tr.MoveHeader,r,t))})})),Bd(this,"wrapper",Zn()((e,t,n,r,i,a,s)=>o().createElement("th",{key:"header-cell-".concat(e),"data-dash-column":t,colSpan:n,className:"dash-header "+"column-".concat(e," ")+(r?"cell--right-last ":""),onMouseEnter:i,onMouseLeave:a,onMouseMove:s}))),this.handlers=t}}var jd=(0,f.ty)((e,t,n)=>Hn(Re.A(0,t),e,(e,t)=>((e,t)=>n=>gl(nl(e,t)(n)))(e,t)(n))),Pd=(0,f.ty)((e,t,n)=>Hn(Re.A(0,e),Re.A(0,t),e=>(e=>t=>gl(il(e)(t)))(e)(n)));function Dd(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Fd{get props(){return this.propsFn()}constructor(e){Dd(this,"propsFn",void 0),Dd(this,"headerContent",Sd()),Dd(this,"headerOperations",ac()),Dd(this,"headerStyles",jd()),Dd(this,"headerOpStyles",Pd()),Dd(this,"headerWrappers",(()=>new _d(()=>this.props))()),Dd(this,"relevantStyles",vl()),Dd(this,"labelsAndIndices",Vn()),Dd(this,"filterMergedCells",(0,f.B4)((e,t)=>{for(var n=[],r=0;r<e.length;r++){for(var o=[],i=0;i<e[r].length;i++)t[r][1].includes(i)&&o.push(e[r][i]);n.push(o)}return n})),Dd(this,"getCells",(0,f.B4)((e,t)=>jt(e,t,(e,t)=>Array.prototype.concat(e,t)))),Dd(this,"getHeaderOpCells",(0,f.B4)((e,t,n)=>Kn(e,t,(e,t,r,i)=>o().cloneElement(e,{style:v([n&&n.getStyle(r,i),t,e.props.style])})))),Dd(this,"getHeaderCells",(0,f.B4)((e,t,n,r)=>$n(e,n,t,(e,t,n,i,a)=>o().cloneElement(e,{children:[n],style:Me.A(t||{},r&&r.getStyle(i,a)||{})})))),this.propsFn=e}createHeaders(e,t){var n=this.props,r=n.column_selectable,o=n.columns,i=n.data,a=n.filter_action,s=n.hidden_columns,l=n.id,c=n.map,u=n.merge_duplicate_headers,d=n.page_action,p=n.row_deletable,h=n.row_selectable,f=n.selected_columns,A=n.setFilter,v=n.setProps,b=n.sort_action,g=n.sort_by,m=n.sort_mode,y=n.style_cell,w=n.style_cell_conditional,E=n.style_header,C=n.style_header_conditional,x=n.visibleColumns,k=this.labelsAndIndices(o,x,u),S=k.length,O=this.relevantStyles(y,E,w,C),B=this.headerOperations(S,h,p),_=this.headerStyles(x,S,O),j=this.headerOpStyles(S,(h?1:0)+(p?1:0),O),P=this.headerWrappers.get(x,k,u),D=this.headerContent(l,x,o,s,i,k,c,r,f,b,m,g,a.operator,d,A,v,u),F=this.getHeaderOpCells(B,j,t),I=this.filterMergedCells(_,k),T=this.getHeaderCells(P,D,I,e);return this.getCells(F,T)}}function Id(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Td(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Id(Object(n),!0).forEach(function(t){Md(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Id(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Md(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Rd=(e,t,n,r,o)=>{e(Td({filter_query:n},$e)),t({workFilter:{map:o,value:n},rawFilterQuery:r})},zd=e=>{var t=(0,f.B4)((e,t)=>Rd.bind(void 0,e,t)),n=(0,f.B4)((e,t)=>n=>{var r=[...t],o=t.indexOf(n),i=Td({},r[o]);return i.filter_options=Td(Td({},i.filter_options),{},{case:i.filter_options.case===Ge.ze.Insensitive?Ge.ze.Sensitive:Ge.ze.Insensitive}),r.splice(o,1,i),e({columns:r}),i}),r=new Ml(e),o=()=>{var r=e();return function(e,t,n){var r=e();return Me.A(r,{map:r.workFilter.map,setFilter:t,toggleFilterOptions:n})}(e,t(r.setProps,r.setState),n(r.setProps,r.columns))},i=new fc(o),a=new Fd(o),s=new Xl(e),l=(0,f.B4)((e,t,n)=>{var r=[];return r.push(...n),r.push(...t),r.push(...e),r});return()=>{var e=s.createEdges(),t=r.createCells(e.dataEdges,e.dataOpEdges),n=i.createFilters(e.filterEdges,e.filterOpEdges),o=a.createHeaders(e.headerEdges,e.headerOpEdges);return l(t,n,o)}},Nd=(0,n(9034).A)(4,[],function(e,t,n,r){var o=yt(function(n,r){return e(n,r)?t(n,r):ye(n)});return de(o,n,r)});function Ld(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function qd(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ld(Object(n),!0).forEach(function(t){Wd(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ld(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Wd(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Vd(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e?o().createElement("table",{className:"cell-table",tabIndex:-1},o().createElement("tbody",null,e.map((e,n)=>o().createElement("tr",{key:"row-".concat(n+t)},e)))):null}var Ud=e=>o().cloneElement(e,qd(qd({},e.props),{},{className:e.props.className?"".concat(e.props.className," phantom-cell"):"phantom-cell"}),"th"===e.type||"td"===e.type?null:e.props.children),Yd=e=>!e||0===e.length||0===e[0].length,Hd=(0,f.ty)((e,t,n,r)=>{var i=t=>Nd(t=>t.count<e,(e,t)=>(e.cells++,e.count+=t.props.colSpan||1,e),{cells:0,count:0},t).cells,a=e?g.A(t=>{var n,r=i(t),a=t.slice(0,r).map((t,n)=>{return r=t,i=e-n-1,o().cloneElement(r,qd(qd({},r.props),{},{colSpan:s.A(r.props.colSpan)?r.props.colSpan:Math.min(r.props.colSpan,i)}));var r,i}).concat(t.slice(r).map(Ud));return a[r-1]=(n=a[r-1],o().cloneElement(n,qd(qd({},n.props),{},{className:n.props.className?"".concat(n.props.className," last-of-type"):"last-of-type"}))),a},n):null;n=s.A(a)?n:g.A(e=>{var t=i(e);return e.slice(0,t).map(Ud).concat(e.slice(t))},n);var l=t?n.slice(0,t):null;n=n.slice(t);var c=t&&a?a.slice(0,t):null;return a=a&&a.slice(t),{grid:[[Vd(c),Vd(l)],[Vd(a),Vd(n,r)]],empty:[[Yd(c),Yd(l)],[Yd(a),Yd(n)]]}}),Kd=wt((0,i.A)(function(e,t){return Number(e)+Number(t)}),0),$d=n(794),Gd=2147483647;function Zd(e){return"number"==typeof e?e:0}function Qd(e){return"number"==typeof e?e:Gd}var Xd,Jd=(0,f.B4)((e,t,n,r,o,i,a,s)=>{var l,c=function(e,t,n,r,o,i){if(e){var a=e.header,s=e.id,l=e.row;if(void 0!==s&&void 0!==l){var c,u=a?void 0:jl(e=>{return!e.if||Xs(e.if,s)&&Js(e.if,l)&&(t=e.if,n=i.data[l-i.offset.rows],!t||void 0===t.filter_query||function(e,t){return e.isValid&&e.evaluate(t)}(new Ns(t.filter_query),n));var t,n},r);if(u)return u;if(a){var d=null==n?void 0:n[s];c=Array.isArray(d)?null==d?void 0:d[l]:d}else{var p;c=null==t||null===(p=t[l])||void 0===p?void 0:p[s]}if(c)return c;var h=null==o?void 0:o[s],f=h&&"string"!=typeof h?h.use_with:$d.F.Both;return f===$d.F.Both||f===$d.F.Header===a?h:void 0}}}(e,t,n,r,o,i),u=Zd(a),d=Qd(s),p=$d.D.Text;return c&&("string"==typeof c?l=c:(u=function(e,t){return"number"==typeof e||null===e?Zd(e):t}(c.delay,u),d=function(e,t){return"number"==typeof e||null===e?Qd(e):t}(c.duration,d),p=c.type||$d.D.Text,l=c.value)),{delay:u,duration:d,type:p,value:l}}),ep=n(726);function tp(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function np(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}!function(e){e.Bottom="bottom",e.Left="left",e.Right="right",e.Top="top"}(Xd||(Xd={}));class rp extends r.Component{constructor(e){super(e),this.state={md:new Li}}UNSAFE_componentWillReceiveProps(e){var t=e.tooltip,n=t.delay,r=t.duration;(0,ep.n)(Te(["arrow"],this.props),Te(["arrow"],e))||this.setState({display:!1,displayTooltipId:Boolean(clearTimeout(this.state.displayTooltipId))||setTimeout(()=>this.setState({display:!0}),n),hideTooltipId:Boolean(clearTimeout(this.state.hideTooltipId))||setTimeout(()=>this.setState({display:!1}),Math.min(n+r,Gd))})}render(){var e=this.props,t=e.arrow,n=e.className,r=this.props.tooltip,i=r.type,a=r.value,s=this.state.md;if(!i||!a)return null;var l=i===$d.D.Text?{children:a}:{dangerouslySetInnerHTML:{__html:s.render(a)}},c=this.state.display;return o().createElement("div",{ref:this.props.divRef,className:"dash-tooltip","data-attr-anchor":t,style:{visibility:c?"visible":"hidden"}},o().createElement("div",function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?tp(Object(n),!0).forEach(function(t){np(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):tp(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({className:n},l)))}}function op(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function ip(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class ap extends r.Component{constructor(e){super(e),ip(this,"tooltipRef",void 0),ip(this,"updateBounds",e=>{this.setState({cell:e})}),this.tooltipRef=(0,r.createRef)(),this.state={arrow:Xd.Bottom}}shouldComponentUpdate(e,t){return this.adjustPosition(),!(0,ep.n)(this.props,e)||!(0,ep.n)(this.state,t)}componentDidUpdate(){this.adjustPosition()}render(){var e=this.state.arrow;return o().createElement(rp,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?op(Object(n),!0).forEach(function(t){ip(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):op(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({key:"tooltip",divRef:this.tooltipRef,arrow:e},this.props))}adjustPosition(){var e=this.state.cell,t=this.tooltipRef.current,n=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;if(e){for(var t=e;"relative"!==getComputedStyle(t).position&&"sticky"!==getComputedStyle(t).position&&t.parentElement;)t=t.parentElement;return t}}(t);if(n&&e&&t){var r=n.getBoundingClientRect(),o=e.getBoundingClientRect(),i=t.clientWidth,a=t.clientHeight,s=Math.max(parseFloat(getComputedStyle(t,":before").borderWidth||"0"),parseFloat(getComputedStyle(t,":after").borderWidth||"0")),l=(o.width-i)/2,c=o.left-r.left+n.scrollLeft+l,u=o.top-r.top+n.scrollTop+o.height,d=c+r.left,p=d+i,h=u+r.top+a+s,f=Xd.Top;c-=Math.min(0,d),c-=Math.max(0,p-document.documentElement.clientWidth),h>document.documentElement.clientHeight&&(u-=a+s+o.height,f=Xd.Bottom),t.style.top="".concat(u,"px"),t.style.left="".concat(c,"px"),t.style.position="absolute",this.state.arrow!==f&&this.setState({arrow:f})}}}class sp extends r.Component{constructor(e){var t,n,r;super(e),t=this,r=e=>{var t=this.props.paginator,n=parseInt(e,10);isNaN(n)||t.loadPage(n-1)},(n=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(n="goToPage"))in t?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r}render(){var e,t=this.props,n=t.paginator,r=t.page_current;if(void 0!==n.lastPage&&n.lastPage<=0)return null;var i=Math.max(3,((null!==(e=n.lastPage)&&void 0!==e?e:0)+1).toString().length),a="".concat(i+1,"ch");return o().createElement("div",{className:"previous-next-container"},o().createElement("button",{className:"first-page",onClick:n.loadFirst,disabled:!n.hasPrevious()},o().createElement(Ad,{icon:"angle-double-left"})),o().createElement("button",{className:"previous-page",onClick:n.loadPrevious,disabled:!n.hasPrevious()},o().createElement(Ad,{icon:"angle-left"})),o().createElement("div",{className:"page-number"},o().createElement("div",{className:"current-page-container"},o().createElement("div",{className:"current-page-shadow",style:{minWidth:a}},(r+1).toString()),o().createElement("input",{type:"text",className:"current-page",style:{minWidth:a},onBlur:e=>{this.goToPage(e.target.value),e.target.value=""},onKeyDown:e=>{e.keyCode===Be.ENTER&&e.currentTarget.blur()},placeholder:(r+1).toString(),defaultValue:""})),void 0!==n.lastPage?" / ":"",void 0!==n.lastPage?o().createElement("div",{className:"last-page",style:{minWidth:a}},n.lastPage+1):""),o().createElement("button",{className:"next-page",onClick:n.loadNext,disabled:!n.hasNext()},o().createElement(Ad,{icon:"angle-right"})),o().createElement("button",{className:"last-page",onClick:n.loadLast,disabled:void 0===n.lastPage||n.isLast()},o().createElement(Ad,{icon:"angle-double-right"})))}}function lp(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function cp(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function up(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var dp=["table","tooltip","r0c0","r0c1","r1c0","r1c1","r1"],pp={width:"100%"},hp={minHeight:"100%",minWidth:"100%"},fp=e=>'[data-dash-column="'.concat(CSS.escape(e),'"]:not(.phantom-cell)');class Ap extends r.PureComponent{constructor(e){var t;super(e),t=this,up(this,"menuRef",o().createRef()),up(this,"stylesheet",new Oe("#".concat(CSS.escape(this.props.id)))),up(this,"tableFn",zd(()=>this.props)),up(this,"tableFragments",Hd()),up(this,"tableStyle",bl()),up(this,"labelsAndIndices",Vn()),up(this,"calculateTableStyle",(0,f.B4)(e=>v(this.tableStyle(pp,e)))),up(this,"tableRefs",void 0),up(this,"getLexerResult",(0,f.B4)(Tt.bind(void 0,zs))),up(this,"handleClick",e=>{this.containsActiveElement()&&this.props.is_focused&&this.props.setProps({is_focused:!1});var t=this.menuRef;this.props.activeMenu&&t&&t.current&&!t.current.contains(e.target)&&this.props.setState({activeMenu:void 0})}),up(this,"handleClipboardEvent",(e,t)=>{this.containsActiveElement()&&t(e)}),up(this,"handleCopy",e=>{this.handleClipboardEvent(e,this.onCopy)}),up(this,"handlePaste",e=>{this.handleClipboardEvent(e,this.onPaste)}),up(this,"resetFragmentCells",e=>{var t=e.querySelectorAll("table.cell-table > tbody > tr:last-of-type > *");t.length&&(Array.from(t).forEach(this.clearCellWidth),Array.from(e.querySelectorAll("table.cell-table > tbody > tr > th:first-of-type")).map(e=>e.parentElement).forEach(e=>{var t=Array.from(null==e?void 0:e.children);t&&t.forEach(this.clearCellWidth)}))}),up(this,"resizeFragmentCells",(e,t)=>{var n=e.querySelectorAll("table.cell-table > tbody > tr:last-of-type > *");n.length&&(Array.from(n).forEach((e,n)=>this.setCellWidth(e,t[n])),Array.from(e.querySelectorAll("table.cell-table > tbody > tr > th:first-of-type")).map(e=>e.parentElement).forEach(e=>{var n=Array.from(null==e?void 0:e.children);n&&(n.length===t.length?n.forEach((e,n)=>this.setCellWidth(e,t[n])):n.forEach(e=>this.setCellWidth(e,0)))}))}),up(this,"resizeFragmentTable",(e,t)=>{e&&(e.style.width=t)}),up(this,"isDisplayed",e=>"none"!==getComputedStyle(e).display),up(this,"forceHandleResize",()=>this.handleResize()),up(this,"getScrollbarWidthOnce",b.A(_t)),up(this,"handleResizeIf",(0,f.B4)(function(){var e=t.tableRefs,n=e.r0c0,r=e.r0c1,o=e.r1c0,i=e.r1c1;i.current&&i.current&&n.current&&r.current&&o.current&&t.isDisplayed(i.current)&&(r.current.style.marginLeft="",i.current.style.marginLeft="",n.current.style.width="",o.current.style.width="",[n,r,o].forEach(e=>{if(e.current){var n=e.current.querySelector("table");n&&(n.style.width=""),t.resetFragmentCells(e.current)}}),t.handleResize())})),up(this,"handleResize",function(){var e,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:NaN,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=t.props,i=o.fixed_columns,a=o.fixed_rows,s=o.setState,l=t.tableRefs,c=l.r1,u=l.r1c1,d=l.r0c0,p=l.r0c1,h=l.r1c0;if(u.current&&c.current&&u.current&&d.current&&p.current&&h.current&&t.isDisplayed(u.current)){t.getScrollbarWidthOnce(c.current).then(e=>s({scrollbarWidth:e}));var f=d.current.querySelector("table"),A=p.current.querySelector("table"),v=h.current.querySelector("table"),b=u.current.querySelector("table"),g=getComputedStyle(b).width;if(r||(t.resizeFragmentTable(f,g),t.resizeFragmentTable(A,g),t.resizeFragmentTable(v,g)),i||a){var m=Array.from(u.current.querySelectorAll("table.cell-table > tbody > tr:last-of-type > *")).map(e=>e.getBoundingClientRect().width);r||(t.resizeFragmentCells(d.current,m),t.resizeFragmentCells(p.current,m),t.resizeFragmentCells(h.current,m))}if(i){var y=u.current.querySelector("tr:first-of-type > *:nth-of-type(".concat(i,")"));if(y){var w,E=y.getBoundingClientRect().right-(null===(w=u.current)||void 0===w?void 0:w.getBoundingClientRect().left);d.current.style.width="".concat(E,"px"),h.current.style.width="".concat(E,"px")}}var C=null===(e=u.current)||void 0===e?void 0:e.querySelector("tr:first-of-type > *:nth-of-type(".concat(i+1,")"));if(C){var x,k=null===(x=u.current)||void 0===x?void 0:x.getBoundingClientRect(),S=C.getBoundingClientRect().left-k.left;p.current.style.marginLeft="-".concat(S+c.current.scrollLeft,"px"),u.current.style.marginLeft="-".concat(S,"px")}if(!r){var O=parseInt(g,10),B=parseInt(getComputedStyle(b).width,10);B!==O&&t.handleResize(O,B===n)}}}),up(this,"handleKeyDown",e=>{var t=this.props,n=t.setProps,r=t.is_focused;if(xe.Ay.trace("handleKeyDown: ".concat(e.key)),o=e.keyCode,![Be.CONTROL,Be.COMMAND_LEFT,Be.COMMAND_RIGHT,Be.COMMAND_FIREFOX].includes(o)){var o,i=(e=>(e.ctrlKey||e.metaKey)&&!e.altKey)(e);i&&e.keyCode===Be.V||e.keyCode===Be.C&&i&&!r||(e.keyCode!==Be.ESCAPE?(!r&&je(e.keyCode)&&this.switchCell(e),r&&!je(e.keyCode)||(e.keyCode!==Be.TAB&&e.keyCode!==Be.ENTER?e.keyCode!==Be.BACKSPACE&&e.keyCode!==Be.DELETE||this.deleteCell(e):this.switchCell(e))):n({is_focused:!1}))}}),up(this,"switchCell",e=>{var t=e,n=this.props,r=n.active_cell,o=n.selected_cells,i=n.start_cell,a=n.end_cell,s=n.setProps,l=n.viewport,c=n.visibleColumns;if(e.preventDefault(),r){this.$el.focus();var u=o.length>1,d=t.keyCode===Be.ENTER||t.keyCode===Be.TAB;if(u&&d)s({is_focused:!1,active_cell:this.getNextCell(t,{currentCell:r,restrictToSelection:!0})});else if(t.shiftKey){var p=Ct(o),h=p.minRow,f=p.minCol,A=p.maxRow,v=p.maxCol,b=t.keyCode===Be.ARROW_DOWN||t.keyCode===Be.ENTER,g=t.keyCode===Be.ARROW_UP,m=t.keyCode===Be.ARROW_RIGHT||t.keyCode===Be.TAB,y=t.keyCode===Be.ARROW_LEFT,w=i&&i.row,E=i&&i.column,C=a&&a.row,x=a&&a.column;if(b)r.row>h?C=++h:A<l.data.length-1&&(C=++A);else if(g)r.row<A?C=--A:h>0&&(C=--h);else if(m)r.column>f?x=++f:v<c.length-1&&(x=++v);else{if(!y)return;r.column<v?x=--v:f>0&&(x=--f)}var k=Bt({minRow:h,maxRow:A,minCol:f,maxCol:v},c,l),S={is_focused:!1,end_cell:Ot(C,x,c,l),selected_cells:k},O=C===h?A:h,B=x===f?v:f;w===O&&E===B||(S.start_cell=Ot(O,B,c,l)),s(S)}else{var _=this.getNextCell(t,{currentCell:r,restrictToSelection:!1});s({is_focused:!1,selected_cells:[_],active_cell:_,start_cell:_,end_cell:_})}}else xe.Ay.warning("Trying to change cell, but no cell is active.")}),up(this,"deleteCell",e=>{var t=this.props,n=t.data,r=t.selected_cells,o=t.setProps,i=t.viewport,a=t.visibleColumns;e.preventDefault();var s=n;g.A(e=>[i.indices[e.row],e.column],r).forEach(e=>{var t=a[e[1]];if(t.editable){var n=Fn(null,t);s=k(P([e[0],t.id]),n.success?n.value:"",s)}}),o({data:s})}),up(this,"getNextCell",(e,t)=>{var n,r=t.restrictToSelection,o=t.currentCell,i=this.props,a=i.selected_cells,s=i.viewport,l=i.visibleColumns,c=e,u=o.row,d=o.column;switch(c.keyCode){case Be.ARROW_LEFT:n=r?xt([u,d-1],a):[u,$(0,d-1)];break;case Be.ARROW_RIGHT:case Be.TAB:n=r?xt([u,d+1],a):[u,G(l.length-1,d+1)];break;case Be.ARROW_UP:n=r?xt([u-1,d],a):[$(0,u-1),d];break;case Be.ARROW_DOWN:case Be.ENTER:n=r?xt([u+1,d],a):[G(s.data.length-1,u+1),d];break;default:throw new Error("Table.getNextCell: unknown navigation keycode ".concat(c.keyCode))}return Ot(n[0],n[1],l,s)}),up(this,"onCopy",e=>{var t=this.props,n=t.selected_cells,r=t.viewport,o=t.columns,i=t.visibleColumns,a=t.include_headers_on_copy_paste;n.length&&Nn.toClipboard(e,n,o,i,r.data,a),this.$el.focus()}),up(this,"onPaste",e=>{var t=this.props,n=t.active_cell,r=t.columns,o=t.data,i=t.editable,a=t.filter_query,s=t.loading_state,l=t.setProps,c=t.sort_by,u=t.viewport,d=t.visibleColumns,p=t.include_headers_on_copy_paste;if(i&&n&&!s){var h=Nn.fromClipboard(e,n,u.indices,r,d,o,!0,!c.length||!a.length,p);h&&l(h)}}),up(this,"handleDropdown",()=>{var e=this.tableRefs.r1c1;e.current&&Ln(e.current.querySelector(".Select-menu-outer"))}),up(this,"onScroll",e=>{var t=this.tableRefs,n=t.r0c0,r=t.r0c1;if(n.current&&r.current){xe.Ay.trace("ControlledTable fragment scrolled to (left,top)=(".concat(e.target.scrollLeft,",").concat(e.target.scrollTop,")"));var o=parseFloat(e.target.scrollLeft)+(parseFloat(n.current.style.width)||0);r.current.style.marginLeft="".concat(-o,"px"),this.updateUiViewport(),this.handleDropdown(),this.adjustTooltipPosition()}}),up(this,"toggleColumn",(e,t,n)=>{var r=this.props,o=r.columns,i=r.hidden_columns,a=r.setProps,s=Ke(e,o,t,n),l=i?i.slice(0):[];s.forEach(e=>{var t=l.indexOf(e);t>=0?l.splice(t,1):l.push(e)}),a({hidden_columns:l})}),this.updateStylesheet(),this.tableRefs=dp.reduce((e,t)=>(e[t]=(0,r.createRef)(),e),{})}get lexerResult(){var e=this.props.filter_query;return this.getLexerResult(e)}updateStylesheet(){var e=this.props.css;Z.A(e=>{var t=e.selector,n=e.rule;this.stylesheet.setRule(t,n)},e)}updateUiViewport(){var e,t=this.props,n=t.setState,r=t.uiViewport;if(t.virtualization){var o=null===(e=this.tableRefs.r1c1.current)||void 0===e?void 0:e.parentElement;r&&r.scrollLeft===o.scrollLeft&&r.scrollTop===o.scrollTop&&r.height===o.clientHeight&&r.width===o.clientWidth||n({uiViewport:{scrollLeft:o.scrollLeft,scrollTop:o.scrollTop,height:o.clientHeight,width:o.clientWidth}})}}componentDidMount(){window.addEventListener("resize",this.forceHandleResize),document.addEventListener("mousedown",this.handleClick),document.addEventListener("paste",this.handlePaste),document.addEventListener("copy",this.handleCopy);var e=this.props,t=e.active_cell,n=e.selected_cells,r=e.setProps;n.length&&t&&!Q.A(t,n)&&r({active_cell:n[0]}),this.updateUiViewport(),this.handleResize()}componentWillUnmount(){window.removeEventListener("resize",this.forceHandleResize),document.removeEventListener("mousedown",this.handleClick),document.removeEventListener("paste",this.handlePaste),document.removeEventListener("copy",this.handleCopy)}componentDidUpdate(){var e,t;this.updateStylesheet(),this.updateUiViewport();var n=this.props,r=n.fixed_columns,o=n.fixed_rows;(r||o)&&this.handleResizeIf(...J(this.props)),this.handleDropdown(),this.adjustTooltipPosition();var i=this.props.active_cell;if(this.containsActiveElement()){var a=this.getActiveCellAttributes();if(a&&i&&(a.column_id!==(null==i?void 0:i.column_id)||a.row!==(null==i?void 0:i.row))){var s=i.column_id,l=i.row,c=this.$el.querySelector('td[data-dash-row="'.concat(l,'"]').concat(fp(s)));c&&c.focus()}}var u=this.props,d=u.setState,p=u.uiCell;if(u.virtualization&&!p){var h=this.tableRefs.r1c1,f=null===(e=h.current)||void 0===e?void 0:e.querySelector("tr > td:first-of-type");if(f){var A=null===(t=h.current)||void 0===t?void 0:t.querySelectorAll("tr th:first-of-type");void 0!==A&&d({uiCell:{height:f.clientHeight},uiHeaders:g.A(e=>({height:e.clientHeight}),Array.from(A))})}}}clearCellWidth(e){e.style.width="",e.style.minWidth="",e.style.maxWidth="",e.style.boxSizing=""}get $el(){return document.getElementById(this.props.id)}containsActiveElement(){var e=this.$el;return e&&e.contains(document.activeElement)}getActiveCellAttributes(){for(var e=document.activeElement;e&&"td"!==e.nodeName.toLowerCase();)e=e.parentElement;if(e){var t=e.getAttribute("data-dash-column"),n=e.getAttribute("data-dash-row");return{column_id:t,row:+(null!=n?n:0)}}}getColumnRef(e,t){if(0===e){if(0===t)return this.tableRefs.r0c0;if(1===t)return this.tableRefs.r0c1}if(1==e){if(0===t)return this.tableRefs.r1c0;if(1===t)return this.tableRefs.r1c1}}get displayPagination(){var e=this.props,t=e.data,n=e.page_action,r=e.page_size;return n===Ge.vh.Native&&r<t.length||n===Ge.vh.Custom}render(){var e=this.props,t=e.columns,n=e.id,r=e.tooltip_conditional,i=e.tooltip,a=e.currentTooltip,s=e.fill_width,l=e.filter_action,c=e.fixed_columns,u=e.fixed_rows,d=e.loading_state,p=e.scrollbarWidth,h=e.style_as_list_view,f=e.style_table,A=e.tooltip_data,v=e.tooltip_delay,b=e.tooltip_duration,m=e.tooltip_header,y=e.uiCell,w=e.uiHeaders,E=e.uiViewport,C=e.viewport,x=e.virtualized,k=e.virtualization,S=e.visibleColumns,O=[[u&&c?"dash-fixed-row dash-fixed-column":"",u?"dash-fixed-row":""],[c?"dash-fixed-column":"","dash-fixed-content"]],B=this.tableFn(),_=this.tableFragments(c,u,B,x.offset.rows),j=_.grid,P=_.empty,D=["dash-spreadsheet",...k?["dash-virtualized"]:[],...u?["dash-freeze-top"]:[],...c?["dash-freeze-left"]:[],...h?["dash-list-view"]:[],...P[0][1]?["dash-empty-01"]:[],...P[1][1]?["dash-empty-11"]:[],...S.length?[]:["dash-no-columns"],...x.data.length?[]:["dash-no-data"],...l.type!==Ge.vh.None?[]:["dash-no-filter"],...s?["dash-fill-width"]:[],...d?["dash-loading"]:[]],F=["dash-spreadsheet-container",...D],I=["dash-spreadsheet-inner",...D],T=this.calculateTableStyle(f),M=((e,t,n,r,o,i,a)=>{var s=[{},{fragment:{marginRight:a}}];if(!e||!t||!r)return[s,[{},{}]];var l=t.height*o.data.length,c=(Math.floor(r.scrollTop/t.height)-i.before)*t.height,u=Kd(g.A(e=>e.height,n||[])),d=e&&r&&t?Math.max(c-u,0):0;return[s,[{cell:{marginTop:d}},{fragment:{height:Math.max(l-d,0),marginTop:d}}]]})(k,y,w,E,C,x.padding.rows,p),R=Jd(a,A,m,r,i,x,v,b),z=this.props,N=z.export_columns,L=z.export_format,q=z.export_headers,W=z.virtual,V=z.merge_duplicate_headers,U=z.paginator,Y=z.page_current,H=z.page_count,K={export_columns:N,export_format:L,virtual_data:W,columns:t,visibleColumns:S,export_headers:q,merge_duplicate_headers:V};return o().createElement("div",{id:n,className:"dash-table-container",onKeyDown:this.handleKeyDown,onPaste:this.onPaste,style:{position:"relative"}},o().createElement(ap,{key:"tooltip",ref:this.tableRefs.tooltip,className:"dash-table-tooltip",tooltip:R}),o().createElement("div",{className:"dash-spreadsheet-menu"},this.renderMenu(),o().createElement(gt,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?cp(Object(n),!0).forEach(function(t){up(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):cp(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({},K))),o().createElement("div",{className:F.join(" "),style:T},o().createElement("div",{ref:this.tableRefs.table,className:I.join(" "),style:hp},j.map((e,t)=>o().createElement("div",{key:"r".concat(t),ref:1===t?this.tableRefs.r1:void 0,className:"dt-table-container__row dt-table-container__row-".concat(t),onScroll:this.onScroll},function(e,t,n,r){for(var o=e.length,i=new Array(o),a=0;a<o;++a)i[a]=r(e[a],t[a],n[a],a);return i}(e,M[t],O[t],(e,n,r,i)=>o().createElement("div",{style:n.fragment,key:i,ref:this.getColumnRef(t,i),className:"cell cell-".concat(t,"-").concat(i," ").concat(r)},e?o().cloneElement(e,{style:n.cell}):e)))))),this.displayPagination?o().createElement(sp,{paginator:U,page_current:Y,page_count:H}):null)}renderMenu(){if(!this.showToggleColumns)return null;var e=this.props,t=e.activeMenu,n=e.columns,r=e.hidden_columns,i=e.merge_duplicate_headers,a=e.setState,l=this.labelsAndIndices(n,n,i),c=l.length-1;return o().createElement("div",{className:"dash-spreadsheet-menu-item",ref:this.menuRef},o().createElement("button",{className:"show-hide",onClick:()=>a({activeMenu:"show/hide"===t?void 0:"show/hide"})},"Toggle Columns"),"show/hide"!==t?null:o().createElement("div",{className:"show-hide-menu"},me(l.map((e,t)=>{var a,s,l=(a=e,s=2,function(e){if(Array.isArray(e))return e}(a)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(a,s)||function(e,t){if(e){if("string"==typeof e)return lp(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?lp(e,t):void 0}}(a,s)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())[1];return l.map((e,a)=>{var s=1===l.length,u=n[e],d=!r||r.indexOf(u.id)<0,p=qn(t,c,u.hideable),h=s&&d||!p&&d;return{i:e,j:a,component:p?o().createElement("div",{className:"show-hide-menu-item"},o().createElement("input",{type:"checkbox",checked:d,disabled:h,onClick:this.toggleColumn.bind(this,u,t,i)}),o().createElement("label",null,u.name?"string"==typeof u.name?u.name:u.name.slice(0,t+1).filter(e=>0!==e.length).join(" | "):u.id)):null}})})).filter(e=>!s.A(e)).sort((e,t)=>e.i-t.i||e.j-t.j).map(e=>e.component)))}adjustTooltipPosition(){var e=this.props.currentTooltip;if(e){var t=e.id,n=e.row,r=e.header,o=this.tableRefs,i=o.table,a=o.tooltip;if(a.current&&i.current){var s=i.current.querySelector(r?"tr:nth-of-type(".concat(n+1,") th").concat(fp(t)):'td[data-dash-row="'.concat(n,'"]').concat(fp(t)));a.current.updateBounds(s)}}}setCellWidth(e,t){"number"==typeof t&&(t="".concat(t,"px")),e.style.width=t,e.style.minWidth=t,e.style.maxWidth=t,e.style.boxSizing="border-box"}get showToggleColumns(){var e=this.props,t=e.columns,n=e.hidden_columns;return n&&n.length>0||Ce(e=>!!e.hideable,t)}}var vp=n(5072),bp=n.n(vp),gp=n(7825),mp=n.n(gp),yp=n(7659),wp=n.n(yp),Ep=n(5056),Cp=n.n(Ep),xp=n(540),kp=n.n(xp),Sp=n(1113),Op=n.n(Sp),Bp=n(1467),_p={};_p.styleTagTransform=Op(),_p.setAttributes=Cp(),_p.insert=wp().bind(null,"head"),_p.domAPI=mp(),_p.insertStyleElement=kp(),bp()(Bp.A,_p),Bp.A&&Bp.A.locals&&Bp.A.locals;var jp=n(5366),Pp={};Pp.styleTagTransform=Op(),Pp.setAttributes=Cp(),Pp.insert=wp().bind(null,"head"),Pp.domAPI=mp(),Pp.insertStyleElement=kp(),bp()(jp.A,Pp),jp.A&&jp.A.locals&&jp.A.locals;Ju.add({prefix:"fas",iconName:"eraser",icon:[512,512,[],"f12d","M497.941 273.941c18.745-18.745 18.745-49.137 0-67.882l-160-160c-18.745-18.745-49.136-18.746-67.883 0l-256 256c-18.745 18.745-18.745 49.137 0 67.882l96 96A48.004 48.004 0 0 0 144 480h356c6.627 0 12-5.373 12-12v-40c0-6.627-5.373-12-12-12H355.883l142.058-142.059zm-302.627-62.627l137.373 137.373L265.373 416H150.628l-80-80 124.686-124.686z"]},{prefix:"far",iconName:"eye-slash",icon:[640,512,[],"f070","M634 471L36 3.51A16 16 0 0 0 13.51 6l-10 12.49A16 16 0 0 0 6 41l598 467.49a16 16 0 0 0 22.49-2.49l10-12.49A16 16 0 0 0 634 471zM296.79 146.47l134.79 105.38C429.36 191.91 380.48 144 320 144a112.26 112.26 0 0 0-23.21 2.47zm46.42 219.07L208.42 260.16C210.65 320.09 259.53 368 320 368a113 113 0 0 0 23.21-2.46zM320 112c98.65 0 189.09 55 237.93 144a285.53 285.53 0 0 1-44 60.2l37.74 29.5a333.7 333.7 0 0 0 52.9-75.11 32.35 32.35 0 0 0 0-29.19C550.29 135.59 442.93 64 320 64c-36.7 0-71.71 7-104.63 18.81l46.41 36.29c18.94-4.3 38.34-7.1 58.22-7.1zm0 288c-98.65 0-189.08-55-237.93-144a285.47 285.47 0 0 1 44.05-60.19l-37.74-29.5a333.6 333.6 0 0 0-52.89 75.1 32.35 32.35 0 0 0 0 29.19C89.72 376.41 197.08 448 320 448c36.7 0 71.71-7.05 104.63-18.81l-46.41-36.28C359.28 397.2 339.89 400 320 400z"]},{prefix:"fas",iconName:"pencil-alt",icon:[512,512,[],"f303","M497.9 142.1l-46.1 46.1c-4.7 4.7-12.3 4.7-17 0l-111-111c-4.7-4.7-4.7-12.3 0-17l46.1-46.1c18.7-18.7 49.1-18.7 67.9 0l60.1 60.1c18.8 18.7 18.8 49.1 0 67.9zM284.2 99.8L21.6 362.4.4 483.9c-2.9 16.4 11.4 30.6 27.8 27.8l121.5-21.3 262.6-262.6c4.7-4.7 4.7-12.3 0-17l-111-111c-4.8-4.7-12.4-4.7-17.1 0zM124.1 339.9c-5.5-5.5-5.5-14.3 0-19.8l154-154c5.5-5.5 14.3-5.5 19.8 0s5.5 14.3 0 19.8l-154 154c-5.5 5.5-14.3 5.5-19.8 0zM88 424h48v36.3l-64.5 11.3-31.1-31.1L51.7 376H88v48z"]},{prefix:"fas",iconName:"sort",icon:[320,512,[],"f0dc","M41 288h238c21.4 0 32.1 25.9 17 41L177 448c-9.4 9.4-24.6 9.4-33.9 0L24 329c-15.1-15.1-4.4-41 17-41zm255-105L177 64c-9.4-9.4-24.6-9.4-33.9 0L24 183c-15.1 15.1-4.4 41 17 41h238c21.4 0 32.1-25.9 17-41z"]},{prefix:"fas",iconName:"sort-down",icon:[320,512,[],"f0dd","M41 288h238c21.4 0 32.1 25.9 17 41L177 448c-9.4 9.4-24.6 9.4-33.9 0L24 329c-15.1-15.1-4.4-41 17-41z"]},{prefix:"fas",iconName:"sort-up",icon:[320,512,[],"f0de","M279 224H41c-21.4 0-32.1-25.9-17-41L143 64c9.4-9.4 24.6-9.4 33.9 0l119 119c15.2 15.1 4.5 41-16.9 41z"]},{prefix:"far",iconName:"trash-alt",icon:[448,512,[],"f2ed","M268 416h24a12 12 0 0 0 12-12V188a12 12 0 0 0-12-12h-24a12 12 0 0 0-12 12v216a12 12 0 0 0 12 12zM432 80h-82.41l-34-56.7A48 48 0 0 0 274.41 0H173.59a48 48 0 0 0-41.16 23.3L98.41 80H16A16 16 0 0 0 0 96v16a16 16 0 0 0 16 16h16v336a48 48 0 0 0 48 48h288a48 48 0 0 0 48-48V128h16a16 16 0 0 0 16-16V96a16 16 0 0 0-16-16zM171.84 50.91A6 6 0 0 1 177 48h94a6 6 0 0 1 5.15 2.91L293.61 80H154.39zM368 464H80V128h288zm-212-48h24a12 12 0 0 0 12-12V188a12 12 0 0 0-12-12h-24a12 12 0 0 0-12 12v216a12 12 0 0 0 12 12z"]},{prefix:"fas",iconName:"angle-left",icon:[256,512,[],"f104","M31.7 239l136-136c9.4-9.4 24.6-9.4 33.9 0l22.6 22.6c9.4 9.4 9.4 24.6 0 33.9L127.9 256l96.4 96.4c9.4 9.4 9.4 24.6 0 33.9L201.7 409c-9.4 9.4-24.6 9.4-33.9 0l-136-136c-9.5-9.4-9.5-24.6-.1-34z"]},{prefix:"fas",iconName:"angle-right",icon:[256,512,[],"f105","M224.3 273l-136 136c-9.4 9.4-24.6 9.4-33.9 0l-22.6-22.6c-9.4-9.4-9.4-24.6 0-33.9l96.4-96.4-96.4-96.4c-9.4-9.4-9.4-24.6 0-33.9L54.3 103c9.4-9.4 24.6-9.4 33.9 0l136 136c9.5 9.4 9.5 24.6.1 34z"]},{prefix:"fas",iconName:"angle-double-left",icon:[448,512,[],"f100","M223.7 239l136-136c9.4-9.4 24.6-9.4 33.9 0l22.6 22.6c9.4 9.4 9.4 24.6 0 33.9L319.9 256l96.4 96.4c9.4 9.4 9.4 24.6 0 33.9L393.7 409c-9.4 9.4-24.6 9.4-33.9 0l-136-136c-9.5-9.4-9.5-24.6-.1-34zm-192 34l136 136c9.4 9.4 24.6 9.4 33.9 0l22.6-22.6c9.4-9.4 9.4-24.6 0-33.9L127.9 256l96.4-96.4c9.4-9.4 9.4-24.6 0-33.9L201.7 103c-9.4-9.4-24.6-9.4-33.9 0l-136 136c-9.5 9.4-9.5 24.6-.1 34z"]},{prefix:"fas",iconName:"angle-double-right",icon:[448,512,[],"f101","M224.3 273l-136 136c-9.4 9.4-24.6 9.4-33.9 0l-22.6-22.6c-9.4-9.4-9.4-24.6 0-33.9l96.4-96.4-96.4-96.4c-9.4-9.4-9.4-24.6 0-33.9L54.3 103c9.4-9.4 24.6-9.4 33.9 0l136 136c9.5 9.4 9.5 24.6.1 34zm192-34l-136-136c-9.4-9.4-24.6-9.4-33.9 0l-22.6 22.6c-9.4 9.4-9.4 24.6 0 33.9l96.4 96.4-96.4 96.4c-9.4 9.4-9.4 24.6 0 33.9l22.6 22.6c9.4 9.4 24.6 9.4 33.9 0l136-136c9.4-9.2 9.4-24.4 0-33.8z"]});var Dp=n(3650),Fp={};function Ip(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Tp(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Mp(e,t){return Math.ceil(e.length/t)}Fp.styleTagTransform=Op(),Fp.setAttributes=Cp(),Fp.insert=wp().bind(null,"head"),Fp.domAPI=mp(),Fp.insertStyleElement=kp(),bp()(Dp.A,Fp),Dp.A&&Dp.A.locals&&Dp.A.locals;var Rp=(0,f.ty)((e,t,n,r,o,i)=>(e===Ge.vh.Native&&(r=Mp(i,n)),r&&(r=Math.max(r,1)),function(e){if(null===e)return{loadNext(){},loadPrevious(){},loadFirst(){},loadLast(){},loadPage(){},hasPrevious:()=>!0,hasNext:()=>!0,isLast:()=>!1,lastPage:void 0};var t=e.setProps,n=e.page_count,r=e.page_current;function o(){t(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ip(Object(n),!0).forEach(function(t){Tp(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ip(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({page_current:r},$e))}function i(e){e=Math.max(0,e),e=n?Math.min(n-1,e):e,r=e,o()}return n&&n-1<r&&(r=0,o()),{loadNext:()=>i(r+1),loadPrevious:()=>i(r-1),loadFirst:()=>i(0),loadPage:i,loadLast:()=>{n&&i(n-1)},hasPrevious:()=>0!==r,hasNext:()=>!n||r!==n-1,isLast:()=>!!n&&r===n-1,lastPage:n?Math.max(0,n-1):void 0}}(e===Ge.vh.None?null:{setProps:o,page_current:t,page_count:r}))),zp=(0,f.ty)((e,t)=>e.map(e=>e.id).filter(e=>-1!==t.indexOf(e))),Np=(0,f.ty)((e,t)=>{var n=new Map;e.forEach((e,t)=>{n.set(e,t)});var r=[];return t.forEach(e=>{var t=n.get(e);void 0!==t&&r.push(t)}),r}),Lp=(0,f.ty)((e,t,n,r,o)=>{switch(e){case Ge.vh.None:return function(e,t){return{data:e,indices:t}}(r,o);case Ge.vh.Native:return function(e,t,n,r){var o=t*Math.min(e,Mp(n,t)),i=Math.min(o+t,n.length);return{data:n.slice(o,i),indices:r.slice(o,i)}}(t,n,r,o);case Ge.vh.Custom:return function(e,t){return{data:e,indices:t}}(r,o);default:throw new Error("Unknown pagination mode: '".concat(e,"'"))}}),qp=(0,f.ty)(function(e,t,n,r,o){var i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:[],a=new Map;if(Xe(Z.A)((e,t)=>{a.set(e,t)},t),n.type===Ge.vh.Native){var l=new Ns(r);t=l.isValid?l.filter(t):t}return o===Ge.vh.Native&&(t=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:jc;return t.length?Bc(g.A(e=>e.direction===kc.Descending?_c((t,r)=>{var o=e.column_id,i=t[o],a=r[o];return!n(i,e.column_id)&&(!!n(a,e.column_id)||i>a)}):_c((t,r)=>{var o=e.column_id,i=t[o],a=r[o];return!n(i,e.column_id)&&(!!n(a,e.column_id)||i<a)}),t),e):e}(t,i,(t,n)=>s.A(t)||Q.A(t,(t=>{var n=Ft(e=>e.id===t,e);return n&&n.sort_as_null||[]})(n)))),{data:t,indices:g.A(e=>a.get(e),t)}});function Wp(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Vp(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Wp(Object(n),!0).forEach(function(t){Up(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Wp(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Up(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Yp=(0,f.ty)((e,t,n,r,o)=>{if(!e)return Vp(Vp({},o),{},{offset:{rows:0,columns:0},padding:{rows:{before:0,after:0}}});if(!r||!t)return{data:o.data.slice(0,1),indices:o.indices.slice(0,1),offset:{rows:0,columns:0},padding:{rows:{before:0,after:0}}};var i=Kd(g.A(e=>e.height,n||[])),a=Math.max(r.scrollTop-i,0),s=Math.max(i-r.scrollTop,0),l=Math.floor(a/t.height),c=Math.ceil((r.height-s+a)/t.height),u=Math.min(l,1),d=Math.min(o.data.length-c,1);return l-=u,c+=d,{data:o.data.slice(l,c),indices:o.indices.slice(l,c),offset:{rows:l,columns:0},padding:{rows:{before:u,after:d}}}});function Hp(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Kp(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Hp(Object(n),!0).forEach(function(t){$p(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Hp(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function $p(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Gp=/^derived_/;function Zp(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Qp(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Xp extends r.Component{constructor(e){var t,n,r,o,i,a,s,l,c,d,p,h,A,b,m,y;super(e),Qp(this,"__setProps",(0,f.B4)(e=>e?t=>{if(u("data",t)){var n=this.props.data;t.data_timestamp=Date.now(),t.data_previous=n}e(t)}:e=>{this.setState(e)})),Qp(this,"__setState",(0,f.B4)(()=>e=>this.setState(e))),Qp(this,"filterMap",lc()),Qp(this,"controlledPropsHelper",(t=Rp(),n=Lp(),r=zp(),o=Np(),i=qp(),a=Np(),s=Yp(),(e,l,c,u)=>{var d=Me.A(c,u),p=d.data,h=d.filter_query,f=d.filter_action,A=d.page_action,b=d.page_current,g=d.page_size,m=d.page_count,y=d.selected_columns,w=d.selected_rows,E=d.sort_action,C=d.sort_by,x=d.uiCell,k=d.uiHeaders,S=d.uiViewport,O=d.virtualization,B=d.visibleColumns,_=i(B,p,f,h,E,C),j=n(A,b,g,_.data,_.indices),P=s(O,x,k,S,j),D=a(_.indices,w),F=r(B,y),I=o(j.indices,w),T=t(A,b,g,m,e,_.data);return v([c,u,{paginator:T,setProps:e,setState:l,viewport:j,viewport_selected_columns:F,viewport_selected_rows:I,virtual:_,virtual_selected_rows:D,virtualized:P}])})),Qp(this,"updateDerivedProps",(l=(0,f.Hc)(e=>e),c=(0,f.Hc)((e,t)=>[e,t]),d=(0,f.Hc)(e=>e),p=(0,f.Hc)(e=>e),h=(0,f.Hc)(e=>e),A=(0,f.Hc)(e=>e),b=(0,f.Hc)(e=>e),m=(0,f.Hc)(e=>e),y=(0,f.Hc)(e=>new Ns(e).toStructure()),(e,t)=>{var n=e.filter_query,r=e.filter_action,o=e.page_action,i=e.page_current,a=e.page_size,s=e.sort_action,u=e.sort_by,f=e.viewport,v=e.viewport_selected_columns,w=e.viewport_selected_rows,E=e.virtual,C=e.virtual_selected_rows,x=y(n),k=p(f).cached,S=b(E).cached,O=h(v).cached,B=A(w).cached,_=m(C).cached,j=l(n),P=c(i,a),D=d(u),F=!j.cached&&!j.first&&r.type===Ge.vh.Custom||!P.cached&&!P.first&&o===Ge.vh.Custom||!D.cached&&!D.first&&s===Ge.vh.Custom,I={};x.cached||(I.derived_filter_query_structure=x.result),S||(I.derived_virtual_data=E.data,I.derived_virtual_indices=E.indices,I.derived_virtual_row_ids=Fe.A("id",E.data)),k||(I.derived_viewport_data=f.data,I.derived_viewport_indices=f.indices,I.derived_viewport_row_ids=Fe.A("id",f.data)),_||(I.derived_virtual_selected_rows=C,I.derived_virtual_selected_row_ids=g.A(e=>E.data[e].id,C)),O||(I.derived_viewport_selected_columns=v),B||(I.derived_viewport_selected_rows=w,I.derived_viewport_selected_row_ids=g.A(e=>f.data[e].id,w)),F&&(I.active_cell=void 0,I.selected_cells=[],I.start_cell=void 0,I.end_cell=void 0,I.selected_rows=[],I.selected_row_ids=[]),R.A(I).length&&setTimeout(()=>t(I),0)})),this.state={workFilter:{value:e.filter_query,map:this.filterMap(new Map,e.filter_action.operator,e.filter_query,e.visibleColumns)},rawFilterQuery:"",scrollbarWidth:0}}UNSAFE_componentWillReceiveProps(e){this.setState(t=>{var n=t.applyFocus,r=t.workFilter,o=r.map,i=r.value,a={};if(e.filter_query!==this.props.filter_query&&i!==e.filter_query){var s=this.filterMap(o,e.filter_action.operator,e.filter_query,e.visibleColumns);s!==o&&(a.workFilter={map:s,value:i})}if(e.active_cell!==this.props.active_cell)a.applyFocus=!0;else if(e.loading_state!==this.props.loading_state){var l=document.activeElement,c=uo.getFirstParentOfType(l,"td"),u=uo.getParentById(c,this.props.id);a.applyFocus=!!u}return a.applyFocus===n&&delete a.applyFocus,h(a).length?a:null})}shouldComponentUpdate(e,t){return((e,t,n,r)=>Ce(n=>!Gp.test(n)&&e[n]!==t[n],h(Kp(Kp({},e),t)))||!(0,ep.n)(n,r))(this.props,e,this.state,t)}render(){var e=this.controlledPropsHelper(this.controlledSetProps,this.controlledSetState,this.props,this.state);return this.updateDerivedProps(e,this.controlledSetProps),o().createElement(Ap,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Zp(Object(n),!0).forEach(function(t){Qp(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Zp(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({},e))}get controlledSetProps(){return this.__setProps(this.props.setProps)}get controlledSetState(){return this.__setState()}}function Jp(e){return e!==Ge.vh.Native}var eh=e=>function(e){var t=e.filter_action,n=e.sort_action;return e.page_action!==Ge.vh.Custom||Jp(t)&&Jp(n)}(e)?!!function(e){var t=e.columns;return s.A(t)||!Ce(e=>e.format&&(e.format.symbol&&2!==e.format.symbol.length||e.format.grouping&&0===e.format.grouping.length||e.format.numerals&&10!==e.format.numerals.length))(t)}(e)||(xe.Ay.error("Invalid column format"),!1):(xe.Ay.error("Invalid combination of filter_action / sort_action / page_action"),!1);function th(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function nh(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?th(Object(n),!0).forEach(function(t){rh(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):th(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function rh(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var oh={symbol:["$",""],decimal:".",group:",",grouping:[3],percent:"%",separate_4digits:!0},ih=[],ah={case:Ge.ze.Sensitive,placeholder_text:"filter data..."},sh=e=>+e||0,lh=(e,t,n)=>e.headers?Le(t)+(n!==Ge.vh.None?1:0)+sh(e.data):0,ch=e=>e.length>0?Object.keys(e[0]).map(e=>new Ge.VP({name:e,id:e})):[],uh=(e,t,n,r,o)=>g.A(n=>{var i,a=Zt.A(n);return a.editable=((e,t)=>s.A(t)?e:t)(r,n.editable),a.filter_options=nh(nh(nh({},ah),null!=o?o:{}),null!==(i=a.filter_options)&&void 0!==i?i:{}),a.sort_as_null=a.sort_as_null||t,a.type===Ge.$C.Numeric&&a.format&&(a.format.locale=Ah(e,a.format.locale),a.format.nully=bh(a.format.nully),a.format.specifier=vh(a.format.specifier)),a},n),dh=e=>Ah(e),ph=e=>{var t,n;return"object"==typeof e?{type:null!==(t=e.type)&&void 0!==t?t:Ge.vh.None,operator:null!==(n=e.operator)&&void 0!==n?n:Ge.iV.And}:{type:e,operator:Ge.iV.And}},hh=(e,t)=>U(e=>!t||t.indexOf(e.id)<0,e);class fh{constructor(){rh(this,"populateColumnsFrom",(0,f.B4)(ch)),rh(this,"applyDefaultToLocale",(0,f.B4)(dh)),rh(this,"applyDefaultsToColumns",(0,f.B4)(uh)),rh(this,"getFilterAction",(0,f.B4)(ph)),rh(this,"getVisibleColumns",(0,f.B4)(hh))}sanitize(e,t){var n,r=this.applyDefaultToLocale(e.locale_format),o=null!==(n=e.data)&&void 0!==n?n:[],i=e.columns?this.applyDefaultsToColumns(r,e.sort_as_null,e.columns,e.editable,e.filter_options):this.populateColumnsFrom(o),a=this.getVisibleColumns(i,e.hidden_columns),l=e.export_headers;e.export_format===Ge.AV.Xlsx&&s.A(l)?l=Ge.ru.Names:e.export_format===Ge.AV.Csv&&s.A(l)&&(l=Ge.ru.Ids);var c,u,d,p=e.cell_selectable?e.active_cell:void 0,h=e.cell_selectable?e.selected_cells:ih;return Me.A(e,{active_cell:p,columns:i,data:o,export_headers:l,filter_action:this.getFilterAction(e.filter_action),fixed_columns:(c=e.fixed_columns,u=e.row_deletable,d=e.row_selectable,c.headers?(u?1:0)+(d?1:0)+sh(c.data):0),fixed_rows:lh(e.fixed_rows,i,e.filter_action),loading_state:t,locale_format:r,selected_cells:h,visibleColumns:a})}}var Ah=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return v([oh,...t])},vh=e=>void 0===e?"":e,bh=e=>void 0===e?"":e,gh=n(8935);function mh(){return mh=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},mh.apply(null,arguments)}var yh=e=>{var t=window.dash_component_api.useDashContext().useLoading({filterFunc:e=>"data"===e.property||""===e.property||void 0===e.property}),n=(0,r.useMemo)(()=>n||function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:36;return e+Math.random().toString(t).substring(2)}("table-"),[n]),i=(0,r.useMemo)(()=>new fh,[]);if(!eh(e))return o().createElement("div",null,"Invalid props combination");var a=i.sanitize(e,t);return e.id?o().createElement(Xp,a):o().createElement(Xp,mh({},a,{id:n}))};yh.propTypes=gh.tu;var wh=yh},4239:(e,t,n)=>{"use strict";var r=n(1069);t.A="function"==typeof Object.assign?Object.assign:function(e){if(null==e)throw new TypeError("Cannot convert undefined or null to object");for(var t=Object(e),n=1,o=arguments.length;n<o;){var i=arguments[n];if(null!=i)for(var a in i)(0,r.A)(a,i)&&(t[a]=i[a]);n+=1}return t}},5366:(e,t,n)=>{"use strict";var r=n(1354),o=n.n(r),i=n(6314),a=n.n(i)()(o());a.push([e.id,".dash-spreadsheet.dash-freeze-left,\n.dash-spreadsheet.dash-freeze-top {\n  width: auto;\n  width: fit-content;\n  width: -moz-fit-content;\n  width: -webkit-fit-content;\n}\n.dash-spreadsheet.dash-freeze-left {\n  max-width: 500px;\n}\n.dash-spreadsheet.dash-freeze-top,\n.dash-spreadsheet.dash-virtualized {\n  max-height: 500px;\n}\n.dash-tooltip {\n  border: 1px solid #e4e4e4;\n  border-radius: 5px;\n  position: absolute;\n  z-index: 500;\n}\n.dash-tooltip .dash-table-tooltip {\n  position: relative;\n  background-color: #f6f6f6;\n  max-width: 300px;\n  min-width: 300px;\n  padding: 2px 10px;\n}\n.dash-tooltip[data-attr-anchor='top'] {\n  margin-top: 10px;\n}\n.dash-tooltip[data-attr-anchor='top']:after,\n.dash-tooltip[data-attr-anchor='top']:before {\n  bottom: 100%;\n  left: 50%;\n  border: solid transparent;\n  content: \" \";\n  height: 0;\n  width: 0;\n  position: absolute;\n  pointer-events: none;\n}\n.dash-tooltip[data-attr-anchor='top']:after {\n  border-color: transparent;\n  border-bottom-color: #f6f6f6;\n  border-width: 8px;\n  margin-left: -8px;\n}\n.dash-tooltip[data-attr-anchor='top']:before {\n  border-color: transparent;\n  border-bottom-color: #e4e4e4;\n  border-width: 9px;\n  margin-left: -9px;\n}\n.dash-tooltip[data-attr-anchor='bottom'] {\n  margin-bottom: 10px;\n}\n.dash-tooltip[data-attr-anchor='bottom']:after,\n.dash-tooltip[data-attr-anchor='bottom']:before {\n  top: 100%;\n  left: 50%;\n  border: solid transparent;\n  content: \" \";\n  height: 0;\n  width: 0;\n  position: absolute;\n  pointer-events: none;\n}\n.dash-tooltip[data-attr-anchor='bottom']:after {\n  border-color: transparent;\n  border-top-color: #f6f6f6;\n  border-width: 8px;\n  margin-left: -8px;\n}\n.dash-tooltip[data-attr-anchor='bottom']:before {\n  border-color: transparent;\n  border-top-color: #e4e4e4;\n  border-width: 9px;\n  margin-left: -9px;\n}\n.dash-spreadsheet-menu {\n  display: flex;\n  flex-direction: row;\n}\n.dash-spreadsheet-menu > * {\n  padding-right: 5px;\n}\n.dash-spreadsheet-menu .dash-spreadsheet-menu-item {\n  position: relative;\n}\n.dash-spreadsheet-menu .dash-spreadsheet-menu-item .show-hide-menu {\n  background-color: #fafafa;\n  border: 1px solid #d3d3d3;\n  display: flex;\n  flex-direction: column;\n  max-height: 300px;\n  overflow: auto;\n  position: absolute;\n  top: 100%;\n  left: 0;\n  z-index: 500;\n}\n.dash-spreadsheet-menu .dash-spreadsheet-menu-item .show-hide-menu .show-hide-menu-item {\n  display: flex;\n  flex-direction: row;\n  padding: 5px;\n}\n.dash-spreadsheet-menu .dash-spreadsheet-menu-item .show-hide-menu .show-hide-menu-item label {\n  white-space: nowrap;\n}\n.dash-table-container .previous-next-container {\n  text-align: right;\n  padding: 5px 0px;\n}\n.dash-table-container .previous-next-container .page-number {\n  font-family: monospace;\n  display: inline-block;\n}\n.dash-table-container .previous-next-container .page-number .last-page {\n  display: inline-block;\n  text-align: center;\n  padding: 1px 2px;\n}\n.dash-table-container .previous-next-container .page-number .current-page-container {\n  display: inline-block;\n  position: relative;\n}\n.dash-table-container .previous-next-container .page-number .current-page-container .current-page-shadow,\n.dash-table-container .previous-next-container .page-number .current-page-container input.current-page {\n  display: inline-block;\n  border-bottom: solid lightgrey 1px !important;\n  color: black;\n  border: none;\n  text-align: center;\n  font-family: monospace;\n  font-size: 10pt;\n  padding: 1px 2px;\n}\n.dash-table-container .previous-next-container .page-number .current-page-container .current-page-shadow::placeholder,\n.dash-table-container .previous-next-container .page-number .current-page-container input.current-page::placeholder {\n  color: black;\n}\n.dash-table-container .previous-next-container .page-number .current-page-container .current-page-shadow:focus,\n.dash-table-container .previous-next-container .page-number .current-page-container input.current-page:focus {\n  outline: none;\n}\n.dash-table-container .previous-next-container .page-number .current-page-container .current-page-shadow:focus::placeholder,\n.dash-table-container .previous-next-container .page-number .current-page-container input.current-page:focus::placeholder {\n  opacity: 0;\n}\n.dash-table-container .previous-next-container .page-number .current-page-container input.current-page {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n}\n.dash-table-container .previous-next-container button.previous-page,\n.dash-table-container .previous-next-container button.next-page,\n.dash-table-container .previous-next-container button.first-page,\n.dash-table-container .previous-next-container button.last-page {\n  transition-duration: 400ms;\n  padding: 5px;\n  border: none;\n  display: inline-block;\n  margin-left: 5px;\n  margin-right: 5px;\n}\n.dash-table-container .previous-next-container button.previous-page:hover,\n.dash-table-container .previous-next-container button.next-page:hover,\n.dash-table-container .previous-next-container button.first-page:hover,\n.dash-table-container .previous-next-container button.last-page:hover {\n  color: hotpink;\n}\n.dash-table-container .previous-next-container button.previous-page:hover:disabled,\n.dash-table-container .previous-next-container button.next-page:hover:disabled,\n.dash-table-container .previous-next-container button.first-page:hover:disabled,\n.dash-table-container .previous-next-container button.last-page:hover:disabled {\n  color: graytext;\n}\n.dash-table-container .previous-next-container button.previous-page:focus,\n.dash-table-container .previous-next-container button.next-page:focus,\n.dash-table-container .previous-next-container button.first-page:focus,\n.dash-table-container .previous-next-container button.last-page:focus {\n  outline: none;\n}\n.dash-table-container .dash-spreadsheet-container {\n  /* The \"normal\" reset CSS */\n  /* The \"modified\" reset CSS applied to the table to ignore markdown cells */\n  display: flex;\n  flex-direction: row;\n  position: relative;\n  line-height: initial;\n  /* focus happens after copying to clipboard */\n}\n.dash-table-container .dash-spreadsheet-container th {\n  font-style: normal;\n  font-weight: normal;\n  text-align: left;\n}\n.dash-table-container .dash-spreadsheet-container th,\n.dash-table-container .dash-spreadsheet-container td {\n  margin: 0;\n  padding: 0;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown),\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) {\n  margin: 0;\n  padding: 0;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) dl,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) dl,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) dt,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) dt,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) dd,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) dd,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) ul,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) ul,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) ol,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) ol,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) li,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) li,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h1,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h1,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h2,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h2,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h3,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h3,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h4,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h4,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h5,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h5,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h6,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h6,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) pre,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) pre,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) code,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) code,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) form,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) form,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) fieldset,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) fieldset,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) legend,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) legend,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) input,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) input,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) textarea,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) textarea,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) p,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) p,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) blockquote,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) blockquote,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) th,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) th,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) td,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) td {\n  margin: 0;\n  padding: 0;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) table,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) table {\n  border-collapse: collapse;\n  border-spacing: 0;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) fieldset,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) fieldset,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) img,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) img {\n  border: 0;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) address,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) address,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) caption,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) caption,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) cite,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) cite,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) code,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) code,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) dfn,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) dfn,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) em,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) em,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) strong,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) strong,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) th,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) th,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) var,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) var {\n  font-style: normal;\n  font-weight: normal;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) ol,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) ol,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) ul,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) ul {\n  list-style: none;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) caption,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) caption,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) th,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) th {\n  text-align: left;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h1,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h1,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h2,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h2,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h3,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h3,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h4,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h4,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h5,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h5,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h6,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h6 {\n  font-size: 100%;\n  font-weight: normal;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) q:before,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) q:before,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) q:after,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) q:after {\n  content: '';\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) abbr,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) abbr,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) acronym,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) acronym {\n  border: 0;\n  font-variant: normal;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) sup,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) sup {\n  vertical-align: text-top;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) sub,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) sub {\n  vertical-align: text-bottom;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) input,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) input,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) textarea,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) textarea,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) select,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) select {\n  font-family: inherit;\n  font-size: inherit;\n  font-weight: inherit;\n  *font-size: 100%;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) legend,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) legend {\n  color: #000;\n}\n.dash-table-container .dash-spreadsheet-container input[type=\"button\"] {\n  border-radius: 0;\n  -webkit-appearance: none;\n}\n.dash-table-container .dash-spreadsheet-container *:focus {\n  outline: none;\n}\n.dash-table-container .dash-spreadsheet-container table {\n  font-size: inherit;\n  pointer-events: none;\n}\n.dash-table-container .dash-spreadsheet-container table td,\n.dash-table-container .dash-spreadsheet-container table th {\n  pointer-events: initial;\n}\n.dash-table-container .dash-spreadsheet-container input[type=\"radio\"] {\n  margin: initial;\n  line-height: initial;\n  box-sizing: initial;\n  padding: initial;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner {\n  box-sizing: border-box;\n  display: flex;\n  flex-direction: column;\n  /*\n             * fixes Firefox td height bug on td > dropdown children\n             * bug should only appear on FF but\n             * @supports = scoped to Firefox only\n             * to minimize side effects\n             */\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner *,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner *:after,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner *:before {\n  box-sizing: inherit;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .Select {\n  overflow: hidden;\n  position: static;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .Select,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .Select-control {\n  background-color: inherit;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .Select-value {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  margin-top: -2px;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .marker-row tr {\n  visibility: hidden !important;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .marker-row td,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .marker-row th {\n  height: 0 !important;\n  padding: 0 !important;\n  margin: 0 !important;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter input::placeholder {\n  color: inherit;\n  font-size: 0.8em;\n  padding-right: 5px;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter + .dash-filter:not(:hover):not(:focus-within) input::placeholder {\n  color: transparent;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter.invalid {\n  background-color: pink;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner:not(.dash-empty-11) .dt-table-container__row-0 tr:last-of-type td,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner:not(.dash-empty-11) .dt-table-container__row-0 tr:last-of-type th {\n  border-bottom: none !important;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner:not(.dash-empty-01) .cell-0-0 tr td:last-of-type,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner:not(.dash-empty-01) .cell-1-0 tr td:last-of-type,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner:not(.dash-empty-01) .cell-0-0 tr th:last-of-type,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner:not(.dash-empty-01) .cell-1-0 tr th:last-of-type {\n  border-right: none !important;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-0-0 {\n  overflow: hidden;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-0-0 td.phantom-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-1-0 td.phantom-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-0-0 th.phantom-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-1-0 th.phantom-cell {\n  border-color: transparent !important;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-0-1 td.phantom-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-1-1 td.phantom-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-0-1 th.phantom-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-1-1 th.phantom-cell {\n  border-color: transparent inherit transparent transparent !important;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-left,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-top,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-virtualized {\n  overflow: hidden !important;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-left .dt-table-container__row-0,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-top .dt-table-container__row-0,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-virtualized .dt-table-container__row-0 {\n  display: flex;\n  flex: 0 0 auto;\n  flex-direction: row;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-left .dt-table-container__row-1,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-top .dt-table-container__row-1,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-virtualized .dt-table-container__row-1 {\n  display: flex;\n  flex-direction: row;\n  overflow: auto;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-left .cell-0-0,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-top .cell-0-0,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-virtualized .cell-0-0,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-left .cell-1-0,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-top .cell-1-0,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-virtualized .cell-1-0 {\n  flex: 0 0 auto;\n  left: 0;\n  position: sticky;\n  position: -webkit-sticky;\n  z-index: 400;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-left .cell-0-1,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-top .cell-0-1,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-virtualized .cell-0-1 {\n  z-index: 300;\n  flex: 0 0 auto;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-left .cell-1-1,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-top .cell-1-1,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-virtualized .cell-1-1 {\n  flex: 0 0 auto;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-fill-width .cell-0-1,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-fill-width .cell-1-1 {\n  flex: 1 0 auto;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-fill-width .cell table {\n  width: 100%;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td {\n  background-color: inherit;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td.focused {\n  margin: -1px;\n  z-index: 200;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .dash-cell-value-container {\n  width: 100%;\n  height: 100%;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .dash-input-cell-value-container {\n  position: relative;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .dash-cell-value {\n  height: 100%;\n  width: 100%;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .dash-cell-value.unfocused.selectable::selection {\n  background-color: transparent;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .dash-cell-value.unfocused {\n  caret-color: transparent;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td input.dash-cell-value {\n  position: absolute;\n  left: 0;\n  top: 0;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .cell-value-shadow {\n  margin: auto 0;\n  opacity: 0;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .input-cell-value-shadow {\n  display: inline-block;\n  height: initial;\n  width: initial;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .dropdown-cell-value-shadow {\n  display: block;\n  height: 0px;\n  padding: 0 42px 0 10px;\n}\n@supports (-moz-appearance:none) {\n  .dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td.dropdown .dash-cell-value-container {\n    height: auto;\n  }\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th.dash-filter {\n  position: relative;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th.dash-filter input {\n  left: 0;\n  top: 0;\n  height: 100%;\n  width: 100%;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th.dash-filter input.dash-filter--case {\n  position: relative;\n  left: auto;\n  top: auto;\n  width: auto;\n  height: 16px;\n  line-height: 0px;\n  padding: 1px;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th.dash-filter input.dash-filter--case--sensitive {\n  border-color: hotpink;\n  border-radius: 3px;\n  border-style: solid;\n  border-width: 2px;\n  color: hotpink;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th {\n  white-space: nowrap;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th .column-header--clear,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th .column-header--delete,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th .column-header--edit,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th .column-header--hide,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th .column-header--sort {\n  -webkit-touch-callout: none;\n  -webkit-user-select: none;\n  -khtml-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  cursor: default;\n  cursor: pointer;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner tr {\n  min-height: 30px;\n  height: 30px;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th {\n  background-clip: padding-box;\n  padding: 2px;\n  overflow-x: hidden;\n  white-space: nowrap;\n  box-sizing: border-box;\n  text-align: right;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td.phantom-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th.phantom-cell {\n  visibility: hidden;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td div.dash-cell-value,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th div.dash-cell-value {\n  display: inline;\n  vertical-align: middle;\n  white-space: inherit;\n  overflow: inherit;\n  text-overflow: inherit;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td div.dash-cell-value.cell-markdown,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th div.dash-cell-value.cell-markdown {\n  text-align: left;\n  font-family: sans-serif;\n  display: inline-block;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td div.dash-cell-value.cell-markdown blockquote,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th div.dash-cell-value.cell-markdown blockquote {\n  white-space: pre;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner textarea {\n  white-space: pre;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner table {\n  border-collapse: collapse;\n  font-family: monospace;\n  --accent: hotpink;\n  --border: lightgrey;\n  --text-color: #3c3c3c;\n  --hover: #fdfdfd;\n  --background-color-ellipses: #fdfdfd;\n  --faded-text: #fafafa;\n  --faded-text-header: #b4b4b4;\n  --selected-background: rgba(255, 65, 54, 0.2);\n  --faded-dropdown: #f0f0f0;\n  --muted: #c8c8c8;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner table:focus {\n  outline: none;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner thead {\n  display: table-row-group;\n}\n.dash-table-container .dash-spreadsheet-container .elip {\n  text-align: center;\n  width: 100%;\n  background-color: var(--background-color-ellipses);\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td.dropdown {\n  /*\n             * To view the dropdown's contents, we need\n             * overflow-y: visible.\n             * Unfortunately, overflow-x: hidden and overflow-y: visible\n             * can't both be set at the same time.\n             * So, we have to make both overflow-x: visible and overflow-y: visble\n             *\n             * See https://stackoverflow.com/questions/6421966/\n             *\n             * There might be another solution with parent divs, but I haven't\n             * tried it.\n             */\n  overflow-x: visible;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner :not(.cell--selected) tr:hover,\n.dash-table-container .dash-spreadsheet-container tr:hover input :not(.cell--selected) {\n  background-color: var(--hover);\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th {\n  background-color: #fafafa;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td {\n  background-color: white;\n}\n.dash-table-container .dash-spreadsheet-container .expanded-row--empty-cell {\n  background-color: transparent;\n}\n.dash-table-container .dash-spreadsheet-container .expanded-row {\n  text-align: center;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner input:not([type=radio]):not([type=checkbox]) {\n  padding: 0px;\n  margin: 0px;\n  height: calc(100% - 1px);\n  line-height: 30px;\n  border: none;\n  font-family: inherit;\n  text-align: right;\n  box-sizing: border-box;\n  color: var(--text-color);\n  background-color: transparent;\n  /* so as to not overlay the box shadow */\n  /* browser's default text-shadow is `$color 0px 0px 0px;`\n             * for `input`, which makes it look a little bit heavier than dropdowns\n             * or bare `td`\n             */\n  text-shadow: none;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner input.unfocused {\n  color: transparent;\n  text-shadow: 0 0 0 var(--text-color);\n  cursor: default;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner input.unfocused:focus {\n  outline: none;\n}\n.dash-table-container .dash-spreadsheet-container .toggle-row {\n  border: none;\n  box-shadow: none;\n  width: 10px;\n  padding-left: 10px;\n  padding-right: 10px;\n  cursor: pointer;\n  color: var(--faded-text);\n}\n.dash-table-container .dash-spreadsheet-container .toggle-row--expanded {\n  color: var(--accent);\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner tr:hover .toggle-row {\n  color: var(--accent);\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-delete-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-delete-header {\n  -webkit-touch-callout: none;\n  -webkit-user-select: none;\n  -khtml-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  cursor: default;\n  font-size: 1.3rem;\n  text-align: center;\n  cursor: pointer;\n  color: var(--muted);\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-delete-cell:hover,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-delete-header:hover {\n  color: var(--accent);\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-header > div,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter > div {\n  display: flex;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-header > div input[type=\"text\"],\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter > div input[type=\"text\"] {\n  flex: 1;\n  line-height: unset;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-header > div input[type=\"text\"]::placeholder,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter > div input[type=\"text\"]::placeholder {\n  font-size: 0.9em;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter > div {\n  flex-direction: row-reverse;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-actions {\n  display: flex;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header-name {\n  flex-grow: 1;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner [class^='column-header--'],\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner [class^='dash-filter--'] {\n  cursor: pointer;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header--select {\n  height: auto;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header--select,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header--sort {\n  color: var(--faded-text-header);\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter--case,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header--clear,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header--delete,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header--edit,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header--hide {\n  opacity: 0.1;\n  padding-left: 2px;\n  padding-right: 2px;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th:hover [class^='column-header--']:not(.disabled),\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th:hover [class^='dash-filter--']:not(.disabled) {\n  color: var(--accent);\n  opacity: 1;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter--case {\n  font-size: 10px;\n}\n","",{version:3,sources:["webpack://./src/dash-table/components/Table/Table.less","webpack://./src/dash-table/style/reset.less"],names:[],mappings:"AAoBI;;EAjBA,WAAA;EACA,kBAAA;EACA,uBAAA;EACA,0BAAA;AAAJ;AAmBI;EACI,gBAAA;AAjBR;AAoBI;;EAEI,iBAAA;AAlBR;AAsBA;EACI,yBAAA;EACA,kBAAA;EACA,kBAAA;EACA,YAAA;AApBJ;AAgBA;EAOQ,kBAAA;EACA,yBAAA;EACA,gBAAA;EACA,gBAAA;EACA,iBAAA;AApBR;AAuBI;EACI,gBAAA;AArBR;AAuBQ;;EACI,YAAA;EACA,SAAA;EACA,yBAAA;EACA,YAAA;EACA,SAAA;EACA,QAAA;EACA,kBAAA;EACA,oBAAA;AApBZ;AAuBQ;EACI,yBAAA;EACA,4BAAA;EACA,iBAAA;EACA,iBAAA;AArBZ;AAwBQ;EACI,yBAAA;EACA,4BAAA;EACA,iBAAA;EACA,iBAAA;AAtBZ;AA0BI;EACI,mBAAA;AAxBR;AA0BQ;;EACI,SAAA;EACA,SAAA;EACA,yBAAA;EACA,YAAA;EACA,SAAA;EACA,QAAA;EACA,kBAAA;EACA,oBAAA;AAvBZ;AA0BQ;EACI,yBAAA;EACA,yBAAA;EACA,iBAAA;EACA,iBAAA;AAxBZ;AA0BQ;EACI,yBAAA;EACA,yBAAA;EACA,iBAAA;EACA,iBAAA;AAxBZ;AA6BA;EACI,aAAA;EACA,mBAAA;AA3BJ;AA6BI;EACI,kBAAA;AA3BR;AAsBA;EASQ,kBAAA;AA5BR;AAmBA;EAYY,yBAAA;EACA,yBAAA;EACA,aAAA;EACA,sBAAA;EACA,iBAAA;EACA,cAAA;EACA,kBAAA;EACA,SAAA;EACA,OAAA;EACA,YAAA;AA5BZ;AAOA;EAwBgB,aAAA;EACA,mBAAA;EACA,YAAA;AA5BhB;AAEA;EA6BoB,mBAAA;AA5BpB;AAmCA;EAEQ,iBAAA;EACA,gBAAA;AAlCR;AA+BA;EAMY,sBAAA;EACA,qBAAA;AAlCZ;AA2BA;EAUgB,qBAAA;EACA,kBAAA;EACA,gBAAA;AAlChB;AAsBA;EAgBgB,qBAAA;EACA,kBAAA;AAnChB;AAkBA;;EAqBoB,qBAAA;EACA,6CAAA;EACA,YAAA;EACA,YAAA;EACA,kBAAA;EACA,sBAAA;EACA,eAAA;EACA,gBAAA;AAnCpB;AAqCoB;;EACI,YAAA;AAlCxB;AAqCoB;;EACI,aAAA;AAlCxB;AAoCwB;;EACI,UAAA;AAjC5B;AALA;EA4CoB,kBAAA;EACA,MAAA;EACA,OAAA;EACA,WAAA;EACA,YAAA;AApCpB;AAZA;;;;EAsDY,0BAAA;EACA,YAAA;EACA,YAAA;EACA,qBAAA;EACA,gBAAA;EACA,iBAAA;AApCZ;AAsCY;;;;EACI,cAAA;AAjChB;AAmCgB;;;;EACI,eAAA;AA9BpB;AAkCY;;;;EACI,aAAA;AA7BhB;AAzCA;EA2CE,2BAA2B;EAC3B,2EAA2E;EAiCrE,aAAA;EACA,mBAAA;EACA,kBAAA;EAKA,oBAAA;EAnCN,6CAA6C;AAC/C;AAlDA;ECxIQ,kBAAA;EACA,mBAAA;EACA,gBAAA;AD6LR;AAvDA;;EClIQ,SAAA;EACA,UAAA;AD6LR;AC3LQ;;EACI,SAAA;EACA,UAAA;AD8LZ;AChMQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAIwG,SAAA;EAAS,UAAA;AD6OzH;ACjPQ;;EAKU,yBAAA;EAAyB,iBAAA;ADiP3C;ACtPQ;;;;EAMiB,SAAA;ADsPzB;AC5PQ;;;;;;;;;;;;;;;;;;EAOmD,kBAAA;EAAkB,mBAAA;AD0Q7E;ACjRQ;;;;EAQU,gBAAA;AD+QlB;ACvRQ;;;;EASe,gBAAA;ADoRvB;AC7RQ;;;;;;;;;;;;EAUsB,eAAA;EAAe,mBAAA;ADkS7C;AC5SQ;;;;EAWqB,WAAA;ADuS7B;AClTQ;;;;EAYiB,SAAA;EAAS,oBAAA;AD6SlC;ACzTQ;;EAaQ,wBAAA;ADgThB;AC7TQ;;EAcQ,2BAAA;ADmThB;ACjUQ;;;;;;EAe0B,oBAAA;EAAoB,kBAAA;EAAkB,oBAAA;GAAoB,eAAA;AD6T5F;AC5UQ;;EAgBW,WAAA;ADgUnB;AAjNA;ECxGQ,gBAAA;EACA,wBAAA;AD4TR;AArNA;ECnGQ,aAAA;AD2TR;AAxNA;EA0FY,kBAAA;EACA,oBAAA;AAiIZ;AA5NA;;EA8FgB,uBAAA;AAkIhB;AAhOA;EAsGY,eAAA;EACA,oBAAA;EAIA,mBAAA;EACA,gBAAA;AA0HZ;AAtOA;EAgHY,sBAAA;EACA,aAAA;EACA,sBAAA;EAyHV;;;;;cAKY;AACd;AAjPA;;;EAuHU,mBAAA;AA+HV;AAtPA;EA2HgB,gBAAA;EACA,gBAAA;AA8HhB;AA1PA;;EAiIU,yBAAA;AA6HV;AA9PA;EAqIgB,aAAA;EACA,sBAAA;EACA,uBAAA;EACA,gBAAA;AA4HhB;AApQA;EA6IoB,6BAAA;AA0HpB;AAvQA;;EAiJoB,oBAAA;EACA,qBAAA;EACA,oBAAA;AA0HpB;AA7QA;EAyJoB,cAAA;EACA,gBAAA;EACA,kBAAA;AAuHpB;AAnHoB;EAEQ,kBAAA;AAoH5B;AA/GgB;EACI,sBAAA;AAiHpB;AA7GY;;EAIgB,8BAAA;AA6G5B;AAlG4B;;;;EACI,6BAAA;AAuGhC;AAlSA;EAmMgB,gBAAA;AAkGhB;AA5FoB;;;;EACI,oCAAA;AAiGxB;AAzFoB;;;;EACI,oEAAA;AA8FxB;AAzFY;;;EAGI,2BAAA;AA2FhB;AA9FY;;;EAMQ,aAAA;EACA,cAAA;EACA,mBAAA;AA6FpB;AArGY;;;EAYQ,aAAA;EACA,mBAAA;EACA,cAAA;AA8FpB;AA5GY;;;;;;EAmBQ,cAAA;EACA,OAAA;EACA,gBAAA;EACA,wBAAA;EACA,YAAA;AAiGpB;AAxHY;;;EA2BQ,YAAA;EACA,cAAA;AAkGpB;AA9HY;;;EAgCQ,cAAA;AAmGpB;AA/FY;;EAGQ,cAAA;AAgGpB;AAnGY;EAQY,WAAA;AA8FxB;AAlWA;EA0QgB,yBAAA;AA2FhB;AAzFgB;EACI,YAAA;EACA,YAAA;AA2FpB;AAzWA;EAkRoB,WAAA;EACA,YAAA;AA0FpB;AA7WA;EAuRoB,kBAAA;AAyFpB;AAhXA;EA2RoB,YAAA;EACA,WAAA;AAwFpB;AAtFoB;EACI,6BAAA;AAwFxB;AArFoB;EACI,wBAAA;AAuFxB;AA1XA;EAwSoB,kBAAA;EACA,OAAA;EACA,MAAA;AAqFpB;AA/XA;EA8SoB,cAAA;EACA,UAAA;AAoFpB;AAnYA;EAmToB,qBAAA;EACA,eAAA;EACA,cAAA;AAmFpB;AAxYA;EAyToB,cAAA;EACA,WAAA;EACA,sBAAA;AAkFpB;AAxEY;EAAA;IAEQ,YAAA;EA0ElB;AACF;AAlZA;EA4UgB,kBAAA;AAyEhB;AAvEgB;EACI,OAAA;EACA,MAAA;EACA,YAAA;EACA,WAAA;AAyEpB;AAvEoB;EACI,kBAAA;EACA,UAAA;EACA,SAAA;EACA,WAAA;EACA,YAAA;EACA,gBAAA;EACA,YAAA;AAyExB;AAvEoB;EACI,qBAAA;EACA,kBAAA;EACA,mBAAA;EACA,iBAAA;EACA,cAAA;AAyExB;AA3aA;EAwWgB,mBAAA;AAsEhB;AA9aA;;;;;EArII,2BAAA;EACA,yBAAA;EACA,wBAAA;EACA,sBAAA;EACA,qBAAA;EACA,iBAAA;EACA,eAAA;EA+egB,eAAA;AA4EpB;AA5bA;EAqXgB,gBAAA;EACA,YAAA;AA0EhB;AAhcA;;EA2XgB,4BAAA;EACA,YAAA;EACA,kBAAA;EACA,mBAAA;EACA,sBAAA;EAEA,iBAAA;AAwEhB;AAtEgB;;EACI,kBAAA;AAyEpB;AA7cA;;EAwYoB,eAAA;EACA,sBAAA;EACA,oBAAA;EACA,iBAAA;EACA,sBAAA;AAyEpB;AAvEoB;;EACI,gBAAA;EACA,uBAAA;EACA,qBAAA;AA0ExB;AA7EoB;;EAMQ,gBAAA;AA2E5B;AA/dA;EA4ZY,gBAAA;AAsEZ;AAleA;EAgaY,yBAAA;EAEA,sBAAA;EACA,iBAAA;EACA,mBAAA;EACA,qBAAA;EACA,gBAAA;EACA,oCAAA;EACA,qBAAA;EACA,4BAAA;EACA,6CAAA;EACA,yBAAA;EACA,gBAAA;AAoEZ;AAhfA;EAibY,aAAA;AAkEZ;AAnfA;EAqbY,wBAAA;AAiEZ;AAtfA;EAybY,kBAAA;EACA,WAAA;EACA,kDAAA;AAgEZ;AA3fA;EA6fE;;;;;;;;;;;cAWY;EA7DF,mBAAA;AA+DZ;AA1gBA;;EAgdY,8BAAA;AA8DZ;AA9gBA;EAodY,yBAAA;AA6DZ;AAjhBA;EAwdY,uBAAA;AA4DZ;AAphBA;EA4dY,6BAAA;AA2DZ;AAvhBA;EAgeY,kBAAA;AA0DZ;AA1hBA;EAoeY,YAAA;EACA,WAAA;EACA,wBAAA;EACA,iBAAA;EACA,YAAA;EACA,oBAAA;EACA,iBAAA;EACA,sBAAA;EACA,wBAAA;EACA,6BAAA;EAyDV,wCAAwC;EACxC;;;cAGY;EAvDF,iBAAA;AAyDZ;AA5iBA;EAufY,kBAAA;EACA,oCAAA;EACA,eAAA;AAwDZ;AAjjBA;EA6fY,aAAA;AAuDZ;AApjBA;EAigBY,YAAA;EACA,gBAAA;EACA,WAAA;EACA,kBAAA;EACA,mBAAA;EACA,eAAA;EACA,wBAAA;AAsDZ;AA7jBA;EA2gBY,oBAAA;AAqDZ;AAhkBA;EA+gBY,oBAAA;AAoDZ;AAnkBA;;EArII,2BAAA;EACA,yBAAA;EACA,wBAAA;EACA,sBAAA;EACA,qBAAA;EACA,iBAAA;EACA,eAAA;EAqpBQ,iBAAA;EACA,kBAAA;EACA,eAAA;EACA,mBAAA;AAwDZ;AAjlBA;;EA6hBY,oBAAA;AAwDZ;AArlBA;;EAmiBgB,aAAA;AAsDhB;AAzlBA;;EAsiBoB,OAAA;EACA,kBAAA;AAuDpB;AAtDoB;;EACI,gBAAA;AAyDxB;AAlmBA;EA8iBgB,2BAAA;AAuDhB;AArmBA;EAijBgB,aAAA;AAuDhB;AAxmBA;EAqjBgB,YAAA;AAsDhB;AA3mBA;;EAyjBgB,eAAA;AAsDhB;AA/mBA;EA6jBU,YAAA;AAqDV;AAlnBA;;EAkkBU,+BAAA;AAoDV;AAtnBA;;;;;EA2kBgB,YAAA;EACA,iBAAA;EACA,kBAAA;AAkDhB;AA7CoB;;EACI,oBAAA;EACA,UAAA;AAgDxB;AApoBA;EA0lBgB,eAAA;AA6ChB",sourcesContent:["@import (reference) '~dash-table/style/reset.less';\n\n.fit-content-polyfill() {\n    width: auto; // MS Edge, IE\n    width: fit-content; // Chrome\n    width: -moz-fit-content; // Firefox\n    width: -webkit-fit-content; // Safari\n}\n\n.not-selectable() {\n    -webkit-touch-callout: none;\n    -webkit-user-select: none;\n    -khtml-user-select: none;\n    -moz-user-select: none;\n    -ms-user-select: none;\n    user-select: none;\n    cursor: default;\n}\n\n.dash-spreadsheet {\n    &.dash-freeze-left,\n    &.dash-freeze-top {\n        .fit-content-polyfill();\n    }\n\n    &.dash-freeze-left {\n        max-width: 500px;\n    }\n\n    &.dash-freeze-top,\n    &.dash-virtualized {\n        max-height: 500px;\n    }\n}\n\n.dash-tooltip {\n    border: 1px solid #e4e4e4;\n    border-radius: 5px;\n    position: absolute;\n    z-index: 500;\n\n    .dash-table-tooltip {\n        position: relative;\n        background-color: #f6f6f6;\n        max-width: 300px;\n        min-width: 300px;\n        padding: 2px 10px;\n    }\n\n    &[data-attr-anchor='top'] {\n        margin-top: 10px;\n\n        &:after, &:before {\n            bottom: 100%;\n            left: 50%;\n            border: solid transparent;\n            content: \" \";\n            height: 0;\n            width: 0;\n            position: absolute;\n            pointer-events: none;\n        }\n\n        &:after {\n            border-color: transparent;\n            border-bottom-color: #f6f6f6;\n            border-width: 8px;\n            margin-left: -8px;\n        }\n\n        &:before {\n            border-color: transparent;\n            border-bottom-color: #e4e4e4;\n            border-width: 9px;\n            margin-left: -9px;\n        }\n    }\n\n    &[data-attr-anchor='bottom'] {\n        margin-bottom: 10px;\n\n        &:after, &:before {\n            top: 100%;\n            left: 50%;\n            border: solid transparent;\n            content: \" \";\n            height: 0;\n            width: 0;\n            position: absolute;\n            pointer-events: none;\n        }\n\n        &:after {\n            border-color: transparent;\n            border-top-color: #f6f6f6;\n            border-width: 8px;\n            margin-left: -8px;\n        }\n        &:before {\n            border-color: transparent;\n            border-top-color: #e4e4e4;\n            border-width: 9px;\n            margin-left: -9px;\n        }\n    }\n}\n\n.dash-spreadsheet-menu {\n    display: flex;\n    flex-direction: row;\n\n    & > * {\n        padding-right: 5px;\n    }\n\n    .dash-spreadsheet-menu-item {\n        position: relative;\n\n        .show-hide-menu {\n            background-color: #fafafa;\n            border: 1px solid #d3d3d3;\n            display: flex;\n            flex-direction: column;\n            max-height: 300px;\n            overflow: auto;\n            position: absolute;\n            top: 100%;\n            left: 0;\n            z-index: 500;\n\n            .show-hide-menu-item {\n                display: flex;\n                flex-direction: row;\n                padding: 5px;\n\n                label {\n                    white-space: nowrap;\n                }\n            }\n        }\n    }\n}\n\n.dash-table-container {\n    .previous-next-container {\n        text-align: right;\n        padding: 5px 0px;\n\n        .page-number {\n            font-family: monospace;\n            display: inline-block;\n\n            .last-page {\n                display: inline-block;\n                text-align: center;\n                padding: 1px 2px;\n            }\n\n            .current-page-container {\n                display: inline-block;\n                position: relative;\n\n                .current-page-shadow,\n                input.current-page {\n                    display: inline-block;\n                    border-bottom: solid lightgrey 1px !important;\n                    color: black;\n                    border: none;\n                    text-align: center;\n                    font-family: monospace;\n                    font-size: 10pt;\n                    padding: 1px 2px;\n\n                    &::placeholder {\n                        color: black;\n                    }\n\n                    &:focus {\n                        outline: none;\n\n                        &::placeholder {\n                            opacity: 0;\n                        }\n                    }\n                }\n\n                input.current-page {\n                    position: absolute;\n                    top: 0;\n                    left: 0;\n                    width: 100%;\n                    height: 100%;\n                }\n            }\n        }\n\n        button.previous-page, button.next-page, button.first-page, button.last-page {\n            transition-duration: 400ms;\n            padding: 5px;\n            border: none;\n            display: inline-block;\n            margin-left: 5px;\n            margin-right: 5px;\n\n            &:hover {\n                color: hotpink;\n\n                &:disabled {\n                    color: graytext\n                }\n            }\n\n            &:focus {\n                outline: none;\n            }\n        }\n    }\n\n    .dash-spreadsheet-container {\n        .reset-css();\n        display: flex;\n        flex-direction: row;\n        position: relative;\n\n        // This overrides Bootstrap 3.4.1 body styling\n        // https://github.com/twbs/bootstrap/blob/v3-dev/dist/css/bootstrap.css#L1087\n        // Also unapplies with the latest `in development` 5.0.0-alpha2 (https://github.com/twbs/bootstrap/blob/main/dist/css/bootstrap.css#L51)\n        line-height: initial;\n\n        // This overrides Chrome's default `font-size: medium;` which is causing performance issues\n        // with AutoInputResize sub-component in react-select\n        // https://github.com/JedWatson/react-input-autosize/blob/05b0f86a7f8b16de99c2b31296ff0d3307f15957/src/AutosizeInput.js#L58\n        table {\n            font-size: inherit;\n            pointer-events: none;\n\n            td, th {\n                pointer-events: initial;\n            }\n        }\n\n        input[type=\"radio\"] {\n            // These override Bootstrap 3.4.1 type=\"radio\" styling\n            // https://github.com/twbs/bootstrap/blob/v3-dev/dist/css/bootstrap.css#L2621\n            // This is not a problem with the latest `in development` 5.0.0-alpha2\n            margin: initial;\n            line-height: initial;\n            // These override Bootstrap 4.5.0 type=\"radio\" styling\n            // https://github.com/twbs/bootstrap/blob/v4-dev/dist/css/bootstrap.css#L287\n            // This is not a problem with the latest `in development` 5.0.0-alpha2\n            box-sizing: initial;\n            padding: initial;\n        }\n\n\t    .dash-spreadsheet-inner {\n            box-sizing: border-box;\n            display: flex;\n            flex-direction: column;\n\n            *,\n            *:after,\n            *:before {\n\t\t        box-sizing: inherit;\n            }\n\n            .Select {\n                overflow: hidden;\n                position: static;\n            }\n\n            .Select,\n            .Select-control {\n\t\t        background-color: inherit;\n            }\n\n            .Select-value {\n                display: flex;\n                flex-direction: column;\n                justify-content: center;\n                margin-top: -2px;\n            }\n\n            .marker-row {\n                tr {\n                    visibility: hidden !important;\n                }\n\n                td, th {\n                    height: 0 !important;\n                    padding: 0 !important;\n                    margin: 0 !important;\n                }\n            }\n\n            .dash-filter {\n\t\t        input::placeholder {\n                    color: inherit;\n                    font-size: 0.8em;\n                    padding-right: 5px;\n                }\n\n                & + .dash-filter {\n                    &:not(:hover):not(:focus-within) {\n                        input::placeholder {\n                            color: transparent;\n                        }\n                    }\n                }\n\n                &.invalid {\n                    background-color: pink;\n                }\n            }\n\n            &:not(.dash-empty-11) {\n                .dt-table-container__row-0 {\n                    tr:last-of-type {\n                        td, th {\n                            border-bottom: none !important;\n                        }\n                    }\n                }\n            }\n\n            &:not(.dash-empty-01) {\n        \t\t.cell-0-0,\n\t\t        .cell-1-0 {\n                    tr {\n                        td, th {\n                            &:last-of-type {\n                                border-right: none !important;\n                            }\n                        }\n                    }\n\t    \t    }\n            }\n\n            .cell-0-0 {\n                overflow: hidden;\n            }\n\n            .cell-0-0,\n            .cell-1-0 {\n                td, th {\n                    &.phantom-cell {\n                        border-color: transparent !important;\n                    }\n                }\n            }\n\n            .cell-0-1,\n            .cell-1-1 {\n                td, th {\n                    &.phantom-cell {\n                        border-color: transparent inherit transparent transparent !important;\n                    }\n                }\n            }\n\n            &.dash-freeze-left,\n            &.dash-freeze-top,\n            &.dash-virtualized {\n                overflow: hidden !important;\n\n                .dt-table-container__row-0 {\n                    display: flex;\n                    flex: 0 0 auto;\n                    flex-direction: row;\n                }\n\n                .dt-table-container__row-1 {\n                    display: flex;\n                    flex-direction: row;\n                    overflow: auto;\n                }\n\n                .cell-0-0,\n                .cell-1-0 {\n                    flex: 0 0 auto;\n                    left: 0;\n                    position: sticky;\n                    position:-webkit-sticky;\n                    z-index: 400;\n                }\n\n                .cell-0-1 {\n                    z-index: 300;\n                    flex: 0 0 auto;\n                }\n\n                .cell-1-1 {\n                    flex: 0 0 auto;\n                }\n            }\n\n            &.dash-fill-width {\n                .cell-0-1,\n                .cell-1-1 {\n                    flex: 1 0 auto;\n                }\n\n                .cell {\n                    table {\n                        width: 100%;\n                    }\n                }\n            }\n\n            td {\n                background-color: inherit;\n\n                &.focused {\n                    margin: -1px;\n                    z-index: 200;\n                }\n\n                .dash-cell-value-container {\n                    width: 100%;\n                    height: 100%;\n                }\n\n                .dash-input-cell-value-container {\n                    position: relative;\n                }\n\n                .dash-cell-value {\n                    height: 100%;\n                    width: 100%;\n\n                    &.unfocused.selectable::selection {\n                        background-color: transparent;\n                    }\n\n                    &.unfocused {\n                        caret-color: transparent;\n                    }\n                }\n\n                input.dash-cell-value {\n                    position: absolute;\n                    left: 0;\n                    top: 0;\n                }\n\n                .cell-value-shadow {\n                    margin: auto 0;\n                    opacity: 0;\n                }\n\n                .input-cell-value-shadow {\n                    display: inline-block;\n                    height: initial;\n                    width: initial;\n                }\n\n                .dropdown-cell-value-shadow {\n                    display: block;\n                    height: 0px;\n                    padding: 0 42px 0 10px;\n                }\n            }\n\n            /*\n             * fixes Firefox td height bug on td > dropdown children\n             * bug should only appear on FF but\n             * @supports = scoped to Firefox only\n             * to minimize side effects\n             */\n            @supports (-moz-appearance:none) {\n                td.dropdown .dash-cell-value-container {\n                    height: auto;\n                }\n            }\n\n            th.dash-filter {\n                position: relative;\n\n                & input {\n                    left: 0;\n                    top: 0;\n                    height: 100%;\n                    width: 100%;\n\n                    &.dash-filter--case {\n                        position: relative;\n                        left: auto;\n                        top: auto;\n                        width: auto;\n                        height: 16px;\n                        line-height: 0px;\n                        padding: 1px;\n                    }\n                    &.dash-filter--case--sensitive {\n                        border-color: hotpink;\n                        border-radius: 3px;\n                        border-style: solid;\n                        border-width: 2px;\n                        color: hotpink;\n                    }\n                }\n            }\n\n            th {\n                white-space: nowrap;\n\n                .column-header--clear,\n                .column-header--delete,\n                .column-header--edit,\n                .column-header--hide,\n                .column-header--sort {\n                    .not-selectable();\n                    cursor: pointer;\n                }\n            }\n\n            tr {\n                min-height: 30px;\n                height: 30px;\n            }\n\n            // cell content styling\n            td, th {\n                background-clip: padding-box;\n                padding: 2px;\n                overflow-x: hidden;\n                white-space: nowrap;\n                box-sizing: border-box;\n\n                text-align: right;\n\n                &.phantom-cell {\n                    visibility: hidden;\n                }\n\n                div.dash-cell-value {\n                    display: inline;\n                    vertical-align: middle;\n                    white-space: inherit;\n                    overflow: inherit;\n                    text-overflow: inherit;\n\n                    &.cell-markdown {\n                        text-align: left;\n                        font-family: sans-serif;\n                        display: inline-block;\n\n                        blockquote {\n                            white-space: pre;\n                        }\n                    }\n                }\n            }\n        }\n\n    \t.dash-spreadsheet-inner textarea {\n            white-space: pre;\n\t    }\n\n\t    .dash-spreadsheet-inner table {\n            border-collapse: collapse;\n\n            font-family: monospace;\n            --accent: hotpink;\n            --border: lightgrey;\n            --text-color: rgb(60, 60, 60);\n            --hover: rgb(253, 253, 253);\n            --background-color-ellipses: rgb(253, 253, 253);\n            --faded-text: rgb(250, 250, 250);\n            --faded-text-header: rgb(180, 180, 180);\n            --selected-background: rgba(255, 65, 54, 0.2);\n            --faded-dropdown: rgb(240, 240, 240);\n            --muted: rgb(200, 200, 200);\n\t    }\n\n\t    /* focus happens after copying to clipboard */\n\t    .dash-spreadsheet-inner table:focus {\n            outline: none;\n\t    }\n\n\t    .dash-spreadsheet-inner thead {\n            display: table-row-group;\n\t    }\n\n\t    .elip {\n            text-align: center;\n            width: 100%;\n            background-color: var(--background-color-ellipses);\n\t    }\n\n\t    .dash-spreadsheet-inner td.dropdown {\n            /*\n             * To view the dropdown's contents, we need\n             * overflow-y: visible.\n             * Unfortunately, overflow-x: hidden and overflow-y: visible\n             * can't both be set at the same time.\n             * So, we have to make both overflow-x: visible and overflow-y: visble\n             *\n             * See https://stackoverflow.com/questions/6421966/\n             *\n             * There might be another solution with parent divs, but I haven't\n             * tried it.\n             */\n            overflow-x: visible;\n\t    }\n\n        .dash-spreadsheet-inner :not(.cell--selected) tr:hover,\n        tr:hover input :not(.cell--selected) {\n            background-color: var(--hover);\n    \t}\n\n    \t.dash-spreadsheet-inner th {\n            background-color: rgb(250, 250, 250);\n\t    }\n\n\t    .dash-spreadsheet-inner td {\n            background-color: white;\n\t    }\n\n\t    .expanded-row--empty-cell {\n            background-color: transparent;\n\t    }\n\n\t    .expanded-row {\n            text-align: center;\n\t    }\n\n\t    .dash-spreadsheet-inner input:not([type=radio]):not([type=checkbox]) {\n            padding: 0px;\n            margin: 0px;\n            height: calc(100% - 1px);\n            line-height: 30px;\n            border: none;\n            font-family: inherit;\n            text-align: right;\n            box-sizing: border-box;\n            color: var(--text-color);\n            background-color: transparent; /* so as to not overlay the box shadow */\n\n            /* browser's default text-shadow is `$color 0px 0px 0px;`\n             * for `input`, which makes it look a little bit heavier than dropdowns\n             * or bare `td`\n             */\n            text-shadow: none;\n\t    }\n\n\t    .dash-spreadsheet-inner input.unfocused {\n            color: transparent;\n            text-shadow: 0 0 0 var(--text-color);\n            cursor: default;\n\t    }\n\n\t    .dash-spreadsheet-inner input.unfocused:focus {\n            outline: none;\n\t    }\n\n\t    .toggle-row {\n            border: none;\n            box-shadow: none;\n            width: 10px;\n            padding-left: 10px;\n            padding-right: 10px;\n            cursor: pointer;\n            color: var(--faded-text);\n\t    }\n\n    \t.toggle-row--expanded {\n            color: var(--accent);\n\t    }\n\n    \t.dash-spreadsheet-inner tr:hover .toggle-row {\n            color: var(--accent);\n\t    }\n\n        .dash-spreadsheet-inner .dash-delete-cell,\n        .dash-spreadsheet-inner .dash-delete-header {\n            .not-selectable();\n\n            font-size: 1.3rem;\n            text-align: center;\n            cursor: pointer;\n            color: var(--muted);\n    \t}\n        .dash-spreadsheet-inner .dash-delete-cell:hover,\n        .dash-spreadsheet-inner .dash-delete-header:hover {\n            color: var(--accent);\n    \t}\n\n\t    .dash-spreadsheet-inner {\n            .dash-header>div,\n            .dash-filter>div {\n                display: flex;\n\n                input[type=\"text\"] {\n                    flex: 1;\n                    line-height: unset;\n                    &::placeholder {\n                        font-size: 0.9em;\n                    }\n                }\n            }\n            .dash-filter>div {\n                flex-direction: row-reverse;\n            }\n            .column-actions {\n                display: flex;\n            }\n\n            .column-header-name {\n                flex-grow: 1;\n            }\n\n            [class^='column-header--'], [class^='dash-filter--'] {\n                cursor: pointer;\n            }\n\n            .column-header--select {\n\t\t        height: auto;\n            }\n\n            .column-header--select,\n            .column-header--sort {\n        \t\tcolor: var(--faded-text-header);\n            }\n\n\n            .dash-filter--case,\n            .column-header--clear,\n            .column-header--delete,\n            .column-header--edit,\n            .column-header--hide {\n                opacity: 0.1;\n                padding-left: 2px;\n                padding-right: 2px;\n            }\n\n            th:hover {\n        \t\t[class^='column-header--'], [class^='dash-filter--'] {\n                    &:not(.disabled) {\n                        color: var(--accent);\n                        opacity: 1;\n                    }\n                }\n            }\n\n            .dash-filter--case {\n                font-size: 10px;\n            }\n\t    }\n    }\n}\n",'/*RESET CSS*/\n.reset-css() {\n    /* The "normal" reset CSS */\n    // div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,code,form,fieldset,legend,input,textarea,p,blockquote,th,td{margin:0;padding:0}table{border-collapse:collapse;border-spacing:0}fieldset,img{border:0}address,caption,cite,code,dfn,em,strong,th,var{font-style:normal;font-weight:normal}ol,ul{list-style:none}caption,th{text-align:left}h1,h2,h3,h4,h5,h6{font-size:100%;font-weight:normal}q:before,q:after{content:\'\'}abbr,acronym{border:0;font-variant:normal}sup{vertical-align:text-top}sub{vertical-align:text-bottom}input,textarea,select{font-family:inherit;font-size:inherit;font-weight:inherit;*font-size:100%}legend{color:#000}#yui3-css-stamp.cssreset{display:none}\n\n    /* The "modified" reset CSS applied to the table to ignore markdown cells */\n    th {\n        font-style:normal;\n        font-weight:normal;\n        text-align:left;\n    }\n\n    th, td {\n        margin:0;\n        padding:0;\n\n        & > div:not(.cell-markdown) {\n            margin:0;\n            padding:0;\n\n            dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,code,form,fieldset,legend,input,textarea,p,blockquote,th,td{margin:0;padding:0}\n            table{border-collapse:collapse;border-spacing:0}\n            fieldset,img{border:0}\n            address,caption,cite,code,dfn,em,strong,th,var{font-style:normal;font-weight:normal}\n            ol,ul{list-style:none}\n            caption,th{text-align:left}\n            h1,h2,h3,h4,h5,h6{font-size:100%;font-weight:normal}\n            q:before,q:after{content:\'\'}\n            abbr,acronym{border:0;font-variant:normal}\n            sup{vertical-align:text-top}\n            sub{vertical-align:text-bottom}\n            input,textarea,select{font-family:inherit;font-size:inherit;font-weight:inherit;*font-size:100%}\n            legend{color:#000}\n        }\n    }\n\n    // Input buttons have an overlay + are rounded by default in iOS Mobile Safari\n    // http://stackoverflow.com/questions/2918707/turn-off-iphone-safari-input-element-rounding\n    input[type="button"] {\n        border-radius: 0;\n        -webkit-appearance: none;\n    }\n\n    *:focus {\n        outline: none;\n    }\n}'],sourceRoot:""}]),t.A=a},6886:e=>{"use strict";e.exports=function(e){for(var t,n=e.length,r=0;r<n;r++)if(((t=e.charCodeAt(r))<9||t>13)&&32!==t&&133!==t&&160!==t&&5760!==t&&6158!==t&&(t<8192||t>8205)&&8232!==t&&8233!==t&&8239!==t&&8287!==t&&8288!==t&&12288!==t&&65279!==t)return!1;return!0}},6942:(e,t)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=a(e,i(n)))}return e}function i(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return o.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)r.call(e,n)&&e[n]&&(t=a(t,n));return t}function a(e,t){return t?e?e+" "+t:e+t:e}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()},7365:(e,t,n)=>{"use strict";var r=n(6886);e.exports=function(e){var t=typeof e;if("string"===t){var n=e;if(0===(e=+e)&&r(n))return!1}else if("number"!==t)return!1;return e-e<1}},7667:(e,t,n)=>{"use strict";n.d(t,{B4:()=>o,Hc:()=>a,ty:()=>i});var r=n(726);function o(e){var t,n=null;return function(){for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return(0,r.y)(n,i)?t:(n=i)&&(t=e(...i))}}function i(e){return()=>o(e)}function a(e){var t,n=null,o=!0;return function(){for(var i=arguments.length,a=new Array(i),s=0;s<i;s++)a[s]=arguments[s];var l=(0,r.y)(n,a)?{cached:!0,first:o,result:t}:{cached:!1,first:o,result:(n=a)&&(t=e(...a))};return o=!1,l}}},9132:(e,t,n)=>{"use strict";var r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=n(1609),a=l(i),s=l(n(6120));function l(e){return e&&e.__esModule?e:{default:e}}var c={position:"absolute",top:0,left:0,visibility:"hidden",height:0,overflow:"scroll",whiteSpace:"pre"},u=["extraWidth","injectStyles","inputClassName","inputRef","inputStyle","minWidth","onAutosize","placeholderIsMinWidth"],d=function(e,t){t.style.fontSize=e.fontSize,t.style.fontFamily=e.fontFamily,t.style.fontWeight=e.fontWeight,t.style.fontStyle=e.fontStyle,t.style.letterSpacing=e.letterSpacing,t.style.textTransform=e.textTransform},p=!("undefined"==typeof window||!window.navigator)&&/MSIE |Trident\/|Edge\//.test(window.navigator.userAgent),h=function(){return p?"_"+Math.random().toString(36).substr(2,12):void 0},f=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.inputRef=function(e){n.input=e,"function"==typeof n.props.inputRef&&n.props.inputRef(e)},n.placeHolderSizerRef=function(e){n.placeHolderSizer=e},n.sizerRef=function(e){n.sizer=e},n.state={inputWidth:e.minWidth,inputId:e.id||h()},n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),o(t,[{key:"componentDidMount",value:function(){this.mounted=!0,this.copyInputStyles(),this.updateInputWidth()}},{key:"UNSAFE_componentWillReceiveProps",value:function(e){var t=e.id;t!==this.props.id&&this.setState({inputId:t||h()})}},{key:"componentDidUpdate",value:function(e,t){t.inputWidth!==this.state.inputWidth&&"function"==typeof this.props.onAutosize&&this.props.onAutosize(this.state.inputWidth),this.updateInputWidth()}},{key:"componentWillUnmount",value:function(){this.mounted=!1}},{key:"copyInputStyles",value:function(){if(this.mounted&&window.getComputedStyle){var e=this.input&&window.getComputedStyle(this.input);e&&(d(e,this.sizer),this.placeHolderSizer&&d(e,this.placeHolderSizer))}}},{key:"updateInputWidth",value:function(){if(this.mounted&&this.sizer&&void 0!==this.sizer.scrollWidth){var e=void 0;e=this.props.placeholder&&(!this.props.value||this.props.value&&this.props.placeholderIsMinWidth)?Math.max(this.sizer.scrollWidth,this.placeHolderSizer.scrollWidth)+2:this.sizer.scrollWidth+2,(e+="number"===this.props.type&&void 0===this.props.extraWidth?16:parseInt(this.props.extraWidth)||0)<this.props.minWidth&&(e=this.props.minWidth),e!==this.state.inputWidth&&this.setState({inputWidth:e})}}},{key:"getInput",value:function(){return this.input}},{key:"focus",value:function(){this.input.focus()}},{key:"blur",value:function(){this.input.blur()}},{key:"select",value:function(){this.input.select()}},{key:"renderStyles",value:function(){var e=this.props.injectStyles;return p&&e?a.default.createElement("style",{dangerouslySetInnerHTML:{__html:"input#"+this.state.inputId+"::-ms-clear {display: none;}"}}):null}},{key:"render",value:function(){var e=[this.props.defaultValue,this.props.value,""].reduce(function(e,t){return null!=e?e:t}),t=r({},this.props.style);t.display||(t.display="inline-block");var n=r({boxSizing:"content-box",width:this.state.inputWidth+"px"},this.props.inputStyle),o=function(e,t){var n={};for(var r in e)t.indexOf(r)>=0||Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n}(this.props,[]);return function(e){u.forEach(function(t){return delete e[t]})}(o),o.className=this.props.inputClassName,o.id=this.state.inputId,o.style=n,a.default.createElement("div",{className:this.props.className,style:t},this.renderStyles(),a.default.createElement("input",r({},o,{ref:this.inputRef})),a.default.createElement("div",{ref:this.sizerRef,style:c},e),this.props.placeholder?a.default.createElement("div",{ref:this.placeHolderSizerRef,style:c},this.props.placeholder):null)}}]),t}(i.Component);f.propTypes={className:s.default.string,defaultValue:s.default.any,extraWidth:s.default.oneOfType([s.default.number,s.default.string]),id:s.default.string,injectStyles:s.default.bool,inputClassName:s.default.string,inputRef:s.default.func,inputStyle:s.default.object,minWidth:s.default.oneOfType([s.default.number,s.default.string]),onAutosize:s.default.func,onChange:s.default.func,placeholder:s.default.string,placeholderIsMinWidth:s.default.bool,style:s.default.object,value:s.default.any},f.defaultProps={minWidth:1,injectStyles:!0},t.A=f},9614:(e,t,n)=>{"use strict";var r=n(371),o=(0,n(2254).A)((0,r.A)("forEach",function(e,t){for(var n=t.length,r=0;r<n;)e(t[r]),r+=1;return t}));t.A=o}}]);
//# sourceMappingURL=async-table.js.map