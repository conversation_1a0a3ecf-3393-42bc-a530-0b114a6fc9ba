{"version": 3, "file": "bundle.js", "mappings": "UACIA,EADAC,ECAAC,EACAC,E,6DCiBW,SAASC,EAAcC,EAAaC,EAAmBC,GACpE,OAAO,WACL,GAAyB,IAArBC,UAAUC,OACZ,OAAOF,IAET,IAAIG,EAAMF,UAAUA,UAAUC,OAAS,GACvC,KAAK,EAAAE,EAAA,GAASD,GAAM,CAElB,IADA,IAAIE,EAAM,EACHA,EAAMP,EAAYI,QAAQ,CAC/B,GAAqC,mBAA1BC,EAAIL,EAAYO,IACzB,OAAOF,EAAIL,EAAYO,IAAMC,MAAMH,EAAKI,MAAMC,UAAUC,MAAMC,KAAKT,UAAW,GAAI,IAEpFI,GAAO,CACT,CACA,GChCS,SAAwBF,GACrC,OAAc,MAAPA,GAAmD,mBAA7BA,EAAI,oBACnC,CD8BUQ,CAAeR,GAEjB,OADiBJ,EAAkBO,MAAM,KAAMC,MAAMC,UAAUC,MAAMC,KAAKT,UAAW,GAAI,GAClFW,CAAWT,EAEtB,CACA,OAAOH,EAAGM,MAAMO,KAAMZ,UACxB,CACF,C,8BEvCe,SAASa,EAAKC,EAAMZ,GACjC,OAAOa,OAAOR,UAAUS,eAAeP,KAAKP,EAAKY,EACnD,C,wCCFAG,EAAOC,QAAUC,OAAc,K,kCCmB3BC,GAAqB,E,QAAA,GAAQ,SAAeC,GAC9C,OAAY,MAALA,CACT,GACA,K,4BCtBA,KACEC,KAAM,WACJ,OAAOV,KAAKW,GAAG,sBACjB,EACAC,OAAQ,SAAUA,GAChB,OAAOZ,KAAKW,GAAG,uBAAuBC,EACxC,E,4CCgBEC,GAAoB,E,QAAA,GAAQ,SAAc1B,GAC5C,IACIyB,EADAE,GAAS,EAEb,OAAO,OAAO3B,EAAGE,OAAQ,WACvB,OAAIyB,EACKF,GAETE,GAAS,EACTF,EAASzB,EAAGM,MAAMO,KAAMZ,WAE1B,EACF,GACA,K,iFCtBe,SAAS2B,EAAQ5B,GAC9B,OAAO,SAAS6B,EAAGC,EAAGC,EAAGC,GACvB,OAAQ/B,UAAUC,QAChB,KAAK,EACH,OAAO2B,EACT,KAAK,EACH,OAAO,OAAeC,GAAKD,GAAK,OAAQ,SAAUI,EAAIC,GACpD,OAAOlC,EAAG8B,EAAGG,EAAIC,EACnB,GACF,KAAK,EACH,OAAO,OAAeJ,KAAM,OAAeC,GAAKF,GAAK,OAAeC,IAAK,OAAQ,SAAUK,EAAID,GAC7F,OAAOlC,EAAGmC,EAAIJ,EAAGG,EACnB,IAAK,OAAeH,IAAK,OAAQ,SAAUE,EAAIC,GAC7C,OAAOlC,EAAG8B,EAAGG,EAAIC,EACnB,IAAK,OAAQ,SAAUA,GACrB,OAAOlC,EAAG8B,EAAGC,EAAGG,EAClB,GACF,QACE,OAAO,OAAeJ,KAAM,OAAeC,KAAM,OAAeC,GAAKH,GAAK,OAAeC,KAAM,OAAeC,IAAK,OAAQ,SAAUI,EAAIF,GACvI,OAAOjC,EAAGmC,EAAIF,EAAID,EACpB,IAAK,OAAeF,KAAM,OAAeE,IAAK,OAAQ,SAAUG,EAAID,GAClE,OAAOlC,EAAGmC,EAAIJ,EAAGG,EACnB,IAAK,OAAeH,KAAM,OAAeC,IAAK,OAAQ,SAAUC,EAAIC,GAClE,OAAOlC,EAAG8B,EAAGG,EAAIC,EACnB,IAAK,OAAeJ,IAAK,OAAQ,SAAUK,GACzC,OAAOnC,EAAGmC,EAAIJ,EAAGC,EACnB,IAAK,OAAeD,IAAK,OAAQ,SAAUE,GACzC,OAAOjC,EAAG8B,EAAGG,EAAID,EACnB,IAAK,OAAeA,IAAK,OAAQ,SAAUE,GACzC,OAAOlC,EAAG8B,EAAGC,EAAGG,EAClB,GAAKlC,EAAG8B,EAAGC,EAAGC,GAEpB,CACF,C,uBC5CE,IAASI,SAYQ,IAAV,EAAAC,EAAwB,EAAAA,EAASxB,KARxCK,EAAOC,QAQuC,SAASiB,GAExD,GAAIA,EAAKE,KAAOF,EAAKE,IAAIC,OACxB,OAAOH,EAAKE,IAAIC,OAIjB,IAAIC,EAAY,SAASC,GACxB,GAAwB,GAApBxC,UAAUC,OACb,MAAM,IAAIwC,UAAU,sCAQrB,IANA,IAGIC,EAHAC,EAASC,OAAOJ,GAChBvC,EAAS0C,EAAO1C,OAChB4C,GAAS,EAETrB,EAAS,GACTsB,EAAgBH,EAAOI,WAAW,KAC7BF,EAAQ5C,GAOA,IANhByC,EAAWC,EAAOI,WAAWF,IA2B5BrB,GAbCkB,GAAY,GAAUA,GAAY,IAAuB,KAAZA,GAGpC,GAATG,GAAcH,GAAY,IAAUA,GAAY,IAIvC,GAATG,GACAH,GAAY,IAAUA,GAAY,IACjB,IAAjBI,EAIS,KAAOJ,EAASM,SAAS,IAAM,IAOhC,GAATH,GACU,GAAV5C,GACY,IAAZyC,KAWAA,GAAY,KACA,IAAZA,GACY,IAAZA,GACAA,GAAY,IAAUA,GAAY,IAClCA,GAAY,IAAUA,GAAY,IAClCA,GAAY,IAAUA,GAAY,KAdxB,KAAOC,EAAOM,OAAOJ,GAiBrBF,EAAOM,OAAOJ,GAhDxBrB,GAAU,IAyDZ,OAAOA,CACR,EAOA,OALKW,EAAKE,MACTF,EAAKE,IAAM,CAAC,GAGbF,EAAKE,IAAIC,OAASC,EACXA,CAER,CApGmBW,CAAQf,E,uECMZ,SAASgB,EAAQpD,GAC9B,OAAO,SAASqD,EAAGvB,EAAGC,GACpB,OAAQ9B,UAAUC,QAChB,KAAK,EACH,OAAOmD,EACT,KAAK,EACH,OAAO,OAAevB,GAAKuB,GAAK,OAAQ,SAAUpB,GAChD,OAAOjC,EAAG8B,EAAGG,EACf,GACF,QACE,OAAO,OAAeH,KAAM,OAAeC,GAAKsB,GAAK,OAAevB,IAAK,OAAQ,SAAUK,GACzF,OAAOnC,EAAGmC,EAAIJ,EAChB,IAAK,OAAeA,IAAK,OAAQ,SAAUE,GACzC,OAAOjC,EAAG8B,EAAGG,EACf,GAAKjC,EAAG8B,EAAGC,GAEjB,CACF,C,qGCaA,GAX6B,EAAAH,EAAA,GAAQ,SAAS0B,EAAUC,EAAMC,EAAKrD,GACjE,GAAoB,IAAhBoD,EAAKrD,OACP,OAAOsD,EAET,IAAInD,EAAMkD,EAAK,GACf,GAAIA,EAAKrD,OAAS,EAAG,CACnB,IAAIuD,IAAW,EAAApC,EAAA,GAAMlB,KAAQ,EAAAW,EAAA,GAAKT,EAAKF,IAA4B,iBAAbA,EAAIE,GAAoBF,EAAIE,IAAO,EAAAqD,EAAA,GAAWH,EAAK,IAAM,GAAK,CAAC,EACrHC,EAAMF,EAAU/C,MAAMC,UAAUC,MAAMC,KAAK6C,EAAM,GAAIC,EAAKC,EAC5D,CACA,OCxBa,SAAgB1C,EAAMyC,EAAKrD,GACxC,IAAI,EAAAuD,EAAA,GAAW3C,KAAS,EAAAX,EAAA,GAASD,GAAM,CACrC,IAAIwD,EAAM,GAAGC,OAAOzD,GAEpB,OADAwD,EAAI5C,GAAQyC,EACLG,CACT,CACA,IAAIlC,EAAS,CAAC,EACd,IAAK,IAAIoC,KAAK1D,EACZsB,EAAOoC,GAAK1D,EAAI0D,GAGlB,OADApC,EAAOV,GAAQyC,EACR/B,CACT,CDYSqC,CAAOzD,EAAKmD,EAAKrD,EAC1B,E,8BExCA,IAAI4D,E,oCACJ,SAAWA,GACPA,EAAWA,EAAkB,MAAI,GAAK,QACtCA,EAAWA,EAAiB,KAAI,GAAK,MACxC,CAHD,CAGGA,IAAeA,EAAa,CAAC,IAChC,ICLIC,EDKJ,KCJA,SAAWA,GACPA,EAASA,EAAgB,MAAI,GAAK,QAClCA,EAASA,EAAe,KAAI,GAAK,OACjCA,EAASA,EAAkB,QAAI,GAAK,UACpCA,EAASA,EAAgB,MAAI,GAAK,QAClCA,EAASA,EAAgB,MAAI,GAAK,QAClCA,EAASA,EAAe,KAAI,GAAK,MACpC,CAPD,CAOGA,IAAaA,EAAW,CAAC,IAC5B,QCPMC,EAAY,GAClBA,EAAUD,EAASE,OAAS,QAC5BD,EAAUD,EAASG,MAAQ,OAC3BF,EAAUD,EAASI,SAAW,UAC9BH,EAAUD,EAASK,OAAS,QAC5BJ,EAAUD,EAASM,OAAS,QAC5BL,EAAUD,EAASO,MAAQ,OAC3BN,EAAUF,EAAWS,OAAS,QAC9BP,EAAUF,EAAWQ,MAAQ,QAC7B,IAAIE,EAAaT,EAASO,KACtBG,EAAeX,EAAWQ,KAG9B,SAASI,EAAMC,EAAOC,GAClB,GAAID,EAAQC,EACR,MAAO,OAEX,IAAI7E,EAEJ,OAAQ4E,GACJ,KAAKZ,EAASE,MACd,KAAKF,EAASG,KACVnE,EAAKoB,OAAO0D,QAAQC,IAEpB,MACJ,KAAKhB,EAAWS,MAChB,KAAKR,EAASI,QACVpE,EAAKoB,OAAO0D,QAAQE,KAEpB,MACJ,KAAKhB,EAASK,MACd,KAAKL,EAASM,MACVtE,EAAKoB,OAAO0D,QAAQG,MAEpB,MACJ,QACI,MAAM,IAAIC,MAAM,eAADtB,OAAgBgB,IAEvC,IAAMO,EAAS,GAAHvB,OAA4C,GAAE,KAAAA,OAAIK,EAAUW,GAAOQ,cAAa,KAKxF,OAAOpF,EAAGqF,KAAKjE,OAAO0D,QAASK,EAEvC,CACA,IAAMG,EAAS,CACXC,aAAAA,CAAcX,GACVF,EAAeE,CACnB,EACAY,WAAAA,CAAYZ,GACRH,EAAaG,CACjB,GAEJ5D,OAAOyE,iBAAiBH,EAAQ,CAC5BI,MAAO,CACHC,IAAKA,IACMhB,EAAMX,EAASE,MAAOO,GAEjCmB,cAAc,EACdC,YAAY,GAEhBC,KAAM,CACFH,IAAKA,IACMhB,EAAMX,EAASG,KAAMM,GAEhCmB,cAAc,EACdC,YAAY,GAEhBE,QAAS,CACLJ,IAAKA,IACMhB,EAAMX,EAASI,QAASK,GAEnCmB,cAAc,EACdC,YAAY,GAEhBZ,MAAO,CACHU,IAAKA,IACMhB,EAAMX,EAASK,MAAOI,GAEjCmB,cAAc,EACdC,YAAY,GAEhBG,MAAO,CACHL,IAAKA,IACMhB,EAAMX,EAASM,MAAOG,GAEjCmB,cAAc,EACdC,YAAY,GAEhBI,MAAO,CACHN,IAAKA,IACMhB,EAAMZ,EAAWS,MAAOE,GAEnCkB,cAAc,EACdC,YAAY,KAGpB7E,OAAOkF,OAAOZ,GACd,O,uGC1DIa,GAAmB,QAAsB,OAAc,CAAC,mBAAoB,OAAQ,IAAO,SAAanG,EAAIoG,GAC9G,OAAQpF,OAAOR,UAAUyC,SAASvC,KAAK0F,IACrC,IAAK,oBACH,OAAO,OAAOA,EAAQlG,OAAQ,WAC5B,OAAOF,EAAGU,KAAKG,KAAMuF,EAAQ9F,MAAMO,KAAMZ,WAC3C,GACF,IAAK,kBACH,OAAO,OAAa,SAAUoG,EAAKC,GAEjC,OADAD,EAAIC,GAAOtG,EAAGoG,EAAQE,IACfD,CACT,EAAG,CAAC,GAAG,OAAKD,IACd,QACE,OAAO,OAAKpG,EAAIoG,GAEtB,IACA,K,8BC1De,SAASG,EAAezE,GACrC,OAAY,MAALA,GAA0B,iBAANA,IAAoD,IAAlCA,EAAE,2BACjD,C,uFCDI,EAAWd,OAAOR,UAAUyC,SAQhC,EAPgC,WAC9B,MAAoC,uBAA7B,EAASvC,KAAKT,WAAsC,SAAsBqB,GAC/E,MAA4B,uBAArB,EAASZ,KAAKY,EACvB,EAAI,SAAsBA,GACxB,OAAO,EAAAR,EAAA,GAAK,SAAUQ,EACxB,CACF,CANgC,GCG5BkF,GAA4B,CAC9BvD,SAAU,MACVwD,qBAAqB,YACnBC,EAAqB,CAAC,cAAe,UAAW,gBAAiB,WAAY,uBAAwB,iBAAkB,kBAEvHC,EAA8B,WAGhC,OAAO1G,UAAUwG,qBAAqB,SACxC,CAJkC,GAK9BG,EAAW,SAAkBC,EAAMC,GAErC,IADA,IAAIzG,EAAM,EACHA,EAAMwG,EAAK3G,QAAQ,CACxB,GAAI2G,EAAKxG,KAASyG,EAChB,OAAO,EAETzG,GAAO,CACT,CACA,OAAO,CACT,EA8CA,EA1BkC,mBAAhBW,OAAO+F,MAAwBJ,GAE/B,EAAAK,EAAA,GAAQ,SAAc7G,GACtC,GAAIa,OAAOb,KAASA,EAClB,MAAO,GAET,IAAIY,EAAMkG,EACNC,EAAK,GACLC,EAAkBR,GAAkB,EAAaxG,GACrD,IAAKY,KAAQZ,IACP,EAAAW,EAAA,GAAKC,EAAMZ,IAAUgH,GAA4B,WAATpG,IAC1CmG,EAAGA,EAAGhH,QAAUa,GAGpB,GAAIyF,EAEF,IADAS,EAAOP,EAAmBxG,OAAS,EAC5B+G,GAAQ,GACblG,EAAO2F,EAAmBO,IACtB,EAAAnG,EAAA,GAAKC,EAAMZ,KAASyG,EAASM,EAAInG,KACnCmG,EAAGA,EAAGhH,QAAUa,GAElBkG,GAAQ,EAGZ,OAAOC,CACT,IAzB+E,EAAAF,EAAA,GAAQ,SAAc7G,GACnG,OAAOa,OAAOb,KAASA,EAAM,GAAKa,OAAO+F,KAAK5G,EAChD,E,8BC9Ce,SAASiH,EAAaC,EAAShB,EAAKQ,GAGjD,IAFA,IAAI/D,EAAQ,EACR5C,EAAS2G,EAAK3G,OACX4C,EAAQ5C,GACbmG,EAAMgB,EAAQhB,EAAKQ,EAAK/D,IACxBA,GAAS,EAEX,OAAOuD,CACT,C,6ECEe,SAASW,EAAQhH,GAC9B,OAAO,SAASsH,EAAGxF,GACjB,OAAyB,IAArB7B,UAAUC,SAAgB,OAAe4B,GACpCwF,EAEAtH,EAAGM,MAAMO,KAAMZ,UAE1B,CACF,C,sDCMIsH,GAAqB,OAAQ,SAAexG,EAAMyC,EAAKrD,GACzD,OAAO,OAAU,CAACY,GAAOyC,EAAKrD,EAChC,GACA,K,4BCnBA,IAAeqH,OAAOC,WAAa,SAAoBC,GACrD,OAAY,EAALA,KAAWA,CACpB,C,iBCV4R,IAAUC,EAAjBvG,OAApNF,EAAOC,SAA8NwG,EAApN,EAAQ,MAAsN,SAASA,GAAG,IAAID,EAAE,CAAC,EAAE,SAASE,EAAEC,GAAG,GAAGH,EAAEG,GAAG,OAAOH,EAAEG,GAAG1G,QAAQ,IAAI2G,EAAEJ,EAAEG,GAAG,CAACE,EAAEF,EAAEG,GAAE,EAAG7G,QAAQ,CAAC,GAAG,OAAOwG,EAAEE,GAAGnH,KAAKoH,EAAE3G,QAAQ2G,EAAEA,EAAE3G,QAAQyG,GAAGE,EAAEE,GAAE,EAAGF,EAAE3G,OAAO,CAAC,OAAOyG,EAAEK,EAAEN,EAAEC,EAAE5F,EAAE0F,EAAEE,EAAEM,EAAE,SAASP,EAAED,EAAEG,GAAGD,EAAEE,EAAEH,EAAED,IAAI1G,OAAOmH,eAAeR,EAAED,EAAE,CAAC7B,YAAW,EAAGF,IAAIkC,GAAG,EAAED,EAAEC,EAAE,SAASF,GAAG,oBAAoBS,QAAQA,OAAOC,aAAarH,OAAOmH,eAAeR,EAAES,OAAOC,YAAY,CAAC5F,MAAM,WAAWzB,OAAOmH,eAAeR,EAAE,aAAa,CAAClF,OAAM,GAAI,EAAEmF,EAAEA,EAAE,SAASD,EAAED,GAAG,GAAG,EAAEA,IAAIC,EAAEC,EAAED,IAAI,EAAED,EAAE,OAAOC,EAAE,GAAG,EAAED,GAAG,iBAAiBC,GAAGA,GAAGA,EAAEW,WAAW,OAAOX,EAAE,IAAIE,EAAE7G,OAAOuH,OAAO,MAAM,GAAGX,EAAEC,EAAEA,GAAG7G,OAAOmH,eAAeN,EAAE,UAAU,CAAChC,YAAW,EAAGpD,MAAMkF,IAAI,EAAED,GAAG,iBAAiBC,EAAE,IAAI,IAAIG,KAAKH,EAAEC,EAAEM,EAAEL,EAAEC,EAAE,SAASJ,GAAG,OAAOC,EAAED,EAAE,EAAErC,KAAK,KAAKyC,IAAI,OAAOD,CAAC,EAAED,EAAEF,EAAE,SAASC,GAAG,IAAID,EAAEC,GAAGA,EAAEW,WAAW,WAAW,OAAOX,EAAEa,OAAO,EAAE,WAAW,OAAOb,CAAC,EAAE,OAAOC,EAAEM,EAAER,EAAE,IAAIA,GAAGA,CAAC,EAAEE,EAAEE,EAAE,SAASH,EAAED,GAAG,OAAO1G,OAAOR,UAAUS,eAAeP,KAAKiH,EAAED,EAAE,EAAEE,EAAE/D,EAAE,GAAG+D,EAAEA,EAAEa,EAAE,EAAE,CAAn5B,CAAq5B,CAAC,SAASf,EAAEE,GAAGF,EAAEvG,QAAQwG,CAAC,EAAE,SAASA,EAAED,EAAEE,GAAG,aAAaA,EAAEC,EAAEH,GAAGE,EAAEM,EAAER,EAAE,iBAAiB,WAAY,OAAOgB,CAAE,GAAGd,EAAEM,EAAER,EAAE,wBAAwB,WAAY,OAAO5F,CAAE,GAAG8F,EAAEM,EAAER,EAAE,UAAU,WAAY,OAAO1F,CAAE,GAAG4F,EAAEM,EAAER,EAAE,UAAU,WAAY,OAAOQ,CAAE,GAAG,IAAIL,EAAED,EAAE,GAAG,SAASE,EAAEH,EAAED,EAAEE,EAAEC,EAAEC,EAAEC,EAAEW,GAAG,IAAI,IAAI5G,EAAE6F,EAAEI,GAAGW,GAAG1G,EAAEF,EAAEW,KAAK,CAAC,MAAMkF,GAAG,YAAYC,EAAED,EAAE,CAAC7F,EAAE6G,KAAKjB,EAAE1F,GAAG4G,QAAQC,QAAQ7G,GAAG8G,KAAKjB,EAAEC,EAAE,CAAC,SAASC,EAAEJ,GAAG,OAAO,WAAW,IAAID,EAAE7G,KAAK+G,EAAE3H,UAAU,OAAO,IAAI2I,QAAQ,SAAUf,EAAEE,GAAG,IAAIW,EAAEf,EAAErH,MAAMoH,EAAEE,GAAG,SAAS9F,EAAE6F,GAAGG,EAAEY,EAAEb,EAAEE,EAAEjG,EAAEE,EAAE,OAAO2F,EAAE,CAAC,SAAS3F,EAAE2F,GAAGG,EAAEY,EAAEb,EAAEE,EAAEjG,EAAEE,EAAE,QAAQ2F,EAAE,CAAC7F,OAAE,EAAQ,EAAE,CAAC,CAAC,IAAI4G,EAAE,SAASf,EAAED,GAAG,IAAIE,EAAEE,EAAE,CAACiB,QAAQ,IAAIH,QAAQ,SAAUjB,GAAGC,EAAED,CAAE,GAAGhC,IAAI3E,OAAO6G,EAAEmB,KAAThI,CAAe,WAAY,OAAO4H,QAAQC,QAAQnB,KAAKoB,KAAK,SAAUnB,GAAG,OAAOsB,WAAWlB,EAAEmB,mBAAmBC,KAAK,SAAUxB,IAAI,OAAOuB,mBAAmBE,KAAK,SAAUzB,GAAG,OAAO,OAAOA,EAAE0B,KAAK1B,EAAE2B,MAAM,KAAK,EAAE,OAAO3B,EAAE2B,KAAK,EAAE1B,GAAE,GAAI,KAAK,EAAEE,EAAEiB,SAAQ,EAAG,KAAK,EAAE,IAAI,MAAM,OAAOpB,EAAE4B,OAAQ,EAAE5B,EAAG,IAAI,GAAGA,CAAE,EAAG,IAAI,OAAO3G,OAAOmH,eAAeR,EAAE,oCAAoC,CAAChC,IAAI,WAAW,OAAOmC,EAAEiB,OAAO,IAAIjB,EAAEnC,GAAG,EAAE7D,EAAE,SAAS6F,EAAED,GAAG1G,OAAOmH,eAAeR,EAAE,oCAAoC,CAAChC,IAAI,WAAW,OAAO3D,EAAE0F,EAAE,GAAG,EAAE1F,EAAE,SAAS2F,GAAG,OAAOA,GAAGA,EAAE6B,iCAAiC,EAAE,SAASC,EAAE9B,EAAED,GAAG,IAAI,IAAIE,EAAE,EAAEA,EAAEF,EAAExH,OAAO0H,IAAI,CAAC,IAAIC,EAAEH,EAAEE,GAAGC,EAAEhC,WAAWgC,EAAEhC,aAAY,EAAGgC,EAAEjC,cAAa,EAAG,UAAUiC,IAAIA,EAAE6B,UAAS,GAAI1I,OAAOmH,eAAeR,EAAEE,EAAEvB,IAAIuB,EAAE,CAAC,CAAC,IAAIY,EAAE,6BAA6BP,EAAE,WAAW,SAASP,KAAK,SAASA,EAAED,GAAG,KAAKC,aAAaD,GAAG,MAAM,IAAIhF,UAAU,oCAAoC,CAA3F,CAA6F7B,KAAK8G,EAAE,CAAC,IAAID,EAAIG,EAAE,OAAOH,EAAEC,EAAEE,EAAE,CAAC,CAACvB,IAAI,sBAAsB7D,MAAM,WAAWrB,OAAOuI,cAAc,IAAIC,YAAYnB,GAAG,GAAG,CAACnC,IAAI,WAAW7D,MAAM,SAASkF,GAAG,OAAOvG,OAAOyI,iBAAiBpB,EAAEd,GAAG,WAAW,OAAOvG,OAAO0I,oBAAoBrB,EAAEd,EAAE,CAAC,IAAO,MAAO8B,EAAE/B,EAAElH,UAAX,MAAwBqH,GAAG4B,EAAE/B,EAAEG,GAAG7G,OAAOmH,eAAeT,EAAE,YAAY,CAACgC,UAAS,IAAK/B,CAAC,CAAlc,EAAqc,I,4BCYriG,IAAepH,MAAMwJ,SAAW,SAAkBvG,GAChD,OAAc,MAAPA,GAAeA,EAAItD,QAAU,GAA6C,mBAAxCc,OAAOR,UAAUyC,SAASvC,KAAK8C,EAC1E,C,wBCdAtC,EAAOC,QAAUC,OAAiB,Q,0EC+C9B4I,GAAsB,OAAQ,SAAgB9J,EAAQF,GACxD,OAAe,IAAXE,GACK,OAAQF,IAEV,OAAOE,GAAQ,OAAQA,EAAQ,GAAIF,GAC5C,GACA,K,gEC3BIe,GAAoB,OAAQ,SAAc8C,EAAG1D,GAC/C,GAAW,MAAPA,EAGJ,OAAO,OAAW0D,IAAK,OAAKA,EAAG1D,GAAOA,EAAI0D,EAC5C,GACA,K,wBChCA3C,EAAOC,QAAUC,OAAkB,S,6DCCpB,SAAS6I,EAAKC,EAAQrD,GACnC,IAAIxG,EAAM6J,EAAS,EAAIrD,EAAK3G,OAASgK,EAASA,EAC9C,OAAO,OAAUrD,GAAQA,EAAK3D,OAAO7C,GAAOwG,EAAKxG,EACnD,C,mECDe,MAAM8J,EAmBjB,aAAOC,CAAOC,GAA6B,IAAzBC,EAAMrK,UAAAC,OAAA,QAAAqK,IAAAtK,UAAA,GAAAA,UAAA,GAAG,GAAIsD,EAAItD,UAAAC,OAAA,QAAAqK,IAAAtK,UAAA,GAAAA,UAAA,GAAG,IAClC,GAAKkK,EAAcK,UAAnB,CAGA,IAAMC,EAAU,IAAIC,KAAKA,KAAKC,MAzBvB,OAyBuCC,cAC9CC,SAASC,OAAS,GAAHlH,OAAMyG,EAAE,cAAAzG,OAAa6G,EAAO,YAAA7G,OAAW0G,EAAM,UAAA1G,OAASL,EAFrE,CAGJ,CACA,UAAOoC,CAAI0E,GACP,GAAKA,EAAGnK,QAGHiK,EAAcK,UAWnB,OARAH,EAAKA,EAAGU,eACQF,SAASC,OAAOE,MAAM,KAAK7E,IAAI2E,IAC3C,IAAMG,EAAYH,EAAOE,MAAM,KAC/B,MAAO,CACHX,GAAIY,EAAU,GAAGC,OACjBzI,MAAOwI,EAAU,MAGTE,KAAKL,GAAUT,IAAOS,EAAOT,GAAGe,sBAC5C,CAAC,GAAG3I,KACZ,CACA,UAAO4I,CAAIhB,EAAI5H,GAAgC,IAAzB6H,EAAMrK,UAAAC,OAAA,QAAAqK,IAAAtK,UAAA,GAAAA,UAAA,GAAG,GAAIsD,EAAItD,UAAAC,OAAA,QAAAqK,IAAAtK,UAAA,GAAAA,UAAA,GAAG,IACtC,GAAKkK,EAAcK,UAAnB,CAGA,IAAMC,EAAU,IAAIC,KAAKA,KAAKC,MAjDpB,SAiDuCC,cAC3CU,EAAQ,GAAH1H,OAAMyG,EAAE,KAAAzG,OAAInB,EAAK,aAAAmB,OAAY6G,EAAO,YAAA7G,OAAW0G,EAAM,UAAA1G,OAASL,GACrE4G,EAAcxE,IAAI0E,IAClBF,EAAcC,OAAOC,EAAIC,EAAQ/G,GAErCsH,SAASC,OAASQ,CANlB,CAOJ,E,EAtDiBnB,E,EAAa,U,EAKboB,EAAAA,EAAO,KACpB,IAEIV,SAASC,OAAS,eAClB,IAAMU,GAAkD,IAA5CX,SAASC,OAAOW,QAAQ,eAIpC,OAFAZ,SAASC,OACL,sDACGU,CACX,CACA,MAAO7D,GACH,OAAO,CACX,K,2yBClBR,IAAM+D,EAAa,aACbC,EAAW,WACF,MAAMC,EAKjB,uBAAWC,GACP,MAAwB,oBAARC,KACZA,IAAItL,WACJsL,IAAItL,UAAUuL,aACd,IAAID,IAAI1K,OAAO4K,SAASC,MAAMJ,cAAiB,CAAElG,IAAKA,IAAM,KACpE,CACA,qBAAWuG,GACP,IAAMjG,EAAQpF,KAAKgL,aAAalG,IAAI+F,IAAevB,EAAcxE,IAAI+F,GACrE,OAAOzF,GACDlC,EAAAA,GAAWkC,IACXlC,EAAAA,GAAWQ,IACrB,CACA,mBAAW4H,GACP,IAAMpH,EAAMlE,KAAKgL,aAAalG,IAAIgG,IAAaxB,EAAcxE,IAAIgG,GACjE,OAAO5G,GAAMf,EAAAA,GAASe,IAAyBf,EAAAA,GAASK,KAC5D,CACA,sBAAW+H,GACP,MAAO,mBACX,CACA,qBAAWC,GACP,OAAOT,EAAYU,WACvB,CACA,+BAAWC,GACP,OAAOX,EAAYY,qBACvB,EACHC,EA9BoBb,EAAWc,EAAXd,EAAW,wBACGe,QAAkB,QAAXC,EAACxL,OAAOkB,WAAG,IAAAsK,GAAU,QAAVC,EAAVD,EAAYE,gBAAQ,IAAAD,OAAA,EAApBA,EAAAnM,KAAAkM,EAAuB,iBAAkB,qBAAmBF,EADlFd,EAAW,cAEPA,EAAYY,sBAC3B,0BACA,oB,8BCRK,SAASO,EAAOrF,EAAG1H,GAEhC,OAAQ0H,GACN,KAAK,EACH,OAAO,WACL,OAAO1H,EAAGM,MAAMO,KAAMZ,UACxB,EACF,KAAK,EACH,OAAO,SAAU+M,GACf,OAAOhN,EAAGM,MAAMO,KAAMZ,UACxB,EACF,KAAK,EACH,OAAO,SAAU+M,EAAIC,GACnB,OAAOjN,EAAGM,MAAMO,KAAMZ,UACxB,EACF,KAAK,EACH,OAAO,SAAU+M,EAAIC,EAAIC,GACvB,OAAOlN,EAAGM,MAAMO,KAAMZ,UACxB,EACF,KAAK,EACH,OAAO,SAAU+M,EAAIC,EAAIC,EAAIC,GAC3B,OAAOnN,EAAGM,MAAMO,KAAMZ,UACxB,EACF,KAAK,EACH,OAAO,SAAU+M,EAAIC,EAAIC,EAAIC,EAAIC,GAC/B,OAAOpN,EAAGM,MAAMO,KAAMZ,UACxB,EACF,KAAK,EACH,OAAO,SAAU+M,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,GACnC,OAAOrN,EAAGM,MAAMO,KAAMZ,UACxB,EACF,KAAK,EACH,OAAO,SAAU+M,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,GACvC,OAAOtN,EAAGM,MAAMO,KAAMZ,UACxB,EACF,KAAK,EACH,OAAO,SAAU+M,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,GAC3C,OAAOvN,EAAGM,MAAMO,KAAMZ,UACxB,EACF,KAAK,EACH,OAAO,SAAU+M,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,GAC/C,OAAOxN,EAAGM,MAAMO,KAAMZ,UACxB,EACF,KAAK,GACH,OAAO,SAAU+M,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,GACnD,OAAOzN,EAAGM,MAAMO,KAAMZ,UACxB,EACF,QACE,MAAM,IAAIiF,MAAM,+EAEtB,C,8CClDe,SAASwI,EAAUpM,GAChC,MAA6C,oBAAtCN,OAAOR,UAAUyC,SAASvC,KAAKY,EACxC,C,8CCFe,SAASqM,EAAK3N,EAAIoG,GAI/B,IAHA,IAAI/F,EAAM,EACNuN,EAAMxH,EAAQlG,OACduB,EAASlB,MAAMqN,GACZvN,EAAMuN,GACXnM,EAAOpB,GAAOL,EAAGoG,EAAQ/F,IACzBA,GAAO,EAET,OAAOoB,CACT,C,uFC2BA,GAV2B,E,QAAA,GAAQ,SAAiBzB,EAAI8B,EAAGC,GAIzD,IAHA,IAAI6L,EAAMC,KAAKC,IAAIhM,EAAE5B,OAAQ6B,EAAE7B,QAC3B6N,EAAKxN,MAAMqN,GACXvN,EAAM,EACHA,EAAMuN,GACXG,EAAG1N,GAAOL,EAAG8B,EAAEzB,GAAM0B,EAAE1B,IACvBA,GAAO,EAET,OAAO0N,CACT,G,oECpBe,MAAMC,UAAkBC,EAAAA,UACnCC,MAAAA,GACI,OACIC,IAAAA,cAACC,EAAAA,SAAQ,CAACC,SAAU,MAChBF,IAAAA,cAACG,EAAkBzN,KAAK0N,OAGpC,EAEJ,IAAMD,GAAgBE,EAAAA,EAAAA,gBAAeR,EAAWS,EAAAA,EAAWC,OA0E9CC,EAAY,CAarBC,KAAMC,IAAAA,QACFA,IAAAA,SACIA,IAAAA,UAAoB,CAChBA,IAAAA,OACAA,IAAAA,OACAA,IAAAA,SAQZC,QAASD,IAAAA,QACLA,IAAAA,MAAgB,CAMZxE,GAAIwE,IAAAA,OAAiBE,WAMrBC,KAAMH,IAAAA,UAAoB,CACtBA,IAAAA,OACAA,IAAAA,QAAkBA,IAAAA,UACnBE,WAsBHE,KAAMJ,IAAAA,MAAgB,CAAC,MAAO,UAAW,OAAQ,aAMjDK,aAAcL,IAAAA,MAAgB,CAAC,QAAS,WAAY,aAkBpDM,WAAYN,IAAAA,UAAoB,CAC5BA,IAAAA,MAAgB,CAAC,QAAS,SAC1BA,IAAAA,KACAA,IAAAA,QAAkBA,IAAAA,QAoBtBO,UAAWP,IAAAA,UAAoB,CAC3BA,IAAAA,MAAgB,CAAC,QAAS,SAC1BA,IAAAA,KACAA,IAAAA,QAAkBA,IAAAA,QAkBtBQ,UAAWR,IAAAA,UAAoB,CAC3BA,IAAAA,MAAgB,CAAC,QAAS,SAC1BA,IAAAA,KACAA,IAAAA,QAAkBA,IAAAA,QAWtBS,SAAUT,IAAAA,KAiBVU,SAAUV,IAAAA,UAAoB,CAC1BA,IAAAA,MAAgB,CAAC,QAAS,SAC1BA,IAAAA,KACAA,IAAAA,QAAkBA,IAAAA,QAkBtBW,UAAWX,IAAAA,UAAoB,CAC3BA,IAAAA,MAAgB,CAAC,QAAS,SAC1BA,IAAAA,KACAA,IAAAA,QAAkBA,IAAAA,QAStBY,eAAgBZ,IAAAA,MAAgB,CAI5Ba,KAAMb,IAAAA,MAAgB,CAAC,YAAa,gBAIpCc,iBAAkBd,IAAAA,SAQtBe,OAAQf,IAAAA,MAAgB,CAKpBgB,OAAQhB,IAAAA,MAAgB,CAMpBiB,OAAQjB,IAAAA,QAAkBA,IAAAA,QAI1BkB,QAASlB,IAAAA,OAITmB,MAAOnB,IAAAA,OAKPoB,SAAUpB,IAAAA,QAAkBA,IAAAA,QAI5BqB,SAAUrB,IAAAA,QAAkBA,IAAAA,QAI5BsB,QAAStB,IAAAA,OAITuB,iBAAkBvB,IAAAA,OAMtBwB,MAAOxB,IAAAA,IAKP1J,OAAQ0J,IAAAA,OAIRyB,UAAWzB,IAAAA,SAKf0B,UAAW1B,IAAAA,MAAgB,CAOvB2B,OAAQ3B,IAAAA,MAAgB,CAAC,SAAU,OAAQ,aAO3C4B,QAAS5B,IAAAA,MAAgB,CAAC,SAAU,UAAW,aAOnD6B,aAAc7B,IAAAA,QACVA,IAAAA,UAAoB,CAChBA,IAAAA,OACAA,IAAAA,OACAA,IAAAA,QAOR8B,WAAY9B,IAAAA,MAAgB,CAIxB+B,WAAY/B,IAAAA,KAIZrG,QAASqG,IAAAA,IAOTgC,SAAUhC,IAAAA,UActBS,SAAUT,IAAAA,KAiBViC,cAAejC,IAAAA,MAAgB,CAI3BD,KAAMC,IAAAA,OACNkC,QAASlC,IAAAA,OAebmC,WAAYnC,IAAAA,MAAgB,CAIxBD,KAAMC,IAAAA,OACNkC,QAASlC,IAAAA,OAcboC,kBAAmBpC,IAAAA,MAAgB,CAAC,SAAU,SAAS,IAKvDqC,gBAAiBrC,IAAAA,KAWjBsC,eAAgBtC,IAAAA,MAAgB,CAAC,SAAU,SAAS,IAKpDuC,cAAevC,IAAAA,KAKfwC,YAAaxC,IAAAA,MAAgB,CACzByC,IAAKzC,IAAAA,OACL0C,OAAQ1C,IAAAA,OACR2C,OAAQ3C,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,SAC/C4C,UAAW5C,IAAAA,SASf6C,eAAgB7C,IAAAA,QACZA,IAAAA,MAAgB,CACZyC,IAAKzC,IAAAA,OACL0C,OAAQ1C,IAAAA,OACR2C,OAAQ3C,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,SAC/C4C,UAAW5C,IAAAA,UAQnB8C,cAAe9C,IAAAA,QAAkBA,IAAAA,QAMjC+C,iBAAkB/C,IAAAA,QAAkBA,IAAAA,QAMpCgD,iBAAkBhD,IAAAA,QACdA,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,UAS3CiD,WAAYjD,IAAAA,MAAgB,CACxByC,IAAKzC,IAAAA,OACL0C,OAAQ1C,IAAAA,OACR2C,OAAQ3C,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,SAC/C4C,UAAW5C,IAAAA,SASfkD,SAAUlD,IAAAA,MAAgB,CACtByC,IAAKzC,IAAAA,OACL0C,OAAQ1C,IAAAA,OACR2C,OAAQ3C,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,SAC/C4C,UAAW5C,IAAAA,SAUfmD,cAAenD,IAAAA,QAAkBA,IAAAA,QAKjCoD,eAAgBpD,IAAAA,QAAkBA,IAAAA,QAIlCqD,WAAYrD,IAAAA,KAOZsD,wBAAyBtD,IAAAA,KAOzBuD,eAAgBvD,IAAAA,OAMhBwD,8BAA+BxD,IAAAA,KAM/ByD,eAAgBzD,IAAAA,MAAgB,CAAC,MAAO,YAKxC0D,cAAe1D,IAAAA,MAAgB,CAAC,MAAO,OAAQ,SAU/C2D,eAAgB3D,IAAAA,MAAgB,CAAC,OAAQ,MAAO,QAAS,YAqBzD4D,YAAa5D,IAAAA,MAAgB,CAAC,SAAU,SAAU,SAMlD6D,aAAc7D,IAAAA,OAOd8D,WAAY9D,IAAAA,OAKZ+D,UAAW/D,IAAAA,OAMXgE,aAAchE,IAAAA,OAYdiE,cAAejE,IAAAA,UAAoB,CAC/BA,IAAAA,MAAgB,CAAC,SAAU,SAAU,SACrCA,IAAAA,MAAgB,CACZI,KAAMJ,IAAAA,MAAgB,CAAC,SAAU,WAAWE,WAC5CgE,SAAUlE,IAAAA,MAAgB,CAAC,MAAO,WAU1CY,eAAgBZ,IAAAA,MAAgB,CAI5Ba,KAAMb,IAAAA,MAAgB,CAAC,YAAa,gBAIpCc,iBAAkBd,IAAAA,SAgBtBmE,YAAanE,IAAAA,MAAgB,CAAC,SAAU,SAAU,SAUlDoE,UAAWpE,IAAAA,MAAgB,CAAC,SAAU,UAYtCqE,QAASrE,IAAAA,QACLA,IAAAA,MAAgB,CACZ4C,UAAW5C,IAAAA,OAAiBE,WAC5BoE,UAAWtE,IAAAA,MAAgB,CAAC,MAAO,SAASE,cASpD2B,aAAc7B,IAAAA,QACVA,IAAAA,UAAoB,CAChBA,IAAAA,OACAA,IAAAA,OACAA,IAAAA,QASRuE,SAAUvE,IAAAA,SACNA,IAAAA,MAAgB,CACZO,UAAWP,IAAAA,KACXwE,QAASxE,IAAAA,QACLA,IAAAA,MAAgB,CACZyE,MAAOzE,IAAAA,OAAiBE,WACxBtM,MAAOoM,IAAAA,UAAoB,CACvBA,IAAAA,OACAA,IAAAA,OACAA,IAAAA,OACDE,cAETA,cAUVwE,qBAAsB1E,IAAAA,QAClBA,IAAAA,MAAgB,CACZO,UAAWP,IAAAA,KACX2E,GAAI3E,IAAAA,MAAgB,CAChB4C,UAAW5C,IAAAA,OACXgE,aAAchE,IAAAA,SAElBwE,QAASxE,IAAAA,QACLA,IAAAA,MAAgB,CACZyE,MAAOzE,IAAAA,OAAiBE,WACxBtM,MAAOoM,IAAAA,UAAoB,CACvBA,IAAAA,OACAA,IAAAA,OACAA,IAAAA,OACDE,cAETA,cAQV0E,cAAe5E,IAAAA,QACXA,IAAAA,SACIA,IAAAA,MAAgB,CACZO,UAAWP,IAAAA,KACXwE,QAASxE,IAAAA,QACLA,IAAAA,MAAgB,CACZyE,MAAOzE,IAAAA,OAAiBE,WACxBtM,MAAOoM,IAAAA,UAAoB,CACvBA,IAAAA,OACAA,IAAAA,OACAA,IAAAA,OACDE,cAETA,eASd2E,QAAS7E,IAAAA,SACLA,IAAAA,UAAoB,CAChBA,IAAAA,OACAA,IAAAA,MAAgB,CAOZ8E,MAAO9E,IAAAA,OAOP+E,SAAU/E,IAAAA,OAMVI,KAAMJ,IAAAA,MAAgB,CAAC,OAAQ,aAM/BgF,SAAUhF,IAAAA,MAAgB,CAAC,OAAQ,OAAQ,WAO3CpM,MAAOoM,IAAAA,OAAiBE,gBAepC+E,oBAAqBjF,IAAAA,QACjBA,IAAAA,MAAgB,CAOZ8E,MAAO9E,IAAAA,OAOP+E,SAAU/E,IAAAA,OAOV2E,GAAI3E,IAAAA,MAAgB,CAIhB4C,UAAW5C,IAAAA,OAIXgE,aAAchE,IAAAA,OAIdkF,UAAWlF,IAAAA,UAAoB,CAC3BA,IAAAA,OACAA,IAAAA,MAAgB,CAAC,MAAO,aAE7BE,WAMHE,KAAMJ,IAAAA,MAAgB,CAAC,OAAQ,aAI/BpM,MAAOoM,IAAAA,OAAiBE,cAShCiF,aAAcnF,IAAAA,QACVA,IAAAA,SACIA,IAAAA,UAAoB,CAChBA,IAAAA,OACAA,IAAAA,MAAgB,CAOZ8E,MAAO9E,IAAAA,OAUP+E,SAAU/E,IAAAA,OAOVI,KAAMJ,IAAAA,MAAgB,CAAC,OAAQ,aAI/BpM,MAAOoM,IAAAA,OAAiBE,iBAexCkF,eAAgBpF,IAAAA,SACZA,IAAAA,UAAoB,CAChBA,IAAAA,OACAA,IAAAA,MAAgB,CAOZ8E,MAAO9E,IAAAA,OAUP+E,SAAU/E,IAAAA,OAOVI,KAAMJ,IAAAA,MAAgB,CAAC,OAAQ,aAI/BpM,MAAOoM,IAAAA,OAAiBE,aAE5BF,IAAAA,QACIA,IAAAA,UAAoB,CAChBA,IAAAA,MAAgB,CAAC,OACjBA,IAAAA,OACAA,IAAAA,MAAgB,CACZ8E,MAAO9E,IAAAA,OACP+E,SAAU/E,IAAAA,OACVI,KAAMJ,IAAAA,MAAgB,CAAC,OAAQ,aAC/BpM,MAAOoM,IAAAA,OAAiBE,mBAY5CmF,cAAerF,IAAAA,OAOfsF,iBAAkBtF,IAAAA,OAMlBuF,cAAevF,IAAAA,MAAgB,CAM3BiB,OAAQjB,IAAAA,QAAkBA,IAAAA,QAI1BkB,QAASlB,IAAAA,OAITmB,MAAOnB,IAAAA,OAIPoB,SAAUpB,IAAAA,QAAkBA,IAAAA,QAI5BqB,SAAUrB,IAAAA,QAAkBA,IAAAA,QAI5BsB,QAAStB,IAAAA,OAITuB,iBAAkBvB,IAAAA,OAMtBwF,mBAAoBxF,IAAAA,KAMpByF,WAAYzF,IAAAA,KAIZ0F,iBAAkB1F,IAAAA,MAAgB,CAM9B2F,YAAa3F,IAAAA,UAAoB,CAC7BA,IAAAA,OACAA,IAAAA,MAAgB,CAAC,SAAU,UAAW,QAAS,WAOnD4F,KAAM5F,IAAAA,OAYV6F,IAAK7F,IAAAA,QACDA,IAAAA,MAAgB,CACZ8F,SAAU9F,IAAAA,OAAiBE,WAC3B6F,KAAM/F,IAAAA,OAAiBE,cAQ/B8F,YAAahG,IAAAA,OAMbiG,WAAYjG,IAAAA,OAKZkG,WAAYlG,IAAAA,OAMZmG,aAAcnG,IAAAA,OAKdoG,aAAcpG,IAAAA,OAKdqG,uBAAwBrG,IAAAA,QACpBA,IAAAA,MAAgB,CACZ2E,GAAI3E,IAAAA,MAAgB,CAChB4C,UAAW5C,IAAAA,UAAoB,CAC3BA,IAAAA,OACAA,IAAAA,QAAkBA,IAAAA,UAEtBsG,YAAatG,IAAAA,MAAgB,CACzB,MACA,UACA,OACA,kBAShBuG,uBAAwBvG,IAAAA,QACpBA,IAAAA,MAAgB,CACZ2E,GAAI3E,IAAAA,MAAgB,CAChB4C,UAAW5C,IAAAA,UAAoB,CAC3BA,IAAAA,OACAA,IAAAA,QAAkBA,IAAAA,UAEtBsG,YAAatG,IAAAA,MAAgB,CACzB,MACA,UACA,OACA,aAEJgE,aAAchE,IAAAA,OACdwG,MAAOxG,IAAAA,MAAgB,CAAC,SAAU,aAClCkF,UAAWlF,IAAAA,UAAoB,CAC3BA,IAAAA,OACAA,IAAAA,MAAgB,CAAC,MAAO,SACxBA,IAAAA,QAAkBA,IAAAA,UAEtByG,gBAAiBzG,IAAAA,UAQ7B0G,yBAA0B1G,IAAAA,QACtBA,IAAAA,MAAgB,CACZ2E,GAAI3E,IAAAA,MAAgB,CAChB4C,UAAW5C,IAAAA,UAAoB,CAC3BA,IAAAA,OACAA,IAAAA,QAAkBA,IAAAA,UAEtBsG,YAAatG,IAAAA,MAAgB,CACzB,MACA,UACA,OACA,aAEJyG,gBAAiBzG,IAAAA,UAQ7B2G,yBAA0B3G,IAAAA,QACtBA,IAAAA,MAAgB,CACZ2E,GAAI3E,IAAAA,MAAgB,CAChB4C,UAAW5C,IAAAA,UAAoB,CAC3BA,IAAAA,OACAA,IAAAA,QAAkBA,IAAAA,UAEtBsG,YAAatG,IAAAA,MAAgB,CACzB,MACA,UACA,OACA,aAEJ4G,aAAc5G,IAAAA,UAAoB,CAC9BA,IAAAA,OACAA,IAAAA,QAAkBA,IAAAA,QAClBA,IAAAA,MAAgB,CAAC,MAAO,WAE5ByG,gBAAiBzG,IAAAA,UAW7B6G,eAAgB7G,IAAAA,KA0BhB8G,+BAAgC9G,IAAAA,OAMhC+G,sBAAuB/G,IAAAA,QAAkBA,IAAAA,QAOzCgH,yBAA0BhH,IAAAA,QAAkBA,IAAAA,QAO5CiH,yBAA0BjH,IAAAA,QACtBA,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,UAM3CkH,kCAAmClH,IAAAA,QAAkBA,IAAAA,QAKrDmH,+BAAgCnH,IAAAA,QAAkBA,IAAAA,QAKlDoH,kCAAmCpH,IAAAA,QAC/BA,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,UAO3CqH,qBAAsBrH,IAAAA,QAAkBA,IAAAA,QAOxCsH,wBAAyBtH,IAAAA,QAAkBA,IAAAA,QAO3CuH,wBAAyBvH,IAAAA,QACrBA,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,UAM3CwH,8BAA+BxH,IAAAA,QAAkBA,IAAAA,QAMjDyH,iCAAkCzH,IAAAA,QAC9BA,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,UAK3CxE,GAAIwE,IAAAA,OAIJ0H,SAAU1H,IAAAA,KAIV2H,cAAe3H,IAAAA,MAAgB,CAI3B4H,WAAY5H,IAAAA,KAIZ6H,UAAW7H,IAAAA,OAIX8H,eAAgB9H,IAAAA,SAUpB+H,YAAa/H,IAAAA,UAAoB,CAC7BA,IAAAA,KACAA,IAAAA,OACAA,IAAAA,SAMJgI,gBAAiBhI,IAAAA,QACbA,IAAAA,MAAgB,CACZ,eACA,OACA,eACA,iBACA,eACA,mBACA,gBACA,aASRiI,iBAAkBjI,IAAAA,MAAgB,CAAC,QAAS,UAAW,YAE3Db,EAAU+I,sBAAwB,CAC9BjI,QAAS,CACLE,KAAM,CACFgI,QAASC,GAAa1L,EAAAA,EAAQ,OAAQ0L,GACtC3W,MAAOA,CAAC4W,EAAaD,IACjB1L,EAAUA,EAAAA,EAAQ,QAAS2L,EAAaD,MAIxDjJ,EAAUmJ,aA74CkB,CACxB1E,YAAa,SACbC,aAAc,EACdE,UAAW,IACX8B,IAAK,GACL7B,aAAc,GACdC,cAAe,OACfpC,aAAc,GACdsC,YAAa,OACbC,UAAW,SACXC,QAAS,GACTmB,oBAAoB,EACpBuB,sBAAuB,GACvBC,yBAA0B,GAC1BC,yBAA0B,GAC1BE,+BAAgC,GAChCC,kCAAmC,GACnCC,qBAAsB,GACtBC,wBAAyB,GACzBC,wBAAyB,GACzBC,8BAA+B,GAC/BC,iCAAkC,GAClClD,SAAU,CAAC,EACXG,qBAAsB,GACtBE,cAAe,GACfa,YAAY,EACZ7E,eAAgB,CAAC,EACjBqB,cAAe,CACXC,SAAS,EACTnC,KAAM,GAEVoC,WAAY,CACRD,SAAS,EACTnC,KAAM,GAEV2F,iBAAkB,CACdC,YAAa,SACbC,MAAM,GAEVf,QAAS,CAAC,EACVI,oBAAqB,GACrBE,aAAc,GACdC,eAAgB,CAAC,EACjBC,cAAe,IACfC,iBAAkB,IAClBlD,mBAAmB,EACnB3B,UAAU,EACVgD,eAAgB,UAChBC,cAAe,OACfF,+BAA+B,EAC/BX,eAAgB,GAChBE,iBAAkB,GAClBD,cAAe,GACfE,iBAAkB,GAClBX,iBAAiB,EACjBC,gBAAgB,EAChB0D,YAAa,CAAC,EACdK,uBAAwB,GACxBE,uBAAwB,GACxBG,yBAA0B,GAC1BC,yBAA0B,GAC1BE,gBAAgB,EAChBmB,gBAAiB,CACb,eACA,eACA,iBACA,eACA,mBACA,gBACA,WAEJC,iBAAkB,SAu0CtB9I,EAAUW,UAAYA,C,uEC15CP,SAASyI,EAAQlX,EAAQmX,EAAUrX,GAChD,OAAO,WAML,IALA,IAAIsX,EAAW,GACXC,EAAU,EACVC,EAAOtX,EACPuX,EAAc,EACdC,GAAiB,EACdD,EAAcJ,EAASnX,QAAUqX,EAAUtX,UAAUC,QAAQ,CAClE,IAAIuB,EACAgW,EAAcJ,EAASnX,WAAY,OAAemX,EAASI,KAAiBF,GAAWtX,UAAUC,QACnGuB,EAAS4V,EAASI,IAElBhW,EAASxB,UAAUsX,GACnBA,GAAW,GAEbD,EAASG,GAAehW,GACnB,OAAeA,GAGlBiW,GAAiB,EAFjBF,GAAQ,EAIVC,GAAe,CACjB,CACA,OAAQC,GAAkBF,GAAQ,EAAIxX,EAAGM,MAAMO,KAAMyW,IAAY,OAAOzJ,KAAK8J,IAAI,EAAGH,GAAOJ,EAAQlX,EAAQoX,EAAUtX,GACvH,CACF,C,4CCrCI4X,EAAoB,WACtB,SAASA,EAAKnO,EAAGjI,GACfX,KAAKW,GAAKA,EACVX,KAAK4I,EAAIA,CACX,CAMA,OALAmO,EAAKpX,UAAU,qBAAuB,IAAQe,KAC9CqW,EAAKpX,UAAU,uBAAyB,IAAQiB,OAChDmW,EAAKpX,UAAU,qBAAuB,SAAUiB,EAAQoW,GACtD,OAAOhX,KAAKW,GAAG,qBAAqBC,EAAQZ,KAAK4I,EAAEoO,GACrD,EACOD,CACT,CAXwB,GAiBxB,IALY,SAAenO,GACzB,OAAO,SAAUjI,GACf,OAAO,IAAIoW,EAAKnO,EAAGjI,EACrB,CACF,C,+CCjBe,MAAMiN,EACjB,eAAWqJ,GACP,OAAO,kCAEX,CACA,eAAWC,GACP,OAAOnP,QAAQC,QAAQzH,OAAO2W,MAC1B,8BACwFjP,KAAKrH,GAAUA,EAAO+G,SACtH,CACA,YAAOkG,GACH,OAAO,qDAEX,E,gECkBAsJ,GAAqB,OAAQ,SAAenU,EAAGgD,GACjD,OAAO,QAAI,OAAKhD,GAAIgD,EACtB,GACA,K,GCjCIoR,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqB5N,IAAjB6N,EACH,OAAOA,EAAajX,QAGrB,IAAID,EAAS+W,EAAyBE,GAAY,CACjD9N,GAAI8N,EAEJhX,QAAS,CAAC,GAOX,OAHAkX,EAAoBF,GAAUzX,KAAKQ,EAAOC,QAASD,EAAQA,EAAOC,QAAS+W,GAGpEhX,EAAOC,OACf,CAGA+W,EAAoBjQ,EAAIoQ,ECxBxBH,EAAoBxQ,EAAKxG,IACxB,IAAIoX,EAASpX,GAAUA,EAAOoH,WAC7B,IAAOpH,EAAiB,QACxB,IAAM,EAEP,OADAgX,EAAoBhQ,EAAEoQ,EAAQ,CAAExW,EAAGwW,IAC5BA,G5CNJ5Y,EAAWsB,OAAOuX,eAAkBpY,GAASa,OAAOuX,eAAepY,GAASA,GAASA,EAAa,UAQtG+X,EAAoBtQ,EAAI,SAASnF,EAAO+V,GAEvC,GADU,EAAPA,IAAU/V,EAAQ5B,KAAK4B,IAChB,EAAP+V,EAAU,OAAO/V,EACpB,GAAoB,iBAAVA,GAAsBA,EAAO,CACtC,GAAW,EAAP+V,GAAa/V,EAAM6F,WAAY,OAAO7F,EAC1C,GAAW,GAAP+V,GAAoC,mBAAf/V,EAAMqG,KAAqB,OAAOrG,CAC5D,CACA,IAAIgW,EAAKzX,OAAOuH,OAAO,MACvB2P,EAAoBrQ,EAAE4Q,GACtB,IAAIC,EAAM,CAAC,EACXjZ,EAAiBA,GAAkB,CAAC,KAAMC,EAAS,CAAC,GAAIA,EAAS,IAAKA,EAASA,IAC/E,IAAI,IAAIiZ,EAAiB,EAAPH,GAAY/V,GAA0B,iBAAXkW,GAAyC,mBAAXA,MAA4BlZ,EAAegM,QAAQkN,GAAUA,EAAUjZ,EAASiZ,GAC1J3X,OAAO4X,oBAAoBD,GAASE,QAASvS,GAASoS,EAAIpS,GAAO,IAAO7D,EAAM6D,IAI/E,OAFAoS,EAAa,QAAI,IAAM,EACvBR,EAAoBhQ,EAAEuQ,EAAIC,GACnBD,CACR,E6CxBAP,EAAoBhQ,EAAI,CAAC/G,EAAS2X,KACjC,IAAI,IAAIxS,KAAOwS,EACXZ,EAAoBpQ,EAAEgR,EAAYxS,KAAS4R,EAAoBpQ,EAAE3G,EAASmF,IAC5EtF,OAAOmH,eAAehH,EAASmF,EAAK,CAAET,YAAY,EAAMF,IAAKmT,EAAWxS,MCJ3E4R,EAAoBzO,EAAI,CAAC,EAGzByO,EAAoBvQ,EAAKoR,GACjBnQ,QAAQoQ,IAAIhY,OAAO+F,KAAKmR,EAAoBzO,GAAGwP,OAAO,CAACC,EAAU5S,KACvE4R,EAAoBzO,EAAEnD,GAAKyS,EAASG,GAC7BA,GACL,KCNJhB,EAAoBxP,EAAKqQ,IAEZ,CAAC,IAAM,cAAc,IAAM,kBAAkB,IAAM,gBAAgBA,GAAW,OCH3Fb,EAAoB7V,EAAI,WACvB,GAA0B,iBAAf8W,WAAyB,OAAOA,WAC3C,IACC,OAAOtY,MAAQ,IAAIuY,SAAS,cAAb,EAChB,CAAE,MAAOzR,GACR,GAAsB,iBAAXvG,OAAqB,OAAOA,MACxC,CACA,CAPuB,GCAxB8W,EAAoBpQ,EAAI,CAAC3H,EAAKY,IAAUC,OAAOR,UAAUS,eAAeP,KAAKP,EAAKY,GhDA9EpB,EAAa,CAAC,EACdC,EAAoB,cAExBsY,EAAoBlQ,EAAI,CAACqR,EAAK1Q,EAAMrC,EAAKyS,KACxC,GAAGpZ,EAAW0Z,GAAQ1Z,EAAW0Z,GAAKC,KAAK3Q,OAA3C,CACA,IAAI4Q,EAAQC,EACZ,QAAWjP,IAARjE,EAEF,IADA,IAAImT,EAAU5O,SAAS6O,qBAAqB,UACpC3R,EAAI,EAAGA,EAAI0R,EAAQvZ,OAAQ6H,IAAK,CACvC,IAAIU,EAAIgR,EAAQ1R,GAChB,GAAGU,EAAEkR,aAAa,QAAUN,GAAO5Q,EAAEkR,aAAa,iBAAmB/Z,EAAoB0G,EAAK,CAAEiT,EAAS9Q,EAAG,KAAO,CACpH,CAEG8Q,IACHC,GAAa,GACbD,EAAS1O,SAAS+O,cAAc,WAEzBC,QAAU,QACjBN,EAAOO,QAAU,IACb5B,EAAoB6B,IACvBR,EAAOS,aAAa,QAAS9B,EAAoB6B,IAElDR,EAAOS,aAAa,eAAgBpa,EAAoB0G,GAExDiT,EAAOU,IAAMZ,GAEd1Z,EAAW0Z,GAAO,CAAC1Q,GACnB,IAAIuR,EAAmB,CAAC7Q,EAAM8Q,KAE7BZ,EAAOa,QAAUb,EAAOc,OAAS,KACjCC,aAAaR,GACb,IAAIS,EAAU5a,EAAW0Z,GAIzB,UAHO1Z,EAAW0Z,GAClBE,EAAOiB,YAAcjB,EAAOiB,WAAWC,YAAYlB,GACnDgB,GAAWA,EAAQ1B,QAAS7Y,GAAQA,EAAGma,IACpC9Q,EAAM,OAAOA,EAAK8Q,IAElBL,EAAU7Q,WAAWiR,EAAiB7U,KAAK,UAAMkF,EAAW,CAAE0E,KAAM,UAAWyL,OAAQnB,IAAW,MACtGA,EAAOa,QAAUF,EAAiB7U,KAAK,KAAMkU,EAAOa,SACpDb,EAAOc,OAASH,EAAiB7U,KAAK,KAAMkU,EAAOc,QACnDb,GAAc3O,SAAS8P,KAAKC,YAAYrB,EApCkB,GiDH3DrB,EAAoBrQ,EAAK1G,IACH,oBAAXiH,QAA0BA,OAAOC,aAC1CrH,OAAOmH,eAAehH,EAASiH,OAAOC,YAAa,CAAE5F,MAAO,WAE7DzB,OAAOmH,eAAehH,EAAS,aAAc,CAAEsB,OAAO,K,MCLvD,IAAIoY,EACA3C,EAAoB7V,EAAEyY,gBAAeD,EAAY3C,EAAoB7V,EAAE2J,SAAW,IACtF,IAAInB,EAAWqN,EAAoB7V,EAAEwI,SACrC,IAAKgQ,GAAahQ,IACbA,EAASkQ,eAAkE,WAAjDlQ,EAASkQ,cAAcC,QAAQ5V,gBAC5DyV,EAAYhQ,EAASkQ,cAAcd,MAC/BY,GAAW,CACf,IAAIpB,EAAU5O,EAAS6O,qBAAqB,UAC5C,GAAGD,EAAQvZ,OAEV,IADA,IAAI6H,EAAI0R,EAAQvZ,OAAS,EAClB6H,GAAK,KAAO8S,IAAc,aAAaI,KAAKJ,KAAaA,EAAYpB,EAAQ1R,KAAKkS,GAE3F,CAID,IAAKY,EAAW,MAAM,IAAI3V,MAAM,yDAChC2V,EAAYA,EAAUK,QAAQ,SAAU,IAAIA,QAAQ,OAAQ,IAAIA,QAAQ,QAAS,IAAIA,QAAQ,YAAa,KAC1GhD,EAAoBrU,EAAIgX,C,KClBxB,IA4BYxB,EA5BR8B,EAAmB,WACnB,IAAI5B,EAAS1O,SAASkQ,cACtB,IAAKxB,EAAQ,CAOT,IAHA,IAAI6B,EAAcvQ,SAAS6O,qBAAqB,UAC5CD,EAAU,GAEL1R,EAAI,EAAGA,EAAIqT,EAAYlb,OAAQ6H,IACpC0R,EAAQH,KAAK8B,EAAYrT,IAI7BwR,GADAE,EAAUA,EAAQ4B,OAAO,SAAS5S,GAAK,OAAQA,EAAE6S,QAAU7S,EAAE8S,OAAS9S,EAAE+S,WAAa,IACpE/a,OAAO,GAAG,EAC/B,CAEA,OAAO8Y,CACX,EAkBA,GAZAvY,OAAOmH,eAAe+P,EAAqB,IAAK,CAC5CvS,KAGQ0T,EAFS8B,IAEIlB,IAAIjP,MAAM,KAAKvK,MAAM,GAAI,GAAGgb,KAAK,KAAO,IAElD,WACH,OAAOpC,CACX,KAIsB,oBAAnBqC,eAAgC,CACvC,IAAIC,EAAqBD,eACzBA,eAAiB,SAAS3C,GACtB,IAnBqBQ,EAoBjBqC,GApBiBrC,EAmBR4B,IAlBV,6BAA6BF,KAAK1B,EAAOU,MAqBxCA,EAAM0B,EAAmB5C,GAE7B,IAAI6C,EACA,OAAO3B,EAGX,IAAI4B,EAAe5B,EAAIjP,MAAM,KACzB8Q,EAAgBD,EAAapb,OAAO,GAAG,GAAGuK,MAAM,KAKpD,OAHA8Q,EAAcC,OAAO,EAAG,EAAG,qBAC3BF,EAAaE,QAAQ,EAAG,EAAGD,EAAcL,KAAK,MAEvCI,EAAaJ,KAAK,IAC7B,CACJ,C,MCnDA,IAAIO,EAAkB,CACrB,GAAI,GAGL9D,EAAoBzO,EAAEwS,EAAI,CAAClD,EAASG,KAElC,IAAIgD,EAAqBhE,EAAoBpQ,EAAEkU,EAAiBjD,GAAWiD,EAAgBjD,QAAWxO,EACtG,GAA0B,IAAvB2R,EAGF,GAAGA,EACFhD,EAASI,KAAK4C,EAAmB,QAC3B,CAGL,IAAIC,EAAU,IAAIvT,QAAQ,CAACC,EAASuT,IAAYF,EAAqBF,EAAgBjD,GAAW,CAAClQ,EAASuT,IAC1GlD,EAASI,KAAK4C,EAAmB,GAAKC,GAGtC,IAAI9C,EAAMnB,EAAoBrU,EAAIqU,EAAoBxP,EAAEqQ,GAEpD9T,EAAQ,IAAIC,MAgBhBgT,EAAoBlQ,EAAEqR,EAfFc,IACnB,GAAGjC,EAAoBpQ,EAAEkU,EAAiBjD,KAEf,KAD1BmD,EAAqBF,EAAgBjD,MACRiD,EAAgBjD,QAAWxO,GACrD2R,GAAoB,CACtB,IAAIG,EAAYlC,IAAyB,SAAfA,EAAMlL,KAAkB,UAAYkL,EAAMlL,MAChEqN,EAAUnC,GAASA,EAAMO,QAAUP,EAAMO,OAAOT,IACpDhV,EAAMsX,QAAU,iBAAmBxD,EAAU,cAAgBsD,EAAY,KAAOC,EAAU,IAC1FrX,EAAM+J,KAAO,iBACb/J,EAAMgK,KAAOoN,EACbpX,EAAMuX,QAAUF,EAChBJ,EAAmB,GAAGjX,EACvB,GAGuC,SAAW8T,EAASA,EAE/D,GAeH,IAAI0D,EAAuB,CAACC,EAA4B9N,KACvD,IAKIuJ,EAAUY,EALV4D,EAAW/N,EAAK,GAChBgO,EAAchO,EAAK,GACnBiO,EAAUjO,EAAK,GAGI7G,EAAI,EAC3B,GAAG4U,EAASG,KAAMzS,GAAgC,IAAxB2R,EAAgB3R,IAAa,CACtD,IAAI8N,KAAYyE,EACZ1E,EAAoBpQ,EAAE8U,EAAazE,KACrCD,EAAoBjQ,EAAEkQ,GAAYyE,EAAYzE,IAG7C0E,GAAsBA,EAAQ3E,EAClC,CAEA,IADGwE,GAA4BA,EAA2B9N,GACrD7G,EAAI4U,EAASzc,OAAQ6H,IACzBgR,EAAU4D,EAAS5U,GAChBmQ,EAAoBpQ,EAAEkU,EAAiBjD,IAAYiD,EAAgBjD,IACrEiD,EAAgBjD,GAAS,KAE1BiD,EAAgBjD,GAAW,GAKzBgE,EAAqBC,KAA6B,uBAAIA,KAA6B,wBAAK,GAC5FD,EAAmBlE,QAAQ4D,EAAqBpX,KAAK,KAAM,IAC3D0X,EAAmBzD,KAAOmD,EAAqBpX,KAAK,KAAM0X,EAAmBzD,KAAKjU,KAAK0X,G,KCvFvF7E,EAAoB6B,QAAKxP,E,yGCIzB0S,EAAAA,GAAO1X,cAAcqG,EAAAA,EAAYM,YACjC+Q,EAAAA,GAAOzX,YAAYoG,EAAAA,EAAYO,S", "sources": ["webpack://dash_table/webpack/runtime/create fake namespace object", "webpack://dash_table/webpack/runtime/load script", "webpack://dash_table/./node_modules/ramda/es/internal/_dispatchable.js", "webpack://dash_table/./node_modules/ramda/es/internal/_isTransformer.js", "webpack://dash_table/./node_modules/ramda/es/internal/_has.js", "webpack://dash_table/external window \"React\"", "webpack://dash_table/./node_modules/ramda/es/isNil.js", "webpack://dash_table/./node_modules/ramda/es/internal/_xfBase.js", "webpack://dash_table/./node_modules/ramda/es/once.js", "webpack://dash_table/./node_modules/ramda/es/internal/_curry3.js", "webpack://dash_table/./node_modules/css.escape/css.escape.js", "webpack://dash_table/./node_modules/ramda/es/internal/_curry2.js", "webpack://dash_table/./node_modules/ramda/es/assocPath.js", "webpack://dash_table/./node_modules/ramda/es/internal/_assoc.js", "webpack://dash_table/./src/core/Logger/DebugLevel.ts", "webpack://dash_table/./src/core/Logger/LogLevel.ts", "webpack://dash_table/./src/core/Logger/index.ts", "webpack://dash_table/./node_modules/ramda/es/map.js", "webpack://dash_table/./node_modules/ramda/es/internal/_isPlaceholder.js", "webpack://dash_table/./node_modules/ramda/es/internal/_isArguments.js", "webpack://dash_table/./node_modules/ramda/es/keys.js", "webpack://dash_table/./node_modules/ramda/es/internal/_arrayReduce.js", "webpack://dash_table/./node_modules/ramda/es/internal/_curry1.js", "webpack://dash_table/./node_modules/ramda/es/assoc.js", "webpack://dash_table/./node_modules/ramda/es/internal/_isInteger.js", "webpack://dash_table/./node_modules/@plotly/dash-component-plugins/dist/index.js", "webpack://dash_table/./node_modules/ramda/es/internal/_isArray.js", "webpack://dash_table/external window \"ReactDOM\"", "webpack://dash_table/./node_modules/ramda/es/curryN.js", "webpack://dash_table/./node_modules/ramda/es/prop.js", "webpack://dash_table/external window \"PropTypes\"", "webpack://dash_table/./node_modules/ramda/es/internal/_nth.js", "webpack://dash_table/./src/core/storage/Cookie.ts", "webpack://dash_table/./src/core/environment/index.ts", "webpack://dash_table/./node_modules/ramda/es/internal/_arity.js", "webpack://dash_table/./node_modules/ramda/es/internal/_isString.js", "webpack://dash_table/./node_modules/ramda/es/internal/_map.js", "webpack://dash_table/./node_modules/ramda/es/zipWith.js", "webpack://dash_table/./src/dash-table/dash/DataTable.js", "webpack://dash_table/./node_modules/ramda/es/internal/_curryN.js", "webpack://dash_table/./node_modules/ramda/es/internal/_xmap.js", "webpack://dash_table/./src/dash-table/LazyLoader.ts", "webpack://dash_table/./node_modules/ramda/es/pluck.js", "webpack://dash_table/webpack/bootstrap", "webpack://dash_table/webpack/runtime/compat get default export", "webpack://dash_table/webpack/runtime/define property getters", "webpack://dash_table/webpack/runtime/ensure chunk", "webpack://dash_table/webpack/runtime/get javascript chunk filename", "webpack://dash_table/webpack/runtime/global", "webpack://dash_table/webpack/runtime/hasOwnProperty shorthand", "webpack://dash_table/webpack/runtime/make namespace object", "webpack://dash_table/webpack/runtime/publicPath", "webpack://dash_table/webpack/runtime/compat", "webpack://dash_table/webpack/runtime/jsonp chunk loading", "webpack://dash_table/webpack/runtime/nonce", "webpack://dash_table/./src/dash-table/index.ts"], "sourcesContent": ["var getProto = Object.getPrototypeOf ? (obj) => (Object.getPrototypeOf(obj)) : (obj) => (obj.__proto__);\nvar leafPrototypes;\n// create a fake namespace object\n// mode & 1: value is a module id, require it\n// mode & 2: merge all properties of value into the ns\n// mode & 4: return value when already ns object\n// mode & 16: return value when it's Promise-like\n// mode & 8|1: behave like require\n__webpack_require__.t = function(value, mode) {\n\tif(mode & 1) value = this(value);\n\tif(mode & 8) return value;\n\tif(typeof value === 'object' && value) {\n\t\tif((mode & 4) && value.__esModule) return value;\n\t\tif((mode & 16) && typeof value.then === 'function') return value;\n\t}\n\tvar ns = Object.create(null);\n\t__webpack_require__.r(ns);\n\tvar def = {};\n\tleafPrototypes = leafPrototypes || [null, getProto({}), getProto([]), getProto(getProto)];\n\tfor(var current = mode & 2 && value; (typeof current == 'object' || typeof current == 'function') && !~leafPrototypes.indexOf(current); current = getProto(current)) {\n\t\tObject.getOwnPropertyNames(current).forEach((key) => (def[key] = () => (value[key])));\n\t}\n\tdef['default'] = () => (value);\n\t__webpack_require__.d(ns, def);\n\treturn ns;\n};", "var inProgress = {};\nvar dataWebpackPrefix = \"dash_table:\";\n// loadScript function to load a script via script tag\n__webpack_require__.l = (url, done, key, chunkId) => {\n\tif(inProgress[url]) { inProgress[url].push(done); return; }\n\tvar script, needAttach;\n\tif(key !== undefined) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tfor(var i = 0; i < scripts.length; i++) {\n\t\t\tvar s = scripts[i];\n\t\t\tif(s.getAttribute(\"src\") == url || s.getAttribute(\"data-webpack\") == dataWebpackPrefix + key) { script = s; break; }\n\t\t}\n\t}\n\tif(!script) {\n\t\tneedAttach = true;\n\t\tscript = document.createElement('script');\n\n\t\tscript.charset = 'utf-8';\n\t\tscript.timeout = 120;\n\t\tif (__webpack_require__.nc) {\n\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n\t\t}\n\t\tscript.setAttribute(\"data-webpack\", dataWebpackPrefix + key);\n\n\t\tscript.src = url;\n\t}\n\tinProgress[url] = [done];\n\tvar onScriptComplete = (prev, event) => {\n\t\t// avoid mem leaks in IE.\n\t\tscript.onerror = script.onload = null;\n\t\tclearTimeout(timeout);\n\t\tvar doneFns = inProgress[url];\n\t\tdelete inProgress[url];\n\t\tscript.parentNode && script.parentNode.removeChild(script);\n\t\tdoneFns && doneFns.forEach((fn) => (fn(event)));\n\t\tif(prev) return prev(event);\n\t}\n\tvar timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);\n\tscript.onerror = onScriptComplete.bind(null, script.onerror);\n\tscript.onload = onScriptComplete.bind(null, script.onload);\n\tneedAttach && document.head.appendChild(script);\n};", "import _isArray from \"./_isArray.js\";\nimport _isTransformer from \"./_isTransformer.js\";\n\n/**\n * Returns a function that dispatches with different strategies based on the\n * object in list position (last argument). If it is an array, executes [fn].\n * Otherwise, if it has a function with one of the given method names, it will\n * execute that function (functor case). Otherwise, if it is a transformer,\n * uses transducer created by [transducerC<PERSON>] to return a new transformer\n * (transducer case).\n * Otherwise, it will default to executing [fn].\n *\n * @private\n * @param {Array} methodNames properties to check for a custom implementation\n * @param {Function} transducerCreator transducer factory if object is transformer\n * @param {Function} fn default ramda implementation\n * @return {Function} A function that dispatches on object in list position\n */\nexport default function _dispatchable(methodNames, transducerCreator, fn) {\n  return function () {\n    if (arguments.length === 0) {\n      return fn();\n    }\n    var obj = arguments[arguments.length - 1];\n    if (!_isArray(obj)) {\n      var idx = 0;\n      while (idx < methodNames.length) {\n        if (typeof obj[methodNames[idx]] === 'function') {\n          return obj[methodNames[idx]].apply(obj, Array.prototype.slice.call(arguments, 0, -1));\n        }\n        idx += 1;\n      }\n      if (_isTransformer(obj)) {\n        var transducer = transducerCreator.apply(null, Array.prototype.slice.call(arguments, 0, -1));\n        return transducer(obj);\n      }\n    }\n    return fn.apply(this, arguments);\n  };\n}", "export default function _isTransformer(obj) {\n  return obj != null && typeof obj['@@transducer/step'] === 'function';\n}", "export default function _has(prop, obj) {\n  return Object.prototype.hasOwnProperty.call(obj, prop);\n}", "module.exports = window[\"React\"];", "import _curry1 from \"./internal/_curry1.js\";\n\n/**\n * Checks if the input value is `null` or `undefined`.\n *\n * @func\n * @memberOf R\n * @since v0.9.0\n * @category Type\n * @sig * -> Boolean\n * @param {*} x The value to test.\n * @return {<PERSON><PERSON>an} `true` if `x` is `undefined` or `null`, otherwise `false`.\n * @example\n *\n *      R.isNil(null); //=> true\n *      R.isNil(undefined); //=> true\n *      R.isNil(0); //=> false\n *      R.isNil([]); //=> false\n */\nvar isNil = /*#__PURE__*/_curry1(function isNil(x) {\n  return x == null;\n});\nexport default isNil;", "export default {\n  init: function () {\n    return this.xf['@@transducer/init']();\n  },\n  result: function (result) {\n    return this.xf['@@transducer/result'](result);\n  }\n};", "import _arity from \"./internal/_arity.js\";\nimport _curry1 from \"./internal/_curry1.js\";\n\n/**\n * Accepts a function `fn` and returns a function that guards invocation of\n * `fn` such that `fn` can only ever be called once, no matter how many times\n * the returned function is invoked. The first value calculated is returned in\n * subsequent invocations.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Function\n * @sig (a... -> b) -> (a... -> b)\n * @param {Function} fn The function to wrap in a call-only-once wrapper.\n * @return {Function} The wrapped function.\n * @example\n *\n *      const addOneOnce = R.once(x => x + 1);\n *      addOneOnce(10); //=> 11\n *      addOneOnce(addOneOnce(50)); //=> 11\n */\nvar once = /*#__PURE__*/_curry1(function once(fn) {\n  var called = false;\n  var result;\n  return _arity(fn.length, function () {\n    if (called) {\n      return result;\n    }\n    called = true;\n    result = fn.apply(this, arguments);\n    return result;\n  });\n});\nexport default once;", "import _curry1 from \"./_curry1.js\";\nimport _curry2 from \"./_curry2.js\";\nimport _isPlaceholder from \"./_isPlaceholder.js\";\n\n/**\n * Optimized internal three-arity curry function.\n *\n * @private\n * @category Function\n * @param {Function} fn The function to curry.\n * @return {Function} The curried function.\n */\nexport default function _curry3(fn) {\n  return function f3(a, b, c) {\n    switch (arguments.length) {\n      case 0:\n        return f3;\n      case 1:\n        return _isPlaceholder(a) ? f3 : _curry2(function (_b, _c) {\n          return fn(a, _b, _c);\n        });\n      case 2:\n        return _isPlaceholder(a) && _isPlaceholder(b) ? f3 : _isPlaceholder(a) ? _curry2(function (_a, _c) {\n          return fn(_a, b, _c);\n        }) : _isPlaceholder(b) ? _curry2(function (_b, _c) {\n          return fn(a, _b, _c);\n        }) : _curry1(function (_c) {\n          return fn(a, b, _c);\n        });\n      default:\n        return _isPlaceholder(a) && _isPlaceholder(b) && _isPlaceholder(c) ? f3 : _isPlaceholder(a) && _isPlaceholder(b) ? _curry2(function (_a, _b) {\n          return fn(_a, _b, c);\n        }) : _isPlaceholder(a) && _isPlaceholder(c) ? _curry2(function (_a, _c) {\n          return fn(_a, b, _c);\n        }) : _isPlaceholder(b) && _isPlaceholder(c) ? _curry2(function (_b, _c) {\n          return fn(a, _b, _c);\n        }) : _isPlaceholder(a) ? _curry1(function (_a) {\n          return fn(_a, b, c);\n        }) : _isPlaceholder(b) ? _curry1(function (_b) {\n          return fn(a, _b, c);\n        }) : _isPlaceholder(c) ? _curry1(function (_c) {\n          return fn(a, b, _c);\n        }) : fn(a, b, c);\n    }\n  };\n}", "/*! https://mths.be/cssescape v1.5.1 by @mathias | MIT license */\n;(function(root, factory) {\n\t// https://github.com/umdjs/umd/blob/master/returnExports.js\n\tif (typeof exports == 'object') {\n\t\t// For Node.js.\n\t\tmodule.exports = factory(root);\n\t} else if (typeof define == 'function' && define.amd) {\n\t\t// For AMD. Register as an anonymous module.\n\t\tdefine([], factory.bind(root, root));\n\t} else {\n\t\t// For browser globals (not exposing the function separately).\n\t\tfactory(root);\n\t}\n}(typeof global != 'undefined' ? global : this, function(root) {\n\n\tif (root.CSS && root.CSS.escape) {\n\t\treturn root.CSS.escape;\n\t}\n\n\t// https://drafts.csswg.org/cssom/#serialize-an-identifier\n\tvar cssEscape = function(value) {\n\t\tif (arguments.length == 0) {\n\t\t\tthrow new TypeError('`CSS.escape` requires an argument.');\n\t\t}\n\t\tvar string = String(value);\n\t\tvar length = string.length;\n\t\tvar index = -1;\n\t\tvar codeUnit;\n\t\tvar result = '';\n\t\tvar firstCodeUnit = string.charCodeAt(0);\n\t\twhile (++index < length) {\n\t\t\tcodeUnit = string.charCodeAt(index);\n\t\t\t// Note: there’s no need to special-case astral symbols, surrogate\n\t\t\t// pairs, or lone surrogates.\n\n\t\t\t// If the character is NULL (U+0000), then the REPLACEMENT CHARACTER\n\t\t\t// (U+FFFD).\n\t\t\tif (codeUnit == 0x0000) {\n\t\t\t\tresult += '\\uFFFD';\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (\n\t\t\t\t// If the character is in the range [\\1-\\1F] (U+0001 to U+001F) or is\n\t\t\t\t// U+007F, […]\n\t\t\t\t(codeUnit >= 0x0001 && codeUnit <= 0x001F) || codeUnit == 0x007F ||\n\t\t\t\t// If the character is the first character and is in the range [0-9]\n\t\t\t\t// (U+0030 to U+0039), […]\n\t\t\t\t(index == 0 && codeUnit >= 0x0030 && codeUnit <= 0x0039) ||\n\t\t\t\t// If the character is the second character and is in the range [0-9]\n\t\t\t\t// (U+0030 to U+0039) and the first character is a `-` (U+002D), […]\n\t\t\t\t(\n\t\t\t\t\tindex == 1 &&\n\t\t\t\t\tcodeUnit >= 0x0030 && codeUnit <= 0x0039 &&\n\t\t\t\t\tfirstCodeUnit == 0x002D\n\t\t\t\t)\n\t\t\t) {\n\t\t\t\t// https://drafts.csswg.org/cssom/#escape-a-character-as-code-point\n\t\t\t\tresult += '\\\\' + codeUnit.toString(16) + ' ';\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (\n\t\t\t\t// If the character is the first character and is a `-` (U+002D), and\n\t\t\t\t// there is no second character, […]\n\t\t\t\tindex == 0 &&\n\t\t\t\tlength == 1 &&\n\t\t\t\tcodeUnit == 0x002D\n\t\t\t) {\n\t\t\t\tresult += '\\\\' + string.charAt(index);\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\t// If the character is not handled by one of the above rules and is\n\t\t\t// greater than or equal to U+0080, is `-` (U+002D) or `_` (U+005F), or\n\t\t\t// is in one of the ranges [0-9] (U+0030 to U+0039), [A-Z] (U+0041 to\n\t\t\t// U+005A), or [a-z] (U+0061 to U+007A), […]\n\t\t\tif (\n\t\t\t\tcodeUnit >= 0x0080 ||\n\t\t\t\tcodeUnit == 0x002D ||\n\t\t\t\tcodeUnit == 0x005F ||\n\t\t\t\tcodeUnit >= 0x0030 && codeUnit <= 0x0039 ||\n\t\t\t\tcodeUnit >= 0x0041 && codeUnit <= 0x005A ||\n\t\t\t\tcodeUnit >= 0x0061 && codeUnit <= 0x007A\n\t\t\t) {\n\t\t\t\t// the character itself\n\t\t\t\tresult += string.charAt(index);\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\t// Otherwise, the escaped character.\n\t\t\t// https://drafts.csswg.org/cssom/#escape-a-character\n\t\t\tresult += '\\\\' + string.charAt(index);\n\n\t\t}\n\t\treturn result;\n\t};\n\n\tif (!root.CSS) {\n\t\troot.CSS = {};\n\t}\n\n\troot.CSS.escape = cssEscape;\n\treturn cssEscape;\n\n}));\n", "import _curry1 from \"./_curry1.js\";\nimport _isPlaceholder from \"./_isPlaceholder.js\";\n\n/**\n * Optimized internal two-arity curry function.\n *\n * @private\n * @category Function\n * @param {Function} fn The function to curry.\n * @return {Function} The curried function.\n */\nexport default function _curry2(fn) {\n  return function f2(a, b) {\n    switch (arguments.length) {\n      case 0:\n        return f2;\n      case 1:\n        return _isPlaceholder(a) ? f2 : _curry1(function (_b) {\n          return fn(a, _b);\n        });\n      default:\n        return _isPlaceholder(a) && _isPlaceholder(b) ? f2 : _isPlaceholder(a) ? _curry1(function (_a) {\n          return fn(_a, b);\n        }) : _isPlaceholder(b) ? _curry1(function (_b) {\n          return fn(a, _b);\n        }) : fn(a, b);\n    }\n  };\n}", "import _curry3 from \"./internal/_curry3.js\";\nimport _has from \"./internal/_has.js\";\nimport _isInteger from \"./internal/_isInteger.js\";\nimport _assoc from \"./internal/_assoc.js\";\nimport isNil from \"./isNil.js\";\n\n/**\n * Makes a shallow clone of an object, setting or overriding the nodes required\n * to create the given path, and placing the specific value at the tail end of\n * that path. Note that this copies and flattens prototype properties onto the\n * new object as well. All non-primitive properties are copied by reference.\n *\n * @func\n * @memberOf R\n * @since v0.8.0\n * @category Object\n * @typedefn Idx = String | Int | Symbol\n * @sig [Idx] -> a -> {a} -> {a}\n * @param {Array} path the path to set\n * @param {*} val The new value\n * @param {Object} obj The object to clone\n * @return {Object} A new object equivalent to the original except along the specified path.\n * @see R.dissocPath\n * @example\n *\n *      R.assocPath(['a', 'b', 'c'], 42, {a: {b: {c: 0}}}); //=> {a: {b: {c: 42}}}\n *\n *      // Any missing or non-object keys in path will be overridden\n *      R.assocPath(['a', 'b', 'c'], 42, {a: 5}); //=> {a: {b: {c: 42}}}\n */\nvar assocPath = /*#__PURE__*/_curry3(function assocPath(path, val, obj) {\n  if (path.length === 0) {\n    return val;\n  }\n  var idx = path[0];\n  if (path.length > 1) {\n    var nextObj = !isNil(obj) && _has(idx, obj) && typeof obj[idx] === 'object' ? obj[idx] : _isInteger(path[1]) ? [] : {};\n    val = assocPath(Array.prototype.slice.call(path, 1), val, nextObj);\n  }\n  return _assoc(idx, val, obj);\n});\nexport default assocPath;", "import _isArray from \"./_isArray.js\";\nimport _isInteger from \"./_isInteger.js\";\n\n/**\n * Makes a shallow clone of an object, setting or overriding the specified\n * property with the given value. Note that this copies and flattens prototype\n * properties onto the new object as well. All non-primitive properties are\n * copied by reference.\n *\n * @private\n * @param {String|Number} prop The property name to set\n * @param {*} val The new value\n * @param {Object|Array} obj The object to clone\n * @return {Object|Array} A new object equivalent to the original except for the changed property.\n */\nexport default function _assoc(prop, val, obj) {\n  if (_isInteger(prop) && _isArray(obj)) {\n    var arr = [].concat(obj);\n    arr[prop] = val;\n    return arr;\n  }\n  var result = {};\n  for (var p in obj) {\n    result[p] = obj[p];\n  }\n  result[prop] = val;\n  return result;\n}", "var DebugLevel;\n(function (DebugLevel) {\n    DebugLevel[DebugLevel[\"DEBUG\"] = 6] = \"DEBUG\";\n    DebugLevel[DebugLevel[\"NONE\"] = 7] = \"NONE\";\n})(DebugLevel || (DebugLevel = {}));\nexport default DebugLevel;\n", "var LogLevel;\n(function (LogLevel) {\n    LogLevel[LogLevel[\"TRACE\"] = 0] = \"TRACE\";\n    LogLevel[LogLevel[\"INFO\"] = 1] = \"INFO\";\n    LogLevel[LogLevel[\"WARNING\"] = 2] = \"WARNING\";\n    LogLevel[LogLevel[\"ERROR\"] = 3] = \"ERROR\";\n    LogLevel[LogLevel[\"FATAL\"] = 4] = \"FATAL\";\n    LogLevel[LogLevel[\"NONE\"] = 5] = \"NONE\";\n})(LogLevel || (LogLevel = {}));\nexport default LogLevel;\n", "import DebugLevel from './DebugLevel';\nimport LogLevel from './LogLevel';\nconst LogString = [];\nLogString[LogLevel.TRACE] = 'trace';\nLogString[LogLevel.INFO] = 'info';\nLogString[LogLevel.WARNING] = 'warning';\nLogString[LogLevel.ERROR] = 'error';\nLogString[LogLevel.FATAL] = 'fatal';\nLogString[LogLevel.NONE] = 'none';\nLogString[DebugLevel.DEBUG] = 'debug';\nLogString[DebugLevel.NONE] = 'trace';\nlet __logLevel = LogLevel.NONE;\nlet __debugLevel = DebugLevel.NONE;\nlet __highlightPrefix;\n__highlightPrefix = false;\nfunction logFn(level, currentLevel) {\n    if (level < currentLevel) {\n        return () => { };\n    }\n    let fn;\n    let fnStyle = '';\n    switch (level) {\n        case LogLevel.TRACE:\n        case LogLevel.INFO:\n            fn = window.console.log;\n            fnStyle = 'color: white; background-color: #3166A2;';\n            break;\n        case DebugLevel.DEBUG:\n        case LogLevel.WARNING:\n            fn = window.console.warn;\n            fnStyle = 'color: white; background-color: #E9B606;';\n            break;\n        case LogLevel.ERROR:\n        case LogLevel.FATAL:\n            fn = window.console.error;\n            fnStyle = 'color: white; background-color: #FF0000;';\n            break;\n        default:\n            throw new Error(`Unknown log ${level}`);\n    }\n    const prefix = `${fnStyle && __highlightPrefix ? '%c' : ''}[${LogString[level].toUpperCase()}]`;\n    if (fnStyle && __highlightPrefix) {\n        return fn.bind(window.console, prefix, fnStyle);\n    }\n    else {\n        return fn.bind(window.console, prefix);\n    }\n}\nconst logger = {\n    setDebugLevel(level) {\n        __debugLevel = level;\n    },\n    setLogLevel(level) {\n        __logLevel = level;\n    }\n};\nObject.defineProperties(logger, {\n    trace: {\n        get: () => {\n            return logFn(LogLevel.TRACE, __logLevel);\n        },\n        configurable: false,\n        enumerable: false\n    },\n    info: {\n        get: () => {\n            return logFn(LogLevel.INFO, __logLevel);\n        },\n        configurable: false,\n        enumerable: false\n    },\n    warning: {\n        get: () => {\n            return logFn(LogLevel.WARNING, __logLevel);\n        },\n        configurable: false,\n        enumerable: false\n    },\n    error: {\n        get: () => {\n            return logFn(LogLevel.ERROR, __logLevel);\n        },\n        configurable: false,\n        enumerable: false\n    },\n    fatal: {\n        get: () => {\n            return logFn(LogLevel.FATAL, __logLevel);\n        },\n        configurable: false,\n        enumerable: false\n    },\n    debug: {\n        get: () => {\n            return logFn(DebugLevel.DEBUG, __debugLevel);\n        },\n        configurable: false,\n        enumerable: false\n    }\n});\nObject.freeze(logger);\nexport default logger;\nexport { DebugLevel, LogLevel };\n", "import _arrayReduce from \"./internal/_arrayReduce.js\";\nimport _curry2 from \"./internal/_curry2.js\";\nimport _dispatchable from \"./internal/_dispatchable.js\";\nimport _map from \"./internal/_map.js\";\nimport _xmap from \"./internal/_xmap.js\";\nimport curryN from \"./curryN.js\";\nimport keys from \"./keys.js\";\n\n/**\n * Takes a function and\n * a [functor](https://github.com/fantasyland/fantasy-land#functor),\n * applies the function to each of the functor's values, and returns\n * a functor of the same shape.\n *\n * Ramda provides suitable `map` implementations for `Array` and `Object`,\n * so this function may be applied to `[1, 2, 3]` or `{x: 1, y: 2, z: 3}`.\n *\n * Dispatches to the `map` method of the second argument, if present.\n *\n * Acts as a transducer if a transformer is given in list position.\n *\n * Also treats functions as functors and will compose them together.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig Functor f => (a -> b) -> f a -> f b\n * @param {Function} fn The function to be called on every element of the input `list`.\n * @param {Array} list The list to be iterated over.\n * @return {Array} The new list.\n * @see R.transduce, R.addIndex, R.pluck, R.project\n * @example\n *\n *      const double = x => x * 2;\n *\n *      R.map(double, [1, 2, 3]); //=> [2, 4, 6]\n *\n *      R.map(double, {x: 1, y: 2, z: 3}); //=> {x: 2, y: 4, z: 6}\n * @symb R.map(f, [a, b]) = [f(a), f(b)]\n * @symb R.map(f, { x: a, y: b }) = { x: f(a), y: f(b) }\n * @symb R.map(f, functor_o) = functor_o.map(f)\n */\nvar map = /*#__PURE__*/_curry2( /*#__PURE__*/_dispatchable(['fantasy-land/map', 'map'], _xmap, function map(fn, functor) {\n  switch (Object.prototype.toString.call(functor)) {\n    case '[object Function]':\n      return curryN(functor.length, function () {\n        return fn.call(this, functor.apply(this, arguments));\n      });\n    case '[object Object]':\n      return _arrayReduce(function (acc, key) {\n        acc[key] = fn(functor[key]);\n        return acc;\n      }, {}, keys(functor));\n    default:\n      return _map(fn, functor);\n  }\n}));\nexport default map;", "export default function _isPlaceholder(a) {\n  return a != null && typeof a === 'object' && a['@@functional/placeholder'] === true;\n}", "import _has from \"./_has.js\";\nvar toString = Object.prototype.toString;\nvar _isArguments = /*#__PURE__*/function () {\n  return toString.call(arguments) === '[object Arguments]' ? function _isArguments(x) {\n    return toString.call(x) === '[object Arguments]';\n  } : function _isArguments(x) {\n    return _has('callee', x);\n  };\n}();\nexport default _isArguments;", "import _curry1 from \"./internal/_curry1.js\";\nimport _has from \"./internal/_has.js\";\nimport _isArguments from \"./internal/_isArguments.js\";\n\n// cover IE < 9 keys issues\nvar hasEnumBug = ! /*#__PURE__*/{\n  toString: null\n}.propertyIsEnumerable('toString');\nvar nonEnumerableProps = ['constructor', 'valueOf', 'isPrototypeOf', 'toString', 'propertyIsEnumerable', 'hasOwnProperty', 'toLocaleString'];\n// Safari bug\nvar hasArgsEnumBug = /*#__PURE__*/function () {\n  'use strict';\n\n  return arguments.propertyIsEnumerable('length');\n}();\nvar contains = function contains(list, item) {\n  var idx = 0;\n  while (idx < list.length) {\n    if (list[idx] === item) {\n      return true;\n    }\n    idx += 1;\n  }\n  return false;\n};\n\n/**\n * Returns a list containing the names of all the enumerable own properties of\n * the supplied object.\n * Note that the order of the output array is not guaranteed to be consistent\n * across different JS platforms.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Object\n * @sig {k: v} -> [k]\n * @param {Object} obj The object to extract properties from\n * @return {Array} An array of the object's own properties.\n * @see R.keysIn, R.values, R.toPairs\n * @example\n *\n *      R.keys({a: 1, b: 2, c: 3}); //=> ['a', 'b', 'c']\n */\nvar keys = typeof Object.keys === 'function' && !hasArgsEnumBug ? /*#__PURE__*/_curry1(function keys(obj) {\n  return Object(obj) !== obj ? [] : Object.keys(obj);\n}) : /*#__PURE__*/_curry1(function keys(obj) {\n  if (Object(obj) !== obj) {\n    return [];\n  }\n  var prop, nIdx;\n  var ks = [];\n  var checkArgsLength = hasArgsEnumBug && _isArguments(obj);\n  for (prop in obj) {\n    if (_has(prop, obj) && (!checkArgsLength || prop !== 'length')) {\n      ks[ks.length] = prop;\n    }\n  }\n  if (hasEnumBug) {\n    nIdx = nonEnumerableProps.length - 1;\n    while (nIdx >= 0) {\n      prop = nonEnumerableProps[nIdx];\n      if (_has(prop, obj) && !contains(ks, prop)) {\n        ks[ks.length] = prop;\n      }\n      nIdx -= 1;\n    }\n  }\n  return ks;\n});\nexport default keys;", "export default function _arrayReduce(reducer, acc, list) {\n  var index = 0;\n  var length = list.length;\n  while (index < length) {\n    acc = reducer(acc, list[index]);\n    index += 1;\n  }\n  return acc;\n}", "import _isPlaceholder from \"./_isPlaceholder.js\";\n\n/**\n * Optimized internal one-arity curry function.\n *\n * @private\n * @category Function\n * @param {Function} fn The function to curry.\n * @return {Function} The curried function.\n */\nexport default function _curry1(fn) {\n  return function f1(a) {\n    if (arguments.length === 0 || _isPlaceholder(a)) {\n      return f1;\n    } else {\n      return fn.apply(this, arguments);\n    }\n  };\n}", "import _curry3 from \"./internal/_curry3.js\";\nimport assocPath from \"./assocPath.js\";\n\n/**\n * Makes a shallow clone of an object, setting or overriding the specified\n * property with the given value. Note that this copies and flattens prototype\n * properties onto the new object as well. All non-primitive properties are\n * copied by reference.\n *\n * @func\n * @memberOf R\n * @since v0.8.0\n * @category Object\n * @typedefn Idx = String | Int\n * @sig Idx -> a -> {k: v} -> {k: v}\n * @param {String|Number} prop The property name to set\n * @param {*} val The new value\n * @param {Object} obj The object to clone\n * @return {Object} A new object equivalent to the original except for the changed property.\n * @see <PERSON>.dissoc, <PERSON>.pick\n * @example\n *\n *      R.assoc('c', 3, {a: 1, b: 2}); //=> {a: 1, b: 2, c: 3}\n */\nvar assoc = /*#__PURE__*/_curry3(function assoc(prop, val, obj) {\n  return assocPath([prop], val, obj);\n});\nexport default assoc;", "/**\n * Determine if the passed argument is an integer.\n *\n * @private\n * @param {*} n\n * @category Type\n * @return {Boolean}\n */\nexport default Number.isInteger || function _isInteger(n) {\n  return n << 0 === n;\n};", "!function(e,n){\"object\"==typeof exports&&\"object\"==typeof module?module.exports=n(require(\"react\")):\"function\"==typeof define&&define.amd?define([\"react\"],n):\"object\"==typeof exports?exports[\"dash-component-plugins\"]=n(require(\"react\")):e[\"dash-component-plugins\"]=n(e.React)}(window,(function(e){return function(e){var n={};function t(r){if(n[r])return n[r].exports;var o=n[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,t),o.l=!0,o.exports}return t.m=e,t.c=n,t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:r})},t.r=function(e){\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(e,\"__esModule\",{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&\"object\"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(t.r(r),Object.defineProperty(r,\"default\",{enumerable:!0,value:e}),2&n&&\"string\"!=typeof e)for(var o in e)t.d(r,o,function(n){return e[n]}.bind(null,o));return r},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,\"a\",n),n},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.p=\"\",t(t.s=1)}([function(n,t){n.exports=e},function(e,n,t){\"use strict\";t.r(n),t.d(n,\"asyncDecorator\",(function(){return u})),t.d(n,\"inheritAsyncDecorator\",(function(){return a})),t.d(n,\"isReady\",(function(){return c})),t.d(n,\"History\",(function(){return d}));var r=t(0);function o(e,n,t,r,o,i,u){try{var a=e[i](u),c=a.value}catch(e){return void t(e)}a.done?n(c):Promise.resolve(c).then(r,o)}function i(e){return function(){var n=this,t=arguments;return new Promise((function(r,i){var u=e.apply(n,t);function a(e){o(u,r,i,a,c,\"next\",e)}function c(e){o(u,r,i,a,c,\"throw\",e)}a(void 0)}))}}var u=function(e,n){var t,o={isReady:new Promise((function(e){t=e})),get:Object(r.lazy)((function(){return Promise.resolve(n()).then((function(e){return setTimeout(i(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t(!0);case 2:o.isReady=!0;case 3:case\"end\":return e.stop()}}),e)}))),0),e}))}))};return Object.defineProperty(e,\"_dashprivate_isLazyComponentReady\",{get:function(){return o.isReady}}),o.get},a=function(e,n){Object.defineProperty(e,\"_dashprivate_isLazyComponentReady\",{get:function(){return c(n)}})},c=function(e){return e&&e._dashprivate_isLazyComponentReady};function f(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,\"value\"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var s=\"_dashprivate_historychange\",d=function(){function e(){!function(e,n){if(!(e instanceof n))throw new TypeError(\"Cannot call a class as a function\")}(this,e)}var n,t,r;return n=e,r=[{key:\"dispatchChangeEvent\",value:function(){window.dispatchEvent(new CustomEvent(s))}},{key:\"onChange\",value:function(e){return window.addEventListener(s,e),function(){return window.removeEventListener(s,e)}}}],(t=null)&&f(n.prototype,t),r&&f(n,r),Object.defineProperty(n,\"prototype\",{writable:!1}),e}()}])}));", "/**\n * Tests whether or not an object is an array.\n *\n * @private\n * @param {*} val The object to test.\n * @return {Boolean} `true` if `val` is an array, `false` otherwise.\n * @example\n *\n *      _isArray([]); //=> true\n *      _isArray(null); //=> false\n *      _isArray({}); //=> false\n */\nexport default Array.isArray || function _isArray(val) {\n  return val != null && val.length >= 0 && Object.prototype.toString.call(val) === '[object Array]';\n};", "module.exports = window[\"ReactDOM\"];", "import _arity from \"./internal/_arity.js\";\nimport _curry1 from \"./internal/_curry1.js\";\nimport _curry2 from \"./internal/_curry2.js\";\nimport _curryN from \"./internal/_curryN.js\";\n\n/**\n * Returns a curried equivalent of the provided function, with the specified\n * arity. The curried function has two unusual capabilities. First, its\n * arguments needn't be provided one at a time. If `g` is `R.curryN(3, f)`, the\n * following are equivalent:\n *\n *   - `g(1)(2)(3)`\n *   - `g(1)(2, 3)`\n *   - `g(1, 2)(3)`\n *   - `g(1, 2, 3)`\n *\n * Secondly, the special placeholder value [`R.__`](#__) may be used to specify\n * \"gaps\", allowing partial application of any combination of arguments,\n * regardless of their positions. If `g` is as above and `_` is [`R.__`](#__),\n * the following are equivalent:\n *\n *   - `g(1, 2, 3)`\n *   - `g(_, 2, 3)(1)`\n *   - `g(_, _, 3)(1)(2)`\n *   - `g(_, _, 3)(1, 2)`\n *   - `g(_, 2)(1)(3)`\n *   - `g(_, 2)(1, 3)`\n *   - `g(_, 2)(_, 3)(1)`\n *\n * @func\n * @memberOf R\n * @since v0.5.0\n * @category Function\n * @sig Number -> (* -> a) -> (* -> a)\n * @param {Number} length The arity for the returned function.\n * @param {Function} fn The function to curry.\n * @return {Function} A new, curried function.\n * @see R.curry\n * @example\n *\n *      const sumArgs = (...args) => R.sum(args);\n *\n *      const curriedAddFourNumbers = R.curryN(4, sumArgs);\n *      const f = curriedAddFourNumbers(1, 2);\n *      const g = f(3);\n *      g(4); //=> 10\n */\nvar curryN = /*#__PURE__*/_curry2(function curryN(length, fn) {\n  if (length === 1) {\n    return _curry1(fn);\n  }\n  return _arity(length, _curryN(length, [], fn));\n});\nexport default curryN;", "import _curry2 from \"./internal/_curry2.js\";\nimport _isInteger from \"./internal/_isInteger.js\";\nimport _nth from \"./internal/_nth.js\";\n\n/**\n * Returns a function that when supplied an object returns the indicated\n * property of that object, if it exists.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Object\n * @typedefn Idx = String | Int | Symbol\n * @sig Idx -> {s: a} -> a | Undefined\n * @param {String|Number} p The property name or array index\n * @param {Object} obj The object to query\n * @return {*} The value at `obj.p`.\n * @see R.path, R.props, R.pluck, R.project, R.nth\n * @example\n *\n *      R.prop('x', {x: 100}); //=> 100\n *      R.prop('x', {}); //=> undefined\n *      R.prop(0, [100]); //=> 100\n *      R.compose(R.inc, R.prop('x'))({ x: 3 }) //=> 4\n */\n\nvar prop = /*#__PURE__*/_curry2(function prop(p, obj) {\n  if (obj == null) {\n    return;\n  }\n  return _isInteger(p) ? _nth(p, obj) : obj[p];\n});\nexport default prop;", "module.exports = window[\"PropTypes\"];", "import _isString from \"./_isString.js\";\nexport default function _nth(offset, list) {\n  var idx = offset < 0 ? list.length + offset : offset;\n  return _isString(list) ? list.charAt(idx) : list[idx];\n}", "import * as R from 'ramda';\nconst __1day = 86400 * 1000;\nconst __20years = 86400 * 1000 * 365 * 20;\nexport default class CookieStorage {\n    // From https://github.com/Modernizr/Modernizr/blob/f4d3aa0b3c9eeb7338e8d89ed77929a8e969c502/feature-detects/cookies.js#L1\n    // try..catch because some in situations `document.cookie` is exposed but throws a\n    // SecurityError if you try to access it; e.g. documents created from data URIs\n    // or in sandboxed iframes (depending on flags/context)\n    static enabled = R.once(() => {\n        try {\n            // Create cookie\n            document.cookie = 'cookietest=1';\n            const ret = document.cookie.indexOf('cookietest=') !== -1;\n            // Delete cookie\n            document.cookie =\n                'cookietest=1; expires=Thu, 01-Jan-1970 00:00:01 GMT';\n            return ret;\n        }\n        catch (e) {\n            return false;\n        }\n    });\n    static delete(id, domain = '', path = '/') {\n        if (!CookieStorage.enabled()) {\n            return;\n        }\n        const expires = new Date(Date.now() - __1day).toUTCString();\n        document.cookie = `${id}=;expires=${expires};domain=${domain};path=${path}`;\n    }\n    static get(id) {\n        if (!id.length) {\n            return;\n        }\n        if (!CookieStorage.enabled()) {\n            return;\n        }\n        id = id.toLowerCase();\n        const cookies = document.cookie.split(';').map(cookie => {\n            const fragments = cookie.split('=');\n            return {\n                id: fragments[0].trim(),\n                value: fragments[1]\n            };\n        });\n        return (cookies.find(cookie => id === cookie.id.toLocaleLowerCase()) ||\n            {}).value;\n    }\n    static set(id, value, domain = '', path = '/') {\n        if (!CookieStorage.enabled()) {\n            return;\n        }\n        const expires = new Date(Date.now() + __20years).toUTCString();\n        const entry = `${id}=${value};expires=${expires};domain=${domain};path=${path}`;\n        if (CookieStorage.get(id)) {\n            CookieStorage.delete(id, domain, path);\n        }\n        document.cookie = entry;\n    }\n}\n", "import CookieStorage from 'core/storage/Cookie';\nimport { DebugLevel, LogLevel } from 'core/Logger';\nconst DASH_DEBUG = 'dash_debug';\nconst DASH_LOG = 'dash_log';\nexport default class Environment {\n    static _supportsCssVariables = Boolean(window.CSS?.supports?.('.some-selector', 'var(--some-var)'));\n    static _activeEdge = Environment._supportsCssVariables\n        ? '1px solid var(--accent)'\n        : '1px solid hotpink';\n    static get searchParams() {\n        return ((typeof URL !== 'undefined' &&\n            URL.prototype &&\n            URL.prototype.constructor &&\n            new URL(window.location.href).searchParams) || { get: () => null });\n    }\n    static get debugLevel() {\n        const debug = this.searchParams.get(DASH_DEBUG) || CookieStorage.get(DASH_DEBUG);\n        return debug\n            ? DebugLevel[debug] || DebugLevel.NONE\n            : DebugLevel.NONE;\n    }\n    static get logLevel() {\n        const log = this.searchParams.get(DASH_LOG) || CookieStorage.get(DASH_LOG);\n        return log ? LogLevel[log] || LogLevel.ERROR : LogLevel.ERROR;\n    }\n    static get defaultEdge() {\n        return '1px solid #d3d3d3';\n    }\n    static get activeEdge() {\n        return Environment._activeEdge;\n    }\n    static get supportsCssVariables() {\n        return Environment._supportsCssVariables;\n    }\n}\n", "export default function _arity(n, fn) {\n  /* eslint-disable no-unused-vars */\n  switch (n) {\n    case 0:\n      return function () {\n        return fn.apply(this, arguments);\n      };\n    case 1:\n      return function (a0) {\n        return fn.apply(this, arguments);\n      };\n    case 2:\n      return function (a0, a1) {\n        return fn.apply(this, arguments);\n      };\n    case 3:\n      return function (a0, a1, a2) {\n        return fn.apply(this, arguments);\n      };\n    case 4:\n      return function (a0, a1, a2, a3) {\n        return fn.apply(this, arguments);\n      };\n    case 5:\n      return function (a0, a1, a2, a3, a4) {\n        return fn.apply(this, arguments);\n      };\n    case 6:\n      return function (a0, a1, a2, a3, a4, a5) {\n        return fn.apply(this, arguments);\n      };\n    case 7:\n      return function (a0, a1, a2, a3, a4, a5, a6) {\n        return fn.apply(this, arguments);\n      };\n    case 8:\n      return function (a0, a1, a2, a3, a4, a5, a6, a7) {\n        return fn.apply(this, arguments);\n      };\n    case 9:\n      return function (a0, a1, a2, a3, a4, a5, a6, a7, a8) {\n        return fn.apply(this, arguments);\n      };\n    case 10:\n      return function (a0, a1, a2, a3, a4, a5, a6, a7, a8, a9) {\n        return fn.apply(this, arguments);\n      };\n    default:\n      throw new Error('First argument to _arity must be a non-negative integer no greater than ten');\n  }\n}", "export default function _isString(x) {\n  return Object.prototype.toString.call(x) === '[object String]';\n}", "export default function _map(fn, functor) {\n  var idx = 0;\n  var len = functor.length;\n  var result = Array(len);\n  while (idx < len) {\n    result[idx] = fn(functor[idx]);\n    idx += 1;\n  }\n  return result;\n}", "import _curry3 from \"./internal/_curry3.js\";\n\n/**\n * Creates a new list out of the two supplied by applying the function to each\n * equally-positioned pair in the lists. The returned list is truncated to the\n * length of the shorter of the two input lists.\n *\n * @function\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig ((a, b) -> c) -> [a] -> [b] -> [c]\n * @param {Function} fn The function used to combine the two elements into one value.\n * @param {Array} list1 The first array to consider.\n * @param {Array} list2 The second array to consider.\n * @return {Array} The list made by combining same-indexed elements of `list1` and `list2`\n *         using `fn`.\n * @example\n *\n *      const f = (x, y) => {\n *        // ...\n *      };\n *      R.zipWith(f, [1, 2, 3], ['a', 'b', 'c']);\n *      //=> [f(1, 'a'), f(2, 'b'), f(3, 'c')]\n * @symb R.zipWith(fn, [a, b, c], [d, e, f]) = [fn(a, d), fn(b, e), fn(c, f)]\n */\nvar zipWith = /*#__PURE__*/_curry3(function zipWith(fn, a, b) {\n  var len = Math.min(a.length, b.length);\n  var rv = Array(len);\n  var idx = 0;\n  while (idx < len) {\n    rv[idx] = fn(a[idx], b[idx]);\n    idx += 1;\n  }\n  return rv;\n});\nexport default zipWith;", "\nimport * as R from 'ramda';\nimport React, {Component, Suspense} from 'react';\nimport PropTypes from 'prop-types';\nimport {asyncDecorator} from '@plotly/dash-component-plugins';\nimport Lazy<PERSON>oader from 'dash-table/LazyLoader';\n/**\n * Dash DataTable is an interactive table component designed for\n * viewing, editing, and exploring large datasets.\n * DataTable is rendered with standard, semantic HTML <table/> markup,\n * which makes it accessible, responsive, and easy to style. This\n * component was written from scratch in React.js specifically for the\n * Dash community. Its API was designed to be ergonomic and its behavior\n * is completely customizable through its properties.\n */\nexport default class DataTable extends Component {\n    render() {\n        return (\n            <Suspense fallback={null}>\n                <RealDataTable {...this.props} />\n            </Suspense>\n        );\n    }\n}\nconst RealDataTable = asyncDecorator(DataTable, LazyLoader.table);\nexport const defaultProps = {\n    page_action: 'native',\n    page_current: 0,\n    page_size: 250,\n    css: [],\n    filter_query: '',\n    filter_action: 'none',\n    sort_as_null: [],\n    sort_action: 'none',\n    sort_mode: 'single',\n    sort_by: [],\n    style_as_list_view: false,\n    derived_viewport_data: [],\n    derived_viewport_indices: [],\n    derived_viewport_row_ids: [],\n    derived_viewport_selected_rows: [],\n    derived_viewport_selected_row_ids: [],\n    derived_virtual_data: [],\n    derived_virtual_indices: [],\n    derived_virtual_row_ids: [],\n    derived_virtual_selected_rows: [],\n    derived_virtual_selected_row_ids: [],\n    dropdown: {},\n    dropdown_conditional: [],\n    dropdown_data: [],\n    fill_width: true,\n    filter_options: {},\n    fixed_columns: {\n        headers: false,\n        data: 0\n    },\n    fixed_rows: {\n        headers: false,\n        data: 0\n    },\n    markdown_options: {\n        link_target: '_blank',\n        html: false\n    },\n    tooltip: {},\n    tooltip_conditional: [],\n    tooltip_data: [],\n    tooltip_header: {},\n    tooltip_delay: 350,\n    tooltip_duration: 2000,\n    column_selectable: false,\n    editable: false,\n    export_columns: 'visible',\n    export_format: 'none',\n    include_headers_on_copy_paste: false,\n    selected_cells: [],\n    selected_columns: [],\n    selected_rows: [],\n    selected_row_ids: [],\n    cell_selectable: true,\n    row_selectable: false,\n    style_table: {},\n    style_cell_conditional: [],\n    style_data_conditional: [],\n    style_filter_conditional: [],\n    style_header_conditional: [],\n    virtualization: false,\n    persisted_props: [\n        'columns.name',\n        'filter_query',\n        'hidden_columns',\n        'page_current',\n        'selected_columns',\n        'selected_rows',\n        'sort_by'\n    ],\n    persistence_type: 'local'\n};\nexport const propTypes = {\n    /**\n     * The contents of the table.\n     * The keys of each item in data should match the column IDs.\n     * Each item can also have an 'id' key, whose value is its row ID. If there\n     * is a column with ID='id' this will display the row ID, otherwise it is\n     * just used to reference the row for selections, filtering, etc.\n     * Example:\n     * [\n     *      {'column-1': 4.5, 'column-2': 'montreal', 'column-3': 'canada'},\n     *      {'column-1': 8, 'column-2': 'boston', 'column-3': 'america'}\n     * ]\n     */\n    data: PropTypes.arrayOf(\n        PropTypes.objectOf(\n            PropTypes.oneOfType([\n                PropTypes.string,\n                PropTypes.number,\n                PropTypes.bool\n            ])\n        )\n    ),\n    /**\n     * Columns describes various aspects about each individual column.\n     * `name` and `id` are the only required parameters.\n     */\n    columns: PropTypes.arrayOf(\n        PropTypes.exact({\n            /**\n             * The `id` of the column.\n             * The column `id` is used to match cells in data with particular columns.\n             * The `id` is not visible in the table.\n             */\n            id: PropTypes.string.isRequired,\n            /**\n             * The `name` of the column, as it appears in the column header.\n             * If `name` is a list of strings, then the columns\n             * will render with multiple headers rows.\n             */\n            name: PropTypes.oneOfType([\n                PropTypes.string,\n                PropTypes.arrayOf(PropTypes.string)\n            ]).isRequired,\n            /**\n             * The data-type provides support for per column typing and allows for data\n             * validation and coercion.\n             * 'numeric': represents both floats and ints.\n             * 'text': represents a string.\n             * 'datetime': a string representing a date or date-time, in the form:\n             *   'YYYY-MM-DD HH:MM:SS.ssssss' or some truncation thereof. Years must\n             *   have 4 digits, unless you use `validation.allow_YY: true`. Also\n             *   accepts 'T' or 't' between date and time, and allows timezone info\n             *   at the end. To convert these strings to Python `datetime` objects,\n             *   use `dateutil.parser.isoparse`. In R use `parse_iso_8601` from the\n             *   `parsedate` library.\n             *   WARNING: these parsers do not work with 2-digit years, if you use\n             *   `validation.allow_YY: true` and do not coerce to 4-digit years.\n             *   And parsers that do work with 2-digit years may make a different\n             *   guess about the century than we make on the front end.\n             * 'any': represents any type of data.\n             * Defaults to 'any' if undefined.\n             *\n             *\n             */\n            type: PropTypes.oneOf(['any', 'numeric', 'text', 'datetime']),\n            /**\n             * The `presentation` to use to display data. Markdown can be used on\n             * columns with type 'text'.  See 'dropdown' for more info.\n             * Defaults to 'input' for ['datetime', 'numeric', 'text', 'any'].\n             */\n            presentation: PropTypes.oneOf(['input', 'dropdown', 'markdown']),\n            /**\n             * If true, the user can select the column by clicking on the checkbox or radio button\n             * in the column. If there are multiple header rows, true will display the input\n             * on each row.\n             * If `last`, the input will only appear on the last header row. If `first` it will only\n             * appear on the first header row. These are respectively shortcut equivalents to\n             * `[false, ..., false, true]` and `[true, false, ..., false]`.\n             * If there are merged, multi-header columns then you can choose which column header\n             * row to display the input in by supplying an array of booleans.\n             * For example, `[true, false]` will display the `selectable` input on the first row,\n             * but now on the second row.\n             * If the `selectable` input appears on a merged columns, then clicking on that input\n             * will select *all* of the merged columns associated with it.\n             * The table-level prop `column_selectable` is used to determine the type of column\n             * selection to use.\n             *\n             */\n            selectable: PropTypes.oneOfType([\n                PropTypes.oneOf(['first', 'last']),\n                PropTypes.bool,\n                PropTypes.arrayOf(PropTypes.bool)\n            ]),\n            /**\n             * If true, the user can clear the column by clicking on the `clear`\n             * action button on the column. If there are multiple header rows, true\n             * will display the action button on each row.\n             * If `last`, the `clear` action button will only appear on the last header\n             * row. If `first` it will only appear on the first header row. These\n             * are respectively shortcut equivalents to `[false, ..., false, true]` and\n             * `[true, false, ..., false]`.\n             * If there are merged, multi-header columns then you can choose\n             * which column header row to display the `clear` action button in by\n             * supplying an array of booleans.\n             * For example, `[true, false]` will display the `clear` action button\n             * on the first row, but not the second row.\n             * If the `clear` action button appears on a merged column, then clicking\n             * on that button will clear *all* of the merged columns associated with it.\n             * Unlike `column.deletable`, this action does not remove the column(s)\n             * from the table. It only removed the associated entries from `data`.\n             */\n            clearable: PropTypes.oneOfType([\n                PropTypes.oneOf(['first', 'last']),\n                PropTypes.bool,\n                PropTypes.arrayOf(PropTypes.bool)\n            ]),\n            /**\n             * If true, the user can remove the column by clicking on the `delete`\n             * action button on the column. If there are multiple header rows, true\n             * will display the action button on each row.\n             * If `last`, the `delete` action button will only appear on the last header\n             * row. If `first` it will only appear on the first header row. These\n             * are respectively shortcut equivalents to `[false, ..., false, true]` and\n             * `[true, false, ..., false]`.\n             * If there are merged, multi-header columns then you can choose\n             * which column header row to display the `delete` action button in by\n             * supplying an array of booleans.\n             * For example, `[true, false]` will display the `delete` action button\n             * on the first row, but not the second row.\n             * If the `delete` action button appears on a merged column, then clicking\n             * on that button will remove *all* of the merged columns associated with it.\n             */\n            deletable: PropTypes.oneOfType([\n                PropTypes.oneOf(['first', 'last']),\n                PropTypes.bool,\n                PropTypes.arrayOf(PropTypes.bool)\n            ]),\n            /**\n             * There are two `editable` flags in the table.\n             * This is the  column-level editable flag and there is\n             * also the table-level `editable` flag.\n             * These flags determine whether the contents of the table\n             * are editable or not.\n             * If the column-level `editable` flag is set it overrides\n             * the table-level `editable` flag for that column.\n             */\n            editable: PropTypes.bool,\n            /**\n             * If true, the user can hide the column by clicking on the `hide`\n             * action button on the column. If there are multiple header rows, true\n             * will display the action button on each row.\n             * If `last`, the `hide` action button will only appear on the last header\n             * row. If `first` it will only appear on the first header row. These\n             * are respectively shortcut equivalents to `[false, ..., false, true]` and\n             * `[true, false, ..., false]`.\n             * If there are merged, multi-header columns then you can choose\n             * which column header row to display the `hide` action button in by\n             * supplying an array of booleans.\n             * For example, `[true, false]` will display the `hide` action button\n             * on the first row, but not the second row.\n             * If the `hide` action button appears on a merged column, then clicking\n             * on that button will hide *all* of the merged columns associated with it.\n             */\n            hideable: PropTypes.oneOfType([\n                PropTypes.oneOf(['first', 'last']),\n                PropTypes.bool,\n                PropTypes.arrayOf(PropTypes.bool)\n            ]),\n            /**\n             * If true, the user can rename the column by clicking on the `rename`\n             * action button on the column. If there are multiple header rows, true\n             * will display the action button on each row.\n             * If `last`, the `rename` action button will only appear on the last header\n             * row. If `first` it will only appear on the first header row. These\n             * are respectively shortcut equivalents to `[false, ..., false, true]` and\n             * `[true, false, ..., false]`.\n             * If there are merged, multi-header columns then you can choose\n             * which column header row to display the `rename` action button in by\n             * supplying an array of booleans.\n             * For example, `[true, false]` will display the `rename` action button\n             * on the first row, but not the second row.\n             * If the `rename` action button appears on a merged column, then clicking\n             * on that button will rename *all* of the merged columns associated with it.\n             */\n            renamable: PropTypes.oneOfType([\n                PropTypes.oneOf(['first', 'last']),\n                PropTypes.bool,\n                PropTypes.arrayOf(PropTypes.bool)\n            ]),\n            /**\n             * There are two `filter_options` props in the table.\n             * This is the column-level filter_options prop and there is\n             * also the table-level `filter_options` prop.\n             * If the column-level `filter_options` prop is set it overrides\n             * the table-level `filter_options` prop for that column.\n             */\n            filter_options: PropTypes.shape({\n                /**\n                 * (default: 'sensitive') Determine whether the applicable filter relational operators will default to `sensitive` or `insensitive` comparison.\n                 */\n                case: PropTypes.oneOf(['sensitive', 'insensitive']),\n                /**\n                 * (default: 'filter data...') The filter cell placeholder text.\n                 */\n                placeholder_text: PropTypes.string\n            }),\n            /**\n             * The formatting applied to the column's data.\n             * This prop is derived from the [d3-format](https://github.com/d3/d3-format) library specification. Apart from\n             * being structured slightly differently (under a single prop), the usage is the same.\n             * See also dash_table.FormatTemplate.  It contains helper functions for typical number formats.\n             */\n            format: PropTypes.exact({\n                /**\n                 * Represents localization specific formatting information.\n                 * When left unspecified, will use the default value provided by d3-format.\n                 */\n                locale: PropTypes.exact({\n                    /**\n                     * (default: ['$', '']).  A list of two strings representing the\n                     *  prefix and suffix symbols. Typically used for currency, and implemented using d3's\n                     *  currency format, but you can use this for other symbols such as measurement units\n                     */\n                    symbol: PropTypes.arrayOf(PropTypes.string),\n                    /**\n                     * (default: '.').  The string used for the decimal separator\n                     */\n                    decimal: PropTypes.string,\n                    /**\n                     * (default: ',').  The string used for the groups separator\n                     */\n                    group: PropTypes.string,\n                    /**\n                     * (default: [3]).  A list of integers representing the grouping pattern. The default is\n                     * 3 for thousands.\n                     */\n                    grouping: PropTypes.arrayOf(PropTypes.number),\n                    /**\n                     *  A list of ten strings used as replacements for numbers 0-9\n                     */\n                    numerals: PropTypes.arrayOf(PropTypes.string),\n                    /**\n                     * (default: '%').  The string used for the percentage symbol\n                     */\n                    percent: PropTypes.string,\n                    /**\n                     * (default: True). Separates integers with 4-digits or less\n                     */\n                    separate_4digits: PropTypes.bool\n                }),\n                /**\n                 * A value that will be used in place of the nully value during formatting.\n                 *   If the value type matches the column type, it will be formatted normally.\n                 */\n                nully: PropTypes.any,\n                /**\n                 * A number representing the SI unit to use during formatting.\n                 *   See `dash_table.Format.Prefix` enumeration for the list of valid values\n                 */\n                prefix: PropTypes.number,\n                /**\n                 *  (default: '').  Represents the d3 rules to apply when formatting the number.\n                 */\n                specifier: PropTypes.string\n            }),\n            /**\n             * The `on_change` behavior of the column for user-initiated modifications.\n             */\n            on_change: PropTypes.exact({\n                /**\n                 * (default 'coerce'):  'none': do not validate data;\n                 *  'coerce': check if the data corresponds to the destination type and\n                 *  attempts to coerce it into the destination type if not;\n                 *  'validate': check if the data corresponds to the destination type (no coercion).\n                 */\n                action: PropTypes.oneOf(['coerce', 'none', 'validate']),\n                /**\n                 *  (default 'reject'):  What to do with the value if the action fails.\n                 *  'accept': use the invalid value;\n                 *  'default': replace the provided value with `validation.default`;\n                 *  'reject': do not modify the existing value.\n                 */\n                failure: PropTypes.oneOf(['accept', 'default', 'reject'])\n            }),\n            /**\n             * An array of string, number and boolean values that are treated as `null`\n             * (i.e. ignored and always displayed last) when sorting.\n             * This value overrides the table-level `sort_as_null`.\n             */\n            sort_as_null: PropTypes.arrayOf(\n                PropTypes.oneOfType([\n                    PropTypes.string,\n                    PropTypes.number,\n                    PropTypes.bool\n                ])\n            ),\n            /**\n             * The `validation` options for user input processing that can accept, reject or apply a\n             * default value.\n             */\n            validation: PropTypes.exact({\n                /**\n                 * Allow the use of nully values. (undefined, null, NaN) (default: False)\n                 */\n                allow_null: PropTypes.bool,\n                /**\n                 * The default value to apply with on_change.failure = 'default'. (default: None)\n                 */\n                default: PropTypes.any,\n                /**\n                 * This is for `datetime` columns only.  Allow 2-digit years (default: False).\n                 *   If True, we interpret years as ranging from now-70 to now+29 - in 2019\n                 *   this is 1949 to 2048 but in 2020 it will be different. If used with\n                 *   `action: 'coerce'`, will convert user input to a 4-digit year.\n                 */\n                allow_YY: PropTypes.bool\n            })\n        })\n    ),\n    /**\n     * If True, then the data in all of the cells is editable.\n     * When `editable` is True, particular columns can be made\n     * uneditable by setting `editable` to `False` inside the `columns`\n     * property.\n     * If False, then the data in all of the cells is uneditable.\n     * When `editable` is False, particular columns can be made\n     * editable by setting `editable` to `True` inside the `columns`\n     * property.\n     */\n    editable: PropTypes.bool,\n    /**\n     * `fixed_columns` will \"fix\" the set of columns so that\n     * they remain visible when scrolling horizontally across\n     * the unfixed columns. `fixed_columns` fixes columns\n     * from left-to-right.\n     * If `headers` is False, no columns are fixed.\n     * If `headers` is True, all operation columns (see `row_deletable` and `row_selectable`)\n     * are fixed. Additional data columns can be fixed by\n     * assigning a number to `data`.\n     *\n     * Note that fixing columns introduces some changes to the\n     * underlying markup of the table and may impact the\n     * way that your columns are rendered or sized.\n     * View the documentation examples to learn more.\n     *\n     */\n    fixed_columns: PropTypes.exact({\n        /**\n         * Example `{'headers':False, 'data':0}` No columns are fixed (the default)\n         */\n        data: PropTypes.number,\n        headers: PropTypes.bool\n    }),\n    /**\n     * `fixed_rows` will \"fix\" the set of rows so that\n     * they remain visible when scrolling vertically down\n     * the table. `fixed_rows` fixes rows\n     * from top-to-bottom, starting from the headers.\n     * If `headers` is False, no rows are fixed.\n     * If `headers` is True, all header and filter rows (see `filter_action`) are\n     * fixed. Additional data rows can be fixed by assigning\n     * a number to `data`.  Note that fixing rows introduces some changes to the\n     * underlying markup of the table and may impact the\n     * way that your columns are rendered or sized.\n     * View the documentation examples to learn more.\n     */\n    fixed_rows: PropTypes.exact({\n        /**\n         * Example `{'headers':False, 'data':0}` No rows are fixed (the default)\n         */\n        data: PropTypes.number,\n        headers: PropTypes.bool\n    }),\n    /**\n     * If `single`, then the user can select a single column or group\n     * of merged columns via the radio button that will appear in the\n     * header rows.\n     * If `multi`, then the user can select multiple columns or groups\n     * of merged columns via the checkbox that will appear in the header\n     * rows.\n     * If false, then the user will not be able to select columns and no\n     * input will appear in the header rows.\n     * When a column is selected, its id will be contained in `selected_columns`\n     * and `derived_viewport_selected_columns`.\n     */\n    column_selectable: PropTypes.oneOf(['single', 'multi', false]),\n    /**\n     * If True (default), then it is possible to click and navigate\n     * table cells.\n     */\n    cell_selectable: PropTypes.bool,\n    /**\n     * If `single`, then the user can select a single row\n     * via a radio button that will appear next to each row.\n     * If `multi`, then the user can select multiple rows\n     * via a checkbox that will appear next to each row.\n     * If false, then the user will not be able to select rows\n     * and no additional UI elements will appear.\n     * When a row is selected, its index will be contained\n     * in `selected_rows`.\n     */\n    row_selectable: PropTypes.oneOf(['single', 'multi', false]),\n    /**\n     * If True, then a `x` will appear next to each `row`\n     * and the user can delete the row.\n     */\n    row_deletable: PropTypes.bool,\n    /**\n     * The row and column indices and IDs of the currently active cell.\n     * `row_id` is only returned if the data rows have an `id` key.\n     */\n    active_cell: PropTypes.exact({\n        row: PropTypes.number,\n        column: PropTypes.number,\n        row_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n        column_id: PropTypes.string\n    }),\n    /**\n     * `selected_cells` represents the set of cells that are selected,\n     * as an array of objects, each item similar to `active_cell`.\n     * Multiple cells can be selected by holding down shift and\n     * clicking on a different cell or holding down shift and navigating\n     * with the arrow keys.\n     */\n    selected_cells: PropTypes.arrayOf(\n        PropTypes.exact({\n            row: PropTypes.number,\n            column: PropTypes.number,\n            row_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n            column_id: PropTypes.string\n        })\n    ),\n    /**\n     * `selected_rows` contains the indices of rows that\n     * are selected via the UI elements that appear when\n     * `row_selectable` is `'single'` or `'multi'`.\n     */\n    selected_rows: PropTypes.arrayOf(PropTypes.number),\n    /**\n     * `selected_columns` contains the ids of columns that\n     * are selected via the UI elements that appear when\n     * `column_selectable` is `'single' or 'multi'`.\n     */\n    selected_columns: PropTypes.arrayOf(PropTypes.string),\n    /**\n     * `selected_row_ids` contains the ids of rows that\n     * are selected via the UI elements that appear when\n     * `row_selectable` is `'single'` or `'multi'`.\n     */\n    selected_row_ids: PropTypes.arrayOf(\n        PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n    ),\n    /**\n     * When selecting multiple cells\n     * (via clicking on a cell and then shift-clicking on another cell),\n     * `start_cell` represents the [row, column] coordinates of the cell\n     * in one of the corners of the region.\n     * `end_cell` represents the coordinates of the other corner.\n     */\n    start_cell: PropTypes.exact({\n        row: PropTypes.number,\n        column: PropTypes.number,\n        row_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n        column_id: PropTypes.string\n    }),\n    /**\n     * When selecting multiple cells\n     * (via clicking on a cell and then shift-clicking on another cell),\n     * `end_cell` represents the row / column coordinates and IDs of the cell\n     * in one of the corners of the region.\n     * `start_cell` represents the coordinates of the other corner.\n     */\n    end_cell: PropTypes.exact({\n        row: PropTypes.number,\n        column: PropTypes.number,\n        row_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n        column_id: PropTypes.string\n    }),\n    /**\n     * The previous state of `data`. `data_previous`\n     * has the same structure as `data` and it will be updated\n     * whenever `data` changes, either through a callback or\n     * by editing the table.\n     * This is a read-only property: setting this property will not\n     * have any impact on the table.\n     */\n    data_previous: PropTypes.arrayOf(PropTypes.object),\n    /**\n     * List of columns ids of the columns that are currently hidden.\n     * See the associated nested prop `columns.hideable`.\n     */\n    hidden_columns: PropTypes.arrayOf(PropTypes.string),\n    /**\n     * If True, then the `active_cell` is in a focused state.\n     */\n    is_focused: PropTypes.bool,\n    /**\n     * If True, then column headers that have neighbors with duplicate names\n     * will be merged into a single cell.\n     * This will be applied for single column headers and multi-column\n     * headers.\n     */\n    merge_duplicate_headers: PropTypes.bool,\n    /**\n     * The unix timestamp when the data was last edited.\n     * Use this property with other timestamp properties\n     * (such as `n_clicks_timestamp` in `dash_html_components`)\n     * to determine which property has changed within a callback.\n     */\n    data_timestamp: PropTypes.number,\n    /**\n     * If true, headers are included when copying from the table to different\n     * tabs and elsewhere. Note that headers are ignored when copying from the table onto itself and\n     * between two tables within the same tab.\n     */\n    include_headers_on_copy_paste: PropTypes.bool,\n    /**\n     * Denotes the columns that will be used in the export data file.\n     * If `all`, all columns will be used (visible + hidden). If `visible`,\n     * only the visible columns will be used. Defaults to `visible`.\n     */\n    export_columns: PropTypes.oneOf(['all', 'visible']),\n    /**\n     * Denotes the type of the export data file,\n     * Defaults to `'none'`\n     */\n    export_format: PropTypes.oneOf(['csv', 'xlsx', 'none']),\n    /**\n     * Denotes the format of the headers in the export data file.\n     * If `'none'`, there will be no header. If `'display'`, then the header\n     * of the data file will be how it is currently displayed. Note that\n     * `'display'` is only supported for `'xlsx'` export_format and will behave\n     * like `'names'` for `'csv'` export format. If `'ids'` or `'names'`,\n     * then the headers of data file will be the column id or the column\n     * names, respectively\n     */\n    export_headers: PropTypes.oneOf(['none', 'ids', 'names', 'display']),\n    /**\n     * `page_action` refers to a mode of the table where\n     * not all of the rows are displayed at once: only a subset\n     * are displayed (a \"page\") and the next subset of rows\n     * can viewed by clicking \"Next\" or \"Previous\" buttons\n     * at the bottom of the page.\n     * Pagination is used to improve performance: instead of\n     * rendering all of the rows at once (which can be expensive),\n     * we only display a subset of them.\n     * With pagination, we can either page through data that exists\n     * in the table (e.g. page through `10,000` rows in `data` `100` rows at a time)\n     * or we can update the data on-the-fly with callbacks\n     * when the user clicks on the \"Previous\" or \"Next\" buttons.\n     * These modes can be toggled with this `page_action` parameter:\n     * `'native'`: all data is passed to the table up-front, paging logic is\n     * handled by the table;\n     * `'custom'`: data is passed to the table one page at a time, paging logic\n     * is handled via callbacks;\n     * `'none'`: disables paging, render all of the data at once.\n     */\n    page_action: PropTypes.oneOf(['custom', 'native', 'none']),\n    /**\n     * `page_current` represents which page the user is on.\n     * Use this property to index through data in your callbacks with\n     * backend paging.\n     */\n    page_current: PropTypes.number,\n    /**\n     * `page_count` represents the number of the pages in the\n     * paginated table. This is really only useful when performing\n     * backend pagination, since the front end is able to use the\n     * full size of the table to calculate the number of pages.\n     */\n    page_count: PropTypes.number,\n    /**\n     * `page_size` represents the number of rows that will be\n     * displayed on a particular page when `page_action` is `'custom'` or `'native'`\n     */\n    page_size: PropTypes.number,\n    /**\n     * If `filter_action` is enabled, then the current filtering\n     * string is represented in this `filter_query`\n     * property.\n     */\n    filter_query: PropTypes.string,\n    /**\n     * The `filter_action` property controls the behavior of the `filtering` UI.\n     * If `'none'`, then the filtering UI is not displayed.\n     * If `'native'`, then the filtering UI is displayed and the filtering\n     * logic is handled by the table. That is, it is performed on the data\n     * that exists in the `data` property.\n     * If `'custom'`, then the filtering UI is displayed but it is the\n     * responsibility of the developer to program the filtering\n     * through a callback (where `filter_query` or `derived_filter_query_structure` would be the input\n     * and `data` would be the output).\n     */\n    filter_action: PropTypes.oneOfType([\n        PropTypes.oneOf(['custom', 'native', 'none']),\n        PropTypes.shape({\n            type: PropTypes.oneOf(['custom', 'native']).isRequired,\n            operator: PropTypes.oneOf(['and', 'or'])\n        })\n    ]),\n    /**\n     * There are two `filter_options` props in the table.\n     * This is the table-level filter_options prop and there is\n     * also the column-level `filter_options` prop.\n     * If the column-level `filter_options` prop is set it overrides\n     * the table-level `filter_options` prop for that column.\n     */\n    filter_options: PropTypes.shape({\n        /**\n         * (default: 'sensitive') Determine whether the applicable filter relational operators will default to `sensitive` or `insensitive` comparison.\n         */\n        case: PropTypes.oneOf(['sensitive', 'insensitive']),\n        /**\n         * (default: 'filter data...') The filter cell placeholder text.\n         */\n        placeholder_text: PropTypes.string\n    }),\n    /**\n     * The `sort_action` property enables data to be\n     * sorted on a per-column basis.\n     * If `'none'`, then the sorting UI is not displayed.\n     * If `'native'`, then the sorting UI is displayed and the sorting\n     * logic is handled by the table. That is, it is performed on the data\n     * that exists in the `data` property.\n     * If `'custom'`, the sorting UI is displayed but it is the\n     * responsibility of the developer to program the sorting\n     * through a callback (where `sort_by` would be the input and `data`\n     * would be the output).\n     * Clicking on the sort arrows will update the\n     * `sort_by` property.\n     */\n    sort_action: PropTypes.oneOf(['custom', 'native', 'none']),\n    /**\n     * Sorting can be performed across multiple columns\n     * (e.g. sort by country, sort within each country,\n     *  sort by year) or by a single column.\n     * NOTE - With multi-column sort, it's currently\n     * not possible to determine the order in which\n     * the columns were sorted through the UI.\n     * See [https://github.com/plotly/dash-table/issues/170](https://github.com/plotly/dash-table/issues/170)\n     */\n    sort_mode: PropTypes.oneOf(['single', 'multi']),\n    /**\n     * `sort_by` describes the current state\n     * of the sorting UI.\n     * That is, if the user clicked on the sort arrow\n     * of a column, then this property will be updated\n     * with the column ID and the direction\n     * (`asc` or `desc`) of the sort.\n     * For multi-column sorting, this will be a list of\n     * sorting parameters, in the order in which they were\n     * clicked.\n     */\n    sort_by: PropTypes.arrayOf(\n        PropTypes.exact({\n            column_id: PropTypes.string.isRequired,\n            direction: PropTypes.oneOf(['asc', 'desc']).isRequired\n        })\n    ),\n    /**\n     * An array of string, number and boolean values that are treated as `None`\n     * (i.e. ignored and always displayed last) when sorting.\n     * This value will be used by columns without `sort_as_null`.\n     * Defaults to `[]`.\n     */\n    sort_as_null: PropTypes.arrayOf(\n        PropTypes.oneOfType([\n            PropTypes.string,\n            PropTypes.number,\n            PropTypes.bool\n        ])\n    ),\n    /**\n     * `dropdown` specifies dropdown options for different columns.\n     * Each entry refers to the column ID.\n     * The `clearable` property defines whether the value can be deleted.\n     * The `options` property refers to the `options` of the dropdown.\n     */\n    dropdown: PropTypes.objectOf(\n        PropTypes.exact({\n            clearable: PropTypes.bool,\n            options: PropTypes.arrayOf(\n                PropTypes.exact({\n                    label: PropTypes.string.isRequired,\n                    value: PropTypes.oneOfType([\n                        PropTypes.number,\n                        PropTypes.string,\n                        PropTypes.bool\n                    ]).isRequired\n                })\n            ).isRequired\n        })\n    ),\n    /**\n     * `dropdown_conditional` specifies dropdown options in various columns and cells.\n     * This property allows you to specify different dropdowns\n     * depending on certain conditions. For example, you may\n     * render different \"city\" dropdowns in a row depending on the\n     * current value in the \"state\" column.\n     */\n    dropdown_conditional: PropTypes.arrayOf(\n        PropTypes.exact({\n            clearable: PropTypes.bool,\n            if: PropTypes.exact({\n                column_id: PropTypes.string,\n                filter_query: PropTypes.string\n            }),\n            options: PropTypes.arrayOf(\n                PropTypes.exact({\n                    label: PropTypes.string.isRequired,\n                    value: PropTypes.oneOfType([\n                        PropTypes.number,\n                        PropTypes.string,\n                        PropTypes.bool\n                    ]).isRequired\n                })\n            ).isRequired\n        })\n    ),\n    /**\n     * `dropdown_data` specifies dropdown options on a row-by-row, column-by-column basis.\n     * Each item in the array corresponds to the corresponding dropdowns for the `data` item\n     * at the same index. Each entry in the item refers to the Column ID.\n     */\n    dropdown_data: PropTypes.arrayOf(\n        PropTypes.objectOf(\n            PropTypes.exact({\n                clearable: PropTypes.bool,\n                options: PropTypes.arrayOf(\n                    PropTypes.exact({\n                        label: PropTypes.string.isRequired,\n                        value: PropTypes.oneOfType([\n                            PropTypes.number,\n                            PropTypes.string,\n                            PropTypes.bool\n                        ]).isRequired\n                    })\n                ).isRequired\n            })\n        )\n    ),\n    /**\n     * `tooltip` is the column based tooltip configuration applied to all rows. The key is the column\n     *  id and the value is a tooltip configuration.\n     * Example: {i: {'value': i, 'use_with: 'both'} for i in df.columns}\n     */\n    tooltip: PropTypes.objectOf(\n        PropTypes.oneOfType([\n            PropTypes.string,\n            PropTypes.exact({\n                /**\n                 * Represents the delay in milliseconds before\n                 * the tooltip is shown when hovering a cell. This overrides\n                 * the table's `tooltip_delay` property. If set to `None`,\n                 * the tooltip will be shown immediately.\n                 */\n                delay: PropTypes.number,\n                /**\n                 * represents the duration in milliseconds\n                 * during which the tooltip is shown when hovering a cell.\n                 * This overrides the table's `tooltip_duration` property.\n                 * If set to `None`, the tooltip will not disappear.\n                 */\n                duration: PropTypes.number,\n                /**\n                 * refers to the type of tooltip syntax used\n                 * for the tooltip generation. Can either be `markdown`\n                 * or `text`. Defaults to `text`.\n                 */\n                type: PropTypes.oneOf(['text', 'markdown']),\n                /**\n                 * Refers to whether the tooltip will be shown\n                 * only on data or headers. Can be `both`, `data`, `header`.\n                 * Defaults to `both`.\n                 */\n                use_with: PropTypes.oneOf(['both', 'data', 'header']),\n                /**\n                 * refers to the syntax-based content of\n                 * the tooltip. This value is required. Alternatively, the value of the\n                 * property can also be  a plain string. The `text` syntax will be used in\n                 * that case.\n                 */\n                value: PropTypes.string.isRequired\n            })\n        ])\n    ),\n    /**\n     * `tooltip_conditional` represents the tooltip shown\n     * for different columns and cells.\n     * This property allows you to specify different tooltips\n     * depending on certain conditions. For example, you may have\n     * different tooltips in the same column based on the value\n     * of a certain data property.\n     * Priority is from first to last defined conditional tooltip\n     * in the list. Higher priority (more specific) conditional\n     * tooltips should be put at the beginning of the list.\n     */\n    tooltip_conditional: PropTypes.arrayOf(\n        PropTypes.exact({\n            /**\n             * The `delay` represents the delay in milliseconds before\n             * the tooltip is shown when hovering a cell. This overrides\n             * the table's `tooltip_delay` property. If set to `None`,\n             * the tooltip will be shown immediately.\n             */\n            delay: PropTypes.number,\n            /**\n             * The `duration` represents the duration in milliseconds\n             * during which the tooltip is shown when hovering a cell.\n             * This overrides the table's `tooltip_duration` property.\n             * If set to `None`, the tooltip will not disappear.\n             */\n            duration: PropTypes.number,\n            /**\n             * The `if` refers to the condition that needs to be fulfilled\n             * in order for the associated tooltip configuration to be\n             * used. If multiple conditions are defined, all conditions\n             * must be met for the tooltip to be used by a cell.\n             */\n            if: PropTypes.exact({\n                /**\n                 * `column_id` refers to the column ID that must be matched.\n                 */\n                column_id: PropTypes.string,\n                /**\n                 * `filter_query` refers to the query that must evaluate to True.\n                 */\n                filter_query: PropTypes.string,\n                /**\n                 * `row_index` refers to the index of the row in the source `data`.\n                 */\n                row_index: PropTypes.oneOfType([\n                    PropTypes.number,\n                    PropTypes.oneOf(['odd', 'even'])\n                ])\n            }).isRequired,\n            /**\n             * The `type` refers to the type of tooltip syntax used\n             * for the tooltip generation. Can either be `markdown`\n             * or `text`. Defaults to `text`.\n             */\n            type: PropTypes.oneOf(['text', 'markdown']),\n            /**\n             * The `value` refers to the syntax-based content of the tooltip. This value is required.\n             */\n            value: PropTypes.string.isRequired\n        })\n    ),\n    /**\n     * `tooltip_data` represents the tooltip shown\n     * for different columns and cells.\n     * A list of dicts for which each key is\n     * a column id and the value is a tooltip configuration.\n     */\n    tooltip_data: PropTypes.arrayOf(\n        PropTypes.objectOf(\n            PropTypes.oneOfType([\n                PropTypes.string,\n                PropTypes.exact({\n                    /**\n                     * The `delay` represents the delay in milliseconds before\n                     * the tooltip is shown when hovering a cell. This overrides\n                     * the table's `tooltip_delay` property. If set to `None`,\n                     * the tooltip will be shown immediately.\n                     */\n                    delay: PropTypes.number,\n                    /**\n                     * The `duration` represents the duration in milliseconds\n                     * during which the tooltip is shown when hovering a cell.\n                     * This overrides the table's `tooltip_duration` property.\n                     * If set to `None`, the tooltip will not disappear.\n                     * Alternatively, the value of the property can also be\n                     * a plain string. The `text` syntax will be used in\n                     * that case.\n                     */\n                    duration: PropTypes.number,\n                    /**\n                     * For each tooltip configuration,\n                     * The `type` refers to the type of tooltip syntax used\n                     * for the tooltip generation. Can either be `markdown`\n                     * or `text`. Defaults to `text`.\n                     */\n                    type: PropTypes.oneOf(['text', 'markdown']),\n                    /**\n                     * The `value` refers to the syntax-based content of the tooltip. This value is required.\n                     */\n                    value: PropTypes.string.isRequired\n                })\n            ])\n        )\n    ),\n    /**\n     * `tooltip_header` represents the tooltip shown\n     * for each header column and optionally each header row.\n     * Example to show long column names in a tooltip: {i: i for i in df.columns}.\n     * Example to show different column names in a tooltip: {'Rep': 'Republican', 'Dem': 'Democrat'}.\n     * If the table has multiple rows of headers, then use a list as the value of the\n     * `tooltip_header` items.\n     *\n     *\n     */\n    tooltip_header: PropTypes.objectOf(\n        PropTypes.oneOfType([\n            PropTypes.string,\n            PropTypes.exact({\n                /**\n                 * The `delay` represents the delay in milliseconds before\n                 * the tooltip is shown when hovering a cell. This overrides\n                 * the table's `tooltip_delay` property. If set to `None`,\n                 * the tooltip will be shown immediately.\n                 */\n                delay: PropTypes.number,\n                /**\n                 * The `duration` represents the duration in milliseconds\n                 * during which the tooltip is shown when hovering a cell.\n                 * This overrides the table's `tooltip_duration` property.\n                 * If set to `None`, the tooltip will not disappear.\n                 * Alternatively, the value of the property can also be\n                 * a plain string. The `text` syntax will be used in\n                 * that case.\n                 */\n                duration: PropTypes.number,\n                /**\n                 * For each tooltip configuration,\n                 * The `type` refers to the type of tooltip syntax used\n                 * for the tooltip generation. Can either be `markdown`\n                 * or `text`. Defaults to `text`.\n                 */\n                type: PropTypes.oneOf(['text', 'markdown']),\n                /**\n                 * The `value` refers to the syntax-based content of the tooltip. This value is required.\n                 */\n                value: PropTypes.string.isRequired\n            }),\n            PropTypes.arrayOf(\n                PropTypes.oneOfType([\n                    PropTypes.oneOf([null]),\n                    PropTypes.string,\n                    PropTypes.exact({\n                        delay: PropTypes.number,\n                        duration: PropTypes.number,\n                        type: PropTypes.oneOf(['text', 'markdown']),\n                        value: PropTypes.string.isRequired\n                    })\n                ])\n            )\n        ])\n    ),\n    /**\n     * `tooltip_delay` represents the table-wide delay in milliseconds before\n     * the tooltip is shown when hovering a cell. If set to `None`, the tooltip\n     * will be shown immediately.\n     * Defaults to 350.\n     */\n    tooltip_delay: PropTypes.number,\n    /**\n     * `tooltip_duration` represents the table-wide duration in milliseconds\n     * during which the tooltip will be displayed when hovering a cell. If\n     * set to `None`, the tooltip will not disappear.\n     * Defaults to 2000.\n     */\n    tooltip_duration: PropTypes.number,\n    /**\n     * The localization specific formatting information applied to all columns in the table.\n     * This prop is derived from the [d3.formatLocale](https://github.com/d3/d3-format#formatLocale) data structure specification.\n     * When left unspecified, each individual nested prop will default to a pre-determined value.\n     */\n    locale_format: PropTypes.exact({\n        /**\n         *   (default: ['$', '']). A  list of two strings representing the\n         *   prefix and suffix symbols. Typically used for currency, and implemented using d3's\n         *   currency format, but you can use this for other symbols such as measurement units.\n         */\n        symbol: PropTypes.arrayOf(PropTypes.string),\n        /**\n         * (default: '.'). The string used for the decimal separator.\n         */\n        decimal: PropTypes.string,\n        /**\n         * (default: ','). The string used for the groups separator.\n         */\n        group: PropTypes.string,\n        /**\n         * (default: [3]). A  list of integers representing the grouping pattern.\n         */\n        grouping: PropTypes.arrayOf(PropTypes.number),\n        /**\n         * A list of ten strings used as replacements for numbers 0-9.\n         */\n        numerals: PropTypes.arrayOf(PropTypes.string),\n        /**\n         * (default: '%'). The string used for the percentage symbol.\n         */\n        percent: PropTypes.string,\n        /**\n         * (default: True). Separate integers with 4-digits or less.\n         */\n        separate_4digits: PropTypes.bool\n    }),\n    /**\n     * If True, then the table will be styled like a list view\n     * and not have borders between the columns.\n     */\n    style_as_list_view: PropTypes.bool,\n    /**\n     * `fill_width` toggles between a set of CSS for two common behaviors:\n     * True: The table container's width will grow to fill the available space;\n     * False: The table container's width will equal the width of its content.\n     */\n    fill_width: PropTypes.bool,\n    /**\n     * The `markdown_options` property allows customization of the markdown cells behavior.\n     */\n    markdown_options: PropTypes.exact({\n        /**\n         * (default: '_blank').  The link's behavior (_blank opens the link in a\n         * new tab, _parent opens the link in the parent frame, _self opens the link in the\n         * current tab, and _top opens the link in the top frame) or a string\n         */\n        link_target: PropTypes.oneOfType([\n            PropTypes.string,\n            PropTypes.oneOf(['_blank', '_parent', '_self', '_top'])\n        ]),\n        /**\n         * (default: False)  If True, html may be used in markdown cells\n         * Be careful enabling html if the content being rendered can come\n         * from an untrusted user, as this may create an XSS vulnerability.\n         */\n        html: PropTypes.bool\n    }),\n    /**\n     * The `css` property is a way to embed CSS selectors and rules\n     * onto the page.\n     * We recommend starting with the `style_*` properties\n     * before using this `css` property.\n     * Example:\n     * [\n     *     {\"selector\": \".dash-spreadsheet\", \"rule\": 'font-family: \"monospace\"'}\n     * ]\n     */\n    css: PropTypes.arrayOf(\n        PropTypes.exact({\n            selector: PropTypes.string.isRequired,\n            rule: PropTypes.string.isRequired\n        })\n    ),\n    /**\n     * CSS styles to be applied to the outer `table` container.\n     * This is commonly used for setting properties like the\n     * width or the height of the table.\n     */\n    style_table: PropTypes.object,\n    /**\n     * CSS styles to be applied to each individual cell of the table.\n     * This includes the header cells, the `data` cells, and the filter\n     * cells.\n     */\n    style_cell: PropTypes.object,\n    /**\n     * CSS styles to be applied to each individual data cell.\n     * That is, unlike `style_cell`, it excludes the header and filter cells.\n     */\n    style_data: PropTypes.object,\n    /**\n     * CSS styles to be applied to the filter cells.\n     * Note that this may change in the future as we build out a\n     * more complex filtering UI.\n     */\n    style_filter: PropTypes.object,\n    /**\n     * CSS styles to be applied to each individual header cell.\n     * That is, unlike `style_cell`, it excludes the `data` and filter cells.\n     */\n    style_header: PropTypes.object,\n    /**\n     * Conditional CSS styles for the cells.\n     * This can be used to apply styles to cells on a per-column basis.\n     */\n    style_cell_conditional: PropTypes.arrayOf(\n        PropTypes.shape({\n            if: PropTypes.exact({\n                column_id: PropTypes.oneOfType([\n                    PropTypes.string,\n                    PropTypes.arrayOf(PropTypes.string)\n                ]),\n                column_type: PropTypes.oneOf([\n                    'any',\n                    'numeric',\n                    'text',\n                    'datetime'\n                ])\n            })\n        })\n    ),\n    /**\n     * Conditional CSS styles for the data cells.\n     * This can be used to apply styles to data cells on a per-column basis.\n     */\n    style_data_conditional: PropTypes.arrayOf(\n        PropTypes.shape({\n            if: PropTypes.exact({\n                column_id: PropTypes.oneOfType([\n                    PropTypes.string,\n                    PropTypes.arrayOf(PropTypes.string)\n                ]),\n                column_type: PropTypes.oneOf([\n                    'any',\n                    'numeric',\n                    'text',\n                    'datetime'\n                ]),\n                filter_query: PropTypes.string,\n                state: PropTypes.oneOf(['active', 'selected']),\n                row_index: PropTypes.oneOfType([\n                    PropTypes.number,\n                    PropTypes.oneOf(['odd', 'even']),\n                    PropTypes.arrayOf(PropTypes.number)\n                ]),\n                column_editable: PropTypes.bool\n            })\n        })\n    ),\n    /**\n     * Conditional CSS styles for the filter cells.\n     * This can be used to apply styles to filter cells on a per-column basis.\n     */\n    style_filter_conditional: PropTypes.arrayOf(\n        PropTypes.shape({\n            if: PropTypes.exact({\n                column_id: PropTypes.oneOfType([\n                    PropTypes.string,\n                    PropTypes.arrayOf(PropTypes.string)\n                ]),\n                column_type: PropTypes.oneOf([\n                    'any',\n                    'numeric',\n                    'text',\n                    'datetime'\n                ]),\n                column_editable: PropTypes.bool\n            })\n        })\n    ),\n    /**\n     * Conditional CSS styles for the header cells.\n     * This can be used to apply styles to header cells on a per-column basis.\n     */\n    style_header_conditional: PropTypes.arrayOf(\n        PropTypes.shape({\n            if: PropTypes.exact({\n                column_id: PropTypes.oneOfType([\n                    PropTypes.string,\n                    PropTypes.arrayOf(PropTypes.string)\n                ]),\n                column_type: PropTypes.oneOf([\n                    'any',\n                    'numeric',\n                    'text',\n                    'datetime'\n                ]),\n                header_index: PropTypes.oneOfType([\n                    PropTypes.number,\n                    PropTypes.arrayOf(PropTypes.number),\n                    PropTypes.oneOf(['odd', 'even'])\n                ]),\n                column_editable: PropTypes.bool\n            })\n        })\n    ),\n    /**\n     * This property tells the table to use virtualization when rendering.\n     * Assumptions are that:\n     * the width of the columns is fixed;\n     * the height of the rows is always the same; and\n     * runtime styling changes will not affect width and height vs. first rendering\n     */\n    virtualization: PropTypes.bool,\n    /**\n     * This property represents the current structure of\n     * `filter_query` as a tree structure. Each node of the\n     * query structure has:\n     * type (string; required):\n     *   'open-block',\n     *   'logical-operator',\n     *   'relational-operator',\n     *   'unary-operator', or\n     *   'expression';\n     * subType (string; optional):\n     *   'open-block': '()',\n     *   'logical-operator': '&&', '||',\n     *   'relational-operator': '=', '>=', '>', '<=', '<', '!=', 'contains',\n     *   'unary-operator': '!', 'is bool', 'is even', 'is nil', 'is num', 'is object', 'is odd', 'is prime', 'is str',\n     *   'expression': 'value', 'field';\n     * value (any):\n     *   'expression, value': passed value,\n     *   'expression, field': the field/prop name.\n     * block (nested query structure; optional).\n     * left (nested query structure; optional).\n     * right (nested query structure; optional).\n     * If the query is invalid or empty, the `derived_filter_query_structure` will\n     * be `None`.\n     */\n    derived_filter_query_structure: PropTypes.object,\n    /**\n     * This property represents the current state of `data`\n     * on the current page. This property will be updated\n     * on paging, sorting, and filtering.\n     */\n    derived_viewport_data: PropTypes.arrayOf(PropTypes.object),\n    /**\n     * `derived_viewport_indices` indicates the order in which the original\n     * rows appear after being filtered, sorted, and/or paged.\n     * `derived_viewport_indices` contains indices for the current page,\n     * while `derived_virtual_indices` contains indices across all pages.\n     */\n    derived_viewport_indices: PropTypes.arrayOf(PropTypes.number),\n    /**\n     * `derived_viewport_row_ids` lists row IDs in the order they appear\n     * after being filtered, sorted, and/or paged.\n     * `derived_viewport_row_ids` contains IDs for the current page,\n     * while `derived_virtual_row_ids` contains IDs across all pages.\n     */\n    derived_viewport_row_ids: PropTypes.arrayOf(\n        PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n    ),\n    /**\n     * `derived_viewport_selected_columns` contains the ids of the\n     * `selected_columns` that are not currently hidden.\n     */\n    derived_viewport_selected_columns: PropTypes.arrayOf(PropTypes.string),\n    /**\n     * `derived_viewport_selected_rows` represents the indices of the\n     * `selected_rows` from the perspective of the `derived_viewport_indices`.\n     */\n    derived_viewport_selected_rows: PropTypes.arrayOf(PropTypes.number),\n    /**\n     * `derived_viewport_selected_row_ids` represents the IDs of the\n     * `selected_rows` on the currently visible page.\n     */\n    derived_viewport_selected_row_ids: PropTypes.arrayOf(\n        PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n    ),\n    /**\n     * This property represents the visible state of `data`\n     * across all pages after the front-end sorting and filtering\n     * as been applied.\n     */\n    derived_virtual_data: PropTypes.arrayOf(PropTypes.object),\n    /**\n     * `derived_virtual_indices` indicates the order in which the original\n     * rows appear after being filtered and sorted.\n     * `derived_viewport_indices` contains indices for the current page,\n     * while `derived_virtual_indices` contains indices across all pages.\n     */\n    derived_virtual_indices: PropTypes.arrayOf(PropTypes.number),\n    /**\n     * `derived_virtual_row_ids` indicates the row IDs in the order in which\n     * they appear after being filtered and sorted.\n     * `derived_viewport_row_ids` contains IDs for the current page,\n     * while `derived_virtual_row_ids` contains IDs across all pages.\n     */\n    derived_virtual_row_ids: PropTypes.arrayOf(\n        PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n    ),\n    /**\n     * `derived_virtual_selected_rows` represents the indices of the\n     *  `selected_rows` from the perspective of the `derived_virtual_indices`.\n     */\n    derived_virtual_selected_rows: PropTypes.arrayOf(PropTypes.number),\n    /**\n     * `derived_virtual_selected_row_ids` represents the IDs of the\n     * `selected_rows` as they appear after filtering and sorting,\n     * across all pages.\n     */\n    derived_virtual_selected_row_ids: PropTypes.arrayOf(\n        PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n    ),\n    /**\n     * The ID of the table.\n     */\n    id: PropTypes.string,\n    /**\n     * Dash-assigned callback that gets fired when the user makes changes.\n     */\n    setProps: PropTypes.func,\n    /**\n     * Object that holds the loading state object coming from dash-renderer\n     */\n    loading_state: PropTypes.shape({\n        /**\n         * Determines if the component is loading or not\n         */\n        is_loading: PropTypes.bool,\n        /**\n         * Holds which property is loading\n         */\n        prop_name: PropTypes.string,\n        /**\n         * Holds the name of the component that is loading\n         */\n        component_name: PropTypes.string\n    }),\n    /**\n     * Used to allow user interactions in this component to be persisted when\n     * the component - or the page - is refreshed. If `persisted` is truthy and\n     * hasn't changed from its previous value, any `persisted_props` that the\n     * user has changed while using the app will keep those changes, as long as\n     * the new prop value also matches what was given originally.\n     * Used in conjunction with `persistence_type` and `persisted_props`.\n     */\n    persistence: PropTypes.oneOfType([\n        PropTypes.bool,\n        PropTypes.string,\n        PropTypes.number\n    ]),\n    /**\n     * Properties whose user interactions will persist after refreshing the\n     * component or the page.\n     */\n    persisted_props: PropTypes.arrayOf(\n        PropTypes.oneOf([\n            'columns.name',\n            'data',\n            'filter_query',\n            'hidden_columns',\n            'page_current',\n            'selected_columns',\n            'selected_rows',\n            'sort_by'\n        ])\n    ),\n    /**\n     * Where persisted user changes will be stored:\n     * memory: only kept in memory, reset on page refresh.\n     * local: window.localStorage, data is kept after the browser quit.\n     * session: window.sessionStorage, data is cleared once the browser quit.\n     */\n    persistence_type: PropTypes.oneOf(['local', 'session', 'memory'])\n};\nDataTable.persistenceTransforms = {\n    columns: {\n        name: {\n            extract: propValue => R.pluck('name', propValue),\n            apply: (storedValue, propValue) =>\n                R.zipWith(R.assoc('name'), storedValue, propValue)\n        }\n    }\n};\nDataTable.defaultProps = defaultProps;\nDataTable.propTypes = propTypes;\n", "import _arity from \"./_arity.js\";\nimport _isPlaceholder from \"./_isPlaceholder.js\";\n\n/**\n * Internal curryN function.\n *\n * @private\n * @category Function\n * @param {Number} length The arity of the curried function.\n * @param {Array} received An array of arguments received thus far.\n * @param {Function} fn The function to curry.\n * @return {Function} The curried function.\n */\nexport default function _curryN(length, received, fn) {\n  return function () {\n    var combined = [];\n    var argsIdx = 0;\n    var left = length;\n    var combinedIdx = 0;\n    var hasPlaceholder = false;\n    while (combinedIdx < received.length || argsIdx < arguments.length) {\n      var result;\n      if (combinedIdx < received.length && (!_isPlaceholder(received[combinedIdx]) || argsIdx >= arguments.length)) {\n        result = received[combinedIdx];\n      } else {\n        result = arguments[argsIdx];\n        argsIdx += 1;\n      }\n      combined[combinedIdx] = result;\n      if (!_isPlaceholder(result)) {\n        left -= 1;\n      } else {\n        hasPlaceholder = true;\n      }\n      combinedIdx += 1;\n    }\n    return !hasPlaceholder && left <= 0 ? fn.apply(this, combined) : _arity(Math.max(0, left), _curryN(length, combined, fn));\n  };\n}", "import _xfBase from \"./_xfBase.js\";\nvar XMap = /*#__PURE__*/function () {\n  function XMap(f, xf) {\n    this.xf = xf;\n    this.f = f;\n  }\n  XMap.prototype['@@transducer/init'] = _xfBase.init;\n  XMap.prototype['@@transducer/result'] = _xfBase.result;\n  XMap.prototype['@@transducer/step'] = function (result, input) {\n    return this.xf['@@transducer/step'](result, this.f(input));\n  };\n  return XMap;\n}();\nvar _xmap = function _xmap(f) {\n  return function (xf) {\n    return new XMap(f, xf);\n  };\n};\nexport default _xmap;", "export default class LazyLoader {\n    static get xlsx() {\n        return import(\n        /* webpackChunkName: \"export\", webpackMode: \"lazy\" */ 'xlsx');\n    }\n    static get hljs() {\n        return Promise.resolve(window.hljs ||\n            import(\n            /* webpackChunkName: \"highlight\", webpackMode: \"lazy\" */ '../third-party/highlight.js').then(result => result.default));\n    }\n    static table() {\n        return import(\n        /* webpackChunkName: \"table\", webpackMode: \"lazy\" */ 'dash-table/dash/fragments/DataTable');\n    }\n}\n", "import _curry2 from \"./internal/_curry2.js\";\nimport map from \"./map.js\";\nimport prop from \"./prop.js\";\n\n/**\n * Returns a new list by plucking the same named property off all objects in\n * the list supplied.\n *\n * `pluck` will work on\n * any [functor](https://github.com/fantasyland/fantasy-land#functor) in\n * addition to arrays, as it is equivalent to `R.map(R.prop(k), f)`.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig Functor f => k -> f {k: v} -> f v\n * @param {Number|String} key The key name to pluck off of each object.\n * @param {Array} f The array or functor to consider.\n * @return {Array} The list of values for the given key.\n * @see R.project, R.prop, R.props\n * @example\n *\n *      var getAges = R.pluck('age');\n *      getAges([{name: 'fred', age: 29}, {name: 'wilma', age: 27}]); //=> [29, 27]\n *\n *      <PERSON><PERSON>pluck(0, [[1, 2], [3, 4]]);               //=> [1, 3]\n *      <PERSON>.pluck('val', {a: {val: 3}, b: {val: 5}}); //=> {a: 3, b: 5}\n * @symb R.pluck('x', [{x: 1, y: 2}, {x: 3, y: 4}, {x: 5, y: 6}]) = [1, 3, 5]\n * @symb R.pluck(0, [[1, 2], [3, 4], [5, 6]]) = [1, 3, 5]\n */\nvar pluck = /*#__PURE__*/_curry2(function pluck(p, list) {\n  return map(prop(p), list);\n});\nexport default pluck;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.f = {};\n// This file contains only the entry chunk.\n// The chunk loading function for additional chunks\n__webpack_require__.e = (chunkId) => {\n\treturn Promise.all(Object.keys(__webpack_require__.f).reduce((promises, key) => {\n\t\t__webpack_require__.f[key](chunkId, promises);\n\t\treturn promises;\n\t}, []));\n};", "// This function allow to reference async chunks\n__webpack_require__.u = (chunkId) => {\n\t// return url for filenames based on template\n\treturn \"\" + {\"214\":\"async-table\",\"254\":\"async-highlight\",\"404\":\"async-export\"}[chunkId] + \".js\";\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "var scriptUrl;\nif (__webpack_require__.g.importScripts) scriptUrl = __webpack_require__.g.location + \"\";\nvar document = __webpack_require__.g.document;\nif (!scriptUrl && document) {\n\tif (document.currentScript && document.currentScript.tagName.toUpperCase() === 'SCRIPT')\n\t\tscriptUrl = document.currentScript.src;\n\tif (!scriptUrl) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tif(scripts.length) {\n\t\t\tvar i = scripts.length - 1;\n\t\t\twhile (i > -1 && (!scriptUrl || !/^http(s?):/.test(scriptUrl))) scriptUrl = scripts[i--].src;\n\t\t}\n\t}\n}\n// When supporting browsers where an automatic publicPath is not supported you must specify an output.publicPath manually via configuration\n// or pass an empty string (\"\") and set the __webpack_public_path__ variable from your code to use your own logic.\nif (!scriptUrl) throw new Error(\"Automatic publicPath is not supported in this browser\");\nscriptUrl = scriptUrl.replace(/^blob:/, \"\").replace(/#.*$/, \"\").replace(/\\?.*$/, \"\").replace(/\\/[^\\/]+$/, \"/\");\n__webpack_require__.p = scriptUrl;", "var getCurrentScript = function() {\n    var script = document.currentScript;\n    if (!script) {\n        /* Shim for IE11 and below */\n        /* Do not take into account async scripts and inline scripts */\n\n        var doc_scripts = document.getElementsByTagName('script');\n        var scripts = [];\n\n        for (var i = 0; i < doc_scripts.length; i++) {\n            scripts.push(doc_scripts[i]);\n        }\n\n        scripts = scripts.filter(function(s) { return !s.async && !s.text && !s.textContent; });\n        script = scripts.slice(-1)[0];\n    }\n\n    return script;\n};\n\nvar isLocalScript = function(script) {\n    return /\\/_dash-component-suites\\//.test(script.src);\n};\n\nObject.defineProperty(__webpack_require__, 'p', {\n    get: (function () {\n        var script = getCurrentScript();\n\n        var url = script.src.split('/').slice(0, -1).join('/') + '/';\n\n        return function() {\n            return url;\n        };\n    })()\n});\n\nif (typeof jsonpScriptSrc !== 'undefined') {\n    var __jsonpScriptSrc__ = jsonpScriptSrc;\n    jsonpScriptSrc = function(chunkId) {\n        var script = getCurrentScript();\n        var isLocal = isLocalScript(script);\n\n        var src = __jsonpScriptSrc__(chunkId);\n\n        if(!isLocal) {\n            return src;\n        }\n\n        var srcFragments = src.split('/');\n        var fileFragments = srcFragments.slice(-1)[0].split('.');\n\n        fileFragments.splice(1, 0, \"v6_0_4m1753987258\");\n        srcFragments.splice(-1, 1, fileFragments.join('.'))\n\n        return srcFragments.join('/');\n    };\n}\n", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t23: 0\n};\n\n__webpack_require__.f.j = (chunkId, promises) => {\n\t\t// JSONP chunk loading for javascript\n\t\tvar installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;\n\t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n\t\t\t// a Promise means \"currently loading\".\n\t\t\tif(installedChunkData) {\n\t\t\t\tpromises.push(installedChunkData[2]);\n\t\t\t} else {\n\t\t\t\tif(true) { // all chunks have JS\n\t\t\t\t\t// setup Promise in chunk cache\n\t\t\t\t\tvar promise = new Promise((resolve, reject) => (installedChunkData = installedChunks[chunkId] = [resolve, reject]));\n\t\t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n\t\t\t\t\t// start chunk loading\n\t\t\t\t\tvar url = __webpack_require__.p + __webpack_require__.u(chunkId);\n\t\t\t\t\t// create error before stack unwound to get useful stacktrace later\n\t\t\t\t\tvar error = new Error();\n\t\t\t\t\tvar loadingEnded = (event) => {\n\t\t\t\t\t\tif(__webpack_require__.o(installedChunks, chunkId)) {\n\t\t\t\t\t\t\tinstalledChunkData = installedChunks[chunkId];\n\t\t\t\t\t\t\tif(installedChunkData !== 0) installedChunks[chunkId] = undefined;\n\t\t\t\t\t\t\tif(installedChunkData) {\n\t\t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n\t\t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n\t\t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n\t\t\t\t\t\t\t\terror.type = errorType;\n\t\t\t\t\t\t\t\terror.request = realSrc;\n\t\t\t\t\t\t\t\tinstalledChunkData[1](error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\t__webpack_require__.l(url, loadingEnded, \"chunk-\" + chunkId, chunkId);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n};\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n// no on chunks loaded\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar chunkIds = data[0];\n\tvar moreModules = data[1];\n\tvar runtime = data[2];\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkdash_table\"] = self[\"webpackChunkdash_table\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "__webpack_require__.nc = undefined;", "import 'css.escape'; // polyfill\nimport Environment from 'core/environment';\nimport Logger from 'core/Logger';\nimport DataTable from 'dash-table/dash/DataTable';\nLogger.setDebugLevel(Environment.debugLevel);\nLogger.setLogLevel(Environment.logLevel);\nexport { DataTable };\n"], "names": ["leafPrototypes", "getProto", "inProgress", "dataWebpackPrefix", "_dispatchable", "methodNames", "transducerCreator", "fn", "arguments", "length", "obj", "_isArray", "idx", "apply", "Array", "prototype", "slice", "call", "_isTransformer", "transducer", "this", "_has", "prop", "Object", "hasOwnProperty", "module", "exports", "window", "isNil", "x", "init", "xf", "result", "once", "called", "_curry3", "f3", "a", "b", "c", "_b", "_c", "_a", "root", "g", "CSS", "escape", "cssEscape", "value", "TypeError", "codeUnit", "string", "String", "index", "firstCodeUnit", "charCodeAt", "toString", "char<PERSON>t", "factory", "_curry2", "f2", "assocPath", "path", "val", "nextObj", "_isInteger", "arr", "concat", "p", "_assoc", "DebugLevel", "LogLevel", "LogString", "TRACE", "INFO", "WARNING", "ERROR", "FATAL", "NONE", "DEBUG", "__logLevel", "__debugLevel", "logFn", "level", "currentLevel", "console", "log", "warn", "error", "Error", "prefix", "toUpperCase", "bind", "logger", "setDebugLevel", "setLogLevel", "defineProperties", "trace", "get", "configurable", "enumerable", "info", "warning", "fatal", "debug", "freeze", "map", "functor", "acc", "key", "_isPlaceholder", "hasEnumBug", "propertyIsEnumerable", "nonEnumerableProps", "hasArgsEnumBug", "contains", "list", "item", "keys", "_curry1", "nIdx", "ks", "check<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_arrayReduce", "reducer", "f1", "assoc", "Number", "isInteger", "n", "e", "t", "r", "o", "i", "l", "m", "d", "defineProperty", "Symbol", "toStringTag", "__esModule", "create", "default", "s", "u", "done", "Promise", "resolve", "then", "isReady", "lazy", "setTimeout", "regeneratorRuntime", "mark", "wrap", "prev", "next", "stop", "_dashprivate_isLazyComponentReady", "f", "writable", "dispatchEvent", "CustomEvent", "addEventListener", "removeEventListener", "isArray", "curryN", "_nth", "offset", "Cookie<PERSON>torage", "delete", "id", "domain", "undefined", "enabled", "expires", "Date", "now", "toUTCString", "document", "cookie", "toLowerCase", "split", "fragments", "trim", "find", "toLocaleLowerCase", "set", "entry", "R", "ret", "indexOf", "DASH_DEBUG", "DASH_LOG", "Environment", "searchParams", "URL", "constructor", "location", "href", "debugLevel", "logLevel", "defaultEdge", "activeEdge", "_activeEdge", "supportsCssVariables", "_supportsCssVariables", "_Environment", "_defineProperty", "Boolean", "_window$CSS", "_window$CSS$supports", "supports", "_arity", "a0", "a1", "a2", "a3", "a4", "a5", "a6", "a7", "a8", "a9", "_isString", "_map", "len", "Math", "min", "rv", "DataTable", "Component", "render", "React", "Suspense", "fallback", "RealDataTable", "props", "asyncDecorator", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "table", "propTypes", "data", "PropTypes", "columns", "isRequired", "name", "type", "presentation", "selectable", "clearable", "deletable", "editable", "hideable", "renamable", "filter_options", "case", "placeholder_text", "format", "locale", "symbol", "decimal", "group", "grouping", "numerals", "percent", "separate_4digits", "nully", "specifier", "on_change", "action", "failure", "sort_as_null", "validation", "allow_null", "allow_YY", "fixed_columns", "headers", "fixed_rows", "column_selectable", "cell_selectable", "row_selectable", "row_deletable", "active_cell", "row", "column", "row_id", "column_id", "selected_cells", "selected_rows", "selected_columns", "selected_row_ids", "start_cell", "end_cell", "data_previous", "hidden_columns", "is_focused", "merge_duplicate_headers", "data_timestamp", "include_headers_on_copy_paste", "export_columns", "export_format", "export_headers", "page_action", "page_current", "page_count", "page_size", "filter_query", "filter_action", "operator", "sort_action", "sort_mode", "sort_by", "direction", "dropdown", "options", "label", "dropdown_conditional", "if", "dropdown_data", "tooltip", "delay", "duration", "use_with", "tooltip_conditional", "row_index", "tooltip_data", "tooltip_header", "tooltip_delay", "tooltip_duration", "locale_format", "style_as_list_view", "fill_width", "markdown_options", "link_target", "html", "css", "selector", "rule", "style_table", "style_cell", "style_data", "style_filter", "style_header", "style_cell_conditional", "column_type", "style_data_conditional", "state", "column_editable", "style_filter_conditional", "style_header_conditional", "header_index", "virtualization", "derived_filter_query_structure", "derived_viewport_data", "derived_viewport_indices", "derived_viewport_row_ids", "derived_viewport_selected_columns", "derived_viewport_selected_rows", "derived_viewport_selected_row_ids", "derived_virtual_data", "derived_virtual_indices", "derived_virtual_row_ids", "derived_virtual_selected_rows", "derived_virtual_selected_row_ids", "setProps", "loading_state", "is_loading", "prop_name", "component_name", "persistence", "persisted_props", "persistence_type", "persistenceTransforms", "extract", "propValue", "storedValue", "defaultProps", "_curryN", "received", "combined", "argsIdx", "left", "combinedIdx", "hasPlaceholder", "max", "XMap", "input", "xlsx", "hljs", "pluck", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "getter", "getPrototypeOf", "mode", "ns", "def", "current", "getOwnPropertyNames", "for<PERSON>ach", "definition", "chunkId", "all", "reduce", "promises", "globalThis", "Function", "url", "push", "script", "<PERSON><PERSON><PERSON><PERSON>", "scripts", "getElementsByTagName", "getAttribute", "createElement", "charset", "timeout", "nc", "setAttribute", "src", "onScriptComplete", "event", "onerror", "onload", "clearTimeout", "doneFns", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "target", "head", "append<PERSON><PERSON><PERSON>", "scriptUrl", "importScripts", "currentScript", "tagName", "test", "replace", "getCurrentScript", "doc_scripts", "filter", "async", "text", "textContent", "join", "jsonpScriptSrc", "__jsonpScriptSrc__", "isLocal", "srcFragments", "fileFragments", "splice", "installedChunks", "j", "installedChunkData", "promise", "reject", "errorType", "realSrc", "message", "request", "webpackJsonpCallback", "parentChunkLoadingFunction", "chunkIds", "moreModules", "runtime", "some", "chunkLoadingGlobal", "self", "<PERSON><PERSON>"], "sourceRoot": ""}