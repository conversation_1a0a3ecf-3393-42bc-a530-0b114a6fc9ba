(()=>{var e,t,r,n,o={845:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(5564);function o(e,t,r){return function(){if(0===arguments.length)return r();var o=arguments[arguments.length-1];if(!(0,n.A)(o)){for(var i=0;i<e.length;){if("function"==typeof o[e[i]])return o[e[i]].apply(o,Array.prototype.slice.call(arguments,0,-1));i+=1}if(function(e){return null!=e&&"function"==typeof e["@@transducer/step"]}(o))return t.apply(null,Array.prototype.slice.call(arguments,0,-1))(o)}return r.apply(this,arguments)}}},1069:(e,t,r)=>{"use strict";function n(e,t){return Object.prototype.hasOwnProperty.call(t,e)}r.d(t,{A:()=>n})},1609:e=>{"use strict";e.exports=window.React},1647:(e,t,r)=>{"use strict";var n=(0,r(3579).A)(function(e){return null==e});t.A=n},1878:(e,t)=>{"use strict";t.A={init:function(){return this.xf["@@transducer/init"]()},result:function(e){return this.xf["@@transducer/result"](e)}}},2039:(e,t,r)=>{"use strict";var n=r(7660),o=(0,r(3579).A)(function(e){var t,r=!1;return(0,n.A)(e.length,function(){return r?t:(r=!0,t=e.apply(this,arguments))})});t.A=o},2173:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(3579),o=r(2254),i=r(2808);function a(e){return function t(r,a,s){switch(arguments.length){case 0:return t;case 1:return(0,i.A)(r)?t:(0,o.A)(function(t,n){return e(r,t,n)});case 2:return(0,i.A)(r)&&(0,i.A)(a)?t:(0,i.A)(r)?(0,o.A)(function(t,r){return e(t,a,r)}):(0,i.A)(a)?(0,o.A)(function(t,n){return e(r,t,n)}):(0,n.A)(function(t){return e(r,a,t)});default:return(0,i.A)(r)&&(0,i.A)(a)&&(0,i.A)(s)?t:(0,i.A)(r)&&(0,i.A)(a)?(0,o.A)(function(t,r){return e(t,r,s)}):(0,i.A)(r)&&(0,i.A)(s)?(0,o.A)(function(t,r){return e(t,a,r)}):(0,i.A)(a)&&(0,i.A)(s)?(0,o.A)(function(t,n){return e(r,t,n)}):(0,i.A)(r)?(0,n.A)(function(t){return e(t,a,s)}):(0,i.A)(a)?(0,n.A)(function(t){return e(r,t,s)}):(0,i.A)(s)?(0,n.A)(function(t){return e(r,a,t)}):e(r,a,s)}}}},2205:function(e,t,r){var n;n=void 0!==r.g?r.g:this,e.exports=function(e){if(e.CSS&&e.CSS.escape)return e.CSS.escape;var t=function(e){if(0==arguments.length)throw new TypeError("`CSS.escape` requires an argument.");for(var t,r=String(e),n=r.length,o=-1,i="",a=r.charCodeAt(0);++o<n;)0!=(t=r.charCodeAt(o))?i+=t>=1&&t<=31||127==t||0==o&&t>=48&&t<=57||1==o&&t>=48&&t<=57&&45==a?"\\"+t.toString(16)+" ":0==o&&1==n&&45==t||!(t>=128||45==t||95==t||t>=48&&t<=57||t>=65&&t<=90||t>=97&&t<=122)?"\\"+r.charAt(o):r.charAt(o):i+="�";return i};return e.CSS||(e.CSS={}),e.CSS.escape=t,t}(n)},2254:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(3579),o=r(2808);function i(e){return function t(r,i){switch(arguments.length){case 0:return t;case 1:return(0,o.A)(r)?t:(0,n.A)(function(t){return e(r,t)});default:return(0,o.A)(r)&&(0,o.A)(i)?t:(0,o.A)(r)?(0,n.A)(function(t){return e(t,i)}):(0,o.A)(i)?(0,n.A)(function(t){return e(r,t)}):e(r,i)}}}},2270:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(2173),o=r(1069),i=r(4279),a=r(5564),s=r(1647),u=(0,n.A)(function e(t,r,n){if(0===t.length)return r;var u=t[0];if(t.length>1){var c=!(0,s.A)(n)&&(0,o.A)(u,n)&&"object"==typeof n[u]?n[u]:(0,i.A)(t[1])?[]:{};r=e(Array.prototype.slice.call(t,1),r,c)}return function(e,t,r){if((0,i.A)(e)&&(0,a.A)(r)){var n=[].concat(r);return n[e]=t,n}var o={};for(var s in r)o[s]=r[s];return o[e]=t,o}(u,r,n)})},2314:(e,t,r)=>{"use strict";var n;r.d(t,{q$:()=>i,$b:()=>a,Ay:()=>d}),function(e){e[e.DEBUG=6]="DEBUG",e[e.NONE=7]="NONE"}(n||(n={}));var o,i=n;!function(e){e[e.TRACE=0]="TRACE",e[e.INFO=1]="INFO",e[e.WARNING=2]="WARNING",e[e.ERROR=3]="ERROR",e[e.FATAL=4]="FATAL",e[e.NONE=5]="NONE"}(o||(o={}));var a=o,s=[];s[a.TRACE]="trace",s[a.INFO]="info",s[a.WARNING]="warning",s[a.ERROR]="error",s[a.FATAL]="fatal",s[a.NONE]="none",s[i.DEBUG]="debug",s[i.NONE]="trace";var u=a.NONE,c=i.NONE;function l(e,t){if(e<t)return()=>{};var r;switch(e){case a.TRACE:case a.INFO:r=window.console.log;break;case i.DEBUG:case a.WARNING:r=window.console.warn;break;case a.ERROR:case a.FATAL:r=window.console.error;break;default:throw new Error("Unknown log ".concat(e))}var n="".concat("","[").concat(s[e].toUpperCase(),"]");return r.bind(window.console,n)}var f={setDebugLevel(e){c=e},setLogLevel(e){u=e}};Object.defineProperties(f,{trace:{get:()=>l(a.TRACE,u),configurable:!1,enumerable:!1},info:{get:()=>l(a.INFO,u),configurable:!1,enumerable:!1},warning:{get:()=>l(a.WARNING,u),configurable:!1,enumerable:!1},error:{get:()=>l(a.ERROR,u),configurable:!1,enumerable:!1},fatal:{get:()=>l(a.FATAL,u),configurable:!1,enumerable:!1},debug:{get:()=>l(i.DEBUG,c),configurable:!1,enumerable:!1}}),Object.freeze(f);var d=f},2598:(e,t,r)=>{"use strict";var n=r(3430),o=r(2254),i=r(845),a=r(8267),s=r(9607),u=r(5845),c=r(2959),l=(0,o.A)((0,i.A)(["fantasy-land/map","map"],s.A,function(e,t){switch(Object.prototype.toString.call(t)){case"[object Function]":return(0,u.A)(t.length,function(){return e.call(this,t.apply(this,arguments))});case"[object Object]":return(0,n.A)(function(r,n){return r[n]=e(t[n]),r},{},(0,c.A)(t));default:return(0,a.A)(e,t)}}));t.A=l},2808:(e,t,r)=>{"use strict";function n(e){return null!=e&&"object"==typeof e&&!0===e["@@functional/placeholder"]}r.d(t,{A:()=>n})},2959:(e,t,r)=>{"use strict";r.d(t,{A:()=>f});var n=r(3579),o=r(1069),i=Object.prototype.toString,a=function(){return"[object Arguments]"===i.call(arguments)?function(e){return"[object Arguments]"===i.call(e)}:function(e){return(0,o.A)("callee",e)}}(),s=!{toString:null}.propertyIsEnumerable("toString"),u=["constructor","valueOf","isPrototypeOf","toString","propertyIsEnumerable","hasOwnProperty","toLocaleString"],c=function(){return arguments.propertyIsEnumerable("length")}(),l=function(e,t){for(var r=0;r<e.length;){if(e[r]===t)return!0;r+=1}return!1},f="function"!=typeof Object.keys||c?(0,n.A)(function(e){if(Object(e)!==e)return[];var t,r,n=[],i=c&&a(e);for(t in e)!(0,o.A)(t,e)||i&&"length"===t||(n[n.length]=t);if(s)for(r=u.length-1;r>=0;)t=u[r],(0,o.A)(t,e)&&!l(n,t)&&(n[n.length]=t),r-=1;return n}):(0,n.A)(function(e){return Object(e)!==e?[]:Object.keys(e)})},3430:(e,t,r)=>{"use strict";function n(e,t,r){for(var n=0,o=r.length;n<o;)t=e(t,r[n]),n+=1;return t}r.d(t,{A:()=>n})},3579:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(2808);function o(e){return function t(r){return 0===arguments.length||(0,n.A)(r)?t:e.apply(this,arguments)}}},3847:(e,t,r)=>{"use strict";var n=r(2173),o=r(2270),i=(0,n.A)(function(e,t,r){return(0,o.A)([e],t,r)});t.A=i},4279:(e,t)=>{"use strict";t.A=Number.isInteger||function(e){return(0|e)===e}},4296:(e,t,r)=>{var n;window,e.exports=(n=r(1609),function(e){var t={};function r(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)r.d(n,o,function(t){return e[t]}.bind(null,o));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=1)}([function(e,t){e.exports=n},function(e,t,r){"use strict";r.r(t),r.d(t,"asyncDecorator",function(){return a}),r.d(t,"inheritAsyncDecorator",function(){return s}),r.d(t,"isReady",function(){return u}),r.d(t,"History",function(){return f});var n=r(0);function o(e,t,r,n,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,o)}function i(e){return function(){var t=this,r=arguments;return new Promise(function(n,i){var a=e.apply(t,r);function s(e){o(a,n,i,s,u,"next",e)}function u(e){o(a,n,i,s,u,"throw",e)}s(void 0)})}}var a=function(e,t){var r,o={isReady:new Promise(function(e){r=e}),get:Object(n.lazy)(function(){return Promise.resolve(t()).then(function(e){return setTimeout(i(regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r(!0);case 2:o.isReady=!0;case 3:case"end":return e.stop()}},e)})),0),e})})};return Object.defineProperty(e,"_dashprivate_isLazyComponentReady",{get:function(){return o.isReady}}),o.get},s=function(e,t){Object.defineProperty(e,"_dashprivate_isLazyComponentReady",{get:function(){return u(t)}})},u=function(e){return e&&e._dashprivate_isLazyComponentReady};function c(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var l="_dashprivate_historychange",f=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,r;return t=e,r=[{key:"dispatchChangeEvent",value:function(){window.dispatchEvent(new CustomEvent(l))}},{key:"onChange",value:function(e){return window.addEventListener(l,e),function(){return window.removeEventListener(l,e)}}}],null&&c(t.prototype,null),r&&c(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}()}]))},5564:(e,t)=>{"use strict";t.A=Array.isArray||function(e){return null!=e&&e.length>=0&&"[object Array]"===Object.prototype.toString.call(e)}},5795:e=>{"use strict";e.exports=window.ReactDOM},5845:(e,t,r)=>{"use strict";var n=r(7660),o=r(3579),i=r(2254),a=r(9034),s=(0,i.A)(function(e,t){return 1===e?(0,o.A)(t):(0,n.A)(e,(0,a.A)(e,[],t))});t.A=s},5987:(e,t,r)=>{"use strict";var n=r(2254),o=r(4279),i=r(6359),a=(0,n.A)(function(e,t){if(null!=t)return(0,o.A)(e)?(0,i.A)(e,t):t[e]});t.A=a},6120:e=>{"use strict";e.exports=window.PropTypes},6359:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(8228);function o(e,t){var r=e<0?t.length+e:e;return(0,n.A)(t)?t.charAt(r):t[r]}},7246:(e,t,r)=>{"use strict";r.d(t,{A:()=>b});var n,o,i,a=r(2039);class s{static delete(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"/";if(s.enabled()){var n=new Date(Date.now()-864e5).toUTCString();document.cookie="".concat(e,"=;expires=").concat(n,";domain=").concat(t,";path=").concat(r)}}static get(e){if(e.length&&s.enabled())return e=e.toLowerCase(),(document.cookie.split(";").map(e=>{var t=e.split("=");return{id:t[0].trim(),value:t[1]}}).find(t=>e===t.id.toLocaleLowerCase())||{}).value}static set(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"/";if(s.enabled()){var o=new Date(Date.now()+63072e7).toUTCString(),i="".concat(e,"=").concat(t,";expires=").concat(o,";domain=").concat(r,";path=").concat(n);s.get(e)&&s.delete(e,r,n),document.cookie=i}}}n=s,o="enabled",i=a.A(()=>{try{document.cookie="cookietest=1";var e=-1!==document.cookie.indexOf("cookietest=");return document.cookie="cookietest=1; expires=Thu, 01-Jan-1970 00:00:01 GMT",e}catch(e){return!1}}),(o=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i;var u,c,l,f=r(2314);function d(e,t,r){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var p="dash_debug",y="dash_log";class b{static get searchParams(){return"undefined"!=typeof URL&&URL.prototype&&URL.prototype.constructor&&new URL(window.location.href).searchParams||{get:()=>null}}static get debugLevel(){var e=this.searchParams.get(p)||s.get(p);return e&&f.q$[e]||f.q$.NONE}static get logLevel(){var e=this.searchParams.get(y)||s.get(y);return e&&f.$b[e]||f.$b.ERROR}static get defaultEdge(){return"1px solid #d3d3d3"}static get activeEdge(){return b._activeEdge}static get supportsCssVariables(){return b._supportsCssVariables}}u=b,d(b,"_supportsCssVariables",Boolean(null===(c=window.CSS)||void 0===c||null===(l=c.supports)||void 0===l?void 0:l.call(c,".some-selector","var(--some-var)"))),d(b,"_activeEdge",u._supportsCssVariables?"1px solid var(--accent)":"1px solid hotpink")},7660:(e,t,r)=>{"use strict";function n(e,t){switch(e){case 0:return function(){return t.apply(this,arguments)};case 1:return function(e){return t.apply(this,arguments)};case 2:return function(e,r){return t.apply(this,arguments)};case 3:return function(e,r,n){return t.apply(this,arguments)};case 4:return function(e,r,n,o){return t.apply(this,arguments)};case 5:return function(e,r,n,o,i){return t.apply(this,arguments)};case 6:return function(e,r,n,o,i,a){return t.apply(this,arguments)};case 7:return function(e,r,n,o,i,a,s){return t.apply(this,arguments)};case 8:return function(e,r,n,o,i,a,s,u){return t.apply(this,arguments)};case 9:return function(e,r,n,o,i,a,s,u,c){return t.apply(this,arguments)};case 10:return function(e,r,n,o,i,a,s,u,c,l){return t.apply(this,arguments)};default:throw new Error("First argument to _arity must be a non-negative integer no greater than ten")}}r.d(t,{A:()=>n})},8228:(e,t,r)=>{"use strict";function n(e){return"[object String]"===Object.prototype.toString.call(e)}r.d(t,{A:()=>n})},8267:(e,t,r)=>{"use strict";function n(e,t){for(var r=0,n=t.length,o=Array(n);r<n;)o[r]=e(t[r]),r+=1;return o}r.d(t,{A:()=>n})},8935:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>d,tu:()=>y});var n=r(9849),o=(0,r(2173).A)(function(e,t,r){for(var n=Math.min(t.length,r.length),o=Array(n),i=0;i<n;)o[i]=e(t[i],r[i]),i+=1;return o}),i=r(3847),a=r(1609),s=r.n(a),u=r(6120),c=r.n(u),l=r(4296),f=r(9734);class d extends a.Component{render(){return s().createElement(a.Suspense,{fallback:null},s().createElement(p,this.props))}}var p=(0,l.asyncDecorator)(d,f.A.table),y={data:c().arrayOf(c().objectOf(c().oneOfType([c().string,c().number,c().bool]))),columns:c().arrayOf(c().exact({id:c().string.isRequired,name:c().oneOfType([c().string,c().arrayOf(c().string)]).isRequired,type:c().oneOf(["any","numeric","text","datetime"]),presentation:c().oneOf(["input","dropdown","markdown"]),selectable:c().oneOfType([c().oneOf(["first","last"]),c().bool,c().arrayOf(c().bool)]),clearable:c().oneOfType([c().oneOf(["first","last"]),c().bool,c().arrayOf(c().bool)]),deletable:c().oneOfType([c().oneOf(["first","last"]),c().bool,c().arrayOf(c().bool)]),editable:c().bool,hideable:c().oneOfType([c().oneOf(["first","last"]),c().bool,c().arrayOf(c().bool)]),renamable:c().oneOfType([c().oneOf(["first","last"]),c().bool,c().arrayOf(c().bool)]),filter_options:c().shape({case:c().oneOf(["sensitive","insensitive"]),placeholder_text:c().string}),format:c().exact({locale:c().exact({symbol:c().arrayOf(c().string),decimal:c().string,group:c().string,grouping:c().arrayOf(c().number),numerals:c().arrayOf(c().string),percent:c().string,separate_4digits:c().bool}),nully:c().any,prefix:c().number,specifier:c().string}),on_change:c().exact({action:c().oneOf(["coerce","none","validate"]),failure:c().oneOf(["accept","default","reject"])}),sort_as_null:c().arrayOf(c().oneOfType([c().string,c().number,c().bool])),validation:c().exact({allow_null:c().bool,default:c().any,allow_YY:c().bool})})),editable:c().bool,fixed_columns:c().exact({data:c().number,headers:c().bool}),fixed_rows:c().exact({data:c().number,headers:c().bool}),column_selectable:c().oneOf(["single","multi",!1]),cell_selectable:c().bool,row_selectable:c().oneOf(["single","multi",!1]),row_deletable:c().bool,active_cell:c().exact({row:c().number,column:c().number,row_id:c().oneOfType([c().string,c().number]),column_id:c().string}),selected_cells:c().arrayOf(c().exact({row:c().number,column:c().number,row_id:c().oneOfType([c().string,c().number]),column_id:c().string})),selected_rows:c().arrayOf(c().number),selected_columns:c().arrayOf(c().string),selected_row_ids:c().arrayOf(c().oneOfType([c().string,c().number])),start_cell:c().exact({row:c().number,column:c().number,row_id:c().oneOfType([c().string,c().number]),column_id:c().string}),end_cell:c().exact({row:c().number,column:c().number,row_id:c().oneOfType([c().string,c().number]),column_id:c().string}),data_previous:c().arrayOf(c().object),hidden_columns:c().arrayOf(c().string),is_focused:c().bool,merge_duplicate_headers:c().bool,data_timestamp:c().number,include_headers_on_copy_paste:c().bool,export_columns:c().oneOf(["all","visible"]),export_format:c().oneOf(["csv","xlsx","none"]),export_headers:c().oneOf(["none","ids","names","display"]),page_action:c().oneOf(["custom","native","none"]),page_current:c().number,page_count:c().number,page_size:c().number,filter_query:c().string,filter_action:c().oneOfType([c().oneOf(["custom","native","none"]),c().shape({type:c().oneOf(["custom","native"]).isRequired,operator:c().oneOf(["and","or"])})]),filter_options:c().shape({case:c().oneOf(["sensitive","insensitive"]),placeholder_text:c().string}),sort_action:c().oneOf(["custom","native","none"]),sort_mode:c().oneOf(["single","multi"]),sort_by:c().arrayOf(c().exact({column_id:c().string.isRequired,direction:c().oneOf(["asc","desc"]).isRequired})),sort_as_null:c().arrayOf(c().oneOfType([c().string,c().number,c().bool])),dropdown:c().objectOf(c().exact({clearable:c().bool,options:c().arrayOf(c().exact({label:c().string.isRequired,value:c().oneOfType([c().number,c().string,c().bool]).isRequired})).isRequired})),dropdown_conditional:c().arrayOf(c().exact({clearable:c().bool,if:c().exact({column_id:c().string,filter_query:c().string}),options:c().arrayOf(c().exact({label:c().string.isRequired,value:c().oneOfType([c().number,c().string,c().bool]).isRequired})).isRequired})),dropdown_data:c().arrayOf(c().objectOf(c().exact({clearable:c().bool,options:c().arrayOf(c().exact({label:c().string.isRequired,value:c().oneOfType([c().number,c().string,c().bool]).isRequired})).isRequired}))),tooltip:c().objectOf(c().oneOfType([c().string,c().exact({delay:c().number,duration:c().number,type:c().oneOf(["text","markdown"]),use_with:c().oneOf(["both","data","header"]),value:c().string.isRequired})])),tooltip_conditional:c().arrayOf(c().exact({delay:c().number,duration:c().number,if:c().exact({column_id:c().string,filter_query:c().string,row_index:c().oneOfType([c().number,c().oneOf(["odd","even"])])}).isRequired,type:c().oneOf(["text","markdown"]),value:c().string.isRequired})),tooltip_data:c().arrayOf(c().objectOf(c().oneOfType([c().string,c().exact({delay:c().number,duration:c().number,type:c().oneOf(["text","markdown"]),value:c().string.isRequired})]))),tooltip_header:c().objectOf(c().oneOfType([c().string,c().exact({delay:c().number,duration:c().number,type:c().oneOf(["text","markdown"]),value:c().string.isRequired}),c().arrayOf(c().oneOfType([c().oneOf([null]),c().string,c().exact({delay:c().number,duration:c().number,type:c().oneOf(["text","markdown"]),value:c().string.isRequired})]))])),tooltip_delay:c().number,tooltip_duration:c().number,locale_format:c().exact({symbol:c().arrayOf(c().string),decimal:c().string,group:c().string,grouping:c().arrayOf(c().number),numerals:c().arrayOf(c().string),percent:c().string,separate_4digits:c().bool}),style_as_list_view:c().bool,fill_width:c().bool,markdown_options:c().exact({link_target:c().oneOfType([c().string,c().oneOf(["_blank","_parent","_self","_top"])]),html:c().bool}),css:c().arrayOf(c().exact({selector:c().string.isRequired,rule:c().string.isRequired})),style_table:c().object,style_cell:c().object,style_data:c().object,style_filter:c().object,style_header:c().object,style_cell_conditional:c().arrayOf(c().shape({if:c().exact({column_id:c().oneOfType([c().string,c().arrayOf(c().string)]),column_type:c().oneOf(["any","numeric","text","datetime"])})})),style_data_conditional:c().arrayOf(c().shape({if:c().exact({column_id:c().oneOfType([c().string,c().arrayOf(c().string)]),column_type:c().oneOf(["any","numeric","text","datetime"]),filter_query:c().string,state:c().oneOf(["active","selected"]),row_index:c().oneOfType([c().number,c().oneOf(["odd","even"]),c().arrayOf(c().number)]),column_editable:c().bool})})),style_filter_conditional:c().arrayOf(c().shape({if:c().exact({column_id:c().oneOfType([c().string,c().arrayOf(c().string)]),column_type:c().oneOf(["any","numeric","text","datetime"]),column_editable:c().bool})})),style_header_conditional:c().arrayOf(c().shape({if:c().exact({column_id:c().oneOfType([c().string,c().arrayOf(c().string)]),column_type:c().oneOf(["any","numeric","text","datetime"]),header_index:c().oneOfType([c().number,c().arrayOf(c().number),c().oneOf(["odd","even"])]),column_editable:c().bool})})),virtualization:c().bool,derived_filter_query_structure:c().object,derived_viewport_data:c().arrayOf(c().object),derived_viewport_indices:c().arrayOf(c().number),derived_viewport_row_ids:c().arrayOf(c().oneOfType([c().string,c().number])),derived_viewport_selected_columns:c().arrayOf(c().string),derived_viewport_selected_rows:c().arrayOf(c().number),derived_viewport_selected_row_ids:c().arrayOf(c().oneOfType([c().string,c().number])),derived_virtual_data:c().arrayOf(c().object),derived_virtual_indices:c().arrayOf(c().number),derived_virtual_row_ids:c().arrayOf(c().oneOfType([c().string,c().number])),derived_virtual_selected_rows:c().arrayOf(c().number),derived_virtual_selected_row_ids:c().arrayOf(c().oneOfType([c().string,c().number])),id:c().string,setProps:c().func,loading_state:c().shape({is_loading:c().bool,prop_name:c().string,component_name:c().string}),persistence:c().oneOfType([c().bool,c().string,c().number]),persisted_props:c().arrayOf(c().oneOf(["columns.name","data","filter_query","hidden_columns","page_current","selected_columns","selected_rows","sort_by"])),persistence_type:c().oneOf(["local","session","memory"])};d.persistenceTransforms={columns:{name:{extract:e=>n.A("name",e),apply:(e,t)=>o(i.A("name"),e,t)}}},d.defaultProps={page_action:"native",page_current:0,page_size:250,css:[],filter_query:"",filter_action:"none",sort_as_null:[],sort_action:"none",sort_mode:"single",sort_by:[],style_as_list_view:!1,derived_viewport_data:[],derived_viewport_indices:[],derived_viewport_row_ids:[],derived_viewport_selected_rows:[],derived_viewport_selected_row_ids:[],derived_virtual_data:[],derived_virtual_indices:[],derived_virtual_row_ids:[],derived_virtual_selected_rows:[],derived_virtual_selected_row_ids:[],dropdown:{},dropdown_conditional:[],dropdown_data:[],fill_width:!0,filter_options:{},fixed_columns:{headers:!1,data:0},fixed_rows:{headers:!1,data:0},markdown_options:{link_target:"_blank",html:!1},tooltip:{},tooltip_conditional:[],tooltip_data:[],tooltip_header:{},tooltip_delay:350,tooltip_duration:2e3,column_selectable:!1,editable:!1,export_columns:"visible",export_format:"none",include_headers_on_copy_paste:!1,selected_cells:[],selected_columns:[],selected_rows:[],selected_row_ids:[],cell_selectable:!0,row_selectable:!1,style_table:{},style_cell_conditional:[],style_data_conditional:[],style_filter_conditional:[],style_header_conditional:[],virtualization:!1,persisted_props:["columns.name","filter_query","hidden_columns","page_current","selected_columns","selected_rows","sort_by"],persistence_type:"local"},d.propTypes=y},9034:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(7660),o=r(2808);function i(e,t,r){return function(){for(var a=[],s=0,u=e,c=0,l=!1;c<t.length||s<arguments.length;){var f;c<t.length&&(!(0,o.A)(t[c])||s>=arguments.length)?f=t[c]:(f=arguments[s],s+=1),a[c]=f,(0,o.A)(f)?l=!0:u-=1,c+=1}return!l&&u<=0?r.apply(this,a):(0,n.A)(Math.max(0,u),i(e,a,r))}}},9607:(e,t,r)=>{"use strict";var n=r(1878),o=function(){function e(e,t){this.xf=t,this.f=e}return e.prototype["@@transducer/init"]=n.A.init,e.prototype["@@transducer/result"]=n.A.result,e.prototype["@@transducer/step"]=function(e,t){return this.xf["@@transducer/step"](e,this.f(t))},e}();t.A=function(e){return function(t){return new o(e,t)}}},9734:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});class n{static get xlsx(){return r.e(404).then(r.t.bind(r,7063,23))}static get hljs(){return Promise.resolve(window.hljs||r.e(254).then(r.bind(r,3948)).then(e=>e.default))}static table(){return Promise.all([r.e(254),r.e(214)]).then(r.bind(r,3838))}}},9849:(e,t,r)=>{"use strict";var n=r(2254),o=r(2598),i=r(5987),a=(0,n.A)(function(e,t){return(0,o.A)((0,i.A)(e),t)});t.A=a}},i={};function a(e){var t=i[e];if(void 0!==t)return t.exports;var r=i[e]={id:e,exports:{}};return o[e].call(r.exports,r,r.exports,a),r.exports}a.m=o,a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,a.t=function(r,n){if(1&n&&(r=this(r)),8&n)return r;if("object"==typeof r&&r){if(4&n&&r.__esModule)return r;if(16&n&&"function"==typeof r.then)return r}var o=Object.create(null);a.r(o);var i={};e=e||[null,t({}),t([]),t(t)];for(var s=2&n&&r;("object"==typeof s||"function"==typeof s)&&!~e.indexOf(s);s=t(s))Object.getOwnPropertyNames(s).forEach(e=>i[e]=()=>r[e]);return i.default=()=>r,a.d(o,i),o},a.d=(e,t)=>{for(var r in t)a.o(t,r)&&!a.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},a.f={},a.e=e=>Promise.all(Object.keys(a.f).reduce((t,r)=>(a.f[r](e,t),t),[])),a.u=e=>({214:"async-table",254:"async-highlight",404:"async-export"}[e]+".js"),a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r={},n="dash_table:",a.l=(e,t,o,i)=>{if(r[e])r[e].push(t);else{var s,u;if(void 0!==o)for(var c=document.getElementsByTagName("script"),l=0;l<c.length;l++){var f=c[l];if(f.getAttribute("src")==e||f.getAttribute("data-webpack")==n+o){s=f;break}}s||(u=!0,(s=document.createElement("script")).charset="utf-8",s.timeout=120,a.nc&&s.setAttribute("nonce",a.nc),s.setAttribute("data-webpack",n+o),s.src=e),r[e]=[t];var d=(t,n)=>{s.onerror=s.onload=null,clearTimeout(p);var o=r[e];if(delete r[e],s.parentNode&&s.parentNode.removeChild(s),o&&o.forEach(e=>e(n)),t)return t(n)},p=setTimeout(d.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=d.bind(null,s.onerror),s.onload=d.bind(null,s.onload),u&&document.head.appendChild(s)}},a.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;a.g.importScripts&&(e=a.g.location+"");var t=a.g.document;if(!e&&t&&(t.currentScript&&"SCRIPT"===t.currentScript.tagName.toUpperCase()&&(e=t.currentScript.src),!e)){var r=t.getElementsByTagName("script");if(r.length)for(var n=r.length-1;n>-1&&(!e||!/^http(s?):/.test(e));)e=r[n--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/^blob:/,"").replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),a.p=e})();var s,u=function(){var e=document.currentScript;if(!e){for(var t=document.getElementsByTagName("script"),r=[],n=0;n<t.length;n++)r.push(t[n]);e=(r=r.filter(function(e){return!e.async&&!e.text&&!e.textContent})).slice(-1)[0]}return e};if(Object.defineProperty(a,"p",{get:(s=u().src.split("/").slice(0,-1).join("/")+"/",function(){return s})}),"undefined"!=typeof jsonpScriptSrc){var c=jsonpScriptSrc;jsonpScriptSrc=function(e){var t,r=(t=u(),/\/_dash-component-suites\//.test(t.src)),n=c(e);if(!r)return n;var o=n.split("/"),i=o.slice(-1)[0].split(".");return i.splice(1,0,"v6_0_4m1753987258"),o.splice(-1,1,i.join(".")),o.join("/")}}(()=>{var e={23:0};a.f.j=(t,r)=>{var n=a.o(e,t)?e[t]:void 0;if(0!==n)if(n)r.push(n[2]);else{var o=new Promise((r,o)=>n=e[t]=[r,o]);r.push(n[2]=o);var i=a.p+a.u(t),s=new Error;a.l(i,r=>{if(a.o(e,t)&&(0!==(n=e[t])&&(e[t]=void 0),n)){var o=r&&("load"===r.type?"missing":r.type),i=r&&r.target&&r.target.src;s.message="Loading chunk "+t+" failed.\n("+o+": "+i+")",s.name="ChunkLoadError",s.type=o,s.request=i,n[1](s)}},"chunk-"+t,t)}};var t=(t,r)=>{var n,o,i=r[0],s=r[1],u=r[2],c=0;if(i.some(t=>0!==e[t])){for(n in s)a.o(s,n)&&(a.m[n]=s[n]);u&&u(a)}for(t&&t(r);c<i.length;c++)o=i[c],a.o(e,o)&&e[o]&&e[o][0](),e[o]=0},r=self.webpackChunkdash_table=self.webpackChunkdash_table||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),a.nc=void 0;var l={};(()=>{"use strict";a.r(l),a.d(l,{DataTable:()=>r.Ay}),a(2205);var e=a(7246),t=a(2314),r=a(8935);t.Ay.setDebugLevel(e.A.debugLevel),t.Ay.setLogLevel(e.A.logLevel)})(),window.dash_table=l})();
//# sourceMappingURL=bundle.js.map