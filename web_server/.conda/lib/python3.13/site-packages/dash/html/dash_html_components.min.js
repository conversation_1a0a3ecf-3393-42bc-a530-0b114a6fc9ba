!function(){"use strict";var n={n:function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},d:function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},o:function(n,e){return Object.prototype.hasOwnProperty.call(n,e)},r:function(n){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})}},e={};n.r(e),n.d(e,{A:function(){return p},Abbr:function(){return m},Acronym:function(){return h},Address:function(){return j},Area:function(){return T},Article:function(){return N},Aside:function(){return A},Audio:function(){return R},B:function(){return z},Base:function(){return X},Basefont:function(){return $},Bdi:function(){return rn},Bdo:function(){return ln},Big:function(){return gn},Blink:function(){return bn},Blockquote:function(){return On},Br:function(){return Pn},Button:function(){return xn},Canvas:function(){return Sn},Caption:function(){return Kn},Center:function(){return Bn},Cite:function(){return Vn},Code:function(){return Wn},Col:function(){return Zn},Colgroup:function(){return te},Content:function(){return ce},Data:function(){return de},Datalist:function(){return _e},Dd:function(){return ke},Del:function(){return ve},Details:function(){return Ee},Dfn:function(){return Ce},Dialog:function(){return Le},Div:function(){return Me},Dl:function(){return Ue},Dt:function(){return Qe},Em:function(){return Ge},Embed:function(){return nt},Fieldset:function(){return it},Figcaption:function(){return lt},Figure:function(){return gt},Font:function(){return bt},Footer:function(){return Ot},Form:function(){return Pt},Frame:function(){return xt},Frameset:function(){return St},H1:function(){return Kt},H2:function(){return Bt},H3:function(){return Vt},H4:function(){return Wt},H5:function(){return Zt},H6:function(){return tr},Header:function(){return cr},Hgroup:function(){return dr},Hr:function(){return _r},I:function(){return kr},Iframe:function(){return vr},Img:function(){return Er},Ins:function(){return Cr},Kbd:function(){return Lr},Keygen:function(){return Mr},Label:function(){return Ur},Legend:function(){return Qr},Li:function(){return Gr},Link:function(){return ni},Main:function(){return ii},MapEl:function(){return li},Mark:function(){return gi},Marquee:function(){return bi},Meta:function(){return Oi},Meter:function(){return Pi},Nav:function(){return xi},Nobr:function(){return Si},Noscript:function(){return Ki},ObjectEl:function(){return Bi},Ol:function(){return Vi},Optgroup:function(){return Wi},Option:function(){return Zi},Output:function(){return ts},P:function(){return cs},Param:function(){return ds},Picture:function(){return _s},Plaintext:function(){return ks},Pre:function(){return vs},Progress:function(){return Es},Q:function(){return Cs},Rb:function(){return Ls},Rp:function(){return Ms},Rt:function(){return Us},Rtc:function(){return Qs},Ruby:function(){return Gs},S:function(){return nc},Samp:function(){return ic},Script:function(){return lc},Section:function(){return gc},Select:function(){return bc},Shadow:function(){return Oc},Slot:function(){return Pc},Small:function(){return xc},Source:function(){return Sc},Spacer:function(){return Kc},Span:function(){return Bc},Strike:function(){return Vc},Strong:function(){return Wc},Sub:function(){return Zc},Summary:function(){return ta},Sup:function(){return ca},Table:function(){return da},Tbody:function(){return _a},Td:function(){return ka},Template:function(){return va},Textarea:function(){return Ea},Tfoot:function(){return Ca},Th:function(){return La},Thead:function(){return Ma},Time:function(){return Ua},Title:function(){return Qa},Tr:function(){return Ga},Track:function(){return nl},U:function(){return il},Ul:function(){return ll},Var:function(){return gl},Video:function(){return bl},Wbr:function(){return Ol},Xmp:function(){return Pl}});var t=window.React,r=n.n(t),i=window.PropTypes,s=n.n(i);function c(n){return null!=n&&"object"==typeof n&&!0===n["@@functional/placeholder"]}function a(n){return function e(t){return 0===arguments.length||c(t)?e:n.apply(this,arguments)}}function l(n){return function e(t,r){switch(arguments.length){case 0:return e;case 1:return c(t)?e:a((function(e){return n(t,e)}));default:return c(t)&&c(r)?e:c(t)?a((function(e){return n(e,r)})):c(r)?a((function(e){return n(t,e)})):n(t,r)}}}var o=l((function(n,e){for(var t={},r={},i=0,s=n.length;i<s;)r[n[i]]=1,i+=1;for(var c in e)r.hasOwnProperty(c)||(t[c]=e[c]);return t})),d=["n_clicks","n_clicks_timestamp"];function u(){return u=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},u.apply(null,arguments)}var g=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,d)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=window.dash_clientside.clean_url,a=r().useMemo((function(){return i.href&&c(i.href)}),[i.href]);a&&(s.href=a),r().useEffect((function(){a&&a!==i.href&&i.setProps({_dash_error:new Error("Dangerous link detected: ".concat(i.href))})}),[i.href,a]);var l=i.disable_n_clicks||!i.id;return r().createElement("a",u({},!l&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};g.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,download:s().string,href:s().string,hrefLang:s().string,media:s().string,referrerPolicy:s().string,rel:s().string,shape:s().string,target:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var p=g,_=["n_clicks","n_clicks_timestamp"];function f(){return f=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},f.apply(null,arguments)}var b=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,_)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("abbr",f({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};b.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var m=b,k=["n_clicks","n_clicks_timestamp"];function y(){return y=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},y.apply(null,arguments)}var O=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,k)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("acronym",y({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};O.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var h=O,v=["n_clicks","n_clicks_timestamp"];function w(){return w=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},w.apply(null,arguments)}var P=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,v)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("address",w({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};P.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var j=P,E=["n_clicks","n_clicks_timestamp"];function D(){return D=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},D.apply(null,arguments)}var x=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,E)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("area",D({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};x.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,alt:s().string,coords:s().string,download:s().string,href:s().string,media:s().string,referrerPolicy:s().string,rel:s().string,shape:s().string,target:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var T=x,C=["n_clicks","n_clicks_timestamp"];function I(){return I=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},I.apply(null,arguments)}var S=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,C)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("article",I({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};S.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var N=S,L=["n_clicks","n_clicks_timestamp"];function H(){return H=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},H.apply(null,arguments)}var K=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,L)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("aside",H({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};K.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var A=K,M=["n_clicks","n_clicks_timestamp"];function F(){return F=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},F.apply(null,arguments)}var B=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,M)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("audio",F({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};B.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,autoPlay:s().oneOfType([s().oneOf(["autoPlay","autoplay","AUTOPLAY"]),s().bool]),controls:s().oneOfType([s().oneOf(["controls","CONTROLS"]),s().bool]),crossOrigin:s().string,loop:s().oneOfType([s().oneOf(["loop","LOOP"]),s().bool]),muted:s().oneOfType([s().oneOf(["muted","MUTED"]),s().bool]),preload:s().string,src:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var R=B,U=["n_clicks","n_clicks_timestamp"];function q(){return q=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},q.apply(null,arguments)}var V=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,U)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("b",q({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};V.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var z=V,Q=["n_clicks","n_clicks_timestamp"];function Y(){return Y=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Y.apply(null,arguments)}var W=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Q)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("base",Y({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};W.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,href:s().string,target:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var X=W,G=["n_clicks","n_clicks_timestamp"];function J(){return J=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},J.apply(null,arguments)}var Z=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,G)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("basefont",J({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Z.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var $=Z,nn=["n_clicks","n_clicks_timestamp"];function en(){return en=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},en.apply(null,arguments)}var tn=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,nn)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("bdi",en({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};tn.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var rn=tn,sn=["n_clicks","n_clicks_timestamp"];function cn(){return cn=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},cn.apply(null,arguments)}var an=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,sn)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("bdo",cn({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};an.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var ln=an,on=["n_clicks","n_clicks_timestamp"];function dn(){return dn=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},dn.apply(null,arguments)}var un=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,on)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("big",dn({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};un.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var gn=un,pn=["n_clicks","n_clicks_timestamp"];function _n(){return _n=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},_n.apply(null,arguments)}var fn=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,pn)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("blink",_n({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};fn.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var bn=fn,mn=["n_clicks","n_clicks_timestamp"];function kn(){return kn=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},kn.apply(null,arguments)}var yn=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,mn)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("blockquote",kn({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};yn.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,cite:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var On=yn,hn=["n_clicks","n_clicks_timestamp"];function vn(){return vn=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},vn.apply(null,arguments)}var wn=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,hn)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("br",vn({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};wn.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Pn=wn,jn=["n_clicks","n_clicks_timestamp"];function En(){return En=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},En.apply(null,arguments)}var Dn=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,jn)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=window.dash_clientside.clean_url,a=r().useMemo((function(){return i.formAction&&c(i.formAction)}),[i.formAction]);a&&(s.formAction=a),r().useEffect((function(){a&&a!==i.formAction&&i.setProps({_dash_error:new Error("Dangerous link detected: ".concat(i.formAction))})}),[i.formAction,a]);var l=i.disable_n_clicks||!i.id;return r().createElement("button",En({},!l&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Dn.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,autoFocus:s().oneOfType([s().oneOf(["autoFocus","autofocus","AUTOFOCUS"]),s().bool]),disabled:s().oneOfType([s().oneOf(["disabled","DISABLED"]),s().bool]),form:s().string,formAction:s().string,formEncType:s().string,formMethod:s().string,formNoValidate:s().oneOfType([s().oneOf(["formNoValidate","formnovalidate","FORMNOVALIDATE"]),s().bool]),formTarget:s().string,name:s().string,type:s().string,value:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var xn=Dn,Tn=["n_clicks","n_clicks_timestamp"];function Cn(){return Cn=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Cn.apply(null,arguments)}var In=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Tn)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("canvas",Cn({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};In.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,height:s().oneOfType([s().string,s().number]),width:s().oneOfType([s().string,s().number]),accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Sn=In,Nn=["n_clicks","n_clicks_timestamp"];function Ln(){return Ln=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Ln.apply(null,arguments)}var Hn=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Nn)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("caption",Ln({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Hn.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Kn=Hn,An=["n_clicks","n_clicks_timestamp"];function Mn(){return Mn=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Mn.apply(null,arguments)}var Fn=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,An)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("center",Mn({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Fn.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Bn=Fn,Rn=["n_clicks","n_clicks_timestamp"];function Un(){return Un=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Un.apply(null,arguments)}var qn=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Rn)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("cite",Un({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};qn.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Vn=qn,zn=["n_clicks","n_clicks_timestamp"];function Qn(){return Qn=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Qn.apply(null,arguments)}var Yn=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,zn)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("code",Qn({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Yn.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Wn=Yn,Xn=["n_clicks","n_clicks_timestamp"];function Gn(){return Gn=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Gn.apply(null,arguments)}var Jn=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Xn)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("col",Gn({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Jn.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,span:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Zn=Jn,$n=["n_clicks","n_clicks_timestamp"];function ne(){return ne=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},ne.apply(null,arguments)}var ee=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,$n)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("colgroup",ne({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};ee.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,span:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var te=ee,re=["n_clicks","n_clicks_timestamp"];function ie(){return ie=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},ie.apply(null,arguments)}var se=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,re)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("content",ie({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};se.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var ce=se,ae=["n_clicks","n_clicks_timestamp"];function le(){return le=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},le.apply(null,arguments)}var oe=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,ae)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("data",le({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};oe.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,value:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var de=oe,ue=["n_clicks","n_clicks_timestamp"];function ge(){return ge=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},ge.apply(null,arguments)}var pe=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,ue)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("datalist",ge({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};pe.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var _e=pe,fe=["n_clicks","n_clicks_timestamp"];function be(){return be=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},be.apply(null,arguments)}var me=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,fe)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("dd",be({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};me.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var ke=me,ye=["n_clicks","n_clicks_timestamp"];function Oe(){return Oe=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Oe.apply(null,arguments)}var he=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,ye)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("del",Oe({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};he.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,cite:s().string,dateTime:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var ve=he,we=["n_clicks","n_clicks_timestamp"];function Pe(){return Pe=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Pe.apply(null,arguments)}var je=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,we)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("details",Pe({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};je.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,open:s().oneOfType([s().oneOf(["open","OPEN"]),s().bool]),accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Ee=je,De=["n_clicks","n_clicks_timestamp"];function xe(){return xe=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},xe.apply(null,arguments)}var Te=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,De)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("dfn",xe({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Te.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Ce=Te,Ie=["n_clicks","n_clicks_timestamp"];function Se(){return Se=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Se.apply(null,arguments)}var Ne=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Ie)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("dialog",Se({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Ne.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,open:s().oneOfType([s().oneOf(["open","OPEN"]),s().bool]),accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Le=Ne,He=["n_clicks","n_clicks_timestamp"];function Ke(){return Ke=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Ke.apply(null,arguments)}var Ae=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,He)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("div",Ke({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Ae.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Me=Ae,Fe=["n_clicks","n_clicks_timestamp"];function Be(){return Be=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Be.apply(null,arguments)}var Re=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Fe)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("dl",Be({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Re.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Ue=Re,qe=["n_clicks","n_clicks_timestamp"];function Ve(){return Ve=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Ve.apply(null,arguments)}var ze=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,qe)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("dt",Ve({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};ze.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Qe=ze,Ye=["n_clicks","n_clicks_timestamp"];function We(){return We=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},We.apply(null,arguments)}var Xe=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Ye)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("em",We({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Xe.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Ge=Xe,Je=["n_clicks","n_clicks_timestamp"];function Ze(){return Ze=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Ze.apply(null,arguments)}var $e=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Je)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=window.dash_clientside.clean_url,a=r().useMemo((function(){return i.src&&c(i.src)}),[i.src]);a&&(s.src=a),r().useEffect((function(){a&&a!==i.src&&i.setProps({_dash_error:new Error("Dangerous link detected: ".concat(i.src))})}),[i.src,a]);var l=i.disable_n_clicks||!i.id;return r().createElement("embed",Ze({},!l&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};$e.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,height:s().oneOfType([s().string,s().number]),src:s().string,type:s().string,width:s().oneOfType([s().string,s().number]),accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var nt=$e,et=["n_clicks","n_clicks_timestamp"];function tt(){return tt=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},tt.apply(null,arguments)}var rt=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,et)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("fieldset",tt({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};rt.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,disabled:s().oneOfType([s().oneOf(["disabled","DISABLED"]),s().bool]),form:s().string,name:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var it=rt,st=["n_clicks","n_clicks_timestamp"];function ct(){return ct=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},ct.apply(null,arguments)}var at=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,st)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("figcaption",ct({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};at.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var lt=at,ot=["n_clicks","n_clicks_timestamp"];function dt(){return dt=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},dt.apply(null,arguments)}var ut=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,ot)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("figure",dt({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};ut.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var gt=ut,pt=["n_clicks","n_clicks_timestamp"];function _t(){return _t=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},_t.apply(null,arguments)}var ft=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,pt)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("font",_t({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};ft.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var bt=ft,mt=["n_clicks","n_clicks_timestamp"];function kt(){return kt=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},kt.apply(null,arguments)}var yt=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,mt)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("footer",kt({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};yt.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Ot=yt,ht=["n_clicks","n_clicks_timestamp"];function vt(){return vt=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},vt.apply(null,arguments)}var wt=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,ht)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=window.dash_clientside.clean_url,a=r().useMemo((function(){return i.action&&c(i.action)}),[i.action]);a&&(s.action=a),r().useEffect((function(){a&&a!==i.action&&i.setProps({_dash_error:new Error("Dangerous link detected: ".concat(i.action))})}),[i.action,a]);var l=i.disable_n_clicks||!i.id;return r().createElement("form",vt({},!l&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};wt.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accept:s().string,acceptCharset:s().string,action:s().string,autoComplete:s().string,encType:s().string,method:s().string,name:s().string,noValidate:s().oneOfType([s().oneOf(["noValidate","novalidate","NOVALIDATE"]),s().bool]),target:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Pt=wt,jt=["n_clicks","n_clicks_timestamp"];function Et(){return Et=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Et.apply(null,arguments)}var Dt=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,jt)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("frame",Et({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Dt.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var xt=Dt,Tt=["n_clicks","n_clicks_timestamp"];function Ct(){return Ct=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Ct.apply(null,arguments)}var It=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Tt)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("frameset",Ct({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};It.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var St=It,Nt=["n_clicks","n_clicks_timestamp"];function Lt(){return Lt=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Lt.apply(null,arguments)}var Ht=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Nt)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("h1",Lt({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Ht.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Kt=Ht,At=["n_clicks","n_clicks_timestamp"];function Mt(){return Mt=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Mt.apply(null,arguments)}var Ft=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,At)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("h2",Mt({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Ft.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Bt=Ft,Rt=["n_clicks","n_clicks_timestamp"];function Ut(){return Ut=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Ut.apply(null,arguments)}var qt=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Rt)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("h3",Ut({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};qt.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Vt=qt,zt=["n_clicks","n_clicks_timestamp"];function Qt(){return Qt=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Qt.apply(null,arguments)}var Yt=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,zt)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("h4",Qt({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Yt.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Wt=Yt,Xt=["n_clicks","n_clicks_timestamp"];function Gt(){return Gt=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Gt.apply(null,arguments)}var Jt=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Xt)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("h5",Gt({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Jt.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Zt=Jt,$t=["n_clicks","n_clicks_timestamp"];function nr(){return nr=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},nr.apply(null,arguments)}var er=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,$t)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("h6",nr({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};er.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var tr=er,rr=["n_clicks","n_clicks_timestamp"];function ir(){return ir=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},ir.apply(null,arguments)}var sr=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,rr)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("header",ir({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};sr.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var cr=sr,ar=["n_clicks","n_clicks_timestamp"];function lr(){return lr=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},lr.apply(null,arguments)}var or=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,ar)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("hgroup",lr({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};or.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var dr=or,ur=["n_clicks","n_clicks_timestamp"];function gr(){return gr=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},gr.apply(null,arguments)}var pr=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,ur)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("hr",gr({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};pr.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var _r=pr,fr=["n_clicks","n_clicks_timestamp"];function br(){return br=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},br.apply(null,arguments)}var mr=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,fr)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("i",br({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};mr.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var kr=mr,yr=["n_clicks","n_clicks_timestamp"];function Or(){return Or=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Or.apply(null,arguments)}var hr=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,yr)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=window.dash_clientside.clean_url,a=r().useMemo((function(){return i.src&&c(i.src)}),[i.src]);a&&(s.src=a),r().useEffect((function(){a&&a!==i.src&&i.setProps({_dash_error:new Error("Dangerous link detected: ".concat(i.src))})}),[i.src,a]);var l=i.disable_n_clicks||!i.id;return r().createElement("iframe",Or({},!l&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};hr.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,allow:s().string,height:s().oneOfType([s().string,s().number]),name:s().string,referrerPolicy:s().string,sandbox:s().string,src:s().string,srcDoc:s().string,width:s().oneOfType([s().string,s().number]),accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var vr=hr,wr=["n_clicks","n_clicks_timestamp"];function Pr(){return Pr=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Pr.apply(null,arguments)}var jr=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,wr)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("img",Pr({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};jr.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,alt:s().string,crossOrigin:s().string,height:s().oneOfType([s().string,s().number]),referrerPolicy:s().string,sizes:s().string,src:s().string,srcSet:s().string,useMap:s().string,width:s().oneOfType([s().string,s().number]),accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Er=jr,Dr=["n_clicks","n_clicks_timestamp"];function xr(){return xr=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},xr.apply(null,arguments)}var Tr=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Dr)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("ins",xr({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Tr.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,cite:s().string,dateTime:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Cr=Tr,Ir=["n_clicks","n_clicks_timestamp"];function Sr(){return Sr=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Sr.apply(null,arguments)}var Nr=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Ir)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("kbd",Sr({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Nr.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Lr=Nr,Hr=["n_clicks","n_clicks_timestamp"];function Kr(){return Kr=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Kr.apply(null,arguments)}var Ar=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Hr)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("keygen",Kr({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Ar.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Mr=Ar,Fr=["n_clicks","n_clicks_timestamp"];function Br(){return Br=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Br.apply(null,arguments)}var Rr=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Fr)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("label",Br({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Rr.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,htmlFor:s().string,form:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Ur=Rr,qr=["n_clicks","n_clicks_timestamp"];function Vr(){return Vr=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Vr.apply(null,arguments)}var zr=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,qr)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("legend",Vr({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};zr.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Qr=zr,Yr=["n_clicks","n_clicks_timestamp"];function Wr(){return Wr=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Wr.apply(null,arguments)}var Xr=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Yr)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("li",Wr({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Xr.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,value:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Gr=Xr,Jr=["n_clicks","n_clicks_timestamp"];function Zr(){return Zr=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Zr.apply(null,arguments)}var $r=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Jr)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("link",Zr({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};$r.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,crossOrigin:s().string,href:s().string,hrefLang:s().string,integrity:s().string,media:s().string,referrerPolicy:s().string,rel:s().string,sizes:s().string,type:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var ni=$r,ei=["n_clicks","n_clicks_timestamp"];function ti(){return ti=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},ti.apply(null,arguments)}var ri=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,ei)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("main",ti({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};ri.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var ii=ri,si=["n_clicks","n_clicks_timestamp"];function ci(){return ci=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},ci.apply(null,arguments)}var ai=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,si)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("map",ci({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};ai.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,name:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var li=ai,oi=["n_clicks","n_clicks_timestamp"];function di(){return di=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},di.apply(null,arguments)}var ui=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,oi)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("mark",di({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};ui.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var gi=ui,pi=["n_clicks","n_clicks_timestamp"];function _i(){return _i=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},_i.apply(null,arguments)}var fi=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,pi)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("marquee",_i({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};fi.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,loop:s().oneOfType([s().oneOf(["loop","LOOP"]),s().bool]),accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var bi=fi,mi=["n_clicks","n_clicks_timestamp"];function ki(){return ki=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},ki.apply(null,arguments)}var yi=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,mi)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("meta",ki({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};yi.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,charSet:s().string,content:s().string,httpEquiv:s().string,name:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Oi=yi,hi=["n_clicks","n_clicks_timestamp"];function vi(){return vi=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},vi.apply(null,arguments)}var wi=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,hi)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("meter",vi({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};wi.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,form:s().string,high:s().string,low:s().string,max:s().oneOfType([s().string,s().number]),min:s().oneOfType([s().string,s().number]),optimum:s().string,value:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Pi=wi,ji=["n_clicks","n_clicks_timestamp"];function Ei(){return Ei=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Ei.apply(null,arguments)}var Di=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,ji)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("nav",Ei({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Di.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var xi=Di,Ti=["n_clicks","n_clicks_timestamp"];function Ci(){return Ci=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Ci.apply(null,arguments)}var Ii=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Ti)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("nobr",Ci({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Ii.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Si=Ii,Ni=["n_clicks","n_clicks_timestamp"];function Li(){return Li=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Li.apply(null,arguments)}var Hi=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Ni)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("noscript",Li({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Hi.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Ki=Hi,Ai=["n_clicks","n_clicks_timestamp"];function Mi(){return Mi=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Mi.apply(null,arguments)}var Fi=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Ai)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=window.dash_clientside.clean_url,a=r().useMemo((function(){return i.data&&c(i.data)}),[i.data]);a&&(s.data=a),r().useEffect((function(){a&&a!==i.data&&i.setProps({_dash_error:new Error("Dangerous link detected: ".concat(i.data))})}),[i.data,a]);var l=i.disable_n_clicks||!i.id;return r().createElement("object",Mi({},!l&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Fi.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,data:s().string,form:s().string,height:s().oneOfType([s().string,s().number]),name:s().string,type:s().string,useMap:s().string,width:s().oneOfType([s().string,s().number]),accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Bi=Fi,Ri=["n_clicks","n_clicks_timestamp"];function Ui(){return Ui=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Ui.apply(null,arguments)}var qi=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Ri)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("ol",Ui({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};qi.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,reversed:s().oneOfType([s().oneOf(["reversed","REVERSED"]),s().bool]),start:s().string,type:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Vi=qi,zi=["n_clicks","n_clicks_timestamp"];function Qi(){return Qi=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Qi.apply(null,arguments)}var Yi=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,zi)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("optgroup",Qi({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Yi.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,disabled:s().oneOfType([s().oneOf(["disabled","DISABLED"]),s().bool]),label:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Wi=Yi,Xi=["n_clicks","n_clicks_timestamp"];function Gi(){return Gi=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Gi.apply(null,arguments)}var Ji=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Xi)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("option",Gi({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Ji.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,disabled:s().oneOfType([s().oneOf(["disabled","DISABLED"]),s().bool]),label:s().string,selected:s().oneOfType([s().oneOf(["selected","SELECTED"]),s().bool]),value:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Zi=Ji,$i=["n_clicks","n_clicks_timestamp"];function ns(){return ns=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},ns.apply(null,arguments)}var es=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,$i)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("output",ns({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};es.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,htmlFor:s().string,form:s().string,name:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var ts=es,rs=["n_clicks","n_clicks_timestamp"];function is(){return is=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},is.apply(null,arguments)}var ss=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,rs)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("p",is({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};ss.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var cs=ss,as=["n_clicks","n_clicks_timestamp"];function ls(){return ls=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},ls.apply(null,arguments)}var os=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,as)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("param",ls({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};os.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,name:s().string,value:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var ds=os,us=["n_clicks","n_clicks_timestamp"];function gs(){return gs=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},gs.apply(null,arguments)}var ps=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,us)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("picture",gs({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};ps.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var _s=ps,fs=["n_clicks","n_clicks_timestamp"];function bs(){return bs=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},bs.apply(null,arguments)}var ms=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,fs)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("plaintext",bs({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};ms.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var ks=ms,ys=["n_clicks","n_clicks_timestamp"];function Os(){return Os=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Os.apply(null,arguments)}var hs=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,ys)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("pre",Os({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};hs.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var vs=hs,ws=["n_clicks","n_clicks_timestamp"];function Ps(){return Ps=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Ps.apply(null,arguments)}var js=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,ws)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("progress",Ps({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};js.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,form:s().string,max:s().oneOfType([s().string,s().number]),value:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Es=js,Ds=["n_clicks","n_clicks_timestamp"];function xs(){return xs=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},xs.apply(null,arguments)}var Ts=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Ds)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("q",xs({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Ts.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,cite:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Cs=Ts,Is=["n_clicks","n_clicks_timestamp"];function Ss(){return Ss=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Ss.apply(null,arguments)}var Ns=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Is)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("rb",Ss({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Ns.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Ls=Ns,Hs=["n_clicks","n_clicks_timestamp"];function Ks(){return Ks=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Ks.apply(null,arguments)}var As=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Hs)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("rp",Ks({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};As.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Ms=As,Fs=["n_clicks","n_clicks_timestamp"];function Bs(){return Bs=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Bs.apply(null,arguments)}var Rs=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Fs)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("rt",Bs({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Rs.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Us=Rs,qs=["n_clicks","n_clicks_timestamp"];function Vs(){return Vs=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Vs.apply(null,arguments)}var zs=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,qs)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("rtc",Vs({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};zs.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Qs=zs,Ys=["n_clicks","n_clicks_timestamp"];function Ws(){return Ws=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Ws.apply(null,arguments)}var Xs=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Ys)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("ruby",Ws({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Xs.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Gs=Xs,Js=["n_clicks","n_clicks_timestamp"];function Zs(){return Zs=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Zs.apply(null,arguments)}var $s=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Js)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("s",Zs({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};$s.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var nc=$s,ec=["n_clicks","n_clicks_timestamp"];function tc(){return tc=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},tc.apply(null,arguments)}var rc=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,ec)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("samp",tc({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};rc.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var ic=rc,sc=["n_clicks","n_clicks_timestamp"];function cc(){return cc=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},cc.apply(null,arguments)}var ac=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,sc)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("script",cc({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};ac.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,async:s().oneOfType([s().oneOf(["async","ASYNC"]),s().bool]),crossOrigin:s().string,defer:s().oneOfType([s().oneOf(["defer","DEFER"]),s().bool]),integrity:s().string,referrerPolicy:s().string,src:s().string,type:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var lc=ac,oc=["n_clicks","n_clicks_timestamp"];function dc(){return dc=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},dc.apply(null,arguments)}var uc=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,oc)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("section",dc({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};uc.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var gc=uc,pc=["n_clicks","n_clicks_timestamp"];function _c(){return _c=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},_c.apply(null,arguments)}var fc=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,pc)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("select",_c({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};fc.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,autoFocus:s().oneOfType([s().oneOf(["autoFocus","autofocus","AUTOFOCUS"]),s().bool]),autoComplete:s().string,disabled:s().oneOfType([s().oneOf(["disabled","DISABLED"]),s().bool]),form:s().string,multiple:s().oneOfType([s().oneOf(["multiple","MULTIPLE"]),s().bool]),name:s().string,required:s().oneOfType([s().oneOf(["required","REQUIRED"]),s().bool]),size:s().oneOfType([s().string,s().number]),accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var bc=fc,mc=["n_clicks","n_clicks_timestamp"];function kc(){return kc=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},kc.apply(null,arguments)}var yc=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,mc)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("shadow",kc({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};yc.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Oc=yc,hc=["n_clicks","n_clicks_timestamp"];function vc(){return vc=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},vc.apply(null,arguments)}var wc=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,hc)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("slot",vc({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};wc.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Pc=wc,jc=["n_clicks","n_clicks_timestamp"];function Ec(){return Ec=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Ec.apply(null,arguments)}var Dc=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,jc)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("small",Ec({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Dc.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var xc=Dc,Tc=["n_clicks","n_clicks_timestamp"];function Cc(){return Cc=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Cc.apply(null,arguments)}var Ic=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Tc)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("source",Cc({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Ic.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,media:s().string,sizes:s().string,src:s().string,srcSet:s().string,type:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Sc=Ic,Nc=["n_clicks","n_clicks_timestamp"];function Lc(){return Lc=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Lc.apply(null,arguments)}var Hc=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Nc)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("spacer",Lc({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Hc.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Kc=Hc,Ac=["n_clicks","n_clicks_timestamp"];function Mc(){return Mc=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Mc.apply(null,arguments)}var Fc=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Ac)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("span",Mc({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Fc.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Bc=Fc,Rc=["n_clicks","n_clicks_timestamp"];function Uc(){return Uc=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Uc.apply(null,arguments)}var qc=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Rc)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("strike",Uc({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};qc.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Vc=qc,zc=["n_clicks","n_clicks_timestamp"];function Qc(){return Qc=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Qc.apply(null,arguments)}var Yc=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,zc)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("strong",Qc({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Yc.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Wc=Yc,Xc=["n_clicks","n_clicks_timestamp"];function Gc(){return Gc=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Gc.apply(null,arguments)}var Jc=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Xc)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("sub",Gc({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Jc.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Zc=Jc,$c=["n_clicks","n_clicks_timestamp"];function na(){return na=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},na.apply(null,arguments)}var ea=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,$c)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("summary",na({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};ea.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var ta=ea,ra=["n_clicks","n_clicks_timestamp"];function ia(){return ia=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},ia.apply(null,arguments)}var sa=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,ra)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("sup",ia({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};sa.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var ca=sa,aa=["n_clicks","n_clicks_timestamp"];function la(){return la=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},la.apply(null,arguments)}var oa=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,aa)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("table",la({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};oa.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var da=oa,ua=["n_clicks","n_clicks_timestamp"];function ga(){return ga=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},ga.apply(null,arguments)}var pa=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,ua)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("tbody",ga({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};pa.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var _a=pa,fa=["n_clicks","n_clicks_timestamp"];function ba(){return ba=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},ba.apply(null,arguments)}var ma=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,fa)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("td",ba({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};ma.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,colSpan:s().oneOfType([s().string,s().number]),headers:s().string,rowSpan:s().oneOfType([s().string,s().number]),accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var ka=ma,ya=["n_clicks","n_clicks_timestamp"];function Oa(){return Oa=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Oa.apply(null,arguments)}var ha=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,ya)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("template",Oa({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};ha.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var va=ha,wa=["n_clicks","n_clicks_timestamp"];function Pa(){return Pa=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Pa.apply(null,arguments)}var ja=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,wa)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("textarea",Pa({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};ja.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,autoFocus:s().oneOfType([s().oneOf(["autoFocus","autofocus","AUTOFOCUS"]),s().bool]),autoComplete:s().string,cols:s().oneOfType([s().string,s().number]),disabled:s().oneOfType([s().oneOf(["disabled","DISABLED"]),s().bool]),form:s().string,inputMode:s().string,maxLength:s().oneOfType([s().string,s().number]),minLength:s().oneOfType([s().string,s().number]),name:s().string,placeholder:s().string,readOnly:s().string,required:s().oneOfType([s().oneOf(["required","REQUIRED"]),s().bool]),rows:s().oneOfType([s().string,s().number]),wrap:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Ea=ja,Da=["n_clicks","n_clicks_timestamp"];function xa(){return xa=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},xa.apply(null,arguments)}var Ta=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Da)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("tfoot",xa({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Ta.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Ca=Ta,Ia=["n_clicks","n_clicks_timestamp"];function Sa(){return Sa=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Sa.apply(null,arguments)}var Na=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Ia)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("th",Sa({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Na.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,colSpan:s().oneOfType([s().string,s().number]),headers:s().string,rowSpan:s().oneOfType([s().string,s().number]),scope:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var La=Na,Ha=["n_clicks","n_clicks_timestamp"];function Ka(){return Ka=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Ka.apply(null,arguments)}var Aa=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Ha)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("thead",Ka({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Aa.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Ma=Aa,Fa=["n_clicks","n_clicks_timestamp"];function Ba(){return Ba=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Ba.apply(null,arguments)}var Ra=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Fa)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("time",Ba({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Ra.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,dateTime:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Ua=Ra,qa=["n_clicks","n_clicks_timestamp"];function Va(){return Va=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Va.apply(null,arguments)}var za=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,qa)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("title",Va({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};za.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Qa=za,Ya=["n_clicks","n_clicks_timestamp"];function Wa(){return Wa=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Wa.apply(null,arguments)}var Xa=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Ya)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("tr",Wa({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};Xa.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Ga=Xa,Ja=["n_clicks","n_clicks_timestamp"];function Za(){return Za=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Za.apply(null,arguments)}var $a=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,Ja)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("track",Za({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};$a.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,default:s().oneOfType([s().oneOf(["default","DEFAULT"]),s().bool]),kind:s().string,label:s().string,src:s().string,srcLang:s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var nl=$a,el=["n_clicks","n_clicks_timestamp"];function tl(){return tl=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},tl.apply(null,arguments)}var rl=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,el)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("u",tl({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};rl.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var il=rl,sl=["n_clicks","n_clicks_timestamp"];function cl(){return cl=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},cl.apply(null,arguments)}var al=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,sl)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("ul",cl({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};al.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var ll=al,ol=["n_clicks","n_clicks_timestamp"];function dl(){return dl=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},dl.apply(null,arguments)}var ul=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,ol)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("var",dl({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};ul.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var gl=ul,pl=["n_clicks","n_clicks_timestamp"];function _l(){return _l=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},_l.apply(null,arguments)}var fl=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,pl)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("video",_l({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};fl.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,autoPlay:s().oneOfType([s().oneOf(["autoPlay","autoplay","AUTOPLAY"]),s().bool]),controls:s().oneOfType([s().oneOf(["controls","CONTROLS"]),s().bool]),crossOrigin:s().string,height:s().oneOfType([s().string,s().number]),loop:s().oneOfType([s().oneOf(["loop","LOOP"]),s().bool]),muted:s().oneOfType([s().oneOf(["muted","MUTED"]),s().bool]),poster:s().string,preload:s().string,src:s().string,width:s().oneOfType([s().string,s().number]),accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var bl=fl,ml=["n_clicks","n_clicks_timestamp"];function kl(){return kl=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},kl.apply(null,arguments)}var yl=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,ml)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("wbr",kl({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};yl.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Ol=yl,hl=["n_clicks","n_clicks_timestamp"];function vl(){return vl=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},vl.apply(null,arguments)}var wl=function(n){var e=n.n_clicks,t=void 0===e?0:e,i=(n.n_clicks_timestamp,function(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t={};for(var r in n)if({}.hasOwnProperty.call(n,r)){if(-1!==e.indexOf(r))continue;t[r]=n[r]}return t}(n,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);for(r=0;r<s.length;r++)t=s[r],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}(n,hl)),s={};window.dash_component_api.useDashContext().useLoading()&&(s["data-dash-is-loading"]=!0);var c=i.disable_n_clicks||!i.id;return r().createElement("xmp",vl({},!c&&{onClick:function(){return i.setProps({n_clicks:t+1,n_clicks_timestamp:Date.now()})}},o(["n_clicks","n_clicks_timestamp","loading_state","setProps","disable_n_clicks"],i),s),i.children)};wl.propTypes={id:s().string,children:s().node,n_clicks:s().number,n_clicks_timestamp:s().number,disable_n_clicks:s().bool,key:s().string,"data-*":s().string,"aria-*":s().string,accessKey:s().string,className:s().string,contentEditable:s().string,dir:s().string,draggable:s().string,hidden:s().oneOfType([s().oneOf(["hidden","HIDDEN"]),s().bool]),lang:s().string,role:s().string,spellCheck:s().string,style:s().object,tabIndex:s().oneOfType([s().string,s().number]),title:s().string,setProps:s().func};var Pl=wl;window.dash_html_components=e}();
//# sourceMappingURL=dash_html_components.min.js.map