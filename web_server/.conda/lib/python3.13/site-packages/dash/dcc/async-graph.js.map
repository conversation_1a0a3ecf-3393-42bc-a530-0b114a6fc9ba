{"version": 3, "file": "async-graph.js", "mappings": "wUAMMA,EAAiBC,IACnB,IAEIC,EAFGC,EAA+BF,EAA/BE,SAAUC,EAAqBH,EAArBG,SAAUC,EAAWJ,EAAXI,QACrBC,GAAMC,EAAAA,EAAAA,aAGNC,GAAyBC,EAAAA,EAAAA,cAAY,KACnCP,GACAQ,aAAaR,GAEjBA,EAAgBS,YAAW,KACvBR,GAAS,EAAK,GAZZ,GAaG,GACV,CAACA,IAEES,GAAWC,EAAAA,EAAAA,UACb,IAAM,IAAIC,eAAeN,IACzB,CAACL,IAcL,OAXAY,EAAAA,EAAAA,YAAU,IACDT,EAAIU,SAGTX,EAAQY,SAAQC,GAAUN,EAASO,QAAQD,EAAOF,WAClDJ,EAASO,QAAQb,EAAIU,SACd,KACHJ,EAASQ,YAAY,GALd,QAOZ,CAACd,EAAIU,UAEDK,IAAAA,cAAA,OAAKf,IAAKA,GAAMF,EAAe,EAG1CJ,EAAesB,UAAY,CACvBnB,SAAUoB,IAAAA,KACVnB,SAAUmB,IAAAA,KACVlB,QAASkB,IAAAA,KAGb,Q,wkCClBA,IAAMC,EAAoB,CACtBC,UAAU,EACVC,YAAQC,EACRC,WAAOD,GAGLE,EAAc,CAAC,EAEfC,EAAsB,CACxBL,UAAU,GAURM,EAAoB,CACtBC,YAAY,GAGVC,EAAc,CAAC,EAEfC,EAAsB,CACxBF,YAAY,GAGVG,EAAkBA,CAACC,EAAIC,EAAWC,KACpC,IAAIC,EACJ,IAAIC,EAAAA,EAAAA,GAASF,EAAO,CAAC,QAAS,QAAS,aAAc,CACjD,IAAMG,EAAS,GAEf,IAAIC,EAAAA,EAAAA,GAAML,GACN,OAAO,KAaX,IAFA,IAAMM,EAAOP,EAAGO,KAAKC,EAAA,WAGjB,IAAMC,EAAYR,EAAUI,OAAOK,GAC7BC,GAAYC,EAAAA,EAAAA,IAAO,SAAUC,GAC/B,QAAQT,EAAAA,EAAAA,IAASU,EAAAA,EAAAA,GAAKD,GAAI,CAAC,SAAU,SACzC,GAAGJ,IAGCM,EAAAA,EAAAA,GAAI,OAAQN,KACZE,EAAUK,KAAOP,EAAUO,OAI3BD,EAAAA,EAAAA,GAAI,cAAeN,KACnBM,EAAAA,EAAAA,GAAI,aAAcR,EAAKI,EAAUM,iBAE7BF,EAAAA,EAAAA,GAAI,cAAeN,GACkB,iBAA1BA,EAAUS,YACjBP,EAAUQ,WACNZ,EAAKI,EAAUM,aAAaE,WACxBV,EAAUS,cAGjBT,EAAUS,aACXT,EAAUF,KAAKa,KAAKhB,SAAS,WAE7BO,EAAUQ,WACNZ,EAAKI,EAAUM,aAAaE,aAE7BJ,EAAAA,EAAAA,GAAI,eAAgBN,KAC3BE,EAAUQ,WAAaV,EAAUY,aAAaC,KAAI,SAC9CC,GAEA,OAAOhB,EAAKI,EAAUM,aAAaE,WAAWI,EAClD,OAKJR,EAAAA,EAAAA,GAAI,eAAgBN,KACpBE,EAAUU,aAAeZ,EAAUY,cAGvChB,EAAOK,GAAKC,CAChB,EA3CSD,EAAI,EAAGA,EAAIT,EAAUI,OAAOmB,OAAQd,IAAGF,IA4ChDL,EAAoB,CAACE,SACzB,KAAqB,aAAVH,GAAkC,YAAVA,IAO/BC,EAAoBF,GAQxB,OANIc,EAAAA,EAAAA,GAAI,QAASd,KACbE,EAAkBsB,MAAQxB,EAAUwB,QAEpCV,EAAAA,EAAAA,GAAI,cAAed,KACnBE,EAAkBuB,YAAczB,EAAUyB,aAEvCvB,CAAiB,EAS5B,MAAMwB,UAAoBC,EAAAA,UACtBC,WAAAA,CAAYhE,GACRiE,MAAMjE,GACNkE,KAAK/B,GAAKf,IAAAA,YACV8C,KAAKC,aAAc,EACnBD,KAAKE,QAAU,KACfF,KAAKG,OAASC,QAAQC,UAEtBL,KAAKM,cAAgBpD,IAAAA,YAErB8C,KAAKO,WAAaP,KAAKO,WAAWC,KAAKR,MACvCA,KAAKS,UAAYT,KAAKS,UAAUD,KAAKR,MACrCA,KAAKU,kBAAoBV,KAAKU,kBAAkBF,KAAKR,MACrDA,KAAKW,UAAYX,KAAKW,UAAUH,KAAKR,MACrCA,KAAKY,kBAAoBZ,KAAKY,kBAAkBJ,KAAKR,MACrDA,KAAKa,YAAcb,KAAKa,YAAYL,KAAKR,MACzCA,KAAKc,aAAed,KAAKc,aAAaN,KAAKR,MAC3CA,KAAKe,YAAcf,KAAKe,YAAYP,KAAKR,MAEzCA,KAAKgB,MAAQ,CAACC,SAAU,CAAC,EAAGC,UAAW,CAAC,EAC5C,CAEAC,IAAAA,CAAKrF,GAAO,IAAAsF,EAAAC,EAAAC,EACHC,EAAkBzF,EAAlByF,OAAQC,EAAU1F,EAAV0F,OACNC,EAAmD3F,EAAnD2F,QAASC,EAA0C5F,EAA1C4F,kBAAmB7D,EAAuB/B,EAAvB+B,WAAY8D,EAAW7F,EAAX6F,QAEzC1D,EAAK+B,KAAK/B,GAAGpB,QACnB0E,EAASzF,EAAM8F,6BAA6BL,EAAQtD,GACpDuD,EAAS1F,EAAM+F,6BAA6BL,EAAQvD,GAEpD,IAAM6D,EAAc9B,KAAKS,UAAUe,EAAQ3D,GAE3CiE,EAAYC,YAAcJ,EAE1B,IAAMK,EAAc,CAChBxD,KAAY,QAAR4C,EAAEG,SAAM,IAAAH,OAAA,EAANA,EAAQ5C,KACdyD,OAAQjC,KAAKW,UAAgB,QAAPU,EAACE,SAAM,IAAAF,OAAA,EAANA,EAAQY,OAAQpE,GACvCqE,OAAc,QAARZ,EAAEC,SAAM,IAAAD,OAAA,EAANA,EAAQY,OAChBV,OAAQM,GAGZ,OACIL,GACAzB,KAAKC,aACLsB,EAAO/C,KAAKiB,SAAWxB,EAAGO,KAAKiB,OAI3B8B,EAAOW,OACAC,OAAOC,aAAanE,GACtBoE,MAAK,IAAMF,OAAOG,UAAUrE,EAAIsD,EAAOW,UACvCG,MAAK,IACFF,OAAOV,QAAQxD,EAAI+D,EAAaN,KAGrCS,OAAOV,QAAQxD,EAAI+D,EAAaN,IAG3CzD,EAAGsE,UAAUC,IAAI,wBAEVC,EAAAA,EAAAA,GAAgBd,GAClBU,MAAK,KACF,IAAMpE,EAAK+B,KAAK/B,GAAGpB,QACnB,OAAOoB,GAAMkE,OAAOO,MAAMzE,EAAI+D,EAAY,IAE7CK,MAAK,KACF,IAAMpE,EAAK+B,KAAK/B,GAAGpB,QAGdoB,IAILA,EAAGsE,UAAUI,OAAO,uBAGhB3C,KAAKC,aAAehC,IAAO+B,KAAKE,UAC5BF,KAAKE,SAAWF,KAAKE,QAAQ0C,qBAC7B5C,KAAKE,QAAQ0C,qBACbT,OAAOU,MAAM7C,KAAKE,UAEtBF,KAAKC,aAAc,GAGlBD,KAAKC,cACND,KAAKO,aACLP,KAAKa,aAAY,GACjBb,KAAKC,aAAc,EACnBD,KAAKE,QAAUjC,GACnB,IAEZ,CAEA8C,WAAAA,CAAY+B,EAAGC,EAAUC,GACrB,IAAoBC,EAAqCF,EAAlDG,YAAqCC,EAAaJ,EAAzBK,WACZC,EAAqCL,EAAlDE,YAAqCI,EAAaN,EAAzBI,WAC1BG,EAAQvD,KAEd,SAASwD,EAAY1H,EAAO2H,EAASC,GACjC,IAAMC,EAAa7H,EAAM6H,WACnBC,EAAY9H,EAAM2H,GAEpBI,EAAKzD,QAAQC,UAqCjB,OAnCAuD,EAAU9G,SAAQ0B,IACd,IAAIsF,EAAYC,EAAcC,E,IAC9B,GAAIC,MAAMC,QAAQ1F,IAA4B,iBAAZA,EAAK,GAAiB,KAAA2F,G,EACR,E,4CAAJ3F,I,s1BAAvCsF,EAAUK,EAAA,GAAEJ,EAAYI,EAAA,GAAEH,EAASG,EAAA,EACxC,MACIL,EAAatF,EAGjB,IAAKuF,EAAc,CACf,SAASK,EAAa5F,GAClB,OAAOA,EAAK6F,OAAOC,KAAK9F,GAAM,GAClC,CAEA,SAAS+F,EAAgB/F,GACrB,OAAOyF,MAAMO,KACTP,MAAMG,EAAa5F,GAAMiB,QAAQ6E,OAEzC,CACAP,EAAeQ,EAAgBT,EACnC,CAEAD,EAAKA,EAAGxB,MAAK,KACT,IAAMpE,EAAKsF,EAAMtF,GAAGpB,QACpB,OACIoB,GACAkE,OAAOuB,GACHzF,EACA6F,EACAC,EACAC,EACH,GAEP,IAGCH,EAAGxB,MAAK,IAAMsB,EAAWF,IACpC,CAEA,IAAIgB,GAAW,EAsBf,OApBIpB,SAAAA,EAAY5D,QAAUwD,IAAeI,IACrCoB,GAAW,EACX3B,EAAIA,EAAET,MAAK,IACPmB,EAAYR,EAAU,cAAe,oBAIzCM,SAAAA,EAAW7D,QAAU0D,IAAcG,IACnCmB,GAAW,EACX3B,EAAIA,EAAET,MAAK,IACPmB,EAAYR,EAAU,aAAc,mBAIxCyB,IACA3B,EAAIA,EAAET,MAAK,IACPW,EAAS0B,8BAA8B1B,EAASzB,WAIjDuB,CACX,CAEArC,SAAAA,CAAUe,EAAQ3D,GACd,OAAO8G,EAAAA,EAAAA,GAAenD,EAAQxB,KAAKU,kBAAkB7C,GACzD,CAEA8C,SAAAA,CAAUsB,EAAQpE,GACd,IAAKoE,EACD,OAAOA,EAEX,IAAMhB,EAAWjB,KAAKY,kBAAkB/C,GACxC+G,EAA6D5E,KAAKgB,MAAjD6D,EAAaD,EAAvB3D,SAAoC6D,EAAcF,EAAzB1D,UAE1BA,EAAY,CAAC,EACnB,IAAK,IAAM6D,KAAO9D,EACVgB,EAAO8C,KAASF,EAAcE,GAC9B7D,EAAU6D,GAAO9C,EAAO8C,GACjBD,EAAeE,eAAeD,KACrC7D,EAAU6D,GAAOD,EAAeC,IAKxC,IAAK,IAAMA,KAFX/E,KAAKiF,SAAS,CAAChE,WAAUC,cAEP4D,EACV7C,EAAO8C,KAASF,EAAcE,KAC9B9C,EAAO8C,GAAOD,EAAeC,IAIrC,IAAK,IAAMA,KAAO9D,EACdgB,EAAO8C,GAAO9D,EAAS8D,GAE3B,OAAO9C,CACX,CAEAvB,iBAAAA,CAAkB7C,GACd,OAAQA,GACJ,KAAK,EACD,OAAOE,EACX,KAAK,EACD,OAAOH,EACX,QACI,OAAOE,EAEnB,CAEA8C,iBAAAA,CAAkB/C,GACd,OAAQA,GACJ,KAAK,EACD,OAAOF,EACX,KAAK,EACD,OAAON,EACX,QACI,OAAOK,EAEnB,CAEAoD,YAAAA,CAAahF,GACT,IAAO0F,EAA8B1F,EAA9B0F,OAAQD,EAAsBzF,EAAtByF,OAAQ1D,EAAc/B,EAAd+B,WAEvB,MAAyB,aAArBkB,EAAAA,EAAAA,GAAKlB,GACEA,EAGJqH,QACH1D,EAAO3D,cACD0D,EAAOU,SACHV,EAAOU,OAAO3E,WACZiB,EAAAA,EAAAA,GAAMgD,EAAOU,OAAO3E,cACnBiB,EAAAA,EAAAA,GAAMgD,EAAOU,OAAO1E,UACjBgB,EAAAA,EAAAA,GAAMgD,EAAOU,OAAOxE,SAE5C,CAEAoD,WAAAA,GACI,GADasE,UAAA1F,OAAA,QAAAjC,IAAA2H,UAAA,IAAAA,UAAA,IACEnF,KAAKc,aAAad,KAAKlE,OAAtC,CAIA,IAAMmC,EAAK+B,KAAK/B,GAAGpB,QACdoB,IAILA,EAAGsE,UAAUC,IAAI,uBAEjBL,OAAOiD,MAAMC,OAAOpH,GACfqH,OAAM,SACNC,SAAQ,IAAMtH,EAAGsE,UAAUI,OAAO,yBAXvC,CAYJ,CAEApC,UAAAA,GACI,IAAAiF,EAOIxF,KAAKlE,MANL2J,EAAQD,EAARC,SACAC,EAAgBF,EAAhBE,iBACAC,EAAYH,EAAZG,aACAC,EAAWJ,EAAXI,YACAC,EAASL,EAATK,UACAC,EAAYN,EAAZM,aAGE7H,EAAK+B,KAAK/B,GAAGpB,QAEnBoB,EAAG8H,GAAG,gBAAgB7H,IAClB,IAAM8H,EAAYhI,EAAgBC,EAAIC,EAAW,UAC5CK,EAAAA,EAAAA,GAAMyH,IACPP,EAAS,CAACO,aACd,IAEJ/H,EAAG8H,GAAG,0BAA0B7H,IAC5B,IAAM+H,GAAsBC,EAAAA,EAAAA,GACxB,CAAC,QAAS,kBACVhI,GAEJuH,EAAS,CAACQ,uBAAqB,IAEnChI,EAAG8H,GAAG,gBAAgB7H,IAClB,IAAMiI,EAAQnI,EAAgBC,EAAIC,EAAW,UACxCK,EAAAA,EAAAA,GAAM4H,KAAWC,EAAAA,EAAAA,GAAOD,EAAON,IAChCJ,EAAS,CAACI,UAAWM,GACzB,IAEJlI,EAAG8H,GAAG,mBAAmB7H,IACrB,IAAMmI,EAAWrI,EAAgBC,EAAIC,EAAW,aAC3CK,EAAAA,EAAAA,GAAM8H,KAAcD,EAAAA,EAAAA,GAAOC,EAAUP,IACtCL,EAAS,CAACK,aAAcO,GAC5B,IAEJpI,EAAG8H,GAAG,mBAAmB,KACrBN,EAAS,CAACK,aAAc,MAAM,IAElC7H,EAAG8H,GAAG,mBAAmB7H,IACrB,IAAMoI,EAAWtI,EAAgBC,EAAIC,EAAW,aAC3CK,EAAAA,EAAAA,GAAM+H,KAAcF,EAAAA,EAAAA,GAAOE,EAAUX,IACtCF,EAAS,CAACE,aAAcW,GAC5B,IAEJrI,EAAG8H,GAAG,kBAAkB7H,IACpB,IAAMqI,EAAUvI,EAAgBC,EAAIC,EAAW,YAC1CK,EAAAA,EAAAA,GAAMgI,KAAaH,EAAAA,EAAAA,GAAOG,EAASX,IACpCH,EAAS,CAACG,YAAaW,GAC3B,IAEJtI,EAAG8H,GAAG,kBAAkB,KAChBL,GACAD,EAAS,CAACI,UAAW,MACzB,GAER,CAEAW,QAAAA,GACI,IAAO3I,EAAcmC,KAAKlE,MAAnB+B,WACF4I,EAASzG,KAAKlE,MAAd2K,MAGL,OAAK5I,GAKA4I,IACDA,EAAQ,CAAC,GAGRA,EAAMlJ,OAIJkJ,EAHIpC,OAAOqC,OAAO,CAACnJ,OAAQ,QAASkJ,IAThCA,CAaf,CAEAE,iBAAAA,GACI,IAAM7D,EAAI9C,KAAKmB,KAAKnB,KAAKlE,OACzBkE,KAAKG,OAASH,KAAKe,YAAY+B,EAAG,CAAC,EAAG9C,KAAKlE,MAC/C,CAEA8K,oBAAAA,GACI,IAAM3I,EAAK+B,KAAK/B,GAAGpB,QACfoB,GAAMA,EAAG2E,qBACT3E,EAAG2E,qBACC5C,KAAKC,aACLkC,OAAOU,MAAM5E,GAGzB,CAEA4I,qBAAAA,CAAsBC,GAClB,OACI9G,KAAKlE,MAAMiL,KAAOD,EAAUC,IAC5BC,KAAKC,UAAUjH,KAAKlE,MAAM2K,SAAWO,KAAKC,UAAUH,EAAUL,MAEtE,CAEAS,gCAAAA,CAAiCJ,GAE7B,GADkB9G,KAAKlE,MAAMiL,KAAOD,EAAUC,GAC9C,CAQA,IAAIjE,EAAI9C,KAAKG,OAGTH,KAAKlE,MAAM6F,UAAYmF,EAAUnF,SACjC3B,KAAKlE,MAAMyF,SAAWuF,EAAUvF,QAChCvB,KAAKlE,MAAM+F,+BACPiF,EAAUjF,8BACd7B,KAAKlE,MAAM8F,+BACPkF,EAAUlF,+BAEdkB,EAAIA,EAAET,MAAK,IAAMrC,KAAKmB,KAAK2F,MAG/B9G,KAAKG,OAASH,KAAKe,YAAY+B,EAAG9C,KAAKlE,MAAOgL,EAf9C,CAgBJ,CAEAK,kBAAAA,CAAmBC,GAEXA,EAAUL,KAAO/G,KAAKlE,MAAMiL,IAC5BK,EAAUzF,UAAY3B,KAAKlE,MAAM6F,UAEjC3B,KAAKG,OAASH,KAAKG,OAAOkC,MAAK,IAAMrC,KAAKmB,KAAKnB,KAAKlE,SAE5D,CAEAuL,MAAAA,GACI,IAAAC,EAAuCtH,KAAKlE,MAArCyL,EAASD,EAATC,UAAWR,EAAEO,EAAFP,GAAIS,EAAaF,EAAbE,cAChBf,EAAQzG,KAAKwG,WAEnB,OAAIiB,OAAOC,mBAEHxK,IAAAA,cAACyK,EAAAA,EAAc,CACXZ,GAAIA,EACJhC,IAAKgC,EACLQ,UAAWA,EACXd,MAAOA,EACPtK,IAAK6D,KAAKM,eAEVpD,IAAAA,cAACrB,EAAc,CACXG,SAAUgE,KAAKa,YACf3E,QAAS,CAAC8D,KAAKM,cAAeN,KAAK/B,MAEvCf,IAAAA,cAAA,OACIf,IAAK6D,KAAK/B,GACVwI,MAAO,CAAClJ,OAAQ,OAAQE,MAAO,WAM3CP,IAAAA,cAAA,OACI6J,GAAIA,EACJhC,IAAKgC,EACLQ,UAAWA,EACXd,MAAOA,EACPtK,IAAK6D,KAAKM,cACV,uBACKkH,GAAiBA,EAAcI,iBAAepK,GAGnDN,IAAAA,cAACrB,EAAc,CACXG,SAAUgE,KAAKa,YACf3E,QAAS,CAAC8D,KAAKM,cAAeN,KAAK/B,MAEvCf,IAAAA,cAAA,OAAKf,IAAK6D,KAAK/B,GAAIwI,MAAO,CAAClJ,OAAQ,OAAQE,MAAO,UAG9D,EAGJmC,EAAYzC,UAAS0K,EAAAA,EAAA,GACdC,EAAAA,IAAc,IACjB5E,YAAa9F,IAAAA,QACTA,IAAAA,UAAoB,CAACA,IAAAA,MAAiBA,IAAAA,UAE1CgG,WAAYhG,IAAAA,QACRA,IAAAA,UAAoB,CAACA,IAAAA,MAAiBA,IAAAA,UAE1CuG,WAAYvG,IAAAA,KAAe2K,aAG/BnI,EAAYoI,aAAYH,EAAAA,EAAA,GACjBI,EAAAA,IAAiB,IACpB/E,YAAa,GACbE,WAAY,KAGhB,O", "sources": ["webpack:///./src/utils/ResizeDetector.js", "webpack:///./src/fragments/Graph.react.js"], "sourcesContent": ["import React, {createRef, useEffect, useCallback, useMemo} from 'react';\nimport PropTypes from 'prop-types';\n\n// Debounce 50 ms\nconst DELAY = 50;\n\nconst ResizeDetector = props => {\n    const {onResize, children, targets} = props;\n    const ref = createRef();\n    let resizeTimeout;\n\n    const debouncedResizeHandler = useCallback(() => {\n        if (resizeTimeout) {\n            clearTimeout(resizeTimeout);\n        }\n        resizeTimeout = setTimeout(() => {\n            onResize(true); // Force on resize.\n        }, DELAY);\n    }, [onResize]);\n\n    const observer = useMemo(\n        () => new ResizeObserver(debouncedResizeHandler),\n        [onResize]\n    );\n\n    useEffect(() => {\n        if (!ref.current) {\n            return () => {};\n        }\n        targets.forEach(target => observer.observe(target.current));\n        observer.observe(ref.current);\n        return () => {\n            observer.disconnect();\n        };\n    }, [ref.current]);\n\n    return <div ref={ref}>{children}</div>;\n};\n\nResizeDetector.propTypes = {\n    onResize: PropTypes.func,\n    children: PropTypes.node,\n    targets: PropTypes.any,\n};\n\nexport default ResizeDetector;\n", "import lazyLoadMathJax from '../utils/LazyLoader/mathjax';\nimport React, {Component} from 'react';\nimport {\n    equals,\n    filter,\n    has,\n    includes,\n    isNil,\n    mergeDeepRight,\n    omit,\n    type,\n} from 'ramda';\nimport PropTypes from 'prop-types';\nimport {graphPropTypes, graphDefaultProps} from '../components/Graph.react';\n\n/* global Plotly:true */\n\nimport ResizeDetector from '../utils/ResizeDetector';\nimport LoadingElement from '../utils/LoadingElement';\n\n/**\n * `autosize: true` causes Plotly.js to conform to the parent element size.\n * This is necessary for `dcc.Graph` call to `Plotly.Plots.resize(target)` to do something.\n *\n * Users can override this value for specific use-cases by explicitly passing `autoresize: true`\n * if `responsive` is not set to True.\n */\nconst RESPONSIVE_LAYOUT = {\n    autosize: true,\n    height: undefined,\n    width: undefined,\n};\n\nconst AUTO_LAYOUT = {};\n\nconst UNRESPONSIVE_LAYOUT = {\n    autosize: false,\n};\n\n/**\n * `responsive: true` causes Plotly.js to resize the graph on `window.resize`.\n * This is necessary for `dcc.Graph` call to `Plotly.Plots.resize(target)` to do something.\n *\n * Users can override this value for specific use-cases by explicitly passing `responsive: false`\n * if `responsive` is not set to True.\n */\nconst RESPONSIVE_CONFIG = {\n    responsive: true,\n};\n\nconst AUTO_CONFIG = {};\n\nconst UNRESPONSIVE_CONFIG = {\n    responsive: false,\n};\n\nconst filterEventData = (gd, eventData, event) => {\n    let filteredEventData;\n    if (includes(event, ['click', 'hover', 'selected'])) {\n        const points = [];\n\n        if (isNil(eventData)) {\n            return null;\n        }\n\n        /*\n         * remove `data`, `layout`, `xaxis`, etc\n         * objects from the event data since they're so big\n         * and cause JSON stringify ciricular structure errors.\n         *\n         * also, pull down the `customdata` point from the data array\n         * into the event object\n         */\n        const data = gd.data;\n\n        for (let i = 0; i < eventData.points.length; i++) {\n            const fullPoint = eventData.points[i];\n            const pointData = filter(function (o) {\n                return !includes(type(o), ['Object', 'Array']);\n            }, fullPoint);\n\n            // permit a bounding box to pass through, if present\n            if (has('bbox', fullPoint)) {\n                pointData.bbox = fullPoint.bbox;\n            }\n\n            if (\n                has('curveNumber', fullPoint) &&\n                has('customdata', data[pointData.curveNumber])\n            ) {\n                if (has('pointNumber', fullPoint)) {\n                    if (typeof fullPoint.pointNumber === 'number') {\n                        pointData.customdata =\n                            data[pointData.curveNumber].customdata[\n                                fullPoint.pointNumber\n                            ];\n                    } else if (\n                        !fullPoint.pointNumber &&\n                        fullPoint.data.mode.includes('lines')\n                    ) {\n                        pointData.customdata =\n                            data[pointData.curveNumber].customdata;\n                    }\n                } else if (has('pointNumbers', fullPoint)) {\n                    pointData.customdata = fullPoint.pointNumbers.map(function (\n                        point\n                    ) {\n                        return data[pointData.curveNumber].customdata[point];\n                    });\n                }\n            }\n\n            // specific to histogram. see https://github.com/plotly/plotly.js/pull/2113/\n            if (has('pointNumbers', fullPoint)) {\n                pointData.pointNumbers = fullPoint.pointNumbers;\n            }\n\n            points[i] = pointData;\n        }\n        filteredEventData = {points};\n    } else if (event === 'relayout' || event === 'restyle') {\n        /*\n         * relayout shouldn't include any big objects\n         * it will usually just contain the ranges of the axes like\n         * \"xaxis.range[0]\": 0.7715822247381828,\n         * \"xaxis.range[1]\": 3.0095292008680063`\n         */\n        filteredEventData = eventData;\n    }\n    if (has('range', eventData)) {\n        filteredEventData.range = eventData.range;\n    }\n    if (has('lassoPoints', eventData)) {\n        filteredEventData.lassoPoints = eventData.lassoPoints;\n    }\n    return filteredEventData;\n};\n\n/**\n * Graph can be used to render any plotly.js-powered data visualization.\n *\n * You can define callbacks based on user interaction with Graphs such as\n * hovering, clicking or selecting\n */\nclass PlotlyGraph extends Component {\n    constructor(props) {\n        super(props);\n        this.gd = React.createRef();\n        this._hasPlotted = false;\n        this._prevGd = null;\n        this._queue = Promise.resolve();\n\n        this.parentElement = React.createRef();\n\n        this.bindEvents = this.bindEvents.bind(this);\n        this.getConfig = this.getConfig.bind(this);\n        this.getConfigOverride = this.getConfigOverride.bind(this);\n        this.getLayout = this.getLayout.bind(this);\n        this.getLayoutOverride = this.getLayoutOverride.bind(this);\n        this.graphResize = this.graphResize.bind(this);\n        this.isResponsive = this.isResponsive.bind(this);\n        this.amendTraces = this.amendTraces.bind(this);\n\n        this.state = {override: {}, originals: {}};\n    }\n\n    plot(props) {\n        let {figure, config} = props;\n        const {animate, animation_options, responsive, mathjax} = props;\n\n        const gd = this.gd.current;\n        figure = props._dashprivate_transformFigure(figure, gd);\n        config = props._dashprivate_transformConfig(config, gd);\n\n        const configClone = this.getConfig(config, responsive);\n        // add typesetMath | not exposed to the dash API\n        configClone.typesetMath = mathjax;\n\n        const figureClone = {\n            data: figure?.data,\n            layout: this.getLayout(figure?.layout, responsive),\n            frames: figure?.frames,\n            config: configClone,\n        };\n\n        if (\n            animate &&\n            this._hasPlotted &&\n            figure.data.length === gd.data.length\n        ) {\n            // in case we've have figure frames,\n            // we need to recreate frames before animation\n            if (figure.frames) {\n                return Plotly.deleteFrames(gd)\n                    .then(() => Plotly.addFrames(gd, figure.frames))\n                    .then(() =>\n                        Plotly.animate(gd, figureClone, animation_options)\n                    );\n            }\n            return Plotly.animate(gd, figureClone, animation_options);\n        }\n\n        gd.classList.add('dash-graph--pending');\n\n        return lazyLoadMathJax(mathjax)\n            .then(() => {\n                const gd = this.gd.current;\n                return gd && Plotly.react(gd, figureClone);\n            })\n            .then(() => {\n                const gd = this.gd.current;\n\n                // double-check gd hasn't been unmounted\n                if (!gd) {\n                    return;\n                }\n\n                gd.classList.remove('dash-graph--pending');\n\n                // in case we've made a new DOM element, transfer events\n                if (this._hasPlotted && gd !== this._prevGd) {\n                    if (this._prevGd && this._prevGd.removeAllListeners) {\n                        this._prevGd.removeAllListeners();\n                        Plotly.purge(this._prevGd);\n                    }\n                    this._hasPlotted = false;\n                }\n\n                if (!this._hasPlotted) {\n                    this.bindEvents();\n                    this.graphResize(true);\n                    this._hasPlotted = true;\n                    this._prevGd = gd;\n                }\n            });\n    }\n\n    amendTraces(p, oldProps, newProps) {\n        const {prependData: oldPrepend, extendData: oldExtend} = oldProps;\n        const {prependData: newPrepend, extendData: newExtend} = newProps;\n        const _this = this;\n\n        function mergeTraces(props, dataKey, plotlyFnKey) {\n            const clearState = props.clearState;\n            const dataArray = props[dataKey];\n\n            let _p = Promise.resolve();\n\n            dataArray.forEach(data => {\n                let updateData, traceIndices, maxPoints;\n                if (Array.isArray(data) && typeof data[0] === 'object') {\n                    [updateData, traceIndices, maxPoints] = data;\n                } else {\n                    updateData = data;\n                }\n\n                if (!traceIndices) {\n                    function getFirstProp(data) {\n                        return data[Object.keys(data)[0]];\n                    }\n\n                    function generateIndices(data) {\n                        return Array.from(\n                            Array(getFirstProp(data).length).keys()\n                        );\n                    }\n                    traceIndices = generateIndices(updateData);\n                }\n\n                _p = _p.then(() => {\n                    const gd = _this.gd.current;\n                    return (\n                        gd &&\n                        Plotly[plotlyFnKey](\n                            gd,\n                            updateData,\n                            traceIndices,\n                            maxPoints\n                        )\n                    );\n                });\n            });\n\n            return _p.then(() => clearState(dataKey));\n        }\n\n        let modified = false;\n\n        if (newPrepend?.length && oldPrepend !== newPrepend) {\n            modified = true;\n            p = p.then(() =>\n                mergeTraces(newProps, 'prependData', 'prependTraces')\n            );\n        }\n\n        if (newExtend?.length && oldExtend !== newExtend) {\n            modified = true;\n            p = p.then(() =>\n                mergeTraces(newProps, 'extendData', 'extendTraces')\n            );\n        }\n\n        if (modified) {\n            p = p.then(() =>\n                newProps._dashprivate_onFigureModified(newProps.figure)\n            );\n        }\n\n        return p;\n    }\n\n    getConfig(config, responsive) {\n        return mergeDeepRight(config, this.getConfigOverride(responsive));\n    }\n\n    getLayout(layout, responsive) {\n        if (!layout) {\n            return layout;\n        }\n        const override = this.getLayoutOverride(responsive);\n        const {override: prev_override, originals: prev_originals} = this.state;\n        // Store the original data that we're about to override\n        const originals = {};\n        for (const key in override) {\n            if (layout[key] !== prev_override[key]) {\n                originals[key] = layout[key];\n            } else if (prev_originals.hasOwnProperty(key)) {\n                originals[key] = prev_originals[key];\n            }\n        }\n        this.setState({override, originals});\n        // Undo the previous override, but only for keys that the user did not change\n        for (const key in prev_originals) {\n            if (layout[key] === prev_override[key]) {\n                layout[key] = prev_originals[key];\n            }\n        }\n        // Apply the current override\n        for (const key in override) {\n            layout[key] = override[key];\n        }\n        return layout; // not really a clone\n    }\n\n    getConfigOverride(responsive) {\n        switch (responsive) {\n            case false:\n                return UNRESPONSIVE_CONFIG;\n            case true:\n                return RESPONSIVE_CONFIG;\n            default:\n                return AUTO_CONFIG;\n        }\n    }\n\n    getLayoutOverride(responsive) {\n        switch (responsive) {\n            case false:\n                return UNRESPONSIVE_LAYOUT;\n            case true:\n                return RESPONSIVE_LAYOUT;\n            default:\n                return AUTO_LAYOUT;\n        }\n    }\n\n    isResponsive(props) {\n        const {config, figure, responsive} = props;\n\n        if (type(responsive) === 'Boolean') {\n            return responsive;\n        }\n\n        return Boolean(\n            config.responsive &&\n                (!figure.layout ||\n                    ((figure.layout.autosize ||\n                        isNil(figure.layout.autosize)) &&\n                        (isNil(figure.layout.height) ||\n                            isNil(figure.layout.width))))\n        );\n    }\n\n    graphResize(force = false) {\n        if (!force && !this.isResponsive(this.props)) {\n            return;\n        }\n\n        const gd = this.gd.current;\n        if (!gd) {\n            return;\n        }\n\n        gd.classList.add('dash-graph--pending');\n\n        Plotly.Plots.resize(gd)\n            .catch(() => {})\n            .finally(() => gd.classList.remove('dash-graph--pending'));\n    }\n\n    bindEvents() {\n        const {\n            setProps,\n            clear_on_unhover,\n            relayoutData,\n            restyleData,\n            hoverData,\n            selectedData,\n        } = this.props;\n\n        const gd = this.gd.current;\n\n        gd.on('plotly_click', eventData => {\n            const clickData = filterEventData(gd, eventData, 'click');\n            if (!isNil(clickData)) {\n                setProps({clickData});\n            }\n        });\n        gd.on('plotly_clickannotation', eventData => {\n            const clickAnnotationData = omit(\n                ['event', 'fullAnnotation'],\n                eventData\n            );\n            setProps({clickAnnotationData});\n        });\n        gd.on('plotly_hover', eventData => {\n            const hover = filterEventData(gd, eventData, 'hover');\n            if (!isNil(hover) && !equals(hover, hoverData)) {\n                setProps({hoverData: hover});\n            }\n        });\n        gd.on('plotly_selected', eventData => {\n            const selected = filterEventData(gd, eventData, 'selected');\n            if (!isNil(selected) && !equals(selected, selectedData)) {\n                setProps({selectedData: selected});\n            }\n        });\n        gd.on('plotly_deselect', () => {\n            setProps({selectedData: null});\n        });\n        gd.on('plotly_relayout', eventData => {\n            const relayout = filterEventData(gd, eventData, 'relayout');\n            if (!isNil(relayout) && !equals(relayout, relayoutData)) {\n                setProps({relayoutData: relayout});\n            }\n        });\n        gd.on('plotly_restyle', eventData => {\n            const restyle = filterEventData(gd, eventData, 'restyle');\n            if (!isNil(restyle) && !equals(restyle, restyleData)) {\n                setProps({restyleData: restyle});\n            }\n        });\n        gd.on('plotly_unhover', () => {\n            if (clear_on_unhover) {\n                setProps({hoverData: null});\n            }\n        });\n    }\n\n    getStyle() {\n        const {responsive} = this.props;\n        let {style} = this.props;\n\n        // When there is no forced responsive style, return the original style property\n        if (!responsive) {\n            return style;\n        }\n\n        // Otherwise, if the height is not set, we make the graph size equal to the parent one\n        if (!style) {\n            style = {};\n        }\n\n        if (!style.height) {\n            return Object.assign({height: '100%'}, style);\n        }\n\n        return style;\n    }\n\n    componentDidMount() {\n        const p = this.plot(this.props);\n        this._queue = this.amendTraces(p, {}, this.props);\n    }\n\n    componentWillUnmount() {\n        const gd = this.gd.current;\n        if (gd && gd.removeAllListeners) {\n            gd.removeAllListeners();\n            if (this._hasPlotted) {\n                Plotly.purge(gd);\n            }\n        }\n    }\n\n    shouldComponentUpdate(nextProps) {\n        return (\n            this.props.id !== nextProps.id ||\n            JSON.stringify(this.props.style) !== JSON.stringify(nextProps.style)\n        );\n    }\n\n    UNSAFE_componentWillReceiveProps(nextProps) {\n        const idChanged = this.props.id !== nextProps.id;\n        if (idChanged) {\n            /*\n             * then the dom needs to get re-rendered with a new ID.\n             * the graph will get updated in componentDidUpdate\n             */\n            return;\n        }\n\n        let p = this._queue;\n\n        if (\n            this.props.mathjax !== nextProps.mathjax ||\n            this.props.figure !== nextProps.figure ||\n            this.props._dashprivate_transformConfig !==\n                nextProps._dashprivate_transformConfig ||\n            this.props._dashprivate_transformFigure !==\n                nextProps._dashprivate_transformFigure\n        ) {\n            p = p.then(() => this.plot(nextProps));\n        }\n\n        this._queue = this.amendTraces(p, this.props, nextProps);\n    }\n\n    componentDidUpdate(prevProps) {\n        if (\n            prevProps.id !== this.props.id ||\n            prevProps.mathjax !== this.props.mathjax\n        ) {\n            this._queue = this._queue.then(() => this.plot(this.props));\n        }\n    }\n\n    render() {\n        const {className, id, loading_state} = this.props;\n        const style = this.getStyle();\n\n        if (window.dash_component_api) {\n            return (\n                <LoadingElement\n                    id={id}\n                    key={id}\n                    className={className}\n                    style={style}\n                    ref={this.parentElement}\n                >\n                    <ResizeDetector\n                        onResize={this.graphResize}\n                        targets={[this.parentElement, this.gd]}\n                    />\n                    <div\n                        ref={this.gd}\n                        style={{height: '100%', width: '100%'}}\n                    />\n                </LoadingElement>\n            );\n        }\n        return (\n            <div\n                id={id}\n                key={id}\n                className={className}\n                style={style}\n                ref={this.parentElement}\n                data-dash-is-loading={\n                    (loading_state && loading_state.is_loading) || undefined\n                }\n            >\n                <ResizeDetector\n                    onResize={this.graphResize}\n                    targets={[this.parentElement, this.gd]}\n                />\n                <div ref={this.gd} style={{height: '100%', width: '100%'}} />\n            </div>\n        );\n    }\n}\n\nPlotlyGraph.propTypes = {\n    ...graphPropTypes,\n    prependData: PropTypes.arrayOf(\n        PropTypes.oneOfType([PropTypes.array, PropTypes.object])\n    ),\n    extendData: PropTypes.arrayOf(\n        PropTypes.oneOfType([PropTypes.array, PropTypes.object])\n    ),\n    clearState: PropTypes.func.isRequired,\n};\n\nPlotlyGraph.defaultProps = {\n    ...graphDefaultProps,\n    prependData: [],\n    extendData: [],\n};\n\nexport default PlotlyGraph;\n"], "names": ["ResizeDetector", "props", "resizeTimeout", "onResize", "children", "targets", "ref", "createRef", "debouncedResizeHandler", "useCallback", "clearTimeout", "setTimeout", "observer", "useMemo", "ResizeObserver", "useEffect", "current", "for<PERSON>ach", "target", "observe", "disconnect", "React", "propTypes", "PropTypes", "RESPONSIVE_LAYOUT", "autosize", "height", "undefined", "width", "AUTO_LAYOUT", "UNRESPONSIVE_LAYOUT", "RESPONSIVE_CONFIG", "responsive", "AUTO_CONFIG", "UNRESPONSIVE_CONFIG", "filterEventData", "gd", "eventData", "event", "filteredEventData", "includes", "points", "isNil", "data", "_loop", "fullPoint", "i", "pointData", "filter", "o", "type", "has", "bbox", "curveNumber", "pointNumber", "customdata", "mode", "pointNumbers", "map", "point", "length", "range", "lassoPoints", "PlotlyGraph", "Component", "constructor", "super", "this", "_hasPlotted", "_prevGd", "_queue", "Promise", "resolve", "parentElement", "bindEvents", "bind", "getConfig", "getConfigOverride", "getLayout", "getLayoutOverride", "graphResize", "isResponsive", "amendTraces", "state", "override", "originals", "plot", "_figure", "_figure2", "_figure3", "figure", "config", "animate", "animation_options", "mathjax", "_dashprivate_transformFigure", "_dashprivate_transformConfig", "config<PERSON><PERSON>", "typesetMath", "figureClone", "layout", "frames", "<PERSON><PERSON><PERSON>", "deleteFrames", "then", "addFrames", "classList", "add", "lazyLoadMathJax", "react", "remove", "removeAllListeners", "purge", "p", "oldProps", "newProps", "oldPrepend", "prependData", "oldExtend", "extendData", "newPrepend", "newExtend", "_this", "mergeTraces", "dataKey", "plotly<PERSON>n<PERSON><PERSON>", "clearState", "dataArray", "_p", "updateData", "traceIndices", "maxPoints", "Array", "isArray", "_data", "getFirstProp", "Object", "keys", "generateIndices", "from", "modified", "_dashprivate_onFigureModified", "mergeDeepRight", "_this$state", "prev_override", "prev_originals", "key", "hasOwnProperty", "setState", "Boolean", "arguments", "Plots", "resize", "catch", "finally", "_this$props", "setProps", "clear_on_unhover", "relayoutData", "restyleData", "hoverData", "selectedData", "on", "clickData", "clickAnnotationData", "omit", "hover", "equals", "selected", "relayout", "restyle", "getStyle", "style", "assign", "componentDidMount", "componentWillUnmount", "shouldComponentUpdate", "nextProps", "id", "JSON", "stringify", "UNSAFE_componentWillReceiveProps", "componentDidUpdate", "prevProps", "render", "_this$props2", "className", "loading_state", "window", "dash_component_api", "LoadingElement", "is_loading", "_objectSpread", "graphPropTypes", "isRequired", "defaultProps", "graphDefaultProps"], "sourceRoot": ""}