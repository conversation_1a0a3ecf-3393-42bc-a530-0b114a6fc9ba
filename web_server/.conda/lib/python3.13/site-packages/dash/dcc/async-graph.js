"use strict";(self.webpackChunkdash_core_components=self.webpackChunkdash_core_components||[]).push([[746],{19979:function(e,t,r){r.r(t),r.d(t,{default:function(){return k}});var i=r(48136),n=r(51609),a=r.n(n),s=r(91487),o=r(51647),l=r(71298),u=r(1322),h=r(18851),d=r(92234),p=r(75647),c=r(84165),v=r(76120),f=r.n(v),g=r(24203),y=e=>{var t,r=e.onResize,i=e.children,s=e.targets,o=(0,n.createRef)(),l=(0,n.useCallback)((()=>{t&&clearTimeout(t),t=setTimeout((()=>{r(!0)}),50)}),[r]),u=(0,n.useMemo)((()=>new ResizeObserver(l)),[r]);return(0,n.useEffect)((()=>o.current?(s.forEach((e=>u.observe(e.current))),u.observe(o.current),()=>{u.disconnect()}):()=>{}),[o.current]),a().createElement("div",{ref:o},i)};y.propTypes={onResize:f().func,children:f().node,targets:f().any};var m=y,b=r(4459);function _(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,i)}return r}function A(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_(Object(r),!0).forEach((function(t){O(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function O(e,t,r){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function P(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,i=Array(t);r<t;r++)i[r]=e[r];return i}var j={autosize:!0,height:void 0,width:void 0},D={},w={autosize:!1},E={responsive:!0},N={},R={responsive:!1},S=(e,t,r)=>{var i;if((0,s.A)(r,["click","hover","selected"])){var n=[];if((0,o.A)(t))return null;for(var a=e.data,d=function(){var e=t.points[p],r=(0,l.A)((function(e){return!(0,s.A)((0,u.A)(e),["Object","Array"])}),e);(0,h.A)("bbox",e)&&(r.bbox=e.bbox),(0,h.A)("curveNumber",e)&&(0,h.A)("customdata",a[r.curveNumber])&&((0,h.A)("pointNumber",e)?"number"==typeof e.pointNumber?r.customdata=a[r.curveNumber].customdata[e.pointNumber]:!e.pointNumber&&e.data.mode.includes("lines")&&(r.customdata=a[r.curveNumber].customdata):(0,h.A)("pointNumbers",e)&&(r.customdata=e.pointNumbers.map((function(e){return a[r.curveNumber].customdata[e]})))),(0,h.A)("pointNumbers",e)&&(r.pointNumbers=e.pointNumbers),n[p]=r},p=0;p<t.points.length;p++)d();i={points:n}}else"relayout"!==r&&"restyle"!==r||(i=t);return(0,h.A)("range",t)&&(i.range=t.range),(0,h.A)("lassoPoints",t)&&(i.lassoPoints=t.lassoPoints),i};class C extends n.Component{constructor(e){super(e),this.gd=a().createRef(),this._hasPlotted=!1,this._prevGd=null,this._queue=Promise.resolve(),this.parentElement=a().createRef(),this.bindEvents=this.bindEvents.bind(this),this.getConfig=this.getConfig.bind(this),this.getConfigOverride=this.getConfigOverride.bind(this),this.getLayout=this.getLayout.bind(this),this.getLayoutOverride=this.getLayoutOverride.bind(this),this.graphResize=this.graphResize.bind(this),this.isResponsive=this.isResponsive.bind(this),this.amendTraces=this.amendTraces.bind(this),this.state={override:{},originals:{}}}plot(e){var t,r,n,a=e.figure,s=e.config,o=e.animate,l=e.animation_options,u=e.responsive,h=e.mathjax,d=this.gd.current;a=e._dashprivate_transformFigure(a,d),s=e._dashprivate_transformConfig(s,d);var p=this.getConfig(s,u);p.typesetMath=h;var c={data:null===(t=a)||void 0===t?void 0:t.data,layout:this.getLayout(null===(r=a)||void 0===r?void 0:r.layout,u),frames:null===(n=a)||void 0===n?void 0:n.frames,config:p};return o&&this._hasPlotted&&a.data.length===d.data.length?a.frames?Plotly.deleteFrames(d).then((()=>Plotly.addFrames(d,a.frames))).then((()=>Plotly.animate(d,c,l))):Plotly.animate(d,c,l):(d.classList.add("dash-graph--pending"),(0,i.A)(h).then((()=>{var e=this.gd.current;return e&&Plotly.react(e,c)})).then((()=>{var e=this.gd.current;e&&(e.classList.remove("dash-graph--pending"),this._hasPlotted&&e!==this._prevGd&&(this._prevGd&&this._prevGd.removeAllListeners&&(this._prevGd.removeAllListeners(),Plotly.purge(this._prevGd)),this._hasPlotted=!1),this._hasPlotted||(this.bindEvents(),this.graphResize(!0),this._hasPlotted=!0,this._prevGd=e))})))}amendTraces(e,t,r){var i=t.prependData,n=t.extendData,a=r.prependData,s=r.extendData,o=this;function l(e,t,r){var i=e.clearState,n=e[t],a=Promise.resolve();return n.forEach((e=>{var t,i,n,s,l;if(Array.isArray(e)&&"object"==typeof e[0]){var u=(l=3,function(e){if(Array.isArray(e))return e}(s=e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var i,n,a,s,o=[],l=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(i=a.call(r)).done)&&(o.push(i.value),o.length!==t);l=!0);}catch(e){u=!0,n=e}finally{try{if(!l&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(u)throw n}}return o}}(s,l)||function(e,t){if(e){if("string"==typeof e)return P(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?P(e,t):void 0}}(s,l)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}());t=u[0],i=u[1],n=u[2]}else t=e;if(!i){function h(e){return e[Object.keys(e)[0]]}function d(e){return Array.from(Array(h(e).length).keys())}i=d(t)}a=a.then((()=>{var e=o.gd.current;return e&&Plotly[r](e,t,i,n)}))})),a.then((()=>i(t)))}var u=!1;return null!=a&&a.length&&i!==a&&(u=!0,e=e.then((()=>l(r,"prependData","prependTraces")))),null!=s&&s.length&&n!==s&&(u=!0,e=e.then((()=>l(r,"extendData","extendTraces")))),u&&(e=e.then((()=>r._dashprivate_onFigureModified(r.figure)))),e}getConfig(e,t){return(0,d.A)(e,this.getConfigOverride(t))}getLayout(e,t){if(!e)return e;var r=this.getLayoutOverride(t),i=this.state,n=i.override,a=i.originals,s={};for(var o in r)e[o]!==n[o]?s[o]=e[o]:a.hasOwnProperty(o)&&(s[o]=a[o]);for(var l in this.setState({override:r,originals:s}),a)e[l]===n[l]&&(e[l]=a[l]);for(var u in r)e[u]=r[u];return e}getConfigOverride(e){switch(e){case!1:return R;case!0:return E;default:return N}}getLayoutOverride(e){switch(e){case!1:return w;case!0:return j;default:return D}}isResponsive(e){var t=e.config,r=e.figure,i=e.responsive;return"Boolean"===(0,u.A)(i)?i:Boolean(t.responsive&&(!r.layout||(r.layout.autosize||(0,o.A)(r.layout.autosize))&&((0,o.A)(r.layout.height)||(0,o.A)(r.layout.width))))}graphResize(){if(arguments.length>0&&void 0!==arguments[0]&&arguments[0]||this.isResponsive(this.props)){var e=this.gd.current;e&&(e.classList.add("dash-graph--pending"),Plotly.Plots.resize(e).catch((()=>{})).finally((()=>e.classList.remove("dash-graph--pending"))))}}bindEvents(){var e=this.props,t=e.setProps,r=e.clear_on_unhover,i=e.relayoutData,n=e.restyleData,a=e.hoverData,s=e.selectedData,l=this.gd.current;l.on("plotly_click",(e=>{var r=S(l,e,"click");(0,o.A)(r)||t({clickData:r})})),l.on("plotly_clickannotation",(e=>{var r=(0,p.A)(["event","fullAnnotation"],e);t({clickAnnotationData:r})})),l.on("plotly_hover",(e=>{var r=S(l,e,"hover");(0,o.A)(r)||(0,c.A)(r,a)||t({hoverData:r})})),l.on("plotly_selected",(e=>{var r=S(l,e,"selected");(0,o.A)(r)||(0,c.A)(r,s)||t({selectedData:r})})),l.on("plotly_deselect",(()=>{t({selectedData:null})})),l.on("plotly_relayout",(e=>{var r=S(l,e,"relayout");(0,o.A)(r)||(0,c.A)(r,i)||t({relayoutData:r})})),l.on("plotly_restyle",(e=>{var r=S(l,e,"restyle");(0,o.A)(r)||(0,c.A)(r,n)||t({restyleData:r})})),l.on("plotly_unhover",(()=>{r&&t({hoverData:null})}))}getStyle(){var e=this.props.responsive,t=this.props.style;return e?(t||(t={}),t.height?t:Object.assign({height:"100%"},t)):t}componentDidMount(){var e=this.plot(this.props);this._queue=this.amendTraces(e,{},this.props)}componentWillUnmount(){var e=this.gd.current;e&&e.removeAllListeners&&(e.removeAllListeners(),this._hasPlotted&&Plotly.purge(e))}shouldComponentUpdate(e){return this.props.id!==e.id||JSON.stringify(this.props.style)!==JSON.stringify(e.style)}UNSAFE_componentWillReceiveProps(e){if(this.props.id===e.id){var t=this._queue;this.props.mathjax===e.mathjax&&this.props.figure===e.figure&&this.props._dashprivate_transformConfig===e._dashprivate_transformConfig&&this.props._dashprivate_transformFigure===e._dashprivate_transformFigure||(t=t.then((()=>this.plot(e)))),this._queue=this.amendTraces(t,this.props,e)}}componentDidUpdate(e){e.id===this.props.id&&e.mathjax===this.props.mathjax||(this._queue=this._queue.then((()=>this.plot(this.props))))}render(){var e=this.props,t=e.className,r=e.id,i=e.loading_state,n=this.getStyle();return window.dash_component_api?a().createElement(b.A,{id:r,key:r,className:t,style:n,ref:this.parentElement},a().createElement(m,{onResize:this.graphResize,targets:[this.parentElement,this.gd]}),a().createElement("div",{ref:this.gd,style:{height:"100%",width:"100%"}})):a().createElement("div",{id:r,key:r,className:t,style:n,ref:this.parentElement,"data-dash-is-loading":i&&i.is_loading||void 0},a().createElement(m,{onResize:this.graphResize,targets:[this.parentElement,this.gd]}),a().createElement("div",{ref:this.gd,style:{height:"100%",width:"100%"}}))}}C.propTypes=A(A({},g.VZ),{},{prependData:f().arrayOf(f().oneOfType([f().array,f().object])),extendData:f().arrayOf(f().oneOfType([f().array,f().object])),clearState:f().func.isRequired}),C.defaultProps=A(A({},g.Sb),{},{prependData:[],extendData:[]});var k=C}}]);
//# sourceMappingURL=async-graph.js.map