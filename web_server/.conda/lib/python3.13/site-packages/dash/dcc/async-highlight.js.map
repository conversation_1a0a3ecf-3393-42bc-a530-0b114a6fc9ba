{"version": 3, "file": "async-highlight.js", "mappings": "ooCAEA,SAASA,EAAWC,GA8BlB,OA7BIA,aAAeC,IACjBD,EAAIE,MACFF,EAAIG,OACJH,EAAII,IACF,WACE,MAAM,IAAIC,MAAM,mBAClB,EACKL,aAAeM,MACxBN,EAAIO,IACFP,EAAIE,MACJF,EAAIG,OACF,WACE,MAAM,IAAIE,MAAM,mBAClB,GAING,OAAOC,OAAOT,GAEdQ,OAAOE,oBAAoBV,GAAKW,SAASC,IACvC,IAAMC,EAAOb,EAAIY,GACXE,SAAcD,EAGN,WAATC,GAA8B,aAATA,GAAyBN,OAAOO,SAASF,IACjEd,EAAWc,EACb,IAGKb,CACT,CAMA,MAAMgB,EAIJC,WAAAA,CAAYC,QAEQC,IAAdD,EAAKE,OAAoBF,EAAKE,KAAO,CAAC,GAE1CC,KAAKD,KAAOF,EAAKE,KACjBC,KAAKC,gBAAiB,CACxB,CAEAC,WAAAA,GACEF,KAAKC,gBAAiB,CACxB,EAOF,SAASE,EAAWC,GAClB,OAAOA,EACJC,QAAQ,KAAM,SACdA,QAAQ,KAAM,QACdA,QAAQ,KAAM,QACdA,QAAQ,KAAM,UACdA,QAAQ,KAAM,SACnB,CAUA,SAASC,EAAUC,GAEjB,IAAMC,EAASrB,OAAOsB,OAAO,MAE7B,IAAK,IAAMC,KAAOH,EAChBC,EAAOE,GAAOH,EAASG,GACxB,QAAAC,EAAAC,UAAAC,OAN6BC,EAAO,IAAAC,MAAAJ,EAAA,EAAAA,EAAA,KAAAK,EAAA,EAAAA,EAAAL,EAAAK,IAAPF,EAAOE,EAAA,GAAAJ,UAAAI,GAYrC,OALAF,EAAQxB,SAAQ,SAASX,GACvB,IAAK,IAAM+B,KAAO/B,EAChB6B,EAAOE,GAAO/B,EAAI+B,EAEtB,IACyBF,CAC3B,CAcA,IAMMS,EAAqBC,KAGhBA,EAAKC,MA0BhB,MAAMC,EAOJxB,WAAAA,CAAYyB,EAAWC,GACrBtB,KAAKuB,OAAS,GACdvB,KAAKwB,YAAcF,EAAQE,YAC3BH,EAAUI,KAAKzB,KACjB,CAMA0B,OAAAA,CAAQC,GACN3B,KAAKuB,QAAUpB,EAAWwB,EAC5B,CAMAC,QAAAA,CAASV,GACP,GAAKD,EAAkBC,GAAvB,CAEA,IAAMW,EA9CcC,CAACvC,IAAqB,IAAbwC,EA+C3B,CAAEA,OAAQ/B,KAAKwB,aA/CYO,OAE/B,GAAIxC,EAAKyC,WAAW,aAClB,OAAOzC,EAAKc,QAAQ,YAAa,aAGnC,GAAId,EAAK0C,SAAS,KAAM,CACtB,IAAMC,EAAS3C,EAAK4C,MAAM,KAC1B,MAAO,CAAC,GAADC,OACFL,GAAMK,OAAGF,EAAOG,YACfH,EAAOI,KAAI,CAACC,EAAGC,IAAM,GAALJ,OAAQG,GAACH,OAAG,IAAIK,OAAOD,EAAI,OAC/CE,KAAK,IACT,CAEA,MAAO,GAAPN,OAAUL,GAAMK,OAAG7C,EAAI,EAgCHuC,CAAgBZ,EAAKC,OAEvCnB,KAAK2C,KAAKd,EAJ0B,CAKtC,CAMAe,SAAAA,CAAU1B,GACHD,EAAkBC,KAEvBlB,KAAKuB,QA3EU,UA4EjB,CAKAnB,KAAAA,GACE,OAAOJ,KAAKuB,MACd,CAQAoB,IAAAA,CAAKd,GACH7B,KAAKuB,QAAU,gBAAJa,OAAoBP,EAAS,KAC1C,EASF,IAAMgB,EAAU,WAAe,IAAdC,EAAIlC,UAAAC,OAAA,QAAAf,IAAAc,UAAA,GAAAA,UAAA,GAAG,CAAC,EAEjBJ,EAAS,CAAEuC,SAAU,IAE3B,OADA5D,OAAO6D,OAAOxC,EAAQsC,GACftC,CACT,EAEA,MAAMyC,EACJrD,WAAAA,GAEEI,KAAKkD,SAAWL,IAChB7C,KAAKmD,MAAQ,CAACnD,KAAKkD,SACrB,CAEA,OAAIE,GACF,OAAOpD,KAAKmD,MAAMnD,KAAKmD,MAAMtC,OAAS,EACxC,CAEA,QAAIwC,GAAS,OAAOrD,KAAKkD,QAAU,CAGnChE,GAAAA,CAAIgC,GACFlB,KAAKoD,IAAIL,SAASO,KAAKpC,EACzB,CAGAU,QAAAA,CAAST,GAEP,IAAMD,EAAO2B,EAAQ,CAAE1B,UACvBnB,KAAKd,IAAIgC,GACTlB,KAAKmD,MAAMG,KAAKpC,EAClB,CAEA0B,SAAAA,GACE,GAAI5C,KAAKmD,MAAMtC,OAAS,EACtB,OAAOb,KAAKmD,MAAMI,KAItB,CAEAC,aAAAA,GACE,KAAOxD,KAAK4C,cACd,CAEAa,MAAAA,GACE,OAAOC,KAAKC,UAAU3D,KAAKkD,SAAU,KAAM,EAC7C,CAMAzB,IAAAA,CAAKmC,GAEH,OAAO5D,KAAKJ,YAAYiE,MAAMD,EAAS5D,KAAKkD,SAG9C,CAMA,YAAOW,CAAMD,EAAS1C,GAQpB,MAPoB,iBAATA,EACT0C,EAAQlC,QAAQR,GACPA,EAAK6B,WACda,EAAQhC,SAASV,GACjBA,EAAK6B,SAASzD,SAASwE,GAAU9D,KAAK6D,MAAMD,EAASE,KACrDF,EAAQhB,UAAU1B,IAEb0C,CACT,CAKA,gBAAOG,CAAU7C,GACK,iBAATA,GACNA,EAAK6B,WAEN7B,EAAK6B,SAASiB,OAAMC,GAAoB,iBAAPA,IAGnC/C,EAAK6B,SAAW,CAAC7B,EAAK6B,SAASL,KAAK,KAEpCxB,EAAK6B,SAASzD,SAASwE,IACrBb,EAAUc,UAAUD,EAAM,IAGhC,EAqBF,MAAMI,UAAyBjB,EAI7BrD,WAAAA,CAAY0B,GACV6C,QACAnE,KAAKsB,QAAUA,CACjB,CAKAI,OAAAA,CAAQC,GACO,KAATA,GAEJ3B,KAAKd,IAAIyC,EACX,CAGAyC,UAAAA,CAAWjD,GACTnB,KAAK4B,SAAST,EAChB,CAEAkD,QAAAA,GACErE,KAAK4C,WACP,CAMA0B,gBAAAA,CAAiBC,EAAShF,GAExB,IAAM2B,EAAOqD,EAAQlB,KACjB9D,IAAM2B,EAAKC,MAAQ,YAAHiB,OAAe7C,IAEnCS,KAAKd,IAAIgC,EACX,CAEAsD,MAAAA,GAEE,OADiB,IAAIpD,EAAapB,KAAMA,KAAKsB,SAC7BlB,OAClB,CAEAqE,QAAAA,GAEE,OADAzE,KAAKwD,iBACE,CACT,EAYF,SAASkB,EAAOC,GACd,OAAKA,EACa,iBAAPA,EAAwBA,EAE5BA,EAAGD,OAHM,IAIlB,CAMA,SAASE,EAAUD,GACjB,OAAOvC,EAAO,MAAOuC,EAAI,IAC3B,CAMA,SAASE,EAAiBF,GACxB,OAAOvC,EAAO,MAAOuC,EAAI,KAC3B,CAMA,SAASG,EAASH,GAChB,OAAOvC,EAAO,MAAOuC,EAAI,KAC3B,CAMA,SAASvC,IAAgB,QAAA2C,EAAAnE,UAAAC,OAANmE,EAAI,IAAAjE,MAAAgE,GAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAJD,EAAIC,GAAArE,UAAAqE,GAErB,OADeD,EAAK1C,KAAKC,GAAMmC,EAAOnC,KAAIG,KAAK,GAEjD,CA0BA,SAASwC,IAAgB,QAAAC,EAAAvE,UAAAC,OAANmE,EAAI,IAAAjE,MAAAoE,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJJ,EAAII,GAAAxE,UAAAwE,GAErB,IAAMtC,EAtBR,SAA8BkC,GAC5B,IAAMlC,EAAOkC,EAAKA,EAAKnE,OAAS,GAEhC,MAAoB,iBAATiC,GAAqBA,EAAKlD,cAAgBT,QACnD6F,EAAKK,OAAOL,EAAKnE,OAAS,EAAG,GACtBiC,GAEA,CAAC,CAEZ,CAaewC,CAAqBN,GAIlC,MAHe,KACVlC,EAAKyC,QAAU,GAAK,MACrBP,EAAK1C,KAAKC,GAAMmC,EAAOnC,KAAIG,KAAK,KAAO,GAE7C,CAMA,SAAS8C,EAAiBb,GACxB,OAAQ,IAAIc,OAAOd,EAAGe,WAAa,KAAMC,KAAK,IAAI9E,OAAS,CAC7D,CAmBA,IAAM+E,EAAa,iDAanB,SAASC,EAAuBC,EAAOC,GAAgB,IAAZC,EAAQD,EAARC,SACrCC,EAAc,EAElB,OAAOH,EAAQxD,KAAK4D,IAMlB,IAJA,IAAMC,EADNF,GAAe,EAEXtB,EAAKD,EAAOwB,GACZE,EAAM,GAEHzB,EAAG9D,OAAS,GAAG,CACpB,IAAMwF,EAAQT,EAAWD,KAAKhB,GAC9B,IAAK0B,EAAO,CACVD,GAAOzB,EACP,KACF,CACAyB,GAAOzB,EAAG2B,UAAU,EAAGD,EAAME,OAC7B5B,EAAKA,EAAG2B,UAAUD,EAAME,MAAQF,EAAM,GAAGxF,QACrB,OAAhBwF,EAAM,GAAG,IAAeA,EAAM,GAEhCD,GAAO,KAAOI,OAAOC,OAAOJ,EAAM,IAAMF,IAExCC,GAAOC,EAAM,GACI,MAAbA,EAAM,IACRJ,IAGN,CACA,OAAOG,CAAG,IACT9D,KAAIqC,GAAM,IAAJvC,OAAQuC,EAAE,OAAKjC,KAAKsD,EAC/B,CAMA,IACMU,EAAW,eACXC,EAAsB,gBACtBC,EAAY,oBACZC,EAAc,yEACdC,EAAmB,eA4BnBC,EAAmB,CACvBC,MAAO,eAAgBC,UAAW,GAE9BC,EAAmB,CACvB/F,MAAO,SACP6F,MAAO,IACPG,IAAK,IACLC,QAAS,MACTC,SAAU,CAACN,IAEPO,EAAoB,CACxBnG,MAAO,SACP6F,MAAO,IACPG,IAAK,IACLC,QAAS,MACTC,SAAU,CAACN,IAaPQ,EAAU,SAASP,EAAOG,GAAuB,IAC/CtH,EAAOS,EACX,CACEa,MAAO,UACP6F,QACAG,MACAE,SAAU,IANgCzG,UAAAC,OAAA,QAAAf,IAAAc,UAAA,GAAAA,UAAA,GAAG,CAAC,GAUlDf,EAAKwH,SAAS/D,KAAK,CACjBnC,MAAO,SAGP6F,MAAO,mDACPG,IAAK,2CACLK,cAAc,EACdP,UAAW,IAEb,IAAMQ,EAAevC,EAEnB,IACA,IACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KAEA,iCACA,qBACA,qBA4BF,OAzBArF,EAAKwH,SAAS/D,KACZ,CAgBE0D,MAAO5E,EACL,OACA,IACAqF,EACA,uBACA,UAGC5H,CACT,EACM6H,EAAsBH,EAAQ,KAAM,KACpCI,EAAuBJ,EAAQ,OAAQ,QACvCK,EAAoBL,EAAQ,IAAK,KACjCM,EAAc,CAClB1G,MAAO,SACP6F,MAAOJ,EACPK,UAAW,GAEPa,EAAgB,CACpB3G,MAAO,SACP6F,MAAOH,EACPI,UAAW,GAEPc,EAAqB,CACzB5G,MAAO,SACP6F,MAAOF,EACPG,UAAW,GAEPe,EAAc,CAClB7G,MAAO,SACP6F,MAAO,kBACPG,IAAK,aACLE,SAAU,CACRN,EACA,CACEC,MAAO,KACPG,IAAK,KACLF,UAAW,EACXI,SAAU,CAACN,MAIXkB,EAAa,CACjB9G,MAAO,QACP6F,MAAON,EACPO,UAAW,GAEPiB,EAAwB,CAC5B/G,MAAO,QACP6F,MAAOL,EACPM,UAAW,GAEPkB,EAAe,CAEnBnB,MAAO,UAAYL,EACnBM,UAAW,GAoBTmB,EAAqBjJ,OAAOC,OAAO,CACrCiJ,UAAW,KACXnB,iBAAkBA,EAClBH,iBAAkBA,EAClBgB,mBAAoBA,EACpBjB,iBAAkBA,EAClBS,QAASA,EACTI,qBAAsBA,EACtBD,oBAAqBA,EACrBI,cAAeA,EACfjB,YAAaA,EACbyB,kBArBwB,SAASzI,GACjC,OAAOV,OAAO6D,OAAOnD,EACnB,CAEE,WAAY0I,CAACC,EAAGC,KAAWA,EAAK1I,KAAK2I,YAAcF,EAAE,EAAE,EAEvD,SAAUG,CAACH,EAAGC,KAAeA,EAAK1I,KAAK2I,cAAgBF,EAAE,IAAIC,EAAKvI,aAAa,GAErF,EAcE0H,kBAAmBA,EACnBlB,SAAUA,EACVkC,iBA7MuB,OA8MvBT,aAAcA,EACdN,YAAaA,EACbjB,UAAWA,EACXiC,mBA/JyB,CACzB7B,MAAO,8IA+JPM,kBAAmBA,EACnBU,YAAaA,EACbc,eA9MqB,+IA+MrBC,QA1Mc,WAAe,IAAdjG,EAAIlC,UAAAC,OAAA,QAAAf,IAAAc,UAAA,GAAAA,UAAA,GAAG,CAAC,EACjBoI,EAAe,YAQrB,OAPIlG,EAAKmG,SACPnG,EAAKkE,MAAQ5E,EACX4G,EACA,OACAlG,EAAKmG,OACL,SAEG3I,EAAU,CACfa,MAAO,OACP6F,MAAOgC,EACP7B,IAAK,IACLF,UAAW,EAEX,WAAYsB,CAACC,EAAGC,KACE,IAAZD,EAAEjC,OAAakC,EAAKvI,aAAa,GAEtC4C,EACL,EAwLEmF,WAAYA,EACZtB,oBAAqBA,EACrBuB,sBAAuBA,IAgCzB,SAASgB,EAAsB7C,EAAO8C,GAErB,MADA9C,EAAM+C,MAAM/C,EAAME,MAAQ,IAEvC4C,EAASjJ,aAEb,CAMA,SAASmJ,EAAexJ,EAAMyJ,QAELxJ,IAAnBD,EAAKgC,YACPhC,EAAKsB,MAAQtB,EAAKgC,iBACXhC,EAAKgC,UAEhB,CAMA,SAAS0H,EAAc1J,EAAM2J,GACtBA,GACA3J,EAAK0J,gBAOV1J,EAAKmH,MAAQ,OAASnH,EAAK0J,cAAcpH,MAAM,KAAKO,KAAK,KAAO,sBAChE7C,EAAK4J,cAAgBP,EACrBrJ,EAAK6J,SAAW7J,EAAK6J,UAAY7J,EAAK0J,qBAC/B1J,EAAK0J,mBAKWzJ,IAAnBD,EAAKoH,YAAyBpH,EAAKoH,UAAY,GACrD,CAMA,SAAS0C,EAAe9J,EAAMyJ,GACvBvI,MAAM6I,QAAQ/J,EAAKuH,WAExBvH,EAAKuH,QAAUlC,KAAUrF,EAAKuH,SAChC,CAMA,SAASyC,EAAahK,EAAMyJ,GAC1B,GAAKzJ,EAAKwG,MAAV,CACA,GAAIxG,EAAKmH,OAASnH,EAAKsH,IAAK,MAAM,IAAInI,MAAM,4CAE5Ca,EAAKmH,MAAQnH,EAAKwG,aACXxG,EAAKwG,KAJW,CAKzB,CAMA,SAASyD,EAAiBjK,EAAMyJ,QAEPxJ,IAAnBD,EAAKoH,YAAyBpH,EAAKoH,UAAY,EACrD,CAIA,IAAM8C,EAAiBA,CAAClK,EAAM2J,KAC5B,GAAK3J,EAAKmK,YAAV,CAGA,GAAInK,EAAKoK,OAAQ,MAAM,IAAIjL,MAAM,0CAEjC,IAAMkL,EAAe/K,OAAO6D,OAAO,CAAC,EAAGnD,GACvCV,OAAOgL,KAAKtK,GAAMP,SAASoB,WAAiBb,EAAKa,EAAI,IAErDb,EAAK6J,SAAWQ,EAAaR,SAC7B7J,EAAKmH,MAAQ5E,EAAO8H,EAAaF,YAAapF,EAAUsF,EAAalD,QACrEnH,EAAKoK,OAAS,CACZhD,UAAW,EACXI,SAAU,CACRlI,OAAO6D,OAAOkH,EAAc,CAAEE,YAAY,MAG9CvK,EAAKoH,UAAY,SAEViD,EAAaF,WAlBS,CAkBE,EAI3BK,EAAkB,CACtB,KACA,MACA,MACA,KACA,MACA,KACA,KACA,OACA,SACA,OACA,SAWF,SAASC,EAAgBC,EAAaC,GAAoD,IAAnCC,EAAS7J,UAAAC,OAAA,QAAAf,IAAAc,UAAA,GAAAA,UAAA,GARlC,UAUtB8J,EAAmBvL,OAAOsB,OAAO,MAiBvC,MAb2B,iBAAhB8J,EACTI,EAAYF,EAAWF,EAAYpI,MAAM,MAChCpB,MAAM6I,QAAQW,GACvBI,EAAYF,EAAWF,GAEvBpL,OAAOgL,KAAKI,GAAajL,SAAQ,SAASmL,GAExCtL,OAAO6D,OACL0H,EACAJ,EAAgBC,EAAYE,GAAYD,EAAiBC,GAE7D,IAEKC,EAYP,SAASC,EAAYF,EAAWG,GAC1BJ,IACFI,EAAcA,EAAYtI,KAAIC,GAAKA,EAAEsI,iBAEvCD,EAAYtL,SAAQ,SAASwL,GAC3B,IAAMC,EAAOD,EAAQ3I,MAAM,KAC3BuI,EAAiBK,EAAK,IAAM,CAACN,EAAWO,EAAgBD,EAAK,GAAIA,EAAK,IACxE,GACF,CACF,CAUA,SAASC,EAAgBF,EAASG,GAGhC,OAAIA,EACKxE,OAAOwE,GAUlB,SAAuBH,GACrB,OAAOT,EAAgBpI,SAAS6I,EAAQD,cAC1C,CATSK,CAAcJ,GAAW,EAAI,CACtC,CAoBA,IAAMK,GAAmB,CAAC,EAKpBC,GAASC,IACbC,EAAQF,MAAMC,EAAQ,EAOlBE,GAAO,SAACF,GAAqB,QAAAG,EAAA5K,UAAAC,OAATmE,EAAI,IAAAjE,MAAAyK,EAAA,EAAAA,EAAA,KAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJzG,EAAIyG,EAAA,GAAA7K,UAAA6K,GAC5BH,EAAQI,IAAI,SAADtJ,OAAUiJ,MAAcrG,EACrC,EAMM2G,GAAaA,CAACC,EAASP,KACvBF,GAAiB,GAAD/I,OAAIwJ,EAAO,KAAAxJ,OAAIiJ,MAEnCC,EAAQI,IAAI,oBAADtJ,OAAqBwJ,EAAO,MAAAxJ,OAAKiJ,IAC5CF,GAAiB,GAAD/I,OAAIwJ,EAAO,KAAAxJ,OAAIiJ,KAAa,EAAI,EAS5CQ,GAAkB,IAAI7M,MA8B5B,SAAS8M,GAAgBjM,EAAMkM,EAAOC,GAQpC,IAR+C,IAAPtL,EAAGsL,EAAHtL,IACpCyF,EAAS,EACP8F,EAAapM,EAAKa,GAElBwL,EAAO,CAAC,EAERC,EAAY,CAAC,EAEV3J,EAAI,EAAGA,GAAKuJ,EAAQlL,OAAQ2B,IACnC2J,EAAU3J,EAAI2D,GAAU8F,EAAWzJ,GACnC0J,EAAK1J,EAAI2D,IAAU,EACnBA,GAAUX,EAAiBuG,EAAQvJ,EAAI,IAIzC3C,EAAKa,GAAOyL,EACZtM,EAAKa,GAAK0L,MAAQF,EAClBrM,EAAKa,GAAK2L,QAAS,CACrB,CA+DA,SAASC,GAAWzM,IAVpB,SAAoBA,GACdA,EAAKsB,OAA+B,iBAAftB,EAAKsB,OAAqC,OAAftB,EAAKsB,QACvDtB,EAAK0M,WAAa1M,EAAKsB,aAChBtB,EAAKsB,MAEhB,CAMEqL,CAAW3M,GAEoB,iBAApBA,EAAK0M,aACd1M,EAAK0M,WAAa,CAAEE,MAAO5M,EAAK0M,aAEL,iBAAlB1M,EAAKwE,WACdxE,EAAKwE,SAAW,CAAEoI,MAAO5M,EAAKwE,WAjElC,SAAyBxE,GACvB,GAAKkB,MAAM6I,QAAQ/J,EAAKmH,OAAxB,CAEA,GAAInH,EAAK6M,MAAQ7M,EAAK2H,cAAgB3H,EAAK8M,YAEzC,MADAvB,GAAM,sEACAS,GAGR,GAA+B,iBAApBhM,EAAK0M,YAA+C,OAApB1M,EAAK0M,WAE9C,MADAnB,GAAM,6BACAS,GAGRC,GAAgBjM,EAAMA,EAAKmH,MAAO,CAAEtG,IAAK,eACzCb,EAAKmH,MAAQnB,EAAuBhG,EAAKmH,MAAO,CAAEhB,SAAU,IAbtB,CAcxC,CAqDE4G,CAAgB/M,GAhDlB,SAAuBA,GACrB,GAAKkB,MAAM6I,QAAQ/J,EAAKsH,KAAxB,CAEA,GAAItH,EAAK6M,MAAQ7M,EAAKgN,YAAchN,EAAKiN,UAEvC,MADA1B,GAAM,gEACAS,GAGR,GAA6B,iBAAlBhM,EAAKwE,UAA2C,OAAlBxE,EAAKwE,SAE5C,MADA+G,GAAM,2BACAS,GAGRC,GAAgBjM,EAAMA,EAAKsH,IAAK,CAAEzG,IAAK,aACvCb,EAAKsH,IAAMtB,EAAuBhG,EAAKsH,IAAK,CAAEnB,SAAU,IAbpB,CActC,CAkCE+G,CAAclN,EAChB,CAoBA,SAASmN,GAAgBC,GAOvB,SAASC,EAAO9M,EAAO+M,GACrB,OAAO,IAAI1H,OACTf,EAAOtE,GACP,KACG6M,EAASG,iBAAmB,IAAM,KAClCH,EAASI,aAAe,IAAM,KAC9BF,EAAS,IAAM,IAEtB,CAeA,MAAMG,EACJ1N,WAAAA,GACEI,KAAKuN,aAAe,CAAC,EAErBvN,KAAK+L,QAAU,GACf/L,KAAKwN,QAAU,EACfxN,KAAKyN,SAAW,CAClB,CAGAC,OAAAA,CAAQ/I,EAAI7B,GACVA,EAAK2K,SAAWzN,KAAKyN,WAErBzN,KAAKuN,aAAavN,KAAKwN,SAAW1K,EAClC9C,KAAK+L,QAAQzI,KAAK,CAACR,EAAM6B,IACzB3E,KAAKwN,SAAWhI,EAAiBb,GAAM,CACzC,CAEAgJ,OAAAA,GAC8B,IAAxB3N,KAAK+L,QAAQlL,SAGfb,KAAK2F,KAAO,IAAM,MAEpB,IAAMiI,EAAc5N,KAAK+L,QAAQzJ,KAAI2B,GAAMA,EAAG,KAC9CjE,KAAK6N,UAAYX,EAAOrH,EAAuB+H,EAAa,CAAE5H,SAAU,OAAQ,GAChFhG,KAAK8N,UAAY,CACnB,CAGAnI,IAAAA,CAAKoI,GACH/N,KAAK6N,UAAUC,UAAY9N,KAAK8N,UAChC,IAAMzH,EAAQrG,KAAK6N,UAAUlI,KAAKoI,GAClC,IAAK1H,EAAS,OAAO,KAGrB,IAAM7D,EAAI6D,EAAM2H,WAAU,CAAC/J,EAAIzB,IAAMA,EAAI,QAAY1C,IAAPmE,IAExCgK,EAAYjO,KAAKuN,aAAa/K,GAKpC,OAFA6D,EAAMhB,OAAO,EAAG7C,GAETrD,OAAO6D,OAAOqD,EAAO4H,EAC9B,EAkCF,MAAMC,EACJtO,WAAAA,GAEEI,KAAKmO,MAAQ,GAEbnO,KAAKoO,aAAe,GACpBpO,KAAKqO,MAAQ,EAEbrO,KAAK8N,UAAY,EACjB9N,KAAKsO,WAAa,CACpB,CAGAC,UAAAA,CAAWhI,GACT,GAAIvG,KAAKoO,aAAa7H,GAAQ,OAAOvG,KAAKoO,aAAa7H,GAEvD,IAAMiI,EAAU,IAAIlB,EAIpB,OAHAtN,KAAKmO,MAAMM,MAAMlI,GAAOjH,SAAQoP,IAAA,IAAAC,EAAAC,EAAAF,EAAA,GAAE/J,EAAEgK,EAAA,GAAE7L,EAAI6L,EAAA,UAAMH,EAAQd,QAAQ/I,EAAI7B,EAAK,IACzE0L,EAAQb,UACR3N,KAAKoO,aAAa7H,GAASiI,EACpBA,CACT,CAEAK,0BAAAA,GACE,OAA2B,IAApB7O,KAAKsO,UACd,CAEAQ,WAAAA,GACE9O,KAAKsO,WAAa,CACpB,CAGAZ,OAAAA,CAAQ/I,EAAI7B,GACV9C,KAAKmO,MAAM7K,KAAK,CAACqB,EAAI7B,IACH,UAAdA,EAAKrD,MAAkBO,KAAKqO,OAClC,CAGA1I,IAAAA,CAAKoI,GACH,IAAMvF,EAAIxI,KAAKuO,WAAWvO,KAAKsO,YAC/B9F,EAAEsF,UAAY9N,KAAK8N,UACnB,IAAItN,EAASgI,EAAE7C,KAAKoI,GAiCpB,GAAI/N,KAAK6O,6BACP,GAAIrO,GAAUA,EAAO+F,QAAUvG,KAAK8N,eAAkB,CACpD,IAAMiB,EAAK/O,KAAKuO,WAAW,GAC3BQ,EAAGjB,UAAY9N,KAAK8N,UAAY,EAChCtN,EAASuO,EAAGpJ,KAAKoI,EACnB,CAWF,OARIvN,IACFR,KAAKsO,YAAc9N,EAAOiN,SAAW,EACjCzN,KAAKsO,aAAetO,KAAKqO,OAE3BrO,KAAK8O,eAIFtO,CACT,EA2IF,GAHKyM,EAAS+B,qBAAoB/B,EAAS+B,mBAAqB,IAG5D/B,EAAS5F,UAAY4F,EAAS5F,SAASpF,SAAS,QAClD,MAAM,IAAIjD,MAAM,6FAMlB,OAFAiO,EAASgC,iBAAmB3O,EAAU2M,EAASgC,kBAAoB,CAAC,GAhFpE,SAASC,EAAYrP,EAAM2J,GACzB,IAAM2F,EAAmCtP,EACzC,GAAIA,EAAKuP,WAAY,OAAOD,EAE5B,CACE9F,EAGAQ,EACAyC,GACAvC,GACAzK,SAAQ+P,GAAOA,EAAIxP,EAAM2J,KAE3ByD,EAAS+B,mBAAmB1P,SAAQ+P,GAAOA,EAAIxP,EAAM2J,KAGrD3J,EAAK4J,cAAgB,KAErB,CACEF,EAGAI,EAEAG,GACAxK,SAAQ+P,GAAOA,EAAIxP,EAAM2J,KAE3B3J,EAAKuP,YAAa,EAElB,IAAIE,EAAiB,KAwCrB,MAvC6B,iBAAlBzP,EAAK6J,UAAyB7J,EAAK6J,SAAS6F,WAIrD1P,EAAK6J,SAAWvK,OAAO6D,OAAO,CAAC,EAAGnD,EAAK6J,UACvC4F,EAAiBzP,EAAK6J,SAAS6F,gBACxB1P,EAAK6J,SAAS6F,UAEvBD,EAAiBA,GAAkB,MAE/BzP,EAAK6J,WACP7J,EAAK6J,SAAWY,EAAgBzK,EAAK6J,SAAUuD,EAASG,mBAG1D+B,EAAMK,iBAAmBtC,EAAOoC,GAAgB,GAE5C9F,IACG3J,EAAKmH,QAAOnH,EAAKmH,MAAQ,SAC9BmI,EAAMM,QAAUvC,EAAOiC,EAAMnI,OACxBnH,EAAKsH,KAAQtH,EAAK6P,iBAAgB7P,EAAKsH,IAAM,SAC9CtH,EAAKsH,MAAKgI,EAAMQ,MAAQzC,EAAOiC,EAAMhI,MACzCgI,EAAMS,cAAgBlL,EAAOyK,EAAMhI,MAAQ,GACvCtH,EAAK6P,gBAAkBlG,EAAOoG,gBAChCT,EAAMS,gBAAkB/P,EAAKsH,IAAM,IAAM,IAAMqC,EAAOoG,gBAGtD/P,EAAKuH,UAAS+H,EAAMU,UAAY3C,EAAuCrN,EAAKuH,UAC3EvH,EAAKwH,WAAUxH,EAAKwH,SAAW,IAEpCxH,EAAKwH,SAAW,GAAGjF,UAAUvC,EAAKwH,SAAS/E,KAAI,SAASwN,GACtD,OAoDN,SAA2BjQ,GAUzB,OATIA,EAAKkQ,WAAalQ,EAAKmQ,iBACzBnQ,EAAKmQ,eAAiBnQ,EAAKkQ,SAASzN,KAAI,SAAS2N,GAC/C,OAAO3P,EAAUT,EAAM,CAAEkQ,SAAU,MAAQE,EAC7C,KAMEpQ,EAAKmQ,eACAnQ,EAAKmQ,eAOVE,GAAmBrQ,GACdS,EAAUT,EAAM,CAAEoK,OAAQpK,EAAKoK,OAAS3J,EAAUT,EAAKoK,QAAU,OAGtE9K,OAAOO,SAASG,GACXS,EAAUT,GAIZA,CACT,CAhFasQ,CAAwB,SAANL,EAAejQ,EAAOiQ,EACjD,KACAjQ,EAAKwH,SAAS/H,SAAQ,SAASwQ,GAAKZ,EAA+BY,EAAIX,EAAQ,IAE3EtP,EAAKoK,QACPiF,EAAYrP,EAAKoK,OAAQT,GAG3B2F,EAAMX,QA1HR,SAAwB3O,GACtB,IAAMuQ,EAAK,IAAIlC,EAWf,OATArO,EAAKwH,SAAS/H,SAAQ+Q,GAAQD,EAAG1C,QAAQ2C,EAAKrJ,MAAO,CAAEsJ,KAAMD,EAAM5Q,KAAM,YAErEI,EAAK+P,eACPQ,EAAG1C,QAAQ7N,EAAK+P,cAAe,CAAEnQ,KAAM,QAErCI,EAAKuH,SACPgJ,EAAG1C,QAAQ7N,EAAKuH,QAAS,CAAE3H,KAAM,YAG5B2Q,CACT,CA6GkBG,CAAepB,GACxBA,CACT,CAYOD,CAA+BjC,EACxC,CAaA,SAASiD,GAAmBrQ,GAC1B,QAAKA,IAEEA,EAAK6P,gBAAkBQ,GAAmBrQ,EAAKoK,QACxD,CA4CA,MAAMuG,WAA2BxR,MAC/BY,WAAAA,CAAY6Q,EAAQC,GAClBvM,MAAMsM,GACNzQ,KAAKT,KAAO,qBACZS,KAAK0Q,KAAOA,CACd,EAgCF,IAAMC,GAASxQ,EACTyQ,GAAUtQ,EACVuQ,GAAWC,OAAO,WAOlBC,GAAO,SAASC,GAGpB,IAAMC,EAAY9R,OAAOsB,OAAO,MAE1ByQ,EAAU/R,OAAOsB,OAAO,MAExB0Q,EAAU,GAIZC,GAAY,EACVC,EAAqB,sFAErBC,EAAqB,CAAEC,mBAAmB,EAAMhS,KAAM,aAAc8H,SAAU,IAKhF/F,EAAU,CACZkQ,qBAAqB,EACrBC,oBAAoB,EACpBC,cAAe,qBACfC,iBAAkB,8BAClBnQ,YAAa,QACboQ,YAAa,WACbX,UAAW,KAGXY,UAAW3N,GASb,SAAS4N,EAAmBC,GAC1B,OAAOzQ,EAAQoQ,cAAcM,KAAKD,EACpC,CA+CA,SAASE,EAAUC,EAAoBC,EAAeC,GACpD,IAAIC,EAAO,GACPN,EAAe,GACU,iBAAlBI,GACTE,EAAOH,EACPE,EAAiBD,EAAcC,eAC/BL,EAAeI,EAAclF,WAG7BtB,GAAW,SAAU,uDACrBA,GAAW,SAAU,yGACrBoG,EAAeG,EACfG,EAAOF,QAKcrS,IAAnBsS,IAAgCA,GAAiB,GAGrD,IAAME,EAAU,CACdD,OACApF,SAAU8E,GAIZQ,EAAK,mBAAoBD,GAIzB,IAAM9R,EAAS8R,EAAQ9R,OACnB8R,EAAQ9R,OACRgS,EAAWF,EAAQrF,SAAUqF,EAAQD,KAAMD,GAM/C,OAJA5R,EAAO6R,KAAOC,EAAQD,KAEtBE,EAAK,kBAAmB/R,GAEjBA,CACT,CAWA,SAASgS,EAAWT,EAAcU,EAAiBL,EAAgBM,GACjE,IAAMC,EAAcxT,OAAOsB,OAAO,MAYlC,SAASmS,IACP,GAAKxP,EAAIsG,SAAT,CAKA,IAAIoE,EAAY,EAChB1K,EAAIoM,iBAAiB1B,UAAY,EAIjC,IAHA,IAZyB+E,EAYrBxM,EAAQjD,EAAIoM,iBAAiB7J,KAAKmN,GAClCC,EAAM,GAEH1M,GAAO,CACZ0M,GAAOD,EAAWxM,UAAUwH,EAAWzH,EAAME,OAC7C,IAAMyM,EAAO/F,EAASG,iBAAmB/G,EAAM,GAAGwE,cAAgBxE,EAAM,GAClEtG,GAlBiB8S,EAkBOG,EAAL5P,EAjBfsG,SAASmJ,IAkBnB,GAAI9S,EAAM,CACR,IAAAkT,EAAArE,EAAiC7O,EAAI,GAA9BmT,EAAID,EAAA,GAAEE,EAAgBF,EAAA,GAM7B,GALA1O,EAAQ7C,QAAQqR,GAChBA,EAAM,GAENJ,EAAYK,IAASL,EAAYK,IAAS,GAAK,EAC3CL,EAAYK,IAjLD,IAiL4B/L,GAAakM,GACpDD,EAAKlR,WAAW,KAGlB+Q,GAAO1M,EAAM,OACR,CACL,IAAM+M,EAAWnG,EAASgC,iBAAiBiE,IAASA,EACpDG,EAAYhN,EAAM,GAAI+M,EACxB,CACF,MACEL,GAAO1M,EAAM,GAEfyH,EAAY1K,EAAIoM,iBAAiB1B,UACjCzH,EAAQjD,EAAIoM,iBAAiB7J,KAAKmN,EACpC,CACAC,GAAOD,EAAWxM,UAAUwH,GAC5BvJ,EAAQ7C,QAAQqR,EAjChB,MAFExO,EAAQ7C,QAAQoR,EAoCpB,CA4BA,SAASQ,IACgB,MAAnBlQ,EAAImQ,YA3BV,WACE,GAAmB,KAAfT,EAAJ,CAEA,IAAItS,EAAS,KAEb,GAA+B,iBAApB4C,EAAImQ,YAA0B,CACvC,IAAKtC,EAAU7N,EAAImQ,aAEjB,YADAhP,EAAQ7C,QAAQoR,GAGlBtS,EAASgS,EAAWpP,EAAImQ,YAAaT,GAAY,EAAMU,EAAcpQ,EAAImQ,cACzEC,EAAcpQ,EAAImQ,aAA4C/S,EAAOiT,IACvE,MACEjT,EAASkT,EAAcZ,EAAY1P,EAAImQ,YAAY1S,OAASuC,EAAImQ,YAAc,MAO5EnQ,EAAI6D,UAAY,IAClBA,GAAazG,EAAOyG,WAEtB1C,EAAQD,iBAAiB9D,EAAOmT,SAAUnT,EAAOyM,SAtBpB,CAuB/B,CAII2G,GAEAhB,IAEFE,EAAa,EACf,CAMA,SAASO,EAAYvI,EAAS3J,GACZ,KAAZ2J,IAEJvG,EAAQH,WAAWjD,GACnBoD,EAAQ7C,QAAQoJ,GAChBvG,EAAQF,WACV,CAMA,SAASwP,EAAe1S,EAAOkF,GAG7B,IAFA,IAAI7D,EAAI,EACFsR,EAAMzN,EAAMxF,OAAS,EACpB2B,GAAKsR,GACV,GAAK3S,EAAMiL,MAAM5J,GAAjB,CACA,IAAMuR,EAAQ9G,EAASgC,iBAAiB9N,EAAMqB,KAAOrB,EAAMqB,GACrDb,EAAO0E,EAAM7D,GACfuR,EACFV,EAAY1R,EAAMoS,IAElBjB,EAAanR,EACbiR,IACAE,EAAa,IAEftQ,GAVsC,MAAfA,GAY3B,CAMA,SAASwR,EAAanU,EAAMwG,GAiB1B,OAhBIxG,EAAKsB,OAA+B,iBAAftB,EAAKsB,OAC5BoD,EAAQ3C,SAASqL,EAASgC,iBAAiBpP,EAAKsB,QAAUtB,EAAKsB,OAE7DtB,EAAK0M,aAEH1M,EAAK0M,WAAWE,OAClB4G,EAAYP,EAAY7F,EAASgC,iBAAiBpP,EAAK0M,WAAWE,QAAU5M,EAAK0M,WAAWE,OAC5FqG,EAAa,IACJjT,EAAK0M,WAAWF,SAEzBwH,EAAehU,EAAK0M,WAAYlG,GAChCyM,EAAa,KAIjB1P,EAAMjE,OAAOsB,OAAOZ,EAAM,CAAE2J,OAAQ,CAAEpJ,MAAOgD,IAE/C,CAQA,SAAS6Q,EAAUpU,EAAMwG,EAAO6N,GAC9B,IAAIC,EAn5CV,SAAoBxP,EAAIyP,GACtB,IAAM/N,EAAQ1B,GAAMA,EAAGgB,KAAKyO,GAC5B,OAAO/N,GAAyB,IAAhBA,EAAME,KACxB,CAg5CoBvE,CAAWnC,EAAK8P,MAAOuE,GAErC,GAAIC,EAAS,CACX,GAAItU,EAAK,UAAW,CAClB,IAAM4I,EAAO,IAAI9I,EAASE,GAC1BA,EAAK,UAAUwG,EAAOoC,GAClBA,EAAKxI,iBAAgBkU,GAAU,EACrC,CAEA,GAAIA,EAAS,CACX,KAAOtU,EAAKuK,YAAcvK,EAAK2J,QAC7B3J,EAAOA,EAAK2J,OAEd,OAAO3J,CACT,CACF,CAGA,GAAIA,EAAK6P,eACP,OAAOuE,EAAUpU,EAAK2J,OAAQnD,EAAO6N,EAEzC,CAOA,SAASG,EAASD,GAChB,OAA+B,IAA3BhR,EAAIoL,QAAQF,YAGdwE,GAAcsB,EAAO,GACd,IAIPE,GAA2B,EACpB,EAEX,CAyCA,SAASC,EAAWlO,GAClB,IAAM+N,EAAS/N,EAAM,GACf6N,EAAqBzB,EAAgBnM,UAAUD,EAAME,OAErDiO,EAAUP,EAAU7Q,EAAKiD,EAAO6N,GACtC,IAAKM,EAAW,OAAO3D,GAEvB,IAAM4D,EAASrR,EACXA,EAAIiB,UAAYjB,EAAIiB,SAASoI,OAC/B6G,IACAD,EAAYe,EAAQhR,EAAIiB,SAASoI,QACxBrJ,EAAIiB,UAAYjB,EAAIiB,SAASgI,QACtCiH,IACAO,EAAezQ,EAAIiB,SAAUgC,IACpBoO,EAAO/H,KAChBoG,GAAcsB,GAERK,EAAO3H,WAAa2H,EAAO5H,aAC/BiG,GAAcsB,GAEhBd,IACImB,EAAO5H,aACTiG,EAAasB,IAGjB,GACMhR,EAAIjC,OACNoD,EAAQ3B,YAELQ,EAAIsJ,MAAStJ,EAAImQ,cACpBtM,GAAa7D,EAAI6D,WAEnB7D,EAAMA,EAAIoG,aACHpG,IAAQoR,EAAQhL,QAIzB,OAHIgL,EAAQvK,QACV+J,EAAaQ,EAAQvK,OAAQ5D,GAExBoO,EAAO3H,UAAY,EAAIsH,EAAOvT,MACvC,CAaA,IAAI6T,EAAY,CAAC,EAQjB,SAASC,EAAcC,EAAiBvO,GACtC,IAAM+N,EAAS/N,GAASA,EAAM,GAK9B,GAFAyM,GAAc8B,EAEA,MAAVR,EAEF,OADAd,IACO,EAOT,GAAuB,UAAnBoB,EAAUjV,MAAmC,QAAf4G,EAAM5G,MAAkBiV,EAAUnO,QAAUF,EAAME,OAAoB,KAAX6N,EAAe,CAG1G,GADAtB,GAAcL,EAAgBhE,MAAMpI,EAAME,MAAOF,EAAME,MAAQ,IAC1D6K,EAAW,CAEd,IAAMyD,EAAM,IAAI7V,MAAM,wBAADoD,OAAyB2P,EAAY,MAG1D,MAFA8C,EAAI9C,aAAeA,EACnB8C,EAAIC,QAAUJ,EAAUpE,KAClBuE,CACR,CACA,OAAO,CACT,CAGA,GAFAH,EAAYrO,EAEO,UAAfA,EAAM5G,KACR,OA1HJ,SAAsB4G,GAOpB,IANA,IAAM+N,EAAS/N,EAAM,GACf0O,EAAU1O,EAAMiK,KAEhB7H,EAAO,IAAI9I,EAASoV,GAG1BC,EAAA,EAAAC,EADwB,CAACF,EAAQtL,cAAesL,EAAQ,aACxBC,EAAAC,EAAApU,OAAAmU,IAAE,CAA7B,IAAME,EAAED,EAAAD,GACX,GAAKE,IACLA,EAAG7O,EAAOoC,GACNA,EAAKxI,gBAAgB,OAAOoU,EAASD,EAC3C,CAcA,OAZIW,EAAQrI,KACVoG,GAAcsB,GAEVW,EAAQvN,eACVsL,GAAcsB,GAEhBd,IACKyB,EAAQpI,aAAgBoI,EAAQvN,eACnCsL,EAAasB,IAGjBJ,EAAae,EAAS1O,GACf0O,EAAQpI,YAAc,EAAIyH,EAAOvT,MAC1C,CAgGWsU,CAAa9O,GACf,GAAmB,YAAfA,EAAM5G,OAAuB2S,EAAgB,CAGtD,IAAMyC,EAAM,IAAI7V,MAAM,mBAAqBoV,EAAS,gBAAkBhR,EAAIjC,OAAS,aAAe,KAElG,MADA0T,EAAIhV,KAAOuD,EACLyR,CACR,CAAO,GAAmB,QAAfxO,EAAM5G,KAAgB,CAC/B,IAAM2V,EAAYb,EAAWlO,GAC7B,GAAI+O,IAAcvE,GAChB,OAAOuE,CAEX,CAKA,GAAmB,YAAf/O,EAAM5G,MAAiC,KAAX2U,EAE9B,OAAO,EAOT,GAAIiB,EAAa,KAAUA,EAA2B,EAAdhP,EAAME,MAE5C,MADY,IAAIvH,MAAM,6DAaxB,OADA8T,GAAcsB,EACPA,EAAOvT,MAChB,CAEA,IAAMoM,EAAWqI,EAAYvD,GAC7B,IAAK9E,EAEH,MADA7B,GAAMiG,EAAmBhR,QAAQ,KAAM0R,IACjC,IAAI/S,MAAM,sBAAwB+S,EAAe,KAGzD,IAAMwD,EAAKvI,GAAgBC,GACvBzM,EAAS,GAET4C,EAAMsP,GAAgB6C,EAEpB/B,EAAgB,CAAC,EACjBjP,EAAU,IAAIjD,EAAQuQ,UAAUvQ,IAxGtC,WAEE,IADA,IAAMkU,EAAO,GACJC,EAAUrS,EAAKqS,IAAYxI,EAAUwI,EAAUA,EAAQjM,OAC1DiM,EAAQtU,OACVqU,EAAKE,QAAQD,EAAQtU,OAGzBqU,EAAKlW,SAAQqW,GAAQpR,EAAQ3C,SAAS+T,IACxC,CAiGAC,GACA,IAAI9C,EAAa,GACb7L,EAAY,EACZV,EAAQ,EACR8O,EAAa,EACbf,GAA2B,EAE/B,IACE,GAAKrH,EAAS4I,aAyBZ5I,EAAS4I,aAAapD,EAAiBlO,OAzBb,CAG1B,IAFAnB,EAAIoL,QAAQM,gBAEH,CACPuG,IACIf,EAGFA,GAA2B,EAE3BlR,EAAIoL,QAAQM,cAEd1L,EAAIoL,QAAQV,UAAYvH,EAExB,IAAMF,EAAQjD,EAAIoL,QAAQ7I,KAAK8M,GAG/B,IAAKpM,EAAO,MAEZ,IACMyP,EAAiBnB,EADHlC,EAAgBnM,UAAUC,EAAOF,EAAME,OACTF,GAClDE,EAAQF,EAAME,MAAQuP,CACxB,CACAnB,EAAclC,EAAgBnM,UAAUC,GAC1C,CAOA,OAHAhC,EAAQE,WACRjE,EAAS+D,EAAQC,SAEV,CACLyI,SAAU8E,EACV3R,MAAOI,EACPyG,YACAG,SAAS,EACTuM,SAAUpP,EACVkP,KAAMrQ,EAEV,CAAE,MAAOyR,GACP,GAAIA,EAAIxJ,SAAWwJ,EAAIxJ,QAAQpJ,SAAS,WACtC,MAAO,CACLgL,SAAU8E,EACV3R,MAAOuQ,GAAO8B,GACdrL,SAAS,EACTH,UAAW,EACX8O,WAAY,CACV1K,QAASwJ,EAAIxJ,QACb9E,QACA+L,QAASG,EAAgBhE,MAAMlI,EAAQ,IAAKA,EAAQ,KACpD1G,KAAMgV,EAAIhV,KACVmW,YAAaxV,GAEfmT,SAAUpP,GAEP,GAAI6M,EACT,MAAO,CACLnE,SAAU8E,EACV3R,MAAOuQ,GAAO8B,GACdrL,SAAS,EACTH,UAAW,EACXgP,YAAapB,EACblB,SAAUpP,EACVkP,KAAMrQ,GAGR,MAAMyR,CAEV,CACF,CAmCA,SAASnB,EAAcrB,EAAM6D,GAC3BA,EAAiBA,GAAkB5U,EAAQ2P,WAAa9R,OAAOgL,KAAK8G,GACpE,IAAMkF,EA5BR,SAAiC9D,GAC/B,IAAM7R,EAAS,CACbJ,MAAOuQ,GAAO0B,GACdjL,SAAS,EACTH,UAAW,EACXwM,KAAMnC,EACNqC,SAAU,IAAIrS,EAAQuQ,UAAUvQ,IAGlC,OADAd,EAAOmT,SAASjS,QAAQ2Q,GACjB7R,CACT,CAkBoB4V,CAAwB/D,GAEpCgE,EAAUH,EAAeI,OAAOhB,GAAagB,OAAOC,GAAejU,KAAI/C,GAC3EiT,EAAWjT,EAAM8S,GAAM,KAEzBgE,EAAQX,QAAQS,GAEhB,IAqBAK,EAAA5H,EArBeyH,EAAQI,MAAK,CAACC,EAAGC,KAE9B,GAAID,EAAEzP,YAAc0P,EAAE1P,UAAW,OAAO0P,EAAE1P,UAAYyP,EAAEzP,UAIxD,GAAIyP,EAAEzJ,UAAY0J,EAAE1J,SAAU,CAC5B,GAAIqI,EAAYoB,EAAEzJ,UAAU2J,aAAeD,EAAE1J,SAC3C,OAAO,EACF,GAAIqI,EAAYqB,EAAE1J,UAAU2J,aAAeF,EAAEzJ,SAClD,OAAQ,CAEZ,CAMA,OAAO,CAAC,IAGuB,GAA1B4J,EAAIL,EAAA,GAAEM,EAAUN,EAAA,GAGjBhW,EAASqW,EAGf,OAFArW,EAAOsW,WAAaA,EAEbtW,CACT,CAqBA,SAASuW,EAAiBC,GAExB,IACM/J,EApoBR,SAAuBgK,GACrB,IAAIC,EAAUD,EAAMpV,UAAY,IAEhCqV,GAAWD,EAAME,WAAaF,EAAME,WAAWtV,UAAY,GAG3D,IAAMwE,EAAQ/E,EAAQqQ,iBAAiBhM,KAAKuR,GAC5C,GAAI7Q,EAAO,CACT,IAAM4G,EAAWqI,EAAYjP,EAAM,IAKnC,OAJK4G,IACH1B,GAAK8F,EAAmBhR,QAAQ,KAAMgG,EAAM,KAC5CkF,GAAK,oDAAqD0L,IAErDhK,EAAW5G,EAAM,GAAK,cAC/B,CAEA,OAAO6Q,EACJ/U,MAAM,OACNiV,MAAMC,GAAWvF,EAAmBuF,IAAW/B,EAAY+B,IAChE,CAinBmBC,CAAcN,GAE/B,IAAIlF,EAAmB7E,GAKvB,GAHAsF,EAAK,0BACH,CAAEtO,GAAI+S,EAAS/J,aAEb+J,EAAQO,QAAQC,YAClBlM,EAAQI,IAAI,yFAA0FsL,OADxG,CAUA,GAAIA,EAAQjU,SAASlC,OAAS,IACvBS,EAAQkQ,sBACXlG,EAAQC,KAAK,iGACbD,EAAQC,KAAK,6DACbD,EAAQC,KAAK,oCACbD,EAAQC,KAAKyL,IAEX1V,EAAQmQ,oBAKV,MAJY,IAAIjB,GACd,mDACAwG,EAAQS,WAOd,IAAM9V,EADCqV,EACWU,YACZlX,EAASyM,EAAWgF,EAAUtQ,EAAM,CAAEsL,WAAUmF,gBAAgB,IAAUsB,EAAc/R,GAE9FqV,EAAQS,UAAYjX,EAAOJ,MAC3B4W,EAAQO,QAAQC,YAAc,MArDhC,SAAyBR,EAASW,EAAaC,GAC7C,IAAM3K,EAAY0K,GAAezG,EAAQyG,IAAiBC,EAE1DZ,EAAQa,UAAU3Y,IAAI,QACtB8X,EAAQa,UAAU3Y,IAAI,YAADkD,OAAa6K,GACpC,CAiDE6K,CAAgBd,EAAS/J,EAAUzM,EAAOyM,UAC1C+J,EAAQxW,OAAS,CACfyM,SAAUzM,EAAOyM,SAEjBtI,GAAInE,EAAOyG,UACXA,UAAWzG,EAAOyG,WAEhBzG,EAAOsW,aACTE,EAAQF,WAAa,CACnB7J,SAAUzM,EAAOsW,WAAW7J,SAC5BhG,UAAWzG,EAAOsW,WAAW7P,YAIjCsL,EAAK,yBAA0B,CAAEtO,GAAI+S,EAASxW,SAAQmB,QA3CtD,CA4CF,CAuBA,IAAIoW,GAAiB,EAKrB,SAASC,IAEqB,YAAxBC,SAASC,WAKED,SAASE,iBAAiB7W,EAAQsQ,aAC1CtS,QAAQyX,GALbgB,GAAiB,CAMrB,CAmEA,SAASzC,EAAY/V,GAEnB,OADAA,GAAQA,GAAQ,IAAIsL,cACboG,EAAU1R,IAAS0R,EAAUC,EAAQ3R,GAC9C,CAOA,SAAS6Y,EAAgBC,EAASC,GAAoB,IAAhBvG,EAAYuG,EAAZvG,aACX,iBAAdsG,IACTA,EAAY,CAACA,IAEfA,EAAU/Y,SAAQiZ,IAAWrH,EAAQqH,EAAM1N,eAAiBkH,CAAY,GAC1E,CAMA,SAASwE,EAAchX,GACrB,IAAMiZ,EAAOlD,EAAY/V,GACzB,OAAOiZ,IAASA,EAAKjH,iBACvB,CAgDA,SAASgB,EAAKkG,EAAOzT,GACnB,IAAMkQ,EAAKuD,EACXtH,EAAQ7R,SAAQ,SAASoZ,GACnBA,EAAOxD,IACTwD,EAAOxD,GAAIlQ,EAEf,GACF,CA+CA,IAAK,IAAMtE,IAzLW,oBAAXiY,QAA0BA,OAAOC,kBAC1CD,OAAOC,iBAAiB,oBAP1B,WAEMb,GAAgBC,GACtB,IAIoD,GAuJpD7Y,OAAO6D,OAAOgO,EAAM,CAClBiB,YACAyB,gBACAsE,eACAjB,mBAEA8B,eAdF,SAAiC5U,GAI/B,OAHA0H,GAAW,SAAU,oDACrBA,GAAW,SAAU,oCAEdoL,EAAiB9S,EAC1B,EAUE6U,UArMF,SAAmBC,GACjBzX,EAAUsP,GAAQtP,EAASyX,EAC7B,EAoMEC,iBAjMuBA,KACvBhB,IACArM,GAAW,SAAU,0DAA0D,EAgM/EsN,uBA5LF,WACEjB,IACArM,GAAW,SAAU,gEACvB,EA0LEuN,iBAxJF,SAA0BnH,EAAcoH,GACtC,IAAIX,EAAO,KACX,IACEA,EAAOW,EAAmBnI,EAC5B,CAAE,MAAOoI,GAGP,GAFAhO,GAAM,wDAAwD/K,QAAQ,KAAM0R,KAEvEX,EAAa,MAAMgI,EAAkBhO,GAAMgO,GAKhDZ,EAAOlH,CACT,CAEKkH,EAAKjZ,OAAMiZ,EAAKjZ,KAAOwS,GAC5Bd,EAAUc,GAAgByG,EAC1BA,EAAKa,cAAgBF,EAAmBG,KAAK,KAAMtI,GAE/CwH,EAAKtH,SACPkH,EAAgBI,EAAKtH,QAAS,CAAEa,gBAEpC,EAmIEwH,mBA5HF,SAA4BxH,UACnBd,EAAUc,GACjB,IAAK,IAALyH,EAAA,EAAAC,EAAoBta,OAAOgL,KAAK+G,GAAQsI,EAAAC,EAAA5Y,OAAA2Y,IAAE,CAArC,IAAMjB,EAAKkB,EAAAD,GACVtI,EAAQqH,KAAWxG,UACdb,EAAQqH,EAEnB,CACF,EAsHEmB,cAjHF,WACE,OAAOva,OAAOgL,KAAK8G,EACrB,EAgHEqE,cACA8C,kBACA7B,gBACA3F,WACA+I,UA1DF,SAAmBjB,IArBnB,SAA0BA,GAEpBA,EAAO,2BAA6BA,EAAO,6BAC7CA,EAAO,2BAA8B3Y,IACnC2Y,EAAO,yBACLvZ,OAAO6D,OAAO,CAAEiU,MAAOlX,EAAKkE,IAAMlE,GACnC,GAGD2Y,EAAO,0BAA4BA,EAAO,4BAC5CA,EAAO,0BAA6B3Y,IAClC2Y,EAAO,wBACLvZ,OAAO6D,OAAO,CAAEiU,MAAOlX,EAAKkE,IAAMlE,GACnC,EAGP,CAME6Z,CAAiBlB,GACjBvH,EAAQ7N,KAAKoV,EACf,EAwDEmB,aAnDF,SAAsBnB,GACpB,IAAMnS,EAAQ4K,EAAQ2I,QAAQpB,IACf,IAAXnS,GACF4K,EAAQ9L,OAAOkB,EAAO,EAE1B,IAiDAyK,EAAK+I,UAAY,WAAa3I,GAAY,CAAO,EACjDJ,EAAKgJ,SAAW,WAAa5I,GAAY,CAAM,EAC/CJ,EAAKiJ,cAn/BO,SAq/BZjJ,EAAK9K,MAAQ,CACX9D,OAAQA,EACRwC,UAAWA,EACXM,OAAQA,EACRJ,SAAUA,EACVD,iBAAkBA,GAGFuD,EAEU,iBAAfA,EAAM1H,IAEfhC,EAAW0J,EAAM1H,IAOrB,OAFAvB,OAAO6D,OAAOgO,EAAM5I,GAEb4I,CACT,EAGMiB,GAAYlB,GAAK,CAAC,GAIxBkB,GAAUiI,YAAc,IAAMnJ,GAAK,CAAC,GAEpCoJ,EAAOC,QAAUnI,GACjBA,GAAUoI,YAAcpI,GACxBA,GAAUqI,QAAUrI,E,wECjiFhBsI,E,MAA0B,GAA4B,KAE1DA,EAAwBjX,KAAK,CAAC6W,EAAOK,GAAI,svEAAuvE,GAAG,CAAC,QAAU,EAAE,QAAU,CAAC,2DAA2D,MAAQ,GAAG,SAAW,mlBAAmlB,eAAiB,CAAC,uvEAAuvE,WAAa,MAErvK,K,sFCJA,E,wHCQIlZ,EAAU,CAAC,EAEfA,EAAQmZ,kBAAoB,IAC5BnZ,EAAQoZ,cAAgB,IACxBpZ,EAAQqZ,OAAS,SAAqB3D,GACF,IAAIxN,EAASyO,SAAS2C,cAAc,QAEhCC,EACAlC,OAAOmC,kCAEND,EAEMA,EAAoBE,YAC3BvR,EAAOwR,aAAahE,EAAS6D,EAAoBE,aAEjDvR,EAAOyR,YAAYjE,GAJnBxN,EAAOwR,aAAahE,EAASxN,EAAO0R,YAQxCvC,OAAOmC,kCAAoC9D,CAC/C,EAChC1V,EAAQ6Z,OAAS,IACjB7Z,EAAQ8Z,mBAAqB,IAEhB,IAAI,IAAS9Z,GAKJ,KAAW,IAAQ+Z,QAAS,IAAQA,OCxC1D,IA6CMC,EAAO,CACX,IACA,OACA,UACA,UACA,QACA,QACA,IACA,aACA,OACA,SACA,SACA,UACA,OACA,OACA,KACA,MACA,UACA,MACA,MACA,KACA,KACA,KACA,WACA,aACA,SACA,SACA,OACA,KACA,KACA,KACA,KACA,KACA,KACA,SACA,SACA,OACA,IACA,SACA,MACA,QACA,MACA,MACA,QACA,SACA,KACA,OACA,OACA,OACA,MACA,SACA,KACA,IACA,IACA,QACA,OACA,UACA,OACA,SACA,UACA,MACA,QACA,QACA,KACA,WACA,QACA,KACA,QACA,OACA,KACA,KACA,MACA,SAGIC,EAAiB,CACrB,YACA,cACA,eACA,QACA,cACA,cACA,sBACA,gBACA,eACA,eACA,gBACA,OACA,SACA,QACA,kBACA,aACA,cACA,iBACA,kBACA,UACA,uBACA,mBACA,yBACA,+BACA,aACA,OACA,YACA,SACA,QAEA,YACA,YACA,aACA,cAIIC,EAAiB,CACrB,SACA,WACA,QACA,UACA,UACA,UACA,UACA,MACA,WACA,OACA,QACA,UACA,QACA,cACA,gBACA,aACA,SACA,QACA,gBACA,eACA,MACA,OACA,eACA,QACA,gBACA,WACA,UACA,KACA,OACA,aACA,eACA,OACA,OACA,aACA,MACA,YACA,UACA,iBACA,eACA,mBACA,cACA,aACA,eACA,WACA,eACA,OACA,oBACA,YACA,aACA,WACA,QACA,OACA,QACA,SACA,gBACA,eACA,QACA,UACA,SAIIC,EAAkB,CACtB,QACA,WACA,SACA,MACA,aACA,eACA,aACA,gBACA,SACA,OACA,cACA,YACA,UACA,kBAGIC,EAAa,CACjB,gBACA,cACA,aACA,MACA,YACA,kBACA,sBACA,qBACA,sBACA,4BACA,iBACA,uBACA,4BACA,sBACA,aACA,wBACA,wBACA,kBACA,mBACA,mBACA,oBACA,sBACA,oBACA,kBACA,aACA,SACA,eACA,qBACA,mBACA,yBACA,yBACA,yBACA,qBACA,2BACA,2BACA,2BACA,qBACA,qBACA,gBACA,sBACA,4BACA,6BACA,sBACA,sBACA,kBACA,eACA,eACA,sBACA,sBACA,qBACA,sBACA,qBACA,gBACA,sBACA,oBACA,0BACA,0BACA,0BACA,sBACA,4BACA,4BACA,4BACA,sBACA,sBACA,cACA,oBACA,oBACA,oBACA,gBACA,eACA,qBACA,qBACA,qBACA,iBACA,eACA,aACA,mBACA,yBACA,0BACA,mBACA,mBACA,eACA,SACA,uBACA,aACA,aACA,cACA,eACA,eACA,eACA,cACA,QACA,OACA,YACA,YACA,QACA,eACA,cACA,aACA,cACA,oBACA,oBACA,oBACA,cACA,eACA,UACA,UACA,UACA,qBACA,oBACA,gBACA,MACA,YACA,aACA,SACA,YACA,UACA,cACA,SACA,OACA,aACA,iBACA,YACA,YACA,cACA,YACA,QACA,OACA,OACA,eACA,cACA,wBACA,eACA,yBACA,YACA,mBACA,iBACA,eACA,aACA,iBACA,eACA,oBACA,0BACA,yBACA,uBACA,wBACA,0BACA,cACA,MACA,6BACA,OACA,YACA,oBACA,iBACA,iBACA,cACA,kBACA,oBACA,WACA,WACA,eACA,iBACA,gBACA,sBACA,wBACA,qBACA,sBACA,SACA,UACA,OACA,oBACA,kBACA,mBACA,WACA,cACA,YACA,kBACA,OACA,iBACA,aACA,cACA,aACA,mBACA,sBACA,kBACA,SACA,eACA,mBACA,qBACA,gBACA,gBACA,oBACA,sBACA,cACA,eACA,aACA,QACA,OACA,cACA,mBACA,qBACA,qBACA,oBACA,qBACA,oBACA,YACA,iBACA,aACA,YACA,cACA,gBACA,cACA,YACA,YACA,iBACA,aACA,kBACA,YACA,iBACA,aACA,kBACA,YACA,iBACA,WACA,YACA,WACA,YACA,SACA,OACA,SACA,aACA,kBACA,UACA,QACA,UACA,UACA,gBACA,iBACA,gBACA,gBACA,WACA,gBACA,aACA,aACA,UACA,gBACA,oBACA,sBACA,iBACA,iBACA,qBACA,uBACA,eACA,gBACA,cACA,mBACA,oBACA,oBACA,QACA,cACA,eACA,cACA,qBACA,iBACA,WACA,SACA,SACA,OACA,aACA,cACA,QACA,UACA,gBACA,sBACA,0BACA,4BACA,uBACA,uBACA,2BACA,6BACA,qBACA,sBACA,oBACA,iBACA,uBACA,2BACA,6BACA,wBACA,wBACA,4BACA,8BACA,sBACA,uBACA,qBACA,oBACA,mBACA,mBACA,kBACA,mBACA,kBACA,wBACA,eACA,gBACA,QACA,WACA,MACA,WACA,eACA,aACA,iBACA,kBACA,uBACA,kBACA,wBACA,uBACA,wBACA,gBACA,sBACA,yBACA,sBACA,cACA,eACA,mBACA,gBACA,iBACA,cACA,iBACA,0BACA,MACA,YACA,gBACA,mBACA,kBACA,aACA,mBACA,sBACA,sBACA,6BACA,eACA,iBACA,aACA,gBACA,iBACA,eACA,cACA,cACA,aACA,eACA,eACA,cACA,SACA,QACA,cACA,aACA,eACA,YACA,eACA,WAGAC,UCnlBF,SAAShL,EAAOvQ,GACd,OAAO,IAAIqF,OAAOrF,EAAMC,QAAQ,wBAAyB,QAAS,IACpE,CAMA,SAASqE,EAAOC,GACd,OAAKA,EACa,iBAAPA,EAAwBA,EAE5BA,EAAGD,OAHM,IAIlB,CAMA,SAASE,EAAUD,GACjB,OAAOvC,EAAO,MAAOuC,EAAI,IAC3B,CAMA,SAASvC,IAAgB,QAAAzB,EAAAC,UAAAC,OAANmE,EAAI,IAAAjE,MAAAJ,GAAAK,EAAA,EAAAA,EAAAL,EAAAK,IAAJgE,EAAIhE,GAAAJ,UAAAI,GAErB,OADegE,EAAK1C,KAAKC,GAAMmC,EAAOnC,KAAIG,KAAK,GAEjD,CA0BA,SAASwC,IAAgB,QAAAH,EAAAnE,UAAAC,OAANmE,EAAI,IAAAjE,MAAAgE,GAAA6W,EAAA,EAAAA,EAAA7W,EAAA6W,IAAJ5W,EAAI4W,GAAAhb,UAAAgb,GAErB,IAAM9Y,EAtBR,SAA8BkC,GAC5B,IAAMlC,EAAOkC,EAAKA,EAAKnE,OAAS,GAEhC,MAAoB,iBAATiC,GAAqBA,EAAKlD,cAAgBT,QACnD6F,EAAKK,OAAOL,EAAKnE,OAAS,EAAG,GACtBiC,GAEA,CAAC,CAEZ,CAaewC,CAAqBN,GAIlC,MAHe,KACVlC,EAAKyC,QAAU,GAAK,MACrBP,EAAK1C,KAAKC,GAAMmC,EAAOnC,KAAIG,KAAK,KAAO,GAE7C,CCnEA,IAAMgE,EAAW,2BACXmV,EAAW,CACf,KACA,KACA,KACA,KACA,MACA,QACA,UACA,MACA,MACA,WACA,KACA,SACA,OACA,OACA,QACA,QACA,aACA,OACA,QACA,OACA,UACA,MACA,SACA,WACA,SACA,SACA,MACA,QACA,QACA,QAIA,WACA,QACA,QACA,SACA,SACA,OACA,SACA,WAEIC,EAAW,CACf,OACA,QACA,OACA,YACA,MACA,YAIIC,EAAQ,CAEZ,SACA,WACA,UACA,SAEA,OACA,OACA,SACA,SAEA,SACA,SAEA,QACA,eACA,eACA,YACA,aACA,oBACA,aACA,aACA,cACA,cACA,gBACA,iBAEA,MACA,MACA,UACA,UAEA,cACA,oBACA,UACA,WACA,OAEA,UACA,YACA,oBACA,gBAEA,UACA,QAEA,OAEA,eAGIC,EAAc,CAClB,QACA,YACA,gBACA,aACA,iBACA,cACA,YACA,YAGIC,EAAmB,CACvB,cACA,aACA,gBACA,eAEA,UACA,UAEA,OACA,WACA,QACA,aACA,WACA,YACA,qBACA,YACA,qBACA,SACA,YAGIC,EAAqB,CACzB,YACA,OACA,QACA,UACA,SACA,WACA,eACA,iBACA,SACA,UAGIC,EAAY,GAAG/Z,OACnB6Z,EACAF,EACAC,GCrIFI,EAAYlD,iBAAiB,QCb7B,SAAclI,GACZ,IAAM9K,EAAQ8K,EAAK9K,MACbmW,EAAM,CAAC,EACPC,EAAa,CACjBtV,MAAO,OACPG,IAAK,KACLE,SAAU,CACR,OACA,CACEL,MAAO,KACPK,SAAU,CAAEgV,MAIlBld,OAAO6D,OAAOqZ,EAAK,CACjBxa,UAAW,WACXkO,SAAU,CACR,CAAE/I,MAAOd,EAAM9D,OAAO,qBAEpB,wBAEFka,KAIJ,IAAMC,EAAQ,CACZ1a,UAAW,QACXmF,MAAO,OACPG,IAAK,KACLE,SAAU,CAAE2J,EAAKjK,mBAEbyV,EAAW,CACfxV,MAAO,iBACPiD,OAAQ,CAAE5C,SAAU,CAClB2J,EAAK1I,kBAAkB,CACrBtB,MAAO,QACPG,IAAK,QACLtF,UAAW,cAIX4a,EAAe,CACnB5a,UAAW,SACXmF,MAAO,IACPG,IAAK,IACLE,SAAU,CACR2J,EAAKjK,iBACLsV,EACAE,IAGJA,EAAMlV,SAAS/D,KAAKmZ,GACpB,IAWMC,EAAa,CACjB1V,MAAO,UACPG,IAAK,OACLE,SAAU,CACR,CACEL,MAAO,gBACPnF,UAAW,UAEbmP,EAAKnJ,YACLwU,IAcEM,EAAgB3L,EAAKjI,QAAQ,CACjCE,OAAQ,IAAF7G,OAZe,CACrB,OACA,OACA,MACA,KACA,MACA,MACA,OACA,OACA,QAG2BM,KAAK,KAAI,KACpCuE,UAAW,KAEP2V,EAAW,CACf/a,UAAW,WACXmF,MAAO,4BACP2F,aAAa,EACbtF,SAAU,CAAE2J,EAAKJ,QAAQI,EAAK/I,WAAY,CAAEjB,MAAO,gBACnDC,UAAW,GA4Pb,MAAO,CACL1H,KAAM,OACN2R,QAAS,CAAE,MACXxH,SAAU,CACR6F,SAAU,wBACVzE,QA9Pa,CACf,KACA,OACA,OACA,OACA,KACA,MACA,QACA,QACA,KACA,KACA,OACA,OACA,OACA,WACA,UAgPE+R,QA7Oa,CACf,OACA,SA4OEC,SAAU,CApOZ,QACA,KACA,WACA,OACA,OACA,OACA,SACA,UACA,OACA,MACA,WACA,SACA,QACA,OACA,QACA,OACA,QACA,QAIA,QACA,OACA,UACA,SACA,UACA,UACA,OACA,SACA,OACA,MACA,QACA,SACA,UACA,SACA,OACA,YACA,SACA,OACA,UACA,SACA,UA+LI,MACA,QA5LJ,WACA,KACA,UACA,MACA,MACA,QACA,QACA,gBACA,WACA,UACA,eACA,YACA,aACA,YACA,WACA,UACA,aACA,OACA,UACA,SACA,SACA,SACA,UACA,KACA,KACA,QACA,YACA,SACA,QACA,UACA,UACA,OACA,OACA,QACA,MACA,SACA,OACA,QACA,QACA,SACA,SACA,QACA,SACA,SACA,OACA,UACA,SACA,aACA,SACA,UACA,WACA,QACA,OACA,SACA,QACA,QACA,WACA,UACA,OACA,MACA,WACA,aACA,QACA,OACA,cACA,UACA,SACA,OAIA,QACA,QACA,QACA,QACA,KACA,KACA,KACA,MACA,YACA,KACA,KACA,QACA,SACA,QACA,SACA,KACA,WACA,KACA,QACA,QACA,OACA,QACA,WACA,OACA,QACA,SACA,SACA,MACA,QACA,OACA,SACA,MACA,SACA,MACA,OACA,OACA,OACA,SACA,KACA,SACA,KACA,QACA,MACA,KACA,UACA,YACA,YACA,YACA,YACA,OACA,OACA,QACA,MACA,MACA,OACA,KACA,QACA,WACA,OACA,KACA,OACA,WACA,SACA,OACA,UACA,KACA,OACA,MACA,OACA,SAEA,SACA,SACA,KACA,OACA,UACA,OACA,QACA,QACA,UACA,QACA,WACA,SACA,MACA,WACA,SACA,MACA,QACA,OACA,SACA,OACA,MACA,OACA,UAEA,MACA,QACA,SACA,SACA,QACA,MACA,SACA,QAoBAzV,SAAU,CACRsV,EACA3L,EAAKjI,UACL6T,EACAF,EACA1L,EAAKpJ,kBACL4U,EAxPc,CAAEnW,MAAO,kBA0PvBoW,EAhUkB,CACpBpW,MAAO,OAEW,CAClBxE,UAAW,SACXmF,MAAO,IACPG,IAAK,KAEc,CACnBd,MAAO,OA2TLgW,GAGN,ID7WAD,EAAYlD,iBAAiB,UEd7B,SAAgBlI,GACd,IA6IM6K,EAAW,CACf/Q,QAjGsB,CACtB,WACA,KACA,OACA,QACA,OACA,QACA,QACA,QACA,WACA,KACA,OACA,QACA,WACA,SACA,UACA,QACA,MACA,UACA,OACA,KACA,WACA,KACA,YACA,WACA,KACA,OACA,YACA,MACA,WACA,MACA,WACA,SACA,UACA,YACA,SACA,WACA,SACA,MACA,SACA,SACA,SACA,SACA,aACA,SACA,SACA,SACA,OACA,QACA,MACA,SACA,YACA,SACA,QACA,UACA,OACA,WACA,SAwCyB1I,OAtCC,CAC1B,MACA,QACA,MACA,YACA,QACA,QACA,KACA,aACA,SACA,OACA,MACA,SACA,QACA,OACA,OACA,OACA,MACA,SACA,MACA,UACA,KACA,KACA,UACA,UACA,SACA,SACA,MACA,YACA,UACA,MACA,OACA,QACA,OACA,UAKA0a,SA/IwB,CACxB,OACA,OACA,OACA,UACA,WACA,SACA,UACA,OACA,QACA,MACA,OACA,OACA,QACA,SACA,QACA,QACA,SACA,QACA,OACA,UA4HAD,QAzGuB,CACvB,UACA,QACA,OACA,SAuGI5U,EAAa+I,EAAKJ,QAAQI,EAAK/I,WAAY,CAAEjB,MAAO,uBACpD+V,EAAU,CACdlb,UAAW,SACXkO,SAAU,CACR,CAAE/I,MAAO,iBACT,CAAEA,MAAO,mEACT,CAAEA,MAAO,wFAEXC,UAAW,GAEP+V,EAAkB,CACtBnb,UAAW,SACXmF,MAAO,KACPG,IAAK,IACLE,SAAU,CAAE,CAAEL,MAAO,QAEjBiW,EAAwBjM,EAAKJ,QAAQoM,EAAiB,CAAE5V,QAAS,OACjEmV,EAAQ,CACZ1a,UAAW,QACXmF,MAAO,KACPG,IAAK,KACLuC,SAAUmS,GAENqB,EAAclM,EAAKJ,QAAQ2L,EAAO,CAAEnV,QAAS,OAC7C+V,EAAsB,CAC1Btb,UAAW,SACXmF,MAAO,MACPG,IAAK,IACLC,QAAS,KACTC,SAAU,CACR,CAAEL,MAAO,QACT,CAAEA,MAAO,QACTgK,EAAKjK,iBACLmW,IAGEE,EAA+B,CACnCvb,UAAW,SACXmF,MAAO,OACPG,IAAK,IACLE,SAAU,CACR,CAAEL,MAAO,QACT,CAAEA,MAAO,QACT,CAAEA,MAAO,MACTuV,IAGEc,EAAqCrM,EAAKJ,QAAQwM,EAA8B,CACpFhW,QAAS,KACTC,SAAU,CACR,CAAEL,MAAO,QACT,CAAEA,MAAO,QACT,CAAEA,MAAO,MACTkW,KAGJX,EAAMlV,SAAW,CACf+V,EACAD,EACAH,EACAhM,EAAK9J,iBACL8J,EAAK1J,kBACLyV,EACA/L,EAAKrJ,sBAEPuV,EAAY7V,SAAW,CACrBgW,EACAF,EACAF,EACAjM,EAAK9J,iBACL8J,EAAK1J,kBACLyV,EACA/L,EAAKJ,QAAQI,EAAKrJ,qBAAsB,CAAEP,QAAS,QAErD,IAAMkW,EAAS,CAAEvN,SAAU,CACzBqN,EACAD,EACAH,EACAhM,EAAK9J,iBACL8J,EAAK1J,oBAGDiW,EAAmB,CACvBvW,MAAO,IACPG,IAAK,IACLE,SAAU,CACR,CAAEkC,cAAe,UACjBtB,IAGEuV,EAAgBxM,EAAKtK,SAAW,KAAOsK,EAAKtK,SAAW,aAAesK,EAAKtK,SAAW,iBACtF+W,EAAgB,CAGpBzW,MAAO,IAAMgK,EAAKtK,SAClBO,UAAW,GAGb,MAAO,CACL1H,KAAM,KACN2R,QAAS,CACP,KACA,MAEFxH,SAAUmS,EACVzU,QAAS,KACTC,SAAU,CACR2J,EAAKzJ,QACH,MACA,IACA,CACEoF,aAAa,EACbtF,SAAU,CACR,CACExF,UAAW,SACXkO,SAAU,CACR,CACE/I,MAAO,MACPC,UAAW,GAEb,CAAED,MAAO,kBACT,CACEA,MAAO,MACPG,IAAK,UAOjB6J,EAAKtJ,oBACLsJ,EAAKrJ,qBACL,CACE9F,UAAW,OACXmF,MAAO,IACPG,IAAK,IACLuC,SAAU,CAAEoB,QAAS,wFAEvBwS,EACAP,EACA,CACExT,cAAe,kBACftC,UAAW,EACXE,IAAK,QACLC,QAAS,UACTC,SAAU,CACR,CAAEkC,cAAe,eACjBtB,EACAsV,EACAvM,EAAKtJ,oBACLsJ,EAAKrJ,uBAGT,CACE4B,cAAe,YACftC,UAAW,EACXE,IAAK,QACLC,QAAS,SACTC,SAAU,CACRY,EACA+I,EAAKtJ,oBACLsJ,EAAKrJ,uBAGT,CACE4B,cAAe,SACftC,UAAW,EACXE,IAAK,QACLC,QAAS,SACTC,SAAU,CACRY,EACAsV,EACAvM,EAAKtJ,oBACLsJ,EAAKrJ,uBAGT,CAEE9F,UAAW,OACXmF,MAAO,oBACPQ,cAAc,EACdL,IAAK,MACL0F,YAAY,EACZxF,SAAU,CACR,CACExF,UAAW,SACXmF,MAAO,IACPG,IAAK,OAIX,CAGEoC,cAAe,8BACftC,UAAW,GAEb,CACEpF,UAAW,WACXmF,MAAO,IAAMwW,EAAgB,SAAWxM,EAAKtK,SAAW,wBACxDiG,aAAa,EACbxF,IAAK,WACL0F,YAAY,EACZnD,SAAUmS,EACVxU,SAAU,CAER,CACEkC,cA3UiB,CACzB,SACA,UACA,YACA,SACA,WACA,YACA,WACA,QACA,SACA,WACA,SACA,UACA,MACA,SACA,WA4T0C7G,KAAK,KACvCuE,UAAW,GAEb,CACED,MAAOgK,EAAKtK,SAAW,wBACvBiG,aAAa,EACbtF,SAAU,CACR2J,EAAK/I,WACLsV,GAEFtW,UAAW,GAEb,CAAEZ,MAAO,QACT,CACExE,UAAW,SACXmF,MAAO,KACPG,IAAK,KACLK,cAAc,EACdqF,YAAY,EACZnD,SAAUmS,EACV5U,UAAW,EACXI,SAAU,CACRiW,EACAP,EACA/L,EAAKrJ,uBAGTqJ,EAAKtJ,oBACLsJ,EAAKrJ,uBAGT8V,GAGN,IFrXArB,EAAYlD,iBAAiB,OHykB7B,SAAalI,GACX,IAAM9K,EAAQ8K,EAAK9K,MACbwX,EAnmBO1M,KACN,CACL2M,UAAW,CACTxc,MAAO,OACP6F,MAAO,cAET4W,cAAe5M,EAAKrJ,qBACpBkW,SAAU,CACR1c,MAAO,SACP6F,MAAO,mDAET8W,kBAAmB,CACjBjc,UAAW,WACXmF,MAAO,gBAET+W,wBAAyB,CACvB5c,MAAO,gBACP6F,MAAO,KACPG,IAAK,KACLC,QAAS,IACTC,SAAU,CACR2J,EAAK9J,iBACL8J,EAAK1J,oBAGT0W,gBAAiB,CACf7c,MAAO,SACP6F,MAAOgK,EAAKpK,UAALoK,kGASP/J,UAAW,GAEbgX,aAAc,CACZpc,UAAW,OACXmF,MAAO,+BA2jBGoB,CAAM4I,GAKdkN,EAAU,CACdlN,EAAK9J,iBACL8J,EAAK1J,mBAGP,MAAO,CACL/H,KAAM,MACN6N,kBAAkB,EAClBhG,QAAS,UACTsC,SAAU,CAAEyU,iBAAkB,WAC9BlP,iBAAkB,CAGhBkP,iBAAkB,gBACpB9W,SAAU,CACRqW,EAAME,cAnBY,CAAE5W,MAAO,gCAuB3B0W,EAAMM,gBACN,CACEnc,UAAW,cACXmF,MAAO,kBACPC,UAAW,GAEb,CACEpF,UAAW,iBACXmF,MAAO,6BACPC,UAAW,GAEbyW,EAAMK,wBACN,CACElc,UAAW,kBACXkO,SAAU,CACR,CAAE/I,MAAO,KAAOwU,EAAe9Y,KAAK,KAAO,KAC3C,CAAEsE,MAAO,SAAWyU,EAAgB/Y,KAAK,KAAO,OASpDgb,EAAMO,aACN,CACEpc,UAAW,YACXmF,MAAO,OAAS0U,EAAWhZ,KAAK,KAAO,QAGzC,CACEsE,MAAO,IACPG,IAAK,QACLE,SAAU,CACRqW,EAAME,cACNF,EAAMG,SACNH,EAAMC,UACND,EAAMM,mBACHE,EAIH,CACElX,MAAO,mBACPG,IAAK,KACLF,UAAW,EACXyC,SAAU,CAAEoT,SAAU,gBACtBzV,SAAU,IACL6W,EACH,CACErc,UAAW,SAGXmF,MAAO,OACP0I,gBAAgB,EAChB7C,YAAY,KAIlB6Q,EAAMI,oBAGV,CACE9W,MAAOd,EAAMtB,UAAU,KACvBuC,IAAK,OACLF,UAAW,EACXG,QAAS,IACTC,SAAU,CACR,CACExF,UAAW,UACXmF,MA5Fa,qBA8Ff,CACEA,MAAO,KACP0I,gBAAgB,EAChB7C,YAAY,EACZ5F,UAAW,EACXyC,SAAU,CACR6F,SAAU,UACVzE,QAtGS,kBAuGTsT,UAAW7C,EAAe7Y,KAAK,MAEjC2E,SAAU,CACR,CACEL,MAAO,eACPnF,UAAW,gBAEVqc,EACHR,EAAMM,oBAKd,CACEnc,UAAW,eACXmF,MAAO,OAASsU,EAAK5Y,KAAK,KAAO,SAIzC,IGtsBA0Z,EAAYlD,iBAAiB,UFsD7B,SAAgBlI,GACd,IAmEMqN,EAAoB,CAExBld,MAAO,UACPkF,MAAO,sCAyCHiY,EAAc,CAElB,OACA,OACA,QACA,OACA,QACA,QACA,QACA,SACA,SACA,MACA,OACA,QACA,SACA,YACA,aACA,UACA,QACA,SACA,UACA,SACA,OACA,SACA,OACA,SAEA,SACA,UACA,OACA,QACA,MACA,QACA,MACA,QACA,YACA,MACA,SACA,UAEA,UA4CIC,EAAe,CACnBzT,QApMe,CACf,WACA,MACA,KACA,SACA,OACA,QACA,QACA,UACA,WACA,KACA,OACA,WACA,SACA,OACA,OACA,MACA,YACA,SAEA,UACA,QACA,MACA,MACA,WACA,SACA,KACA,KACA,UACA,SACA,YACA,WACA,OACA,MACA,QACA,SACA,SACA,UACA,YACA,MAGA,KACA,OACA,KACA,WACA,UACA,SACA,MACA,SACA,SACA,SACA,OACA,KAEA,MACA,OACA,SACA,MACA,MACA,OACA,OACA,QACA,OACA,SAqIA+R,QA5Ge,CACf,OACA,QACA,OACA,OACA,OACA,KACA,QACA,WACA,YACA,MACA,QAkGAC,SA5Ce,CAKf,MACA,MACA,QACA,UACA,OACA,eACA,MACA,MACA,OACA,SACA,SACA,YACA,SACA,UACA,aACA,YACA,KACA,MACA,MACA,SACA,OACA,QACA,MACA,QACA,WACA,SACA,UACA,UACA,UACA,WACA,UACA,WACA,WACA,aAOA,oBAhG0B,CAC1B,WACA,uBACA,oBAsGIvV,EAAU,CACdwI,SAAU,CALViB,EAAKzJ,QAAQ,aAAc,OAAQ,CACjCF,SAAU,CAAC,UAMX2J,EAAKtJ,sBAOH8W,EAAoB,CACxBrd,MAAO,WACP6F,MAAO,KACPG,IAAK,MAIDsX,EAA+B,WAC/BC,EAAsB,CAC1Bvd,MAAO,SACP4O,SAAU,CAER,CAAE1J,MAAOjE,EAAOqc,EAA8B,YAE9C,CAAEpY,MAAOjE,EAAOqc,EAA8BzN,EAAKrK,uBAErDM,UAAW,GAGP0X,EAAmB,SAAHC,GAA8B,IAE9CC,EAEFA,EAJ4CD,EAAZE,aAIb,kBAEA,iBACrB,IACMC,EAAmB3c,EAAO,OADTrB,MAAMie,KAAKH,GACqBvc,IAAIqO,GAAS,KAE9DsO,EAA0B/Z,EAAO6Z,EAAkB,MAEnDG,EAAqC9c,EAAO6c,EAAyBra,EAAUqa,IAC/EE,EAAuBja,EAC3B9C,EAAO8c,EAAoCD,EAAyB,KACpE7c,EAAO2c,EAAkB,MAE3B,MAAO,CACL5d,MAAO,WACPkF,MAAOnB,EAELia,EAGA,OACA,MACA,KACA,KACA,MACA,MACFlY,UAAW,EAEf,EAEMmY,EAAWT,EAAiB,CAAEG,cAAc,IAE5CO,EAAyBV,EAAiB,CAAEG,cAAc,IAE1DQ,EAAyB,SAASvd,EAAQwd,GAC9C,MAAO,CACLvY,MAAO5E,EACLL,EACA6C,EACExC,EACE,MACA8C,EACE,KACA,IACA,KACA,IACA,KACA,KACA,UAERqH,WAAYgT,EAMZpY,IAAKvC,EACHM,EACE,KACA,MACJ+B,UAAW,EAEXyC,SAAUsH,EAAKJ,QAAQ2N,EAAc,CAAE9e,KAAM6e,IAC7CjX,SAAU,CACRE,EACAmX,EACA1N,EAAKJ,QAAQ4N,EAAmB,CAAErd,MAAO,OACzCke,GAGN,EAEMG,EAAkBF,EAAuB,IAAK,YAC9CG,EAAsCH,EAAuB,SAAU,WAGvEI,EAAmB,CACvB1Y,MAAO,CACL,UACA,OACA,MAzGkB,oBA4GpBuF,WAAY,CACV,EAAG,UACH,EAAG,eAELpF,IAAKvC,EAAU,UACf8E,SAAU6U,EACVlX,SAAU,CACRE,EACAyJ,EAAKJ,QAAQ4N,EAAmB,CAAErd,MAAO,OACzCud,EACA,CAEEvd,MAAO,WACPkF,MAAO,OAETmZ,IAIEG,EAAyB,CAE7Bxe,MAAO,yBAEPkF,MAAO,wBAGHuZ,EAAe,CAEnB5Y,MAAO,CACL,OACA5E,EAAO,IAAK8C,EAxRd,KACA,OACA,QACA,OACA,SACA,QACA,IACA,IACA,IACA,OACA,OACA,OACA,SA6QE,MAEFqH,WAAY,CAAE,EAAG,QACjBpF,IAAKvC,EAAU,SAKXib,EAAS,CACb9P,SAAU,CACRiB,EAAKjJ,mBACLiJ,EAAKlJ,gBAQHgY,EAAgB,CACpB3e,MAAO,SACP6F,MAAO,IACPG,IAAK,IACLE,SAAU,CACR2J,EAAKjK,mBAIHiW,EAAkB,CACtB7b,MAAO,SACP6F,MAAO,KACPG,IAAK,IACLE,SAAU,CACR,CACEhB,MAAO,MAET2K,EAAKjK,mBAIHgZ,EAAuB,CAC3B5e,MAAO,SACP6F,MAAO,MACPG,IAAK,MACLF,UAAW,GAEPsV,EAAQ,CACZpb,MAAO,QACP6F,MAAO,KACPG,IAAK,KACLuC,SAAU6U,GAGNpB,EAAsB,CAC1Bhc,MAAO,SACP6F,MAAO,MACPG,IAAK,IACLE,SAAU,CACR,CACEhB,MAAO,QAET,CACEA,MAAO,QAET2K,EAAKjK,iBACLwV,IAIEa,EAA+B,CACnCjc,MAAO,SACP6F,MAAO,aACPG,IAAK,IACLE,SAAU,CACR,CACEhB,MAAO,QAET,CACEA,MAAO,QAET,CACEA,MAAO,MAET2K,EAAKjK,iBACLwV,IAIEyD,EAAoC,CACxC7e,MAAO,SACP6F,MAAO,QACPG,IAAK,MACLE,SAAU,CACR,CACEhB,MAAO,QAET,CACEA,MAAO,QAETkW,GAEFtV,UAAW,GAGPgZ,EAAe,CACnB9e,MAAO,SACPkF,MAAOjE,EACL,IACA8C,EACE,SACA,8DAEF,MAkCJ,OA5BAqX,EAAMlV,SAAW,CACf+V,EACAD,EACAH,EACA8C,EACAG,EACA5B,EACA9W,EACAiX,EACAgB,EACAG,EACAC,EACAC,EACAnB,EACAU,GAcK,CACL7f,KAAM,KACN2R,QAAS,CACP,KACA,MAEFxH,SAAU6U,EACVnX,QAAS,OACT6H,iBAAkB,CAChB,yBAA0B,WAE5B5H,SAAU,CACRgX,EAxBW,CACbtO,SAAU,CACRiQ,EACA5C,EACAD,EACA4C,EACA/C,EACA8C,EACAG,IAkBA1Y,EACAiX,EACAkB,EACA,CAGEve,MAAO,OACP6F,MAAO,MACPG,IAAK,MACLF,UAAW,EACXI,SAAU,CACRmX,EAEAuB,EACA/C,EACA8C,EACAG,EACAJ,IAGJJ,EACAD,EACAG,EACAC,EACAC,EACAnB,EACAU,GAGN,IEtlBAhD,EAAYlD,iBAAiB,QGlB7B,SAAclI,GACZ,IACMkP,EAAU,uBAEVC,EAAS,CACbte,UAAW,YACXmF,MALYgK,EAAK9K,MAKJ9D,OAAO,IAHF,wBAGoB,cACtC6H,OAAQ,CAAE5C,SAAU,CAClB,CACExF,UAAW,cACXmF,MAAO,KACPC,UAAW,EACXgD,OAAQ,CACN9C,IAAK,IACLF,UAAW,OAKbmZ,EAAmB,CACvBD,EACA,CACEnZ,MAAO,SACPiD,OAAQ,CACNsJ,YAAa,GACb7D,gBAAgB,KAKtB,MAAO,CACLnQ,KAAM,OACN2R,QAAS,CAAE,SACX9J,QAAS,KACTC,SAAU,CAER,CACEL,MAAO,OAASkZ,EAAU,WAC1B/Y,IAAK,IACLE,SAAU,CACR,CACExF,UAAW,OACXmF,MAAOkZ,GAET,CACEre,UAAW,SACXmF,MAAO,iBAGXiD,OAAQ,CACN9C,IAAK,OACLC,QAAS,KACTC,SAAU+Y,IAId,CACEpZ,MAAO,oBAAsBkZ,EAAU,KACvC/Y,IAAK,IACLE,SAAU,CACR,CACExF,UAAW,SACXmF,MAAO,IACPG,IAAK,IACLK,cAAc,EACdqF,YAAY,GAEd,CACEhL,UAAW,OACXmF,MAAOkZ,GAET,CACEre,UAAW,UACXmF,MAAO,WAGXiD,OAAQ,CACN9C,IAAK,OACLC,QAAS,KACTC,SAAU+Y,IAIdpP,EAAKJ,QAAQuP,EAAQ,CAAElZ,UAAW,KAGxC,IHnEAmV,EAAYlD,iBAAiB,cD4I7B,SAAoBlI,GAClB,IAAM9K,EAAQ8K,EAAK9K,MAcbma,EAAa3Z,EAOb4Z,EAAU,CACdtZ,MAAO,sBACPG,IAAK,4BAKLoZ,kBAAmBA,CAACla,EAAO8C,KACzB,IAAMqX,EAAkBna,EAAM,GAAGxF,OAASwF,EAAME,MAC1Cka,EAAWpa,EAAM+C,MAAMoX,GAC7B,GAIe,MAAbC,GAGa,MAAbA,EAPF,CA0BA,IAAIjY,EAXa,MAAbiY,IAtCcC,EAACra,EAAKuY,KAAgB,IAAZ+B,EAAK/B,EAAL+B,MACxBC,EAAM,KAAOva,EAAM,GAAGoI,MAAM,GAElC,OAAgB,IADJpI,EAAM+C,MAAM0Q,QAAQ8G,EAAKD,EACpB,EAsCRD,CAAcra,EAAO,CAAEsa,MAAOH,KACjCrX,EAASjJ,eAQb,IAAM2gB,EAAaxa,EAAM+C,MAAM9C,UAAUka,IAIhCK,EAAWxa,MAAM,WAQrBmC,EAAIqY,EAAWxa,MAAM,oBACR,IAAZmC,EAAEjC,QARN4C,EAASjJ,aArBX,MAFEiJ,EAASjJ,aAoCX,GAGE4gB,EAAa,CACjBvR,SAAU7I,EACVoE,QAAS+Q,EACTgB,QAASf,EACTgB,SAAUX,EACV,oBAAqBD,GAIjB6E,EAAgB,kBAChBC,EAAO,OAAH5e,OAAU2e,EAAa,KAG3BE,EAAiB,sCACjBpB,EAAS,CACbhe,UAAW,SACXkO,SAAU,CAER,CAAE/I,MAAO,QAAA5E,OAAQ6e,EAAc,OAAA7e,OAAM4e,EAAI,aAAA5e,OAAY4e,EAAI,mBAAA5e,OAC1C2e,EAAa,SAC5B,CAAE/Z,MAAO,OAAF5E,OAAS6e,EAAc,UAAA7e,OAAS4e,EAAI,gBAAA5e,OAAe4e,EAAI,SAG9D,CAAEha,MAAO,8BAGT,CAAEA,MAAO,4CACT,CAAEA,MAAO,gCACT,CAAEA,MAAO,gCAIT,CAAEA,MAAO,oBAEXC,UAAW,GAGPsV,EAAQ,CACZ1a,UAAW,QACXmF,MAAO,SACPG,IAAK,MACLuC,SAAUoX,EACVzZ,SAAU,IAEN6Z,EAAgB,CACpBla,MAAO,QACPG,IAAK,GACL8C,OAAQ,CACN9C,IAAK,IACL2F,WAAW,EACXzF,SAAU,CACR2J,EAAKjK,iBACLwV,GAEFhJ,YAAa,QAGX4N,EAAe,CACnBna,MAAO,OACPG,IAAK,GACL8C,OAAQ,CACN9C,IAAK,IACL2F,WAAW,EACXzF,SAAU,CACR2J,EAAKjK,iBACLwV,GAEFhJ,YAAa,QAGX6N,EAAmB,CACvBpa,MAAO,OACPG,IAAK,GACL8C,OAAQ,CACN9C,IAAK,IACL2F,WAAW,EACXzF,SAAU,CACR2J,EAAKjK,iBACLwV,GAEFhJ,YAAa,YAGX8N,EAAkB,CACtBxf,UAAW,SACXmF,MAAO,IACPG,IAAK,IACLE,SAAU,CACR2J,EAAKjK,iBACLwV,IA0CEhV,EAAU,CACd1F,UAAW,UACXkO,SAAU,CAzCUiB,EAAKzJ,QACzB,eACA,OACA,CACEN,UAAW,EACXI,SAAU,CACR,CACEL,MAAO,iBACPC,UAAW,EACXI,SAAU,CACR,CACExF,UAAW,SACXmF,MAAO,cAET,CACEnF,UAAW,OACXmF,MAAO,MACPG,IAAK,MACL0F,YAAY,EACZrF,cAAc,EACdP,UAAW,GAEb,CACEpF,UAAW,WACXmF,MAAOqZ,EAAa,gBACpBjW,YAAY,EACZnD,UAAW,GAIb,CACED,MAAO,cACPC,UAAW,QAWnB+J,EAAKrJ,qBACLqJ,EAAKtJ,sBAGH4Z,EAAkB,CACtBtQ,EAAK9J,iBACL8J,EAAK1J,kBACL4Z,EACAC,EACAC,EACAC,EAEA,CAAEhb,MAAO,SACTwZ,GAKFtD,EAAMlV,SAAWia,EACdlf,OAAO,CAGN4E,MAAO,KACPG,IAAK,KACLuC,SAAUoX,EACVzZ,SAAU,CACR,QACAjF,OAAOkf,KAEb,IA4HgB9L,EA5HV+L,EAAqB,GAAGnf,OAAOmF,EAASgV,EAAMlV,UAC9Cma,EAAkBD,EAAmBnf,OAAO,CAEhD,CACE4E,MAAO,KACPG,IAAK,KACLuC,SAAUoX,EACVzZ,SAAU,CAAC,QAAQjF,OAAOmf,MAGxBE,EAAS,CACb5f,UAAW,SACXmF,MAAO,KACPG,IAAK,KACLK,cAAc,EACdqF,YAAY,EACZnD,SAAUoX,EACVzZ,SAAUma,GAINE,EAAmB,CACvB3R,SAAU,CAER,CACE1J,MAAO,CACL,QACA,MACAga,EACA,MACA,UACA,MACAna,EAAM9D,OAAOie,EAAY,IAAKna,EAAM9D,OAAO,KAAMie,GAAa,OAEhElf,MAAO,CACL,EAAG,UACH,EAAG,cACH,EAAG,UACH,EAAG,0BAIP,CACEkF,MAAO,CACL,QACA,MACAga,GAEFlf,MAAO,CACL,EAAG,UACH,EAAG,kBAOLwgB,EAAkB,CACtB1a,UAAW,EACXZ,MACAH,EAAMhB,OAEJ,SAEA,iCAEA,6CAEA,oDAMFrD,UAAW,cACX6H,SAAU,CACRkY,EAAG,IAEE7F,KACAC,KAYH6F,EAAsB,CAC1B9R,SAAU,CACR,CACE1J,MAAO,CACL,WACA,MACAga,EACA,cAIJ,CACEha,MAAO,CACL,WACA,eAINxE,UAAW,CACT,EAAG,UACH,EAAG,kBAELigB,MAAO,WACPza,SAAU,CAAEoa,GACZra,QAAS,KAaL2a,EAAgB,CACpB1b,MAAOH,EAAM9D,OACX,MANYoT,EAOL,IACFyG,EACH,QACA,UATG/V,EAAM9D,OAAO,MAAOoT,EAAK9S,KAAK,KAAM,MAWzC2d,EAAYna,EAAMtB,UAAU,OAC9B/C,UAAW,iBACXoF,UAAW,GAGP+a,EAAkB,CACtBhb,MAAOd,EAAM9D,OAAO,KAAM8D,EAAMtB,UAC9BsB,EAAM9D,OAAOie,EAAY,wBAE3BlZ,IAAKkZ,EACL7Y,cAAc,EACdkC,SAAU,YACV7H,UAAW,WACXoF,UAAW,GAGPgb,EAAmB,CACvB5b,MAAO,CACL,UACA,MACAga,EACA,UAEFxe,UAAW,CACT,EAAG,UACH,EAAG,kBAELwF,SAAU,CACR,CACEL,MAAO,QAETya,IAIES,EAAkB,2DAMblR,EAAKrK,oBAAsB,UAEhCwb,EAAoB,CACxB9b,MAAO,CACL,gBAAiB,MACjBga,EAAY,MACZ,OACA,cACAna,EAAMtB,UAAUsd,IAElBxY,SAAU,QACV7H,UAAW,CACT,EAAG,UACH,EAAG,kBAELwF,SAAU,CACRoa,IAIJ,MAAO,CACLliB,KAAM,aACN2R,QAAS,CAAC,KAAM,MAAO,MAAO,OAC9BxH,SAAUoX,EAEV1G,QAAS,CAAEoH,kBAAiBG,mBAC5Bva,QAAS,eACTC,SAAU,CACR2J,EAAKjI,QAAQ,CACX+Y,MAAO,UACP7Y,OAAQ,OACRhC,UAAW,IA5HE,CACjB6a,MAAO,aACPjgB,UAAW,OACXoF,UAAW,GACXD,MAAO,gCA2HLgK,EAAK9J,iBACL8J,EAAK1J,kBACL4Z,EACAC,EACAC,EACAC,EACA9Z,EAEA,CAAElB,MAAO,SACTwZ,EACA8B,EACA,CACE9f,UAAW,OACXmF,MAAOqZ,EAAana,EAAMtB,UAAU,KACpCqC,UAAW,GAEbkb,EACA,CACEnb,MAAO,IAAMgK,EAAKlI,eAAiB,kCACnCY,SAAU,oBACVzC,UAAW,EACXI,SAAU,CACRE,EACAyJ,EAAKhJ,YACL,CACEnG,UAAW,WAIXmF,MAAOkb,EACPvV,aAAa,EACbxF,IAAK,SACLE,SAAU,CACR,CACExF,UAAW,SACXkO,SAAU,CACR,CACE/I,MAAOgK,EAAKrK,oBACZM,UAAW,GAEb,CACEpF,UAAW,KACXmF,MAAO,UACP0F,MAAM,GAER,CACE1F,MAAO,KACPG,IAAK,KACLK,cAAc,EACdqF,YAAY,EACZnD,SAAUoX,EACVzZ,SAAUma,OAMpB,CACExa,MAAO,IACPC,UAAW,GAEb,CACEZ,MAAO,MACPY,UAAW,GAEb,CACE8I,SAAU,CACR,CAAE/I,MAzfL,KAyf4BG,IAxf9B,OAyfK,CAAEd,MAtfW,6BAufb,CACEW,MAAOsZ,EAAQtZ,MAGf,WAAYsZ,EAAQC,kBACpBpZ,IAAKmZ,EAAQnZ,MAGjBoM,YAAa,MACblM,SAAU,CACR,CACEL,MAAOsZ,EAAQtZ,MACfG,IAAKmZ,EAAQnZ,IACbuF,MAAM,EACNrF,SAAU,CAAC,aAMrBwa,EACA,CAGEtY,cAAe,6BAEjB,CAIEvC,MAAO,kBAAoBgK,EAAKrK,oBAAzB,gEAQPgG,aAAY,EACZmV,MAAO,WACPza,SAAU,CACRoa,EACAzQ,EAAKJ,QAAQI,EAAK/I,WAAY,CAAEjB,MAAOqZ,EAAYxe,UAAW,qBAIlE,CACEwE,MAAO,SACPY,UAAW,GAEb+a,EAIA,CACE3b,MAAO,MAAQga,EACfpZ,UAAW,GAEb,CACEZ,MAAO,CAAE,0BACTxE,UAAW,CAAE,EAAG,kBAChBwF,SAAU,CAAEoa,IAEdM,EAjOwB,CAC1B9a,UAAW,EACXZ,MAAO,sBACPxE,UAAW,qBAgOT6f,EACAO,EACA,CACE5b,MAAO,WAIf,IC/tBA+V,EAAYlD,iBAAiB,QIpB7B,SAAclI,GACZ,IAUM8K,EAAW,CACf,OACA,QACA,QAOIsG,EAAgB,CACpBjhB,MAAO,UACPoI,cAAeuS,EAASpZ,KAAK,MAG/B,MAAO,CACLnD,KAAM,OACNmK,SAAS,CACPmT,QAASf,GAEXzU,SAAU,CA9BM,CAChBxF,UAAW,OACXmF,MAAO,8BACPC,UAAW,MAEO,CAClBZ,MAAO,YACPxE,UAAW,cACXoF,UAAW,GAyBT+J,EAAK1J,kBACL8a,EACApR,EAAKlJ,cACLkJ,EAAKtJ,oBACLsJ,EAAKrJ,sBAEPP,QAAS,MAEb,IJrBAgV,EAAYlD,iBAAiB,SKrB7B,SAAelI,GAOb,IAAMqR,EAAmB,uDAoTnBxG,EAAW,CACftM,SAAU8S,EACVvX,QA1SmB,CACnB,aACA,QACA,QACA,QACA,QACA,QACA,WACA,KACA,OACA,SACA,MACA,SACA,QACA,UACA,MACA,WACA,SACA,KACA,SACA,KACA,MACA,MACA,QACA,QACA,SACA,QACA,SACA,OACA,MACA,QACA,QACA,SA2QA+R,QAzPmB,CACnB,OACA,SACA,aACA,aACA,MACA,MACA,QACA,QACA,QACA,gBACA,YACA,YACA,MACA,QACA,QACA,QACA,eACA,YACA,YACA,gBACA,eACA,uBACA,qBACA,cACA,UACA,YACA,UACA,QACA,KACA,UACA,UACA,KACA,SACA,QACA,SACA,OACA,QACA,IACA,KAmNAC,SAjMoB,CACpB,gBACA,kBACA,eACA,eACA,kBACA,gBACA,qBACA,iBACA,gBACA,cACA,iBACA,oBACA,mBACA,iBACA,MACA,gBACA,QACA,iBACA,WACA,SACA,WACA,YACA,SACA,YACA,OACA,cACA,oBACA,iBACA,mBACA,QACA,UACA,SACA,UACA,OACA,OACA,YACA,QACA,YACA,MACA,QACA,UACA,aACA,aACA,aACA,qBACA,YACA,aACA,SACA,UACA,WACA,UACA,SACA,QACA,aACA,SACA,aACA,UACA,QACA,WACA,WACA,WACA,aACA,cACA,gBACA,cACA,OACA,oBACA,OACA,cACA,cACA,WACA,OACA,iBACA,YACA,qBACA,OACA,UACA,UACA,UACA,WACA,YACA,OACA,KACA,WACA,YACA,WACA,SACA,iBACA,cACA,aACA,eACA,YACA,MACA,SACA,QACA,QACA,QACA,OACA,UACA,qBACA,wBACA,aACA,WACA,WACA,iBACA,gBACA,YACA,OACA,SACA,SACA,cACA,UACA,mBACA,SACA,SACA,aACA,UACA,SACA,eACA,mBACA,gBACA,OACA,mBACA,oBACA,OACA,yBACA,MACA,YACA,WACA,QACA,sBACA,OACA,gBACA,MACA,QACA,aACA,eACA,oBACA,MACA,SACA,OACA,qBACA,YACA,eACA,eACA,gBACA,kBACA,gBACA,SACA,mBACA,WACA,YACA,qBACA,SACA,cACA,OACA,sBACA,OACA,cACA,QACA,QACA,OACA,YACA,UACA,OACA,UACA,SACA,SACA,SACA,QACA,mBACA,oBACA,gBACA,gBACA,QACA,WACA,YACA,WACA,MACA,SACA,aACA,WACA,SACA,gBACA,cACA,YAWIwF,EAAU,CACd5Y,SAAUmS,EACVzU,QAAS,OAsBLmb,EAAgB,CACpB1gB,UAAW,QACXmF,MAAO,OACPG,IAAK,KACLuC,SAAUmS,GAGN2G,EAAwB,CAC5B3gB,UAAW,WACXmF,MAAO,MAAQqb,GAIX/E,EAAS,CACbzb,UAAW,SACXwF,SAAU,CACR2J,EAAKjK,iBACLwb,EACAC,GAEFzS,SAAU,CACR,CACE/I,MAAO,SACPG,IAAK,SACLF,UAAW,IAEb,CACED,MAAO,OACPG,IAAK,UAKLsb,EAAU,CACd5gB,UAAW,SACXwF,SAAU,CACR2J,EAAKjK,iBACLwb,EACAC,GAEFxb,MAAO,IACPG,IAAK,KAGDub,EAAY,CAChB7gB,UAAW,OACXmF,MAAO,IAAMqb,GAoCf,OAlBAC,EAAQ/iB,KAAO,QACf+iB,EAAQjb,SAAW,CAnFJ,CACbxF,UAAW,SAQXmF,MAAO,qIACPC,UAAW,GAGA,CACXpF,UAAW,SACXmF,MAAO,8BAuEPsW,EACAmF,EACAC,EArBc,CACd7gB,UAAW,UACXkO,SAAU,CACR,CACE/I,MAAO,KACPG,IAAK,KACLF,UAAW,IAEb,CACED,MAAO,IACPG,IAAK,OAaT6J,EAAKpJ,kBACL,CACE/F,UAAW,UACXmF,MACE,+DAEJ,CAAEA,MAAO,OAEXub,EAAclb,SAAWib,EAAQjb,SAE1Bib,CACT,ILxZAlG,EAAYlD,iBAAiB,YMtB7B,SAAkBlI,GAChB,IACM2R,EAAc,CAClB3b,MAAO,gBACPG,IAAK,IACLoM,YAAa,MACbtM,UAAW,GA8DP2b,EAAO,CACX7S,SAAU,CAGR,CACE/I,MAAO,iBACPC,UAAW,GAGb,CACED,MAAO,gEACPC,UAAW,GAEb,CACED,MAjFQgK,EAAK9K,MAiFA9D,OAAO,YAfP,0BAegC,cAC7C6E,UAAW,GAGb,CACED,MAAO,wBACPC,UAAW,GAGb,CACED,MAAO,iBACPC,UAAW,IAGf0F,aAAa,EACbtF,SAAU,CACR,CAEEhB,MAAO,YACT,CACExE,UAAW,SACXoF,UAAW,EACXD,MAAO,MACPG,IAAK,MACLK,cAAc,EACdsF,WAAW,GAEb,CACEjL,UAAW,OACXoF,UAAW,EACXD,MAAO,SACPG,IAAK,MACLK,cAAc,EACdqF,YAAY,GAEd,CACEhL,UAAW,SACXoF,UAAW,EACXD,MAAO,SACPG,IAAK,MACLK,cAAc,EACdqF,YAAY,KAIZgW,EAAO,CACXhhB,UAAW,SACXwF,SAAU,GACV0I,SAAU,CACR,CACE/I,MAAO,aACPG,IAAK,QAEP,CACEH,MAAO,cACPG,IAAK,WAIL2b,EAAS,CACbjhB,UAAW,WACXwF,SAAU,GACV0I,SAAU,CACR,CACE/I,MAAO,cACPG,IAAK,MAEP,CACEH,MAAO,aACPG,IAAK,IACLF,UAAW,KAQX8b,EAAsB/R,EAAKJ,QAAQiS,EAAM,CAAExb,SAAU,KACrD2b,EAAsBhS,EAAKJ,QAAQkS,EAAQ,CAAEzb,SAAU,KAC7Dwb,EAAKxb,SAAS/D,KAAK0f,GACnBF,EAAOzb,SAAS/D,KAAKyf,GAErB,IAAIE,EAAc,CAChBN,EACAC,GA2CF,MAxCA,CACEC,EACAC,EACAC,EACAC,GACA1jB,SAAQkJ,IACRA,EAAEnB,SAAWmB,EAAEnB,SAASjF,OAAO6gB,EAAY,IAkCtC,CACL1jB,KAAM,WACN2R,QAAS,CACP,KACA,SACA,OAEF7J,SAAU,CApCG,CACbxF,UAAW,UACXkO,SAAU,CACR,CACE/I,MAAO,UACPG,IAAK,IACLE,SARN4b,EAAcA,EAAY7gB,OAAOygB,EAAMC,IAUnC,CACE9b,MAAO,uBACPK,SAAU,CACR,CAAEL,MAAO,WACT,CACEA,MAAO,IACPG,IAAK,MACLE,SAAU4b,OAuBhBN,EAjLS,CACX9gB,UAAW,SACXmF,MAAO,mCACPG,IAAK,OACL0F,YAAY,GA+KVgW,EACAC,EAnBe,CACjBjhB,UAAW,QACXmF,MAAO,SACPK,SAAU4b,EACV9b,IAAK,KAnMM,CACXtF,UAAW,OACXkO,SAAU,CAER,CAAE/I,MAAO,iCACT,CAAEA,MAAO,iCAET,CACEA,MAAO,MACPG,IAAK,aAEP,CACEH,MAAO,MACPG,IAAK,aAEP,CAAEH,MAAO,SACT,CACEA,MAAO,kBAGPK,SAAU,CACR,CACEL,MAAO,cACPG,IAAK,WAGTF,UAAW,KA9BO,CACtBD,MAAO,cACPG,IAAK,KAwNHyb,EAlLmB,CACrB5b,MAAO,eACP2F,aAAa,EACbtF,SAAU,CACR,CACExF,UAAW,SACXmF,MAAO,KACPG,IAAK,KACLK,cAAc,EACdqF,YAAY,GAEd,CACEhL,UAAW,OACXmF,MAAO,OACPG,IAAK,IACLK,cAAc,MAuKtB,IN/MA4U,EAAYlD,iBAAiB,UOnB7B,SAAgBlI,GACd,IAAMkS,EAAe,YACfC,EAAY,CAChBlc,UAAW,EACXI,SAAU,CAAE,CAAEL,MAAOkc,KAGvB,MAAO,CACL3jB,KAAM,SACNmK,SAAU,CACRoB,QACE,qLAEFgS,SACE,swCAiBJ1V,QAAS,0BACTC,SAAU,CACR,CACExF,UAAW,WACX0H,cAAe,WACfpC,IAAK,IACLE,SAAU,CACR2J,EAAK9I,sBACL,CACErG,UAAW,SACXkO,SAAU,CACR,CACE/I,MAAO,MACPG,IAAK,OAEP,CACEH,MAAO,MACPG,IAAK,WAMf,CACEtF,UAAW,WACXmF,MAAO,aACPC,UAAW,EACXgD,OAAQkZ,GAEV,CACEnc,MAAO,wBAA0Bkc,EACjCjc,UAAW,GAEb,CACEpF,UAAW,SACXmF,MAAOgK,EAAKnK,YACZI,UAAW,EACXgD,OAAQkZ,GAEV,CACEthB,UAAW,SACXmF,MAAO,IACPG,IAAK,IACLE,SAAU,CAAE,CAAEL,MAAO,QAEvB,CACEA,MAAO,WACPC,UAAW,EACXgD,OAAQkZ,GAEV,CACEthB,UAAW,SACXmF,MAAO,IACPG,IAAK,IACLE,SAAU,CAAE,CAAEL,MAAO,OACrBiD,OAAQkZ,GAEVnS,EAAKzJ,QAAQ,iBAAkB,kBAC/ByJ,EAAKzJ,QAAQ,IAAK,MAGxB,IPxEA6U,EAAYlD,iBAAiB,aQzB7B,SAAmBlI,GACjB,MAAO,CACLzR,KAAM,aACN2R,QAAS,CACP,OACA,OAEFK,mBAAmB,EAEvB,IRiBA6K,EAAYlD,iBAAiB,US1B7B,SAAgBlI,GACd,IAAM9K,EAAQ8K,EAAK9K,MACbQ,EAAW,66jBACX0c,EAAiB,CACrB,MACA,KACA,SACA,QACA,QACA,QACA,OACA,QACA,WACA,MACA,MACA,OACA,OACA,SACA,UACA,MACA,OACA,SACA,KACA,SACA,KACA,KACA,SACA,QACA,cACA,MACA,KACA,OACA,QACA,SACA,MACA,QACA,OACA,SAuGIvH,EAAW,CACftM,SAAU,sBACVzE,QAASsY,EACTtG,SAvGgB,CAChB,aACA,MACA,MACA,MACA,QACA,MACA,OACA,aACA,YACA,QACA,WACA,MACA,cACA,UACA,UACA,UACA,OACA,MACA,SACA,YACA,OACA,OACA,SACA,QACA,SACA,YACA,UACA,UACA,UACA,OACA,OACA,MACA,KACA,QACA,MACA,aACA,aACA,OACA,MACA,OACA,SACA,MACA,MACA,aACA,MACA,OACA,SACA,MACA,OACA,MACA,MACA,QACA,WACA,QACA,OACA,WACA,QACA,MACA,UACA,QACA,SACA,eACA,MACA,MACA,QACA,QACA,OACA,OACA,OAmCAD,QAhCe,CACf,YACA,WACA,QACA,OACA,iBACA,QA2BApd,KArBY,CACZ,MACA,WACA,YACA,OACA,OACA,UACA,UACA,WACA,WACA,MACA,QACA,OACA,UAWI4jB,EAAS,CACbxhB,UAAW,OACXmF,MAAO,kBAGHuV,EAAQ,CACZ1a,UAAW,QACXmF,MAAO,KACPG,IAAK,KACLuC,SAAUmS,EACVzU,QAAS,KAGLkc,EAAkB,CACtBtc,MAAO,OACPC,UAAW,GAGPqW,EAAS,CACbzb,UAAW,SACXwF,SAAU,CAAE2J,EAAKjK,kBACjBgJ,SAAU,CACR,CACE/I,MAAO,yCACPG,IAAK,MACLE,SAAU,CACR2J,EAAKjK,iBACLsc,GAEFpc,UAAW,IAEb,CACED,MAAO,yCACPG,IAAK,MACLE,SAAU,CACR2J,EAAKjK,iBACLsc,GAEFpc,UAAW,IAEb,CACED,MAAO,8BACPG,IAAK,MACLE,SAAU,CACR2J,EAAKjK,iBACLsc,EACAC,EACA/G,IAGJ,CACEvV,MAAO,8BACPG,IAAK,MACLE,SAAU,CACR2J,EAAKjK,iBACLsc,EACAC,EACA/G,IAGJ,CACEvV,MAAO,eACPG,IAAK,IACLF,UAAW,IAEb,CACED,MAAO,eACPG,IAAK,IACLF,UAAW,IAEb,CACED,MAAO,4BACPG,IAAK,KAEP,CACEH,MAAO,4BACPG,IAAK,KAEP,CACEH,MAAO,4BACPG,IAAK,IACLE,SAAU,CACR2J,EAAKjK,iBACLuc,EACA/G,IAGJ,CACEvV,MAAO,4BACPG,IAAK,IACLE,SAAU,CACR2J,EAAKjK,iBACLuc,EACA/G,IAGJvL,EAAK9J,iBACL8J,EAAK1J,oBAKHic,EAAY,kBACZC,EAAa,QAAHphB,OAAWmhB,EAAS,WAAAnhB,OAAUmhB,EAAS,UAAAnhB,OAASmhB,EAAS,QAMnE3e,EAAY,OAAHxC,OAAUghB,EAAe1gB,KAAK,MACvCmd,EAAS,CACbhe,UAAW,SACXoF,UAAW,EACX8I,SAAU,CAWR,CACE/I,MAAO,QAAF5E,OAAUmhB,EAAS,OAAAnhB,OAAMohB,EAAU,gBAAAphB,OAAemhB,EAAS,aAAAnhB,OAAYwC,EAAS,MAEvF,CACEoC,MAAO,IAAF5E,OAAMohB,EAAU,WASvB,CACExc,MAAO,0CAAF5E,OAA4CwC,EAAS,MAE5D,CACEoC,MAAO,4BAAF5E,OAA8BwC,EAAS,MAE9C,CACEoC,MAAO,6BAAF5E,OAA+BwC,EAAS,MAE/C,CACEoC,MAAO,mCAAF5E,OAAqCwC,EAAS,MAKrD,CACEoC,MAAO,OAAF5E,OAASmhB,EAAS,YAAAnhB,OAAWwC,EAAS,QAI3C6e,EAAe,CACnB5hB,UAAW,UACXmF,MAAOd,EAAMtB,UAAU,WACvBuC,IAAK,IACLuC,SAAUmS,EACVxU,SAAU,CACR,CACEL,MAAO,WAGT,CACEA,MAAO,IACPG,IAAK,OACLuI,gBAAgB,KAIhB+R,EAAS,CACb5f,UAAW,SACXkO,SAAU,CAER,CACElO,UAAW,GACXmF,MAAO,UACP0F,MAAM,GAER,CACE1F,MAAO,KACPG,IAAK,KACLK,cAAc,EACdqF,YAAY,EACZnD,SAAUmS,EACVxU,SAAU,CACR,OACAgc,EACAxD,EACAvC,EACAtM,EAAKpJ,sBAWb,OANA2U,EAAMlV,SAAW,CACfiW,EACAuC,EACAwD,GAGK,CACL9jB,KAAM,SACN2R,QAAS,CACP,KACA,MACA,WAEF7D,cAAc,EACd3D,SAAUmS,EACVzU,QAAS,cACTC,SAAU,CACRgc,EACAxD,EACA,CAEE7Y,MAAO,YAET,CAGEuC,cAAe,KACftC,UAAW,GAEbqW,EACAmG,EACAzS,EAAKpJ,kBACL,CACEvB,MAAO,CACL,QAAS,MACTK,GAEFvF,MAAO,CACL,EAAG,UACH,EAAG,kBAELkG,SAAU,CAAEoa,IAEd,CACE1R,SAAU,CACR,CACE1J,MAAO,CACL,UAAW,MACXK,EAAU,MACV,QAASA,EAAS,UAGtB,CACEL,MAAO,CACL,UAAW,MACXK,KAINvF,MAAO,CACL,EAAG,UACH,EAAG,cACH,EAAG,0BAGP,CACEU,UAAW,OACXmF,MAAO,WACPG,IAAK,UACLE,SAAU,CACRwY,EACA4B,EACAnE,KAKV,IT7YAlB,EAAYlD,iBAAiB,KUxB7B,SAAWlI,GACT,IAAM9K,EAAQ8K,EAAK9K,MAObQ,EAAW,uDACXgd,EAAkBxd,EAAMhB,OAE5B,gDAEA,0CAEA,iDAEIye,EAAe,mEACfC,EAAiB1d,EAAMhB,OAC3B,OACA,OACA,OACA,QACA,KACA,KAGF,MAAO,CACL3F,KAAM,IAENmK,SAAU,CACR6F,SAAU7I,EACVoE,QACE,kDACF+R,QACE,wFAEFC,SAEE,khCAuBJzV,SAAU,CAER2J,EAAKzJ,QACH,KACA,IACA,CAAEF,SAAU,CACV,CAMElG,MAAO,SACPkF,MAAO,YACP4D,OAAQ,CACN9C,IAAKjB,EAAMtB,UAAUsB,EAAMhB,OAEzB,yBAEA,cAEFkF,YAAY,IAGhB,CAGEjJ,MAAO,SACP6F,MAAO,SACPG,IAAK,IACLE,SAAU,CACR,CACElG,MAAO,WACP4O,SAAU,CACR,CAAE1J,MAAOK,GACT,CAAEL,MAAO,sBAEX+D,YAAY,KAIlB,CACEjJ,MAAO,SACPkF,MAAO,cAET,CACElF,MAAO,UACPkF,MAAO,kBAKb2K,EAAKpJ,kBAEL,CACEzG,MAAO,SACPkG,SAAU,CAAE2J,EAAKjK,kBACjBgJ,SAAU,CACRiB,EAAK1I,kBAAkB,CACrBtB,MAAO,cACPG,IAAK,YAEP6J,EAAK1I,kBAAkB,CACrBtB,MAAO,cACPG,IAAK,YAEP6J,EAAK1I,kBAAkB,CACrBtB,MAAO,cACPG,IAAK,YAEP6J,EAAK1I,kBAAkB,CACrBtB,MAAO,cACPG,IAAK,YAEP6J,EAAK1I,kBAAkB,CACrBtB,MAAO,cACPG,IAAK,YAEP6J,EAAK1I,kBAAkB,CACrBtB,MAAO,cACPG,IAAK,YAEP,CACEH,MAAO,IACPG,IAAK,IACLF,UAAW,GAEb,CACED,MAAO,IACPG,IAAK,IACLF,UAAW,KAcjB,CACEA,UAAW,EACX8I,SAAU,CACR,CACE5O,MAAO,CACL,EAAG,WACH,EAAG,UAELkF,MAAO,CACLsd,EACAD,IAGJ,CACEviB,MAAO,CACL,EAAG,WACH,EAAG,UAELkF,MAAO,CACL,UACAqd,IAGJ,CACEviB,MAAO,CACL,EAAG,cACH,EAAG,UAELkF,MAAO,CACLud,EACAF,IAGJ,CACEviB,MAAO,CAAE,EAAG,UACZkF,MAAO,CACL,mBACAqd,MAOR,CAEEviB,MAAO,CAAE,EAAG,YACZkF,MAAO,CACLK,EACA,MACA,KACA,QAIJ,CACEvF,MAAO,WACP8F,UAAW,EACX8I,SAAU,CACR,CAAE1J,MAAOsd,GACT,CAAEtd,MAAO,aAIb,CACElF,MAAO,cACP8F,UAAW,EACXZ,MAAOud,GAGT,CAEE5c,MAAO,IACPG,IAAK,IACLE,SAAU,CAAE,CAAEL,MAAO,UAI7B,IV3NAoV,EAAYlD,iBAAiB,QW1B7B,SAAclI,GACZ,IAAM9K,EAAQ8K,EAAK9K,MACb2d,EAAiB,qFAEjBC,EAAgB5d,EAAMhB,OAC1B,uBAEA,8BAGI6e,EAA+B7d,EAAM9D,OAAO0hB,EAAe,YAa3DE,EAAgB,CACpB,oBAAqB,CACnB,WACA,WACA,gBAEF,oBAAqB,CACnB,OACA,SAEFlZ,QAAS,CACP,QACA,MACA,QACA,QACA,QACA,OACA,QACA,UACA,KACA,OACA,QACA,MACA,MACA,SACA,MACA,KACA,KACA,SACA,OACA,MACA,KACA,OACA,UACA,SACA,QACA,SACA,OACA,QACA,SACA,QACA,OACA,QACA,QApDF,UACA,SACA,UACA,SACA,UACA,YACA,QACA,SAgDAgS,SAAU,CACR,OACA,SACA,gBACA,cACA,cACA,gBACA,mBACA,mBAEFD,QAAS,CACP,OACA,QACA,QAGEoH,EAAY,CAChBpiB,UAAW,SACXmF,MAAO,cAEHkd,EAAa,CACjBld,MAAO,KACPG,IAAK,KAEDgd,EAAgB,CACpBnT,EAAKzJ,QACH,IACA,IACA,CAAEF,SAAU,CAAE4c,KAEhBjT,EAAKzJ,QACH,UACA,QACA,CACEF,SAAU,CAAE4c,GACZhd,UAAW,KAGf+J,EAAKzJ,QAAQ,WAAYyJ,EAAKpI,mBAE1B2T,EAAQ,CACZ1a,UAAW,QACXmF,MAAO,MACPG,IAAK,KACLuC,SAAUsa,GAEN1G,EAAS,CACbzb,UAAW,SACXwF,SAAU,CACR2J,EAAKjK,iBACLwV,GAEFxM,SAAU,CACR,CACE/I,MAAO,IACPG,IAAK,KAEP,CACEH,MAAO,IACPG,IAAK,KAEP,CACEH,MAAO,IACPG,IAAK,KAEP,CACEH,MAAO,cACPG,IAAK,MAEP,CACEH,MAAO,cACPG,IAAK,MAEP,CACEH,MAAO,cACPG,IAAK,MAEP,CACEH,MAAO,aACPG,IAAK,KAEP,CACEH,MAAO,cACPG,IAAK,MAEP,CACEH,MAAO,aACPG,IAAK,KAEP,CACEH,MAAO,aACPG,IAAK,KAEP,CACEH,MAAO,cACPG,IAAK,MAIP,CAAEH,MAAO,mBACT,CAAEA,MAAO,6BACT,CAAEA,MAAO,mCACT,CAAEA,MAAO,2DACT,CAAEA,MAAO,2BACT,CAAEA,MAAO,aAET,CAGEA,MAAOd,EAAM9D,OACX,YACA8D,EAAMtB,UAAU,6CAElByC,SAAU,CACR2J,EAAK1I,kBAAkB,CACrBtB,MAAO,QACPG,IAAK,QACLE,SAAU,CACR2J,EAAKjK,iBACLwV,SAYN6H,EAAS,kBACTvE,EAAS,CACbhe,UAAW,SACXoF,UAAW,EACX8I,SAAU,CAER,CAAE/I,MAAO,OAAF5E,OAPK,oBAOW,UAAAA,OAASgiB,EAAM,kBAAAhiB,OAAiBgiB,EAAM,eAI7D,CAAEpd,MAAO,kCACT,CAAEA,MAAO,kCACT,CAAEA,MAAO,kCACT,CAAEA,MAAO,8CAGT,CAAEA,MAAO,2BAIPya,EAAS,CACb1R,SAAU,CACR,CACE1J,MAAO,QAET,CACExE,UAAW,SACXmF,MAAO,KACPG,IAAK,SACLK,cAAc,EACd4C,YAAY,EACZV,SAAUsa,KA8EVK,EAAwB,CAC5B/G,EA/DuB,CACvBvN,SAAU,CACR,CACE1J,MAAO,CACL,WACA0d,EACA,UACAA,IAGJ,CACE1d,MAAO,CACL,sBACA0d,KAIN5iB,MAAO,CACL,EAAG,cACH,EAAG,yBAELuI,SAAUsa,GAhCW,CACrB3d,MAAO,CACL,sBACA0d,GAEF5iB,MAAO,CACL,EAAG,eAELuI,SAAUsa,GA+CY,CACtB/c,UAAW,EACXZ,MAAO,CACL0d,EACA,cAEF5iB,MAAO,CACL,EAAG,gBA3BqB,CAC1B8F,UAAW,EACXZ,MAAO,sBACPxE,UAAW,qBA6BW,CACtBoF,UAAW,EACXZ,MAAOyd,EACP3iB,MAAO,eA7BiB,CACxBkF,MAAO,CACL,MAAO,MACPwd,GAEF1iB,MAAO,CACL,EAAG,UACH,EAAG,kBAELkG,SAAU,CACRoa,IA8BF,CAEEza,MAAOgK,EAAKtK,SAAW,MACzB,CACE7E,UAAW,SACXmF,MAAOgK,EAAKrK,oBAAsB,YAClCM,UAAW,GAEb,CACEpF,UAAW,SACXmF,MAAO,WACPK,SAAU,CACRiW,EACA,CAAEtW,MAAO6c,IAEX5c,UAAW,GAEb4Y,EACA,CAGEhe,UAAW,WACXmF,MAAO,8DAET,CACEnF,UAAW,SACXmF,MAAO,KACPG,IAAK,KACLK,cAAc,EACdqF,YAAY,EACZ5F,UAAW,EACXyC,SAAUsa,GAEZ,CACEhd,MAAO,IAAMgK,EAAKlI,eAAiB,eACnCY,SAAU,SACVrC,SAAU,CACR,CACExF,UAAW,SACXwF,SAAU,CACR2J,EAAKjK,iBACLwV,GAEFnV,QAAS,KACT2I,SAAU,CACR,CACE/I,MAAO,IACPG,IAAK,WAEP,CACEH,MAAO,OACPG,IAAK,YAEP,CACEH,MAAO,QACPG,IAAK,aAEP,CACEH,MAAO,MACPG,IAAK,WAEP,CACEH,MAAO,QACPG,IAAK,gBAIX/E,OAAO8hB,EAAYC,GACrBld,UAAW,IAEb7E,OAAO8hB,EAAYC,GAErB5H,EAAMlV,SAAWgd,EACjB5C,EAAOpa,SAAWgd,EAIlB,IAKMC,EAAc,CAClB,CACEtd,MAAO,SACPiD,OAAQ,CACN9C,IAAK,IACLE,SAAUgd,IAGd,CACExiB,UAAW,cACXmF,MAAO,iGACPiD,OAAQ,CACN9C,IAAK,IACLuC,SAAUsa,EACV3c,SAAUgd,KAOhB,OAFAF,EAAczO,QAAQwO,GAEf,CACL3kB,KAAM,OACN2R,QAAS,CACP,KACA,UACA,UACA,OACA,OAEFxH,SAAUsa,EACV5c,QAAS,OACTC,SAAU,CAAE2J,EAAKjI,QAAQ,CAAEE,OAAQ,UAChC7G,OAAOkiB,GACPliB,OAAO+hB,GACP/hB,OAAOiiB,GAEd,IXzZAjI,EAAYlD,iBAAiB,SY3B7B,SAAelI,GACb,MAAO,CACLzR,KAAM,gBACN2R,QAAS,CACP,UACA,gBAEF7J,SAAU,CACR,CACExF,UAAW,cAIXmF,MAAO,qCACPiD,OAAQ,CACN9C,IAAK,gBACLoM,YAAa,UAKvB,IZOA6I,EAAYlD,iBAAiB,Oaf7B,SAAalI,GACX,IAAM9K,EAAQ8K,EAAK9K,MACbqe,EAAevT,EAAKzJ,QAAQ,KAAM,KAiBlCuU,EAAW,CACf,OACA,QAGA,WAUIC,EAAQ,CACZ,SACA,SACA,OACA,UACA,OACA,YACA,OACA,OACA,MACA,WACA,UACA,QACA,MACA,UACA,WACA,QACA,QACA,WACA,UACA,OACA,MACA,WACA,OACA,YACA,UACA,UACA,aAmYIyI,EAAqB,CACzB,MACA,OACA,YACA,OACA,OACA,MACA,OACA,OACA,UACA,WACA,OACA,MACA,OACA,QACA,YACA,aACA,YACA,aACA,QACA,UACA,MACA,UACA,cACA,QACA,aACA,gBACA,cACA,cACA,iBACA,aACA,aACA,uBACA,aACA,MACA,aACA,OACA,UACA,KACA,MACA,QACA,QACA,MACA,MACA,MACA,YACA,QACA,SACA,eACA,kBACA,kBACA,WACA,iBACA,QACA,OACA,YACA,YACA,aACA,iBACA,UACA,aACA,WACA,WACA,WACA,aACA,MACA,OACA,OACA,aACA,cACA,YACA,kBACA,MACA,MACA,OACA,YACA,kBACA,QACA,OACA,aACA,SACA,QACA,WACA,UACA,WACA,gBAwBIC,EAAS,CACb,eACA,cACA,cACA,cACA,WACA,cACA,iBACA,gBACA,cACA,gBACA,gBACA,eACA,cACA,aACA,cACA,iBAGIC,EAAYF,EAEZ3I,EAAW,CArff,MACA,OACA,MACA,WACA,QACA,MACA,MACA,MACA,QACA,YACA,wBACA,KACA,aACA,OACA,aACA,KACA,OACA,SACA,gBACA,MACA,QACA,cACA,kBACA,UACA,SACA,SACA,OACA,UACA,OACA,KACA,OACA,SACA,cACA,WACA,OACA,OACA,OACA,UACA,OACA,cACA,YACA,mBACA,QACA,aACA,OACA,QACA,WACA,UACA,UACA,SACA,SACA,YACA,UACA,aACA,WACA,UACA,OACA,OACA,gBACA,MACA,OACA,QACA,YACA,aACA,SACA,QACA,OACA,YACA,UACA,kBACA,eACA,kCACA,eACA,eACA,cACA,iBACA,eACA,oBACA,eACA,eACA,mCACA,eACA,SACA,QACA,OACA,MACA,aACA,MACA,UACA,WACA,UACA,UACA,SACA,SACA,aACA,QACA,WACA,gBACA,aACA,WACA,SACA,OACA,UACA,OACA,UACA,OACA,QACA,MACA,YACA,gBACA,WACA,SACA,SACA,QACA,SACA,OACA,UACA,SACA,MACA,WACA,UACA,QACA,QACA,SACA,cACA,QACA,QACA,MACA,UACA,YACA,OACA,OACA,OACA,WACA,SACA,MACA,SACA,QACA,QACA,WACA,SACA,SACA,OACA,OACA,WACA,KACA,YACA,UACA,QACA,QACA,cACA,SACA,MACA,UACA,YACA,eACA,WACA,OACA,KACA,OACA,aACA,gBACA,cACA,cACA,iBACA,aACA,aACA,uBACA,aACA,MACA,WACA,QACA,aACA,UACA,OACA,UACA,OACA,OACA,aACA,UACA,KACA,QACA,YACA,iBACA,MACA,QACA,QACA,QACA,eACA,kBACA,UACA,MACA,SACA,QACA,SACA,MACA,SACA,MACA,WACA,SACA,QACA,WACA,WACA,UACA,QACA,QACA,MACA,KACA,OACA,YACA,MACA,YACA,QACA,OACA,SACA,UACA,eACA,oBACA,KACA,SACA,MACA,OACA,KACA,MACA,OACA,OACA,KACA,QACA,MACA,QACA,OACA,WACA,UACA,YACA,YACA,UACA,MACA,UACA,eACA,kBACA,kBACA,SACA,UACA,WACA,iBACA,QACA,WACA,YACA,UACA,UACA,YACA,MACA,QACA,OACA,QACA,OACA,YACA,MACA,aACA,cACA,YACA,YACA,aACA,iBACA,UACA,aACA,WACA,WACA,WACA,UACA,SACA,SACA,UACA,SACA,QACA,WACA,SACA,MACA,aACA,OACA,UACA,YACA,QACA,SACA,SACA,SACA,OACA,SACA,YACA,eACA,MACA,OACA,UACA,MACA,OACA,OACA,WACA,OACA,WACA,eACA,MACA,eACA,WACA,aACA,OACA,QACA,SACA,aACA,cACA,cACA,SACA,YACA,kBACA,WACA,MACA,YACA,SACA,cACA,cACA,QACA,cACA,MACA,OACA,OACA,OACA,YACA,gBACA,kBACA,KACA,WACA,YACA,kBACA,cACA,QACA,UACA,OACA,aACA,OACA,WACA,UACA,QACA,SACA,UACA,SACA,SACA,QACA,OACA,QACA,QACA,SACA,WACA,UACA,WACA,YACA,UACA,UACA,aACA,OACA,WACA,QACA,eACA,SACA,OACA,SACA,UACA,OAzXA,MACA,MACA,YACA,OACA,QACA,QACA,OACA,QA6fAvF,QAAQxL,IACA0Z,EAAmBviB,SAAS6I,KAchCiX,EAAgB,CACpB/a,MAAOd,EAAM9D,OAAO,KAAM8D,EAAMhB,UAAUwf,GAAY,SACtDzd,UAAW,EACXyC,SAAU,CAAEoT,SAAU4H,IAoBxB,MAAO,CACLnlB,KAAM,MACN6N,kBAAkB,EAElBhG,QAAS,WACTsC,SAAU,CACR6F,SAAU,YACVzE,QAvBJ,SAAyB0K,GAEjB,IAAAoJ,EAAAhe,UAAAC,OAAA,QAAAf,IAAAc,UAAA,GAAAA,UAAA,GAAJ,CAAC,EADH+jB,EAAU/F,EAAV+F,WAEMC,EAFUhG,EAAJiG,KAIZ,OADAF,EAAaA,GAAc,GACpBnP,EAAKlT,KAAKqT,GACXA,EAAKtP,MAAM,WAAase,EAAW1iB,SAAS0T,GACvCA,EACEiP,EAAUjP,GACZ,GAAPvT,OAAUuT,EAAI,MAEPA,GAGb,CAUMmP,CAAgBjJ,EAAU,CAAEgJ,KAAOtiB,GAAMA,EAAE1B,OAAS,IACtDgc,QAASf,EACTrc,KAAMsc,EACNe,SA7F4B,CAC9B,kBACA,eACA,kCACA,eACA,eACA,iBACA,mCACA,eACA,eACA,cACA,cACA,eACA,YACA,oBACA,mBAgFAzV,SAAU,CACR,CACEL,MAAOd,EAAMhB,UAAUuf,GACvBxd,UAAW,EACXyC,SAAU,CACR6F,SAAU,UACVzE,QAAS+Q,EAASzZ,OAAOqiB,GACzB5H,QAASf,EACTrc,KAAMsc,IAGV,CACEla,UAAW,OACXmF,MAAOd,EAAMhB,OAzmBjB,mBACA,eACA,gBACA,qBAwmBE6c,EA9Da,CACflgB,UAAW,WACXmF,MAAO,uBAxkBM,CACbnF,UAAW,SACXkO,SAAU,CACR,CACE/I,MAAO,IACPG,IAAK,IACLE,SAAU,CAAE,CAAEL,MAAO,UAID,CACxBA,MAAO,IACPG,IAAK,IACLE,SAAU,CAAE,CAAEL,MAAO,QA2nBnBgK,EAAKlJ,cACLkJ,EAAKrJ,qBACL4c,EA/Da,CACf1iB,UAAW,WACXmF,MAAO,gDACPC,UAAW,IAgEf,IbjoBAmV,EAAYlD,iBAAiB,Oc9B7B,SAAalI,GACX,IAAM9K,EAAQ8K,EAAK9K,MAQb6e,EAAc7e,EAAM9D,OAAO,4kQAAa8D,EAAMpB,SAAS,wlQAAqB,ulQAE5EkgB,EAAe,CACnBnjB,UAAW,SACXmF,MAAO,oCAEHie,EAAoB,CACxBje,MAAO,KACPK,SAAU,CACR,CACExF,UAAW,UACXmF,MAAO,sBACPI,QAAS,QAIT8d,EAAwBlU,EAAKJ,QAAQqU,EAAmB,CAC5Dje,MAAO,KACPG,IAAK,OAEDge,EAAwBnU,EAAKJ,QAAQI,EAAK9J,iBAAkB,CAAErF,UAAW,WACzEujB,EAAyBpU,EAAKJ,QAAQI,EAAK1J,kBAAmB,CAAEzF,UAAW,WAC3EwjB,EAAgB,CACpB3V,gBAAgB,EAChBtI,QAAS,IACTH,UAAW,EACXI,SAAU,CACR,CACExF,UAAW,OACXmF,MA5Be,slQA6BfC,UAAW,GAEb,CACED,MAAO,OACPC,UAAW,EACXI,SAAU,CACR,CACExF,UAAW,SACXuI,YAAY,EACZ2F,SAAU,CACR,CACE/I,MAAO,IACPG,IAAK,IACLE,SAAU,CAAE2d,IAEd,CACEhe,MAAO,IACPG,IAAK,IACLE,SAAU,CAAE2d,IAEd,CAAEhe,MAAO,sBAOrB,MAAO,CACLzH,KAAM,YACN2R,QAAS,CACP,OACA,QACA,MACA,OACA,MACA,MACA,MACA,QACA,MACA,OAEF9D,kBAAkB,EAClBC,cAAc,EACdhG,SAAU,CACR,CACExF,UAAW,OACXmF,MAAO,UACPG,IAAK,IACLF,UAAW,GACXI,SAAU,CACR4d,EACAG,EACAD,EACAD,EACA,CACEle,MAAO,KACPG,IAAK,KACLE,SAAU,CACR,CACExF,UAAW,OACXmF,MAAO,UACPG,IAAK,IACLE,SAAU,CACR4d,EACAC,EACAE,EACAD,QAOZnU,EAAKzJ,QACH,OACA,MACA,CAAEN,UAAW,KAEf,CACED,MAAO,cACPG,IAAK,QACLF,UAAW,IAEb+d,EAEA,CACEnjB,UAAW,OACXsF,IAAK,MACL4I,SAAU,CACR,CACE/I,MAAO,SACPC,UAAW,GACXI,SAAU,CACR+d,IAGJ,CACEpe,MAAO,uBAKb,CACEnF,UAAW,MAMXmF,MAAO,iBACPG,IAAK,IACLuC,SAAU,CAAEnK,KAAM,SAClB8H,SAAU,CAAEge,GACZpb,OAAQ,CACN9C,IAAK,YACL2F,WAAW,EACXyG,YAAa,CACX,MACA,SAIN,CACE1R,UAAW,MAEXmF,MAAO,kBACPG,IAAK,IACLuC,SAAU,CAAEnK,KAAM,UAClB8H,SAAU,CAAEge,GACZpb,OAAQ,CACN9C,IAAK,aACL2F,WAAW,EACXyG,YAAa,CACX,aACA,aACA,SAKN,CACE1R,UAAW,MACXmF,MAAO,WAGT,CACEnF,UAAW,MACXmF,MAAOd,EAAM9D,OACX,IACA8D,EAAMtB,UAAUsB,EAAM9D,OACpB2iB,EAIA7e,EAAMhB,OAAO,MAAO,IAAK,SAG7BiC,IAAK,OACLE,SAAU,CACR,CACExF,UAAW,OACXmF,MAAO+d,EACP9d,UAAW,EACXgD,OAAQob,KAKd,CACExjB,UAAW,MACXmF,MAAOd,EAAM9D,OACX,MACA8D,EAAMtB,UAAUsB,EAAM9D,OACpB2iB,EAAa,OAGjB1d,SAAU,CACR,CACExF,UAAW,OACXmF,MAAO+d,EACP9d,UAAW,GAEb,CACED,MAAO,IACPC,UAAW,EACXmD,YAAY,MAMxB,IdvMAgS,EAAYlD,iBAAiB,Qe9B7B,SAAclI,GACZ,IAAM8K,EAAW,yBAGXwJ,EAAiB,8BA8BjBhI,EAAS,CACbzb,UAAW,SACXoF,UAAW,EACX8I,SAAU,CACR,CACE/I,MAAO,IACPG,IAAK,KAEP,CACEH,MAAO,IACPG,IAAK,KAEP,CAAEH,MAAO,QAEXK,SAAU,CACR2J,EAAKjK,iBA5BkB,CACzBlF,UAAW,oBACXkO,SAAU,CACR,CACE/I,MAAO,OACPG,IAAK,QAEP,CACEH,MAAO,MACPG,IAAK,UA0BLoe,EAAmBvU,EAAKJ,QAAQ0M,EAAQ,CAAEvN,SAAU,CACxD,CACE/I,MAAO,IACPG,IAAK,KAEP,CACEH,MAAO,IACPG,IAAK,KAEP,CAAEH,MAAO,mBAYLwe,EAAkB,CACtBre,IAAK,IACLuI,gBAAgB,EAChB7C,YAAY,EACZnD,SAAUoS,EACV7U,UAAW,GAEPwe,EAAS,CACbze,MAAO,KACPG,IAAK,KACLE,SAAU,CAAEme,GACZpe,QAAS,MACTH,UAAW,GAEPye,EAAQ,CACZ1e,MAAO,MACPG,IAAK,MACLE,SAAU,CAAEme,GACZpe,QAAS,MACTH,UAAW,GAGPmB,EAAQ,CAzFF,CACVvG,UAAW,OACXkO,SAAU,CACR,CAAE/I,MAAO,gCACT,CACEA,MAAO,kCACT,CACEA,MAAO,oCAoFX,CACEnF,UAAW,OACXmF,MAAO,YACPC,UAAW,IAEb,CAKEpF,UAAW,SACXmF,MAAO,iEAET,CACEA,MAAO,WACPG,IAAK,UACLoM,YAAa,OACb/L,cAAc,EACdqF,YAAY,EACZ5F,UAAW,GAEb,CACEpF,UAAW,OACXmF,MAAO,SAAWse,GAGpB,CACEzjB,UAAW,OACXmF,MAAO,KAAOse,EAAiB,KAEjC,CACEzjB,UAAW,OACXmF,MAAO,IAAMse,GAEf,CACEzjB,UAAW,OACXmF,MAAO,KAAOse,GAEhB,CACEzjB,UAAW,OACXmF,MAAO,IAAMgK,EAAKrK,oBAAsB,KAE1C,CACE9E,UAAW,OACXmF,MAAO,MAAQgK,EAAKrK,oBAAsB,KAE5C,CACE9E,UAAW,SAEXmF,MAAO,aACPC,UAAW,GAEb+J,EAAKpJ,kBACL,CACE2B,cAAeuS,EACfpS,SAAU,CAAEmT,QAASf,IApFP,CAChBja,UAAW,SACXmF,MAAO,iIAuFP,CACEnF,UAAW,SACXmF,MAAOgK,EAAKnK,YAAc,MAC1BI,UAAW,GAEbwe,EACAC,EACApI,GAGIqI,EAAc,IAAKvd,GAKzB,OAJAud,EAAYpiB,MACZoiB,EAAYriB,KAAKiiB,GACjBC,EAAgBne,SAAWse,EAEpB,CACLpmB,KAAM,OACN6N,kBAAkB,EAClB8D,QAAS,CAAE,OACX7J,SAAUe,EAEd,IftJA,O", "sources": ["webpack:///./node_modules/highlight.js/lib/core.js", "webpack:///./node_modules/highlight.js/styles/github.css", "webpack:///./node_modules/highlight.js/es/core.js", "webpack:///./node_modules/highlight.js/styles/github.css?a299", "webpack:///./node_modules/highlight.js/es/languages/css.js", "webpack:///./node_modules/highlight.js/es/languages/fsharp.js", "webpack:///./node_modules/highlight.js/es/languages/javascript.js", "webpack:///./src/third-party/highlight.js", "webpack:///./node_modules/highlight.js/es/languages/bash.js", "webpack:///./node_modules/highlight.js/es/languages/csharp.js", "webpack:///./node_modules/highlight.js/es/languages/http.js", "webpack:///./node_modules/highlight.js/es/languages/json.js", "webpack:///./node_modules/highlight.js/es/languages/julia.js", "webpack:///./node_modules/highlight.js/es/languages/markdown.js", "webpack:///./node_modules/highlight.js/es/languages/matlab.js", "webpack:///./node_modules/highlight.js/es/languages/plaintext.js", "webpack:///./node_modules/highlight.js/es/languages/python.js", "webpack:///./node_modules/highlight.js/es/languages/r.js", "webpack:///./node_modules/highlight.js/es/languages/ruby.js", "webpack:///./node_modules/highlight.js/es/languages/shell.js", "webpack:///./node_modules/highlight.js/es/languages/sql.js", "webpack:///./node_modules/highlight.js/es/languages/xml.js", "webpack:///./node_modules/highlight.js/es/languages/yaml.js"], "sourcesContent": ["/* eslint-disable no-multi-assign */\n\nfunction deepFreeze(obj) {\n  if (obj instanceof Map) {\n    obj.clear =\n      obj.delete =\n      obj.set =\n        function () {\n          throw new Error('map is read-only');\n        };\n  } else if (obj instanceof Set) {\n    obj.add =\n      obj.clear =\n      obj.delete =\n        function () {\n          throw new Error('set is read-only');\n        };\n  }\n\n  // Freeze self\n  Object.freeze(obj);\n\n  Object.getOwnPropertyNames(obj).forEach((name) => {\n    const prop = obj[name];\n    const type = typeof prop;\n\n    // Freeze prop if it is an object or function and also not already frozen\n    if ((type === 'object' || type === 'function') && !Object.isFrozen(prop)) {\n      deepFreeze(prop);\n    }\n  });\n\n  return obj;\n}\n\n/** @typedef {import('highlight.js').CallbackResponse} CallbackResponse */\n/** @typedef {import('highlight.js').CompiledMode} CompiledMode */\n/** @implements CallbackResponse */\n\nclass Response {\n  /**\n   * @param {CompiledMode} mode\n   */\n  constructor(mode) {\n    // eslint-disable-next-line no-undefined\n    if (mode.data === undefined) mode.data = {};\n\n    this.data = mode.data;\n    this.isMatchIgnored = false;\n  }\n\n  ignoreMatch() {\n    this.isMatchIgnored = true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {string}\n */\nfunction escapeHTML(value) {\n  return value\n    .replace(/&/g, '&amp;')\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n    .replace(/\"/g, '&quot;')\n    .replace(/'/g, '&#x27;');\n}\n\n/**\n * performs a shallow merge of multiple objects into one\n *\n * @template T\n * @param {T} original\n * @param {Record<string,any>[]} objects\n * @returns {T} a single new object\n */\nfunction inherit$1(original, ...objects) {\n  /** @type Record<string,any> */\n  const result = Object.create(null);\n\n  for (const key in original) {\n    result[key] = original[key];\n  }\n  objects.forEach(function(obj) {\n    for (const key in obj) {\n      result[key] = obj[key];\n    }\n  });\n  return /** @type {T} */ (result);\n}\n\n/**\n * @typedef {object} Renderer\n * @property {(text: string) => void} addText\n * @property {(node: Node) => void} openNode\n * @property {(node: Node) => void} closeNode\n * @property {() => string} value\n */\n\n/** @typedef {{scope?: string, language?: string, sublanguage?: boolean}} Node */\n/** @typedef {{walk: (r: Renderer) => void}} Tree */\n/** */\n\nconst SPAN_CLOSE = '</span>';\n\n/**\n * Determines if a node needs to be wrapped in <span>\n *\n * @param {Node} node */\nconst emitsWrappingTags = (node) => {\n  // rarely we can have a sublanguage where language is undefined\n  // TODO: track down why\n  return !!node.scope;\n};\n\n/**\n *\n * @param {string} name\n * @param {{prefix:string}} options\n */\nconst scopeToCSSClass = (name, { prefix }) => {\n  // sub-language\n  if (name.startsWith(\"language:\")) {\n    return name.replace(\"language:\", \"language-\");\n  }\n  // tiered scope: comment.line\n  if (name.includes(\".\")) {\n    const pieces = name.split(\".\");\n    return [\n      `${prefix}${pieces.shift()}`,\n      ...(pieces.map((x, i) => `${x}${\"_\".repeat(i + 1)}`))\n    ].join(\" \");\n  }\n  // simple scope\n  return `${prefix}${name}`;\n};\n\n/** @type {Renderer} */\nclass HTMLRenderer {\n  /**\n   * Creates a new HTMLRenderer\n   *\n   * @param {Tree} parseTree - the parse tree (must support `walk` API)\n   * @param {{classPrefix: string}} options\n   */\n  constructor(parseTree, options) {\n    this.buffer = \"\";\n    this.classPrefix = options.classPrefix;\n    parseTree.walk(this);\n  }\n\n  /**\n   * Adds texts to the output stream\n   *\n   * @param {string} text */\n  addText(text) {\n    this.buffer += escapeHTML(text);\n  }\n\n  /**\n   * Adds a node open to the output stream (if needed)\n   *\n   * @param {Node} node */\n  openNode(node) {\n    if (!emitsWrappingTags(node)) return;\n\n    const className = scopeToCSSClass(node.scope,\n      { prefix: this.classPrefix });\n    this.span(className);\n  }\n\n  /**\n   * Adds a node close to the output stream (if needed)\n   *\n   * @param {Node} node */\n  closeNode(node) {\n    if (!emitsWrappingTags(node)) return;\n\n    this.buffer += SPAN_CLOSE;\n  }\n\n  /**\n   * returns the accumulated buffer\n  */\n  value() {\n    return this.buffer;\n  }\n\n  // helpers\n\n  /**\n   * Builds a span element\n   *\n   * @param {string} className */\n  span(className) {\n    this.buffer += `<span class=\"${className}\">`;\n  }\n}\n\n/** @typedef {{scope?: string, language?: string, children: Node[]} | string} Node */\n/** @typedef {{scope?: string, language?: string, children: Node[]} } DataNode */\n/** @typedef {import('highlight.js').Emitter} Emitter */\n/**  */\n\n/** @returns {DataNode} */\nconst newNode = (opts = {}) => {\n  /** @type DataNode */\n  const result = { children: [] };\n  Object.assign(result, opts);\n  return result;\n};\n\nclass TokenTree {\n  constructor() {\n    /** @type DataNode */\n    this.rootNode = newNode();\n    this.stack = [this.rootNode];\n  }\n\n  get top() {\n    return this.stack[this.stack.length - 1];\n  }\n\n  get root() { return this.rootNode; }\n\n  /** @param {Node} node */\n  add(node) {\n    this.top.children.push(node);\n  }\n\n  /** @param {string} scope */\n  openNode(scope) {\n    /** @type Node */\n    const node = newNode({ scope });\n    this.add(node);\n    this.stack.push(node);\n  }\n\n  closeNode() {\n    if (this.stack.length > 1) {\n      return this.stack.pop();\n    }\n    // eslint-disable-next-line no-undefined\n    return undefined;\n  }\n\n  closeAllNodes() {\n    while (this.closeNode());\n  }\n\n  toJSON() {\n    return JSON.stringify(this.rootNode, null, 4);\n  }\n\n  /**\n   * @typedef { import(\"./html_renderer\").Renderer } Renderer\n   * @param {Renderer} builder\n   */\n  walk(builder) {\n    // this does not\n    return this.constructor._walk(builder, this.rootNode);\n    // this works\n    // return TokenTree._walk(builder, this.rootNode);\n  }\n\n  /**\n   * @param {Renderer} builder\n   * @param {Node} node\n   */\n  static _walk(builder, node) {\n    if (typeof node === \"string\") {\n      builder.addText(node);\n    } else if (node.children) {\n      builder.openNode(node);\n      node.children.forEach((child) => this._walk(builder, child));\n      builder.closeNode(node);\n    }\n    return builder;\n  }\n\n  /**\n   * @param {Node} node\n   */\n  static _collapse(node) {\n    if (typeof node === \"string\") return;\n    if (!node.children) return;\n\n    if (node.children.every(el => typeof el === \"string\")) {\n      // node.text = node.children.join(\"\");\n      // delete node.children;\n      node.children = [node.children.join(\"\")];\n    } else {\n      node.children.forEach((child) => {\n        TokenTree._collapse(child);\n      });\n    }\n  }\n}\n\n/**\n  Currently this is all private API, but this is the minimal API necessary\n  that an Emitter must implement to fully support the parser.\n\n  Minimal interface:\n\n  - addText(text)\n  - __addSublanguage(emitter, subLanguageName)\n  - startScope(scope)\n  - endScope()\n  - finalize()\n  - toHTML()\n\n*/\n\n/**\n * @implements {Emitter}\n */\nclass TokenTreeEmitter extends TokenTree {\n  /**\n   * @param {*} options\n   */\n  constructor(options) {\n    super();\n    this.options = options;\n  }\n\n  /**\n   * @param {string} text\n   */\n  addText(text) {\n    if (text === \"\") { return; }\n\n    this.add(text);\n  }\n\n  /** @param {string} scope */\n  startScope(scope) {\n    this.openNode(scope);\n  }\n\n  endScope() {\n    this.closeNode();\n  }\n\n  /**\n   * @param {Emitter & {root: DataNode}} emitter\n   * @param {string} name\n   */\n  __addSublanguage(emitter, name) {\n    /** @type DataNode */\n    const node = emitter.root;\n    if (name) node.scope = `language:${name}`;\n\n    this.add(node);\n  }\n\n  toHTML() {\n    const renderer = new HTMLRenderer(this, this.options);\n    return renderer.value();\n  }\n\n  finalize() {\n    this.closeAllNodes();\n    return true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction anyNumberOfTimes(re) {\n  return concat('(?:', re, ')*');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction optional(re) {\n  return concat('(?:', re, ')?');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * @param { Array<string | RegExp | Object> } args\n * @returns {object}\n */\nfunction stripOptionsFromArgs(args) {\n  const opts = args[args.length - 1];\n\n  if (typeof opts === 'object' && opts.constructor === Object) {\n    args.splice(args.length - 1, 1);\n    return opts;\n  } else {\n    return {};\n  }\n}\n\n/** @typedef { {capture?: boolean} } RegexEitherOptions */\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] | [...(RegExp | string)[], RegexEitherOptions]} args\n * @returns {string}\n */\nfunction either(...args) {\n  /** @type { object & {capture?: boolean} }  */\n  const opts = stripOptionsFromArgs(args);\n  const joined = '('\n    + (opts.capture ? \"\" : \"?:\")\n    + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/**\n * @param {RegExp | string} re\n * @returns {number}\n */\nfunction countMatchGroups(re) {\n  return (new RegExp(re.toString() + '|')).exec('').length - 1;\n}\n\n/**\n * Does lexeme start with a regular expression match at the beginning\n * @param {RegExp} re\n * @param {string} lexeme\n */\nfunction startsWith(re, lexeme) {\n  const match = re && re.exec(lexeme);\n  return match && match.index === 0;\n}\n\n// BACKREF_RE matches an open parenthesis or backreference. To avoid\n// an incorrect parse, it additionally matches the following:\n// - [...] elements, where the meaning of parentheses and escapes change\n// - other escape sequences, so we do not misparse escape sequences as\n//   interesting elements\n// - non-matching or lookahead parentheses, which do not capture. These\n//   follow the '(' with a '?'.\nconst BACKREF_RE = /\\[(?:[^\\\\\\]]|\\\\.)*\\]|\\(\\??|\\\\([1-9][0-9]*)|\\\\./;\n\n// **INTERNAL** Not intended for outside usage\n// join logically computes regexps.join(separator), but fixes the\n// backreferences so they continue to match.\n// it also places each individual regular expression into it's own\n// match group, keeping track of the sequencing of those match groups\n// is currently an exercise for the caller. :-)\n/**\n * @param {(string | RegExp)[]} regexps\n * @param {{joinWith: string}} opts\n * @returns {string}\n */\nfunction _rewriteBackreferences(regexps, { joinWith }) {\n  let numCaptures = 0;\n\n  return regexps.map((regex) => {\n    numCaptures += 1;\n    const offset = numCaptures;\n    let re = source(regex);\n    let out = '';\n\n    while (re.length > 0) {\n      const match = BACKREF_RE.exec(re);\n      if (!match) {\n        out += re;\n        break;\n      }\n      out += re.substring(0, match.index);\n      re = re.substring(match.index + match[0].length);\n      if (match[0][0] === '\\\\' && match[1]) {\n        // Adjust the backreference.\n        out += '\\\\' + String(Number(match[1]) + offset);\n      } else {\n        out += match[0];\n        if (match[0] === '(') {\n          numCaptures++;\n        }\n      }\n    }\n    return out;\n  }).map(re => `(${re})`).join(joinWith);\n}\n\n/** @typedef {import('highlight.js').Mode} Mode */\n/** @typedef {import('highlight.js').ModeCallback} ModeCallback */\n\n// Common regexps\nconst MATCH_NOTHING_RE = /\\b\\B/;\nconst IDENT_RE = '[a-zA-Z]\\\\w*';\nconst UNDERSCORE_IDENT_RE = '[a-zA-Z_]\\\\w*';\nconst NUMBER_RE = '\\\\b\\\\d+(\\\\.\\\\d+)?';\nconst C_NUMBER_RE = '(-?)(\\\\b0[xX][a-fA-F0-9]+|(\\\\b\\\\d+(\\\\.\\\\d*)?|\\\\.\\\\d+)([eE][-+]?\\\\d+)?)'; // 0x..., 0..., decimal, float\nconst BINARY_NUMBER_RE = '\\\\b(0b[01]+)'; // 0b...\nconst RE_STARTERS_RE = '!|!=|!==|%|%=|&|&&|&=|\\\\*|\\\\*=|\\\\+|\\\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\\\?|\\\\[|\\\\{|\\\\(|\\\\^|\\\\^=|\\\\||\\\\|=|\\\\|\\\\||~';\n\n/**\n* @param { Partial<Mode> & {binary?: string | RegExp} } opts\n*/\nconst SHEBANG = (opts = {}) => {\n  const beginShebang = /^#![ ]*\\//;\n  if (opts.binary) {\n    opts.begin = concat(\n      beginShebang,\n      /.*\\b/,\n      opts.binary,\n      /\\b.*/);\n  }\n  return inherit$1({\n    scope: 'meta',\n    begin: beginShebang,\n    end: /$/,\n    relevance: 0,\n    /** @type {ModeCallback} */\n    \"on:begin\": (m, resp) => {\n      if (m.index !== 0) resp.ignoreMatch();\n    }\n  }, opts);\n};\n\n// Common modes\nconst BACKSLASH_ESCAPE = {\n  begin: '\\\\\\\\[\\\\s\\\\S]', relevance: 0\n};\nconst APOS_STRING_MODE = {\n  scope: 'string',\n  begin: '\\'',\n  end: '\\'',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst QUOTE_STRING_MODE = {\n  scope: 'string',\n  begin: '\"',\n  end: '\"',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst PHRASAL_WORDS_MODE = {\n  begin: /\\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\\b/\n};\n/**\n * Creates a comment mode\n *\n * @param {string | RegExp} begin\n * @param {string | RegExp} end\n * @param {Mode | {}} [modeOptions]\n * @returns {Partial<Mode>}\n */\nconst COMMENT = function(begin, end, modeOptions = {}) {\n  const mode = inherit$1(\n    {\n      scope: 'comment',\n      begin,\n      end,\n      contains: []\n    },\n    modeOptions\n  );\n  mode.contains.push({\n    scope: 'doctag',\n    // hack to avoid the space from being included. the space is necessary to\n    // match here to prevent the plain text rule below from gobbling up doctags\n    begin: '[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)',\n    end: /(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,\n    excludeBegin: true,\n    relevance: 0\n  });\n  const ENGLISH_WORD = either(\n    // list of common 1 and 2 letter words in English\n    \"I\",\n    \"a\",\n    \"is\",\n    \"so\",\n    \"us\",\n    \"to\",\n    \"at\",\n    \"if\",\n    \"in\",\n    \"it\",\n    \"on\",\n    // note: this is not an exhaustive list of contractions, just popular ones\n    /[A-Za-z]+['](d|ve|re|ll|t|s|n)/, // contractions - can't we'd they're let's, etc\n    /[A-Za-z]+[-][a-z]+/, // `no-way`, etc.\n    /[A-Za-z][a-z]{2,}/ // allow capitalized words at beginning of sentences\n  );\n  // looking like plain text, more likely to be a comment\n  mode.contains.push(\n    {\n      // TODO: how to include \", (, ) without breaking grammars that use these for\n      // comment delimiters?\n      // begin: /[ ]+([()\"]?([A-Za-z'-]{3,}|is|a|I|so|us|[tT][oO]|at|if|in|it|on)[.]?[()\":]?([.][ ]|[ ]|\\))){3}/\n      // ---\n\n      // this tries to find sequences of 3 english words in a row (without any\n      // \"programming\" type syntax) this gives us a strong signal that we've\n      // TRULY found a comment - vs perhaps scanning with the wrong language.\n      // It's possible to find something that LOOKS like the start of the\n      // comment - but then if there is no readable text - good chance it is a\n      // false match and not a comment.\n      //\n      // for a visual example please see:\n      // https://github.com/highlightjs/highlight.js/issues/2827\n\n      begin: concat(\n        /[ ]+/, // necessary to prevent us gobbling up doctags like /* <AUTHOR> Mcgill */\n        '(',\n        ENGLISH_WORD,\n        /[.]?[:]?([.][ ]|[ ])/,\n        '){3}') // look for 3 words in a row\n    }\n  );\n  return mode;\n};\nconst C_LINE_COMMENT_MODE = COMMENT('//', '$');\nconst C_BLOCK_COMMENT_MODE = COMMENT('/\\\\*', '\\\\*/');\nconst HASH_COMMENT_MODE = COMMENT('#', '$');\nconst NUMBER_MODE = {\n  scope: 'number',\n  begin: NUMBER_RE,\n  relevance: 0\n};\nconst C_NUMBER_MODE = {\n  scope: 'number',\n  begin: C_NUMBER_RE,\n  relevance: 0\n};\nconst BINARY_NUMBER_MODE = {\n  scope: 'number',\n  begin: BINARY_NUMBER_RE,\n  relevance: 0\n};\nconst REGEXP_MODE = {\n  scope: \"regexp\",\n  begin: /\\/(?=[^/\\n]*\\/)/,\n  end: /\\/[gimuy]*/,\n  contains: [\n    BACKSLASH_ESCAPE,\n    {\n      begin: /\\[/,\n      end: /\\]/,\n      relevance: 0,\n      contains: [BACKSLASH_ESCAPE]\n    }\n  ]\n};\nconst TITLE_MODE = {\n  scope: 'title',\n  begin: IDENT_RE,\n  relevance: 0\n};\nconst UNDERSCORE_TITLE_MODE = {\n  scope: 'title',\n  begin: UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\nconst METHOD_GUARD = {\n  // excludes method names from keyword processing\n  begin: '\\\\.\\\\s*' + UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\n\n/**\n * Adds end same as begin mechanics to a mode\n *\n * Your mode must include at least a single () match group as that first match\n * group is what is used for comparison\n * @param {Partial<Mode>} mode\n */\nconst END_SAME_AS_BEGIN = function(mode) {\n  return Object.assign(mode,\n    {\n      /** @type {ModeCallback} */\n      'on:begin': (m, resp) => { resp.data._beginMatch = m[1]; },\n      /** @type {ModeCallback} */\n      'on:end': (m, resp) => { if (resp.data._beginMatch !== m[1]) resp.ignoreMatch(); }\n    });\n};\n\nvar MODES = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  APOS_STRING_MODE: APOS_STRING_MODE,\n  BACKSLASH_ESCAPE: BACKSLASH_ESCAPE,\n  BINARY_NUMBER_MODE: BINARY_NUMBER_MODE,\n  BINARY_NUMBER_RE: BINARY_NUMBER_RE,\n  COMMENT: COMMENT,\n  C_BLOCK_COMMENT_MODE: C_BLOCK_COMMENT_MODE,\n  C_LINE_COMMENT_MODE: C_LINE_COMMENT_MODE,\n  C_NUMBER_MODE: C_NUMBER_MODE,\n  C_NUMBER_RE: C_NUMBER_RE,\n  END_SAME_AS_BEGIN: END_SAME_AS_BEGIN,\n  HASH_COMMENT_MODE: HASH_COMMENT_MODE,\n  IDENT_RE: IDENT_RE,\n  MATCH_NOTHING_RE: MATCH_NOTHING_RE,\n  METHOD_GUARD: METHOD_GUARD,\n  NUMBER_MODE: NUMBER_MODE,\n  NUMBER_RE: NUMBER_RE,\n  PHRASAL_WORDS_MODE: PHRASAL_WORDS_MODE,\n  QUOTE_STRING_MODE: QUOTE_STRING_MODE,\n  REGEXP_MODE: REGEXP_MODE,\n  RE_STARTERS_RE: RE_STARTERS_RE,\n  SHEBANG: SHEBANG,\n  TITLE_MODE: TITLE_MODE,\n  UNDERSCORE_IDENT_RE: UNDERSCORE_IDENT_RE,\n  UNDERSCORE_TITLE_MODE: UNDERSCORE_TITLE_MODE\n});\n\n/**\n@typedef {import('highlight.js').CallbackResponse} CallbackResponse\n@typedef {import('highlight.js').CompilerExt} CompilerExt\n*/\n\n// Grammar extensions / plugins\n// See: https://github.com/highlightjs/highlight.js/issues/2833\n\n// Grammar extensions allow \"syntactic sugar\" to be added to the grammar modes\n// without requiring any underlying changes to the compiler internals.\n\n// `compileMatch` being the perfect small example of now allowing a grammar\n// author to write `match` when they desire to match a single expression rather\n// than being forced to use `begin`.  The extension then just moves `match` into\n// `begin` when it runs.  Ie, no features have been added, but we've just made\n// the experience of writing (and reading grammars) a little bit nicer.\n\n// ------\n\n// TODO: We need negative look-behind support to do this properly\n/**\n * Skip a match if it has a preceding dot\n *\n * This is used for `beginKeywords` to prevent matching expressions such as\n * `bob.keyword.do()`. The mode compiler automatically wires this up as a\n * special _internal_ 'on:begin' callback for modes with `beginKeywords`\n * @param {RegExpMatchArray} match\n * @param {CallbackResponse} response\n */\nfunction skipIfHasPrecedingDot(match, response) {\n  const before = match.input[match.index - 1];\n  if (before === \".\") {\n    response.ignoreMatch();\n  }\n}\n\n/**\n *\n * @type {CompilerExt}\n */\nfunction scopeClassName(mode, _parent) {\n  // eslint-disable-next-line no-undefined\n  if (mode.className !== undefined) {\n    mode.scope = mode.className;\n    delete mode.className;\n  }\n}\n\n/**\n * `beginKeywords` syntactic sugar\n * @type {CompilerExt}\n */\nfunction beginKeywords(mode, parent) {\n  if (!parent) return;\n  if (!mode.beginKeywords) return;\n\n  // for languages with keywords that include non-word characters checking for\n  // a word boundary is not sufficient, so instead we check for a word boundary\n  // or whitespace - this does no harm in any case since our keyword engine\n  // doesn't allow spaces in keywords anyways and we still check for the boundary\n  // first\n  mode.begin = '\\\\b(' + mode.beginKeywords.split(' ').join('|') + ')(?!\\\\.)(?=\\\\b|\\\\s)';\n  mode.__beforeBegin = skipIfHasPrecedingDot;\n  mode.keywords = mode.keywords || mode.beginKeywords;\n  delete mode.beginKeywords;\n\n  // prevents double relevance, the keywords themselves provide\n  // relevance, the mode doesn't need to double it\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 0;\n}\n\n/**\n * Allow `illegal` to contain an array of illegal values\n * @type {CompilerExt}\n */\nfunction compileIllegal(mode, _parent) {\n  if (!Array.isArray(mode.illegal)) return;\n\n  mode.illegal = either(...mode.illegal);\n}\n\n/**\n * `match` to match a single expression for readability\n * @type {CompilerExt}\n */\nfunction compileMatch(mode, _parent) {\n  if (!mode.match) return;\n  if (mode.begin || mode.end) throw new Error(\"begin & end are not supported with match\");\n\n  mode.begin = mode.match;\n  delete mode.match;\n}\n\n/**\n * provides the default 1 relevance to all modes\n * @type {CompilerExt}\n */\nfunction compileRelevance(mode, _parent) {\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 1;\n}\n\n// allow beforeMatch to act as a \"qualifier\" for the match\n// the full match begin must be [beforeMatch][begin]\nconst beforeMatchExt = (mode, parent) => {\n  if (!mode.beforeMatch) return;\n  // starts conflicts with endsParent which we need to make sure the child\n  // rule is not matched multiple times\n  if (mode.starts) throw new Error(\"beforeMatch cannot be used with starts\");\n\n  const originalMode = Object.assign({}, mode);\n  Object.keys(mode).forEach((key) => { delete mode[key]; });\n\n  mode.keywords = originalMode.keywords;\n  mode.begin = concat(originalMode.beforeMatch, lookahead(originalMode.begin));\n  mode.starts = {\n    relevance: 0,\n    contains: [\n      Object.assign(originalMode, { endsParent: true })\n    ]\n  };\n  mode.relevance = 0;\n\n  delete originalMode.beforeMatch;\n};\n\n// keywords that should have no default relevance value\nconst COMMON_KEYWORDS = [\n  'of',\n  'and',\n  'for',\n  'in',\n  'not',\n  'or',\n  'if',\n  'then',\n  'parent', // common variable name\n  'list', // common variable name\n  'value' // common variable name\n];\n\nconst DEFAULT_KEYWORD_SCOPE = \"keyword\";\n\n/**\n * Given raw keywords from a language definition, compile them.\n *\n * @param {string | Record<string,string|string[]> | Array<string>} rawKeywords\n * @param {boolean} caseInsensitive\n */\nfunction compileKeywords(rawKeywords, caseInsensitive, scopeName = DEFAULT_KEYWORD_SCOPE) {\n  /** @type {import(\"highlight.js/private\").KeywordDict} */\n  const compiledKeywords = Object.create(null);\n\n  // input can be a string of keywords, an array of keywords, or a object with\n  // named keys representing scopeName (which can then point to a string or array)\n  if (typeof rawKeywords === 'string') {\n    compileList(scopeName, rawKeywords.split(\" \"));\n  } else if (Array.isArray(rawKeywords)) {\n    compileList(scopeName, rawKeywords);\n  } else {\n    Object.keys(rawKeywords).forEach(function(scopeName) {\n      // collapse all our objects back into the parent object\n      Object.assign(\n        compiledKeywords,\n        compileKeywords(rawKeywords[scopeName], caseInsensitive, scopeName)\n      );\n    });\n  }\n  return compiledKeywords;\n\n  // ---\n\n  /**\n   * Compiles an individual list of keywords\n   *\n   * Ex: \"for if when while|5\"\n   *\n   * @param {string} scopeName\n   * @param {Array<string>} keywordList\n   */\n  function compileList(scopeName, keywordList) {\n    if (caseInsensitive) {\n      keywordList = keywordList.map(x => x.toLowerCase());\n    }\n    keywordList.forEach(function(keyword) {\n      const pair = keyword.split('|');\n      compiledKeywords[pair[0]] = [scopeName, scoreForKeyword(pair[0], pair[1])];\n    });\n  }\n}\n\n/**\n * Returns the proper score for a given keyword\n *\n * Also takes into account comment keywords, which will be scored 0 UNLESS\n * another score has been manually assigned.\n * @param {string} keyword\n * @param {string} [providedScore]\n */\nfunction scoreForKeyword(keyword, providedScore) {\n  // manual scores always win over common keywords\n  // so you can force a score of 1 if you really insist\n  if (providedScore) {\n    return Number(providedScore);\n  }\n\n  return commonKeyword(keyword) ? 0 : 1;\n}\n\n/**\n * Determines if a given keyword is common or not\n *\n * @param {string} keyword */\nfunction commonKeyword(keyword) {\n  return COMMON_KEYWORDS.includes(keyword.toLowerCase());\n}\n\n/*\n\nFor the reasoning behind this please see:\nhttps://github.com/highlightjs/highlight.js/issues/2880#issuecomment-*********\n\n*/\n\n/**\n * @type {Record<string, boolean>}\n */\nconst seenDeprecations = {};\n\n/**\n * @param {string} message\n */\nconst error = (message) => {\n  console.error(message);\n};\n\n/**\n * @param {string} message\n * @param {any} args\n */\nconst warn = (message, ...args) => {\n  console.log(`WARN: ${message}`, ...args);\n};\n\n/**\n * @param {string} version\n * @param {string} message\n */\nconst deprecated = (version, message) => {\n  if (seenDeprecations[`${version}/${message}`]) return;\n\n  console.log(`Deprecated as of ${version}. ${message}`);\n  seenDeprecations[`${version}/${message}`] = true;\n};\n\n/* eslint-disable no-throw-literal */\n\n/**\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n*/\n\nconst MultiClassError = new Error();\n\n/**\n * Renumbers labeled scope names to account for additional inner match\n * groups that otherwise would break everything.\n *\n * Lets say we 3 match scopes:\n *\n *   { 1 => ..., 2 => ..., 3 => ... }\n *\n * So what we need is a clean match like this:\n *\n *   (a)(b)(c) => [ \"a\", \"b\", \"c\" ]\n *\n * But this falls apart with inner match groups:\n *\n * (a)(((b)))(c) => [\"a\", \"b\", \"b\", \"b\", \"c\" ]\n *\n * Our scopes are now \"out of alignment\" and we're repeating `b` 3 times.\n * What needs to happen is the numbers are remapped:\n *\n *   { 1 => ..., 2 => ..., 5 => ... }\n *\n * We also need to know that the ONLY groups that should be output\n * are 1, 2, and 5.  This function handles this behavior.\n *\n * @param {CompiledMode} mode\n * @param {Array<RegExp | string>} regexes\n * @param {{key: \"beginScope\"|\"endScope\"}} opts\n */\nfunction remapScopeNames(mode, regexes, { key }) {\n  let offset = 0;\n  const scopeNames = mode[key];\n  /** @type Record<number,boolean> */\n  const emit = {};\n  /** @type Record<number,string> */\n  const positions = {};\n\n  for (let i = 1; i <= regexes.length; i++) {\n    positions[i + offset] = scopeNames[i];\n    emit[i + offset] = true;\n    offset += countMatchGroups(regexes[i - 1]);\n  }\n  // we use _emit to keep track of which match groups are \"top-level\" to avoid double\n  // output from inside match groups\n  mode[key] = positions;\n  mode[key]._emit = emit;\n  mode[key]._multi = true;\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction beginMultiClass(mode) {\n  if (!Array.isArray(mode.begin)) return;\n\n  if (mode.skip || mode.excludeBegin || mode.returnBegin) {\n    error(\"skip, excludeBegin, returnBegin not compatible with beginScope: {}\");\n    throw MultiClassError;\n  }\n\n  if (typeof mode.beginScope !== \"object\" || mode.beginScope === null) {\n    error(\"beginScope must be object\");\n    throw MultiClassError;\n  }\n\n  remapScopeNames(mode, mode.begin, { key: \"beginScope\" });\n  mode.begin = _rewriteBackreferences(mode.begin, { joinWith: \"\" });\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction endMultiClass(mode) {\n  if (!Array.isArray(mode.end)) return;\n\n  if (mode.skip || mode.excludeEnd || mode.returnEnd) {\n    error(\"skip, excludeEnd, returnEnd not compatible with endScope: {}\");\n    throw MultiClassError;\n  }\n\n  if (typeof mode.endScope !== \"object\" || mode.endScope === null) {\n    error(\"endScope must be object\");\n    throw MultiClassError;\n  }\n\n  remapScopeNames(mode, mode.end, { key: \"endScope\" });\n  mode.end = _rewriteBackreferences(mode.end, { joinWith: \"\" });\n}\n\n/**\n * this exists only to allow `scope: {}` to be used beside `match:`\n * Otherwise `beginScope` would necessary and that would look weird\n\n  {\n    match: [ /def/, /\\w+/ ]\n    scope: { 1: \"keyword\" , 2: \"title\" }\n  }\n\n * @param {CompiledMode} mode\n */\nfunction scopeSugar(mode) {\n  if (mode.scope && typeof mode.scope === \"object\" && mode.scope !== null) {\n    mode.beginScope = mode.scope;\n    delete mode.scope;\n  }\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction MultiClass(mode) {\n  scopeSugar(mode);\n\n  if (typeof mode.beginScope === \"string\") {\n    mode.beginScope = { _wrap: mode.beginScope };\n  }\n  if (typeof mode.endScope === \"string\") {\n    mode.endScope = { _wrap: mode.endScope };\n  }\n\n  beginMultiClass(mode);\n  endMultiClass(mode);\n}\n\n/**\n@typedef {import('highlight.js').Mode} Mode\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n@typedef {import('highlight.js').Language} Language\n@typedef {import('highlight.js').HLJSPlugin} HLJSPlugin\n@typedef {import('highlight.js').CompiledLanguage} CompiledLanguage\n*/\n\n// compilation\n\n/**\n * Compiles a language definition result\n *\n * Given the raw result of a language definition (Language), compiles this so\n * that it is ready for highlighting code.\n * @param {Language} language\n * @returns {CompiledLanguage}\n */\nfunction compileLanguage(language) {\n  /**\n   * Builds a regex with the case sensitivity of the current language\n   *\n   * @param {RegExp | string} value\n   * @param {boolean} [global]\n   */\n  function langRe(value, global) {\n    return new RegExp(\n      source(value),\n      'm'\n      + (language.case_insensitive ? 'i' : '')\n      + (language.unicodeRegex ? 'u' : '')\n      + (global ? 'g' : '')\n    );\n  }\n\n  /**\n    Stores multiple regular expressions and allows you to quickly search for\n    them all in a string simultaneously - returning the first match.  It does\n    this by creating a huge (a|b|c) regex - each individual item wrapped with ()\n    and joined by `|` - using match groups to track position.  When a match is\n    found checking which position in the array has content allows us to figure\n    out which of the original regexes / match groups triggered the match.\n\n    The match object itself (the result of `Regex.exec`) is returned but also\n    enhanced by merging in any meta-data that was registered with the regex.\n    This is how we keep track of which mode matched, and what type of rule\n    (`illegal`, `begin`, end, etc).\n  */\n  class MultiRegex {\n    constructor() {\n      this.matchIndexes = {};\n      // @ts-ignore\n      this.regexes = [];\n      this.matchAt = 1;\n      this.position = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      opts.position = this.position++;\n      // @ts-ignore\n      this.matchIndexes[this.matchAt] = opts;\n      this.regexes.push([opts, re]);\n      this.matchAt += countMatchGroups(re) + 1;\n    }\n\n    compile() {\n      if (this.regexes.length === 0) {\n        // avoids the need to check length every time exec is called\n        // @ts-ignore\n        this.exec = () => null;\n      }\n      const terminators = this.regexes.map(el => el[1]);\n      this.matcherRe = langRe(_rewriteBackreferences(terminators, { joinWith: '|' }), true);\n      this.lastIndex = 0;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      this.matcherRe.lastIndex = this.lastIndex;\n      const match = this.matcherRe.exec(s);\n      if (!match) { return null; }\n\n      // eslint-disable-next-line no-undefined\n      const i = match.findIndex((el, i) => i > 0 && el !== undefined);\n      // @ts-ignore\n      const matchData = this.matchIndexes[i];\n      // trim off any earlier non-relevant match groups (ie, the other regex\n      // match groups that make up the multi-matcher)\n      match.splice(0, i);\n\n      return Object.assign(match, matchData);\n    }\n  }\n\n  /*\n    Created to solve the key deficiently with MultiRegex - there is no way to\n    test for multiple matches at a single location.  Why would we need to do\n    that?  In the future a more dynamic engine will allow certain matches to be\n    ignored.  An example: if we matched say the 3rd regex in a large group but\n    decided to ignore it - we'd need to started testing again at the 4th\n    regex... but MultiRegex itself gives us no real way to do that.\n\n    So what this class creates MultiRegexs on the fly for whatever search\n    position they are needed.\n\n    NOTE: These additional MultiRegex objects are created dynamically.  For most\n    grammars most of the time we will never actually need anything more than the\n    first MultiRegex - so this shouldn't have too much overhead.\n\n    Say this is our search group, and we match regex3, but wish to ignore it.\n\n      regex1 | regex2 | regex3 | regex4 | regex5    ' ie, startAt = 0\n\n    What we need is a new MultiRegex that only includes the remaining\n    possibilities:\n\n      regex4 | regex5                               ' ie, startAt = 3\n\n    This class wraps all that complexity up in a simple API... `startAt` decides\n    where in the array of expressions to start doing the matching. It\n    auto-increments, so if a match is found at position 2, then startAt will be\n    set to 3.  If the end is reached startAt will return to 0.\n\n    MOST of the time the parser will be setting startAt manually to 0.\n  */\n  class ResumableMultiRegex {\n    constructor() {\n      // @ts-ignore\n      this.rules = [];\n      // @ts-ignore\n      this.multiRegexes = [];\n      this.count = 0;\n\n      this.lastIndex = 0;\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    getMatcher(index) {\n      if (this.multiRegexes[index]) return this.multiRegexes[index];\n\n      const matcher = new MultiRegex();\n      this.rules.slice(index).forEach(([re, opts]) => matcher.addRule(re, opts));\n      matcher.compile();\n      this.multiRegexes[index] = matcher;\n      return matcher;\n    }\n\n    resumingScanAtSamePosition() {\n      return this.regexIndex !== 0;\n    }\n\n    considerAll() {\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      this.rules.push([re, opts]);\n      if (opts.type === \"begin\") this.count++;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      const m = this.getMatcher(this.regexIndex);\n      m.lastIndex = this.lastIndex;\n      let result = m.exec(s);\n\n      // The following is because we have no easy way to say \"resume scanning at the\n      // existing position but also skip the current rule ONLY\". What happens is\n      // all prior rules are also skipped which can result in matching the wrong\n      // thing. Example of matching \"booger\":\n\n      // our matcher is [string, \"booger\", number]\n      //\n      // ....booger....\n\n      // if \"booger\" is ignored then we'd really need a regex to scan from the\n      // SAME position for only: [string, number] but ignoring \"booger\" (if it\n      // was the first match), a simple resume would scan ahead who knows how\n      // far looking only for \"number\", ignoring potential string matches (or\n      // future \"booger\" matches that might be valid.)\n\n      // So what we do: We execute two matchers, one resuming at the same\n      // position, but the second full matcher starting at the position after:\n\n      //     /--- resume first regex match here (for [number])\n      //     |/---- full match here for [string, \"booger\", number]\n      //     vv\n      // ....booger....\n\n      // Which ever results in a match first is then used. So this 3-4 step\n      // process essentially allows us to say \"match at this position, excluding\n      // a prior rule that was ignored\".\n      //\n      // 1. Match \"booger\" first, ignore. Also proves that [string] does non match.\n      // 2. Resume matching for [number]\n      // 3. Match at index + 1 for [string, \"booger\", number]\n      // 4. If #2 and #3 result in matches, which came first?\n      if (this.resumingScanAtSamePosition()) {\n        if (result && result.index === this.lastIndex) ; else { // use the second matcher result\n          const m2 = this.getMatcher(0);\n          m2.lastIndex = this.lastIndex + 1;\n          result = m2.exec(s);\n        }\n      }\n\n      if (result) {\n        this.regexIndex += result.position + 1;\n        if (this.regexIndex === this.count) {\n          // wrap-around to considering all matches again\n          this.considerAll();\n        }\n      }\n\n      return result;\n    }\n  }\n\n  /**\n   * Given a mode, builds a huge ResumableMultiRegex that can be used to walk\n   * the content and find matches.\n   *\n   * @param {CompiledMode} mode\n   * @returns {ResumableMultiRegex}\n   */\n  function buildModeRegex(mode) {\n    const mm = new ResumableMultiRegex();\n\n    mode.contains.forEach(term => mm.addRule(term.begin, { rule: term, type: \"begin\" }));\n\n    if (mode.terminatorEnd) {\n      mm.addRule(mode.terminatorEnd, { type: \"end\" });\n    }\n    if (mode.illegal) {\n      mm.addRule(mode.illegal, { type: \"illegal\" });\n    }\n\n    return mm;\n  }\n\n  /** skip vs abort vs ignore\n   *\n   * @skip   - The mode is still entered and exited normally (and contains rules apply),\n   *           but all content is held and added to the parent buffer rather than being\n   *           output when the mode ends.  Mostly used with `sublanguage` to build up\n   *           a single large buffer than can be parsed by sublanguage.\n   *\n   *             - The mode begin ands ends normally.\n   *             - Content matched is added to the parent mode buffer.\n   *             - The parser cursor is moved forward normally.\n   *\n   * @abort  - A hack placeholder until we have ignore.  Aborts the mode (as if it\n   *           never matched) but DOES NOT continue to match subsequent `contains`\n   *           modes.  Abort is bad/suboptimal because it can result in modes\n   *           farther down not getting applied because an earlier rule eats the\n   *           content but then aborts.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is added to the mode buffer.\n   *             - The parser cursor is moved forward accordingly.\n   *\n   * @ignore - Ignores the mode (as if it never matched) and continues to match any\n   *           subsequent `contains` modes.  Ignore isn't technically possible with\n   *           the current parser implementation.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is ignored.\n   *             - The parser cursor is not moved forward.\n   */\n\n  /**\n   * Compiles an individual mode\n   *\n   * This can raise an error if the mode contains certain detectable known logic\n   * issues.\n   * @param {Mode} mode\n   * @param {CompiledMode | null} [parent]\n   * @returns {CompiledMode | never}\n   */\n  function compileMode(mode, parent) {\n    const cmode = /** @type CompiledMode */ (mode);\n    if (mode.isCompiled) return cmode;\n\n    [\n      scopeClassName,\n      // do this early so compiler extensions generally don't have to worry about\n      // the distinction between match/begin\n      compileMatch,\n      MultiClass,\n      beforeMatchExt\n    ].forEach(ext => ext(mode, parent));\n\n    language.compilerExtensions.forEach(ext => ext(mode, parent));\n\n    // __beforeBegin is considered private API, internal use only\n    mode.__beforeBegin = null;\n\n    [\n      beginKeywords,\n      // do this later so compiler extensions that come earlier have access to the\n      // raw array if they wanted to perhaps manipulate it, etc.\n      compileIllegal,\n      // default to 1 relevance if not specified\n      compileRelevance\n    ].forEach(ext => ext(mode, parent));\n\n    mode.isCompiled = true;\n\n    let keywordPattern = null;\n    if (typeof mode.keywords === \"object\" && mode.keywords.$pattern) {\n      // we need a copy because keywords might be compiled multiple times\n      // so we can't go deleting $pattern from the original on the first\n      // pass\n      mode.keywords = Object.assign({}, mode.keywords);\n      keywordPattern = mode.keywords.$pattern;\n      delete mode.keywords.$pattern;\n    }\n    keywordPattern = keywordPattern || /\\w+/;\n\n    if (mode.keywords) {\n      mode.keywords = compileKeywords(mode.keywords, language.case_insensitive);\n    }\n\n    cmode.keywordPatternRe = langRe(keywordPattern, true);\n\n    if (parent) {\n      if (!mode.begin) mode.begin = /\\B|\\b/;\n      cmode.beginRe = langRe(cmode.begin);\n      if (!mode.end && !mode.endsWithParent) mode.end = /\\B|\\b/;\n      if (mode.end) cmode.endRe = langRe(cmode.end);\n      cmode.terminatorEnd = source(cmode.end) || '';\n      if (mode.endsWithParent && parent.terminatorEnd) {\n        cmode.terminatorEnd += (mode.end ? '|' : '') + parent.terminatorEnd;\n      }\n    }\n    if (mode.illegal) cmode.illegalRe = langRe(/** @type {RegExp | string} */ (mode.illegal));\n    if (!mode.contains) mode.contains = [];\n\n    mode.contains = [].concat(...mode.contains.map(function(c) {\n      return expandOrCloneMode(c === 'self' ? mode : c);\n    }));\n    mode.contains.forEach(function(c) { compileMode(/** @type Mode */ (c), cmode); });\n\n    if (mode.starts) {\n      compileMode(mode.starts, parent);\n    }\n\n    cmode.matcher = buildModeRegex(cmode);\n    return cmode;\n  }\n\n  if (!language.compilerExtensions) language.compilerExtensions = [];\n\n  // self is not valid at the top-level\n  if (language.contains && language.contains.includes('self')) {\n    throw new Error(\"ERR: contains `self` is not supported at the top-level of a language.  See documentation.\");\n  }\n\n  // we need a null object, which inherit will guarantee\n  language.classNameAliases = inherit$1(language.classNameAliases || {});\n\n  return compileMode(/** @type Mode */ (language));\n}\n\n/**\n * Determines if a mode has a dependency on it's parent or not\n *\n * If a mode does have a parent dependency then often we need to clone it if\n * it's used in multiple places so that each copy points to the correct parent,\n * where-as modes without a parent can often safely be re-used at the bottom of\n * a mode chain.\n *\n * @param {Mode | null} mode\n * @returns {boolean} - is there a dependency on the parent?\n * */\nfunction dependencyOnParent(mode) {\n  if (!mode) return false;\n\n  return mode.endsWithParent || dependencyOnParent(mode.starts);\n}\n\n/**\n * Expands a mode or clones it if necessary\n *\n * This is necessary for modes with parental dependenceis (see notes on\n * `dependencyOnParent`) and for nodes that have `variants` - which must then be\n * exploded into their own individual modes at compile time.\n *\n * @param {Mode} mode\n * @returns {Mode | Mode[]}\n * */\nfunction expandOrCloneMode(mode) {\n  if (mode.variants && !mode.cachedVariants) {\n    mode.cachedVariants = mode.variants.map(function(variant) {\n      return inherit$1(mode, { variants: null }, variant);\n    });\n  }\n\n  // EXPAND\n  // if we have variants then essentially \"replace\" the mode with the variants\n  // this happens in compileMode, where this function is called from\n  if (mode.cachedVariants) {\n    return mode.cachedVariants;\n  }\n\n  // CLONE\n  // if we have dependencies on parents then we need a unique\n  // instance of ourselves, so we can be reused with many\n  // different parents without issue\n  if (dependencyOnParent(mode)) {\n    return inherit$1(mode, { starts: mode.starts ? inherit$1(mode.starts) : null });\n  }\n\n  if (Object.isFrozen(mode)) {\n    return inherit$1(mode);\n  }\n\n  // no special dependency issues, just return ourselves\n  return mode;\n}\n\nvar version = \"11.9.0\";\n\nclass HTMLInjectionError extends Error {\n  constructor(reason, html) {\n    super(reason);\n    this.name = \"HTMLInjectionError\";\n    this.html = html;\n  }\n}\n\n/*\nSyntax highlighting with language autodetection.\nhttps://highlightjs.org/\n*/\n\n\n\n/**\n@typedef {import('highlight.js').Mode} Mode\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n@typedef {import('highlight.js').CompiledScope} CompiledScope\n@typedef {import('highlight.js').Language} Language\n@typedef {import('highlight.js').HLJSApi} HLJSApi\n@typedef {import('highlight.js').HLJSPlugin} HLJSPlugin\n@typedef {import('highlight.js').PluginEvent} PluginEvent\n@typedef {import('highlight.js').HLJSOptions} HLJSOptions\n@typedef {import('highlight.js').LanguageFn} LanguageFn\n@typedef {import('highlight.js').HighlightedHTMLElement} HighlightedHTMLElement\n@typedef {import('highlight.js').BeforeHighlightContext} BeforeHighlightContext\n@typedef {import('highlight.js/private').MatchType} MatchType\n@typedef {import('highlight.js/private').KeywordData} KeywordData\n@typedef {import('highlight.js/private').EnhancedMatch} EnhancedMatch\n@typedef {import('highlight.js/private').AnnotatedError} AnnotatedError\n@typedef {import('highlight.js').AutoHighlightResult} AutoHighlightResult\n@typedef {import('highlight.js').HighlightOptions} HighlightOptions\n@typedef {import('highlight.js').HighlightResult} HighlightResult\n*/\n\n\nconst escape = escapeHTML;\nconst inherit = inherit$1;\nconst NO_MATCH = Symbol(\"nomatch\");\nconst MAX_KEYWORD_HITS = 7;\n\n/**\n * @param {any} hljs - object that is extended (legacy)\n * @returns {HLJSApi}\n */\nconst HLJS = function(hljs) {\n  // Global internal variables used within the highlight.js library.\n  /** @type {Record<string, Language>} */\n  const languages = Object.create(null);\n  /** @type {Record<string, string>} */\n  const aliases = Object.create(null);\n  /** @type {HLJSPlugin[]} */\n  const plugins = [];\n\n  // safe/production mode - swallows more errors, tries to keep running\n  // even if a single syntax or parse hits a fatal error\n  let SAFE_MODE = true;\n  const LANGUAGE_NOT_FOUND = \"Could not find the language '{}', did you forget to load/include a language module?\";\n  /** @type {Language} */\n  const PLAINTEXT_LANGUAGE = { disableAutodetect: true, name: 'Plain text', contains: [] };\n\n  // Global options used when within external APIs. This is modified when\n  // calling the `hljs.configure` function.\n  /** @type HLJSOptions */\n  let options = {\n    ignoreUnescapedHTML: false,\n    throwUnescapedHTML: false,\n    noHighlightRe: /^(no-?highlight)$/i,\n    languageDetectRe: /\\blang(?:uage)?-([\\w-]+)\\b/i,\n    classPrefix: 'hljs-',\n    cssSelector: 'pre code',\n    languages: null,\n    // beta configuration options, subject to change, welcome to discuss\n    // https://github.com/highlightjs/highlight.js/issues/1086\n    __emitter: TokenTreeEmitter\n  };\n\n  /* Utility functions */\n\n  /**\n   * Tests a language name to see if highlighting should be skipped\n   * @param {string} languageName\n   */\n  function shouldNotHighlight(languageName) {\n    return options.noHighlightRe.test(languageName);\n  }\n\n  /**\n   * @param {HighlightedHTMLElement} block - the HTML element to determine language for\n   */\n  function blockLanguage(block) {\n    let classes = block.className + ' ';\n\n    classes += block.parentNode ? block.parentNode.className : '';\n\n    // language-* takes precedence over non-prefixed class names.\n    const match = options.languageDetectRe.exec(classes);\n    if (match) {\n      const language = getLanguage(match[1]);\n      if (!language) {\n        warn(LANGUAGE_NOT_FOUND.replace(\"{}\", match[1]));\n        warn(\"Falling back to no-highlight mode for this block.\", block);\n      }\n      return language ? match[1] : 'no-highlight';\n    }\n\n    return classes\n      .split(/\\s+/)\n      .find((_class) => shouldNotHighlight(_class) || getLanguage(_class));\n  }\n\n  /**\n   * Core highlighting function.\n   *\n   * OLD API\n   * highlight(lang, code, ignoreIllegals, continuation)\n   *\n   * NEW API\n   * highlight(code, {lang, ignoreIllegals})\n   *\n   * @param {string} codeOrLanguageName - the language to use for highlighting\n   * @param {string | HighlightOptions} optionsOrCode - the code to highlight\n   * @param {boolean} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   *\n   * @returns {HighlightResult} Result - an object that represents the result\n   * @property {string} language - the language name\n   * @property {number} relevance - the relevance score\n   * @property {string} value - the highlighted HTML code\n   * @property {string} code - the original raw code\n   * @property {CompiledMode} top - top of the current mode stack\n   * @property {boolean} illegal - indicates whether any illegal matches were found\n  */\n  function highlight(codeOrLanguageName, optionsOrCode, ignoreIllegals) {\n    let code = \"\";\n    let languageName = \"\";\n    if (typeof optionsOrCode === \"object\") {\n      code = codeOrLanguageName;\n      ignoreIllegals = optionsOrCode.ignoreIllegals;\n      languageName = optionsOrCode.language;\n    } else {\n      // old API\n      deprecated(\"10.7.0\", \"highlight(lang, code, ...args) has been deprecated.\");\n      deprecated(\"10.7.0\", \"Please use highlight(code, options) instead.\\nhttps://github.com/highlightjs/highlight.js/issues/2277\");\n      languageName = codeOrLanguageName;\n      code = optionsOrCode;\n    }\n\n    // https://github.com/highlightjs/highlight.js/issues/3149\n    // eslint-disable-next-line no-undefined\n    if (ignoreIllegals === undefined) { ignoreIllegals = true; }\n\n    /** @type {BeforeHighlightContext} */\n    const context = {\n      code,\n      language: languageName\n    };\n    // the plugin can change the desired language or the code to be highlighted\n    // just be changing the object it was passed\n    fire(\"before:highlight\", context);\n\n    // a before plugin can usurp the result completely by providing it's own\n    // in which case we don't even need to call highlight\n    const result = context.result\n      ? context.result\n      : _highlight(context.language, context.code, ignoreIllegals);\n\n    result.code = context.code;\n    // the plugin can change anything in result to suite it\n    fire(\"after:highlight\", result);\n\n    return result;\n  }\n\n  /**\n   * private highlight that's used internally and does not fire callbacks\n   *\n   * @param {string} languageName - the language to use for highlighting\n   * @param {string} codeToHighlight - the code to highlight\n   * @param {boolean?} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   * @param {CompiledMode?} [continuation] - current continuation mode, if any\n   * @returns {HighlightResult} - result of the highlight operation\n  */\n  function _highlight(languageName, codeToHighlight, ignoreIllegals, continuation) {\n    const keywordHits = Object.create(null);\n\n    /**\n     * Return keyword data if a match is a keyword\n     * @param {CompiledMode} mode - current mode\n     * @param {string} matchText - the textual match\n     * @returns {KeywordData | false}\n     */\n    function keywordData(mode, matchText) {\n      return mode.keywords[matchText];\n    }\n\n    function processKeywords() {\n      if (!top.keywords) {\n        emitter.addText(modeBuffer);\n        return;\n      }\n\n      let lastIndex = 0;\n      top.keywordPatternRe.lastIndex = 0;\n      let match = top.keywordPatternRe.exec(modeBuffer);\n      let buf = \"\";\n\n      while (match) {\n        buf += modeBuffer.substring(lastIndex, match.index);\n        const word = language.case_insensitive ? match[0].toLowerCase() : match[0];\n        const data = keywordData(top, word);\n        if (data) {\n          const [kind, keywordRelevance] = data;\n          emitter.addText(buf);\n          buf = \"\";\n\n          keywordHits[word] = (keywordHits[word] || 0) + 1;\n          if (keywordHits[word] <= MAX_KEYWORD_HITS) relevance += keywordRelevance;\n          if (kind.startsWith(\"_\")) {\n            // _ implied for relevance only, do not highlight\n            // by applying a class name\n            buf += match[0];\n          } else {\n            const cssClass = language.classNameAliases[kind] || kind;\n            emitKeyword(match[0], cssClass);\n          }\n        } else {\n          buf += match[0];\n        }\n        lastIndex = top.keywordPatternRe.lastIndex;\n        match = top.keywordPatternRe.exec(modeBuffer);\n      }\n      buf += modeBuffer.substring(lastIndex);\n      emitter.addText(buf);\n    }\n\n    function processSubLanguage() {\n      if (modeBuffer === \"\") return;\n      /** @type HighlightResult */\n      let result = null;\n\n      if (typeof top.subLanguage === 'string') {\n        if (!languages[top.subLanguage]) {\n          emitter.addText(modeBuffer);\n          return;\n        }\n        result = _highlight(top.subLanguage, modeBuffer, true, continuations[top.subLanguage]);\n        continuations[top.subLanguage] = /** @type {CompiledMode} */ (result._top);\n      } else {\n        result = highlightAuto(modeBuffer, top.subLanguage.length ? top.subLanguage : null);\n      }\n\n      // Counting embedded language score towards the host language may be disabled\n      // with zeroing the containing mode relevance. Use case in point is Markdown that\n      // allows XML everywhere and makes every XML snippet to have a much larger Markdown\n      // score.\n      if (top.relevance > 0) {\n        relevance += result.relevance;\n      }\n      emitter.__addSublanguage(result._emitter, result.language);\n    }\n\n    function processBuffer() {\n      if (top.subLanguage != null) {\n        processSubLanguage();\n      } else {\n        processKeywords();\n      }\n      modeBuffer = '';\n    }\n\n    /**\n     * @param {string} text\n     * @param {string} scope\n     */\n    function emitKeyword(keyword, scope) {\n      if (keyword === \"\") return;\n\n      emitter.startScope(scope);\n      emitter.addText(keyword);\n      emitter.endScope();\n    }\n\n    /**\n     * @param {CompiledScope} scope\n     * @param {RegExpMatchArray} match\n     */\n    function emitMultiClass(scope, match) {\n      let i = 1;\n      const max = match.length - 1;\n      while (i <= max) {\n        if (!scope._emit[i]) { i++; continue; }\n        const klass = language.classNameAliases[scope[i]] || scope[i];\n        const text = match[i];\n        if (klass) {\n          emitKeyword(text, klass);\n        } else {\n          modeBuffer = text;\n          processKeywords();\n          modeBuffer = \"\";\n        }\n        i++;\n      }\n    }\n\n    /**\n     * @param {CompiledMode} mode - new mode to start\n     * @param {RegExpMatchArray} match\n     */\n    function startNewMode(mode, match) {\n      if (mode.scope && typeof mode.scope === \"string\") {\n        emitter.openNode(language.classNameAliases[mode.scope] || mode.scope);\n      }\n      if (mode.beginScope) {\n        // beginScope just wraps the begin match itself in a scope\n        if (mode.beginScope._wrap) {\n          emitKeyword(modeBuffer, language.classNameAliases[mode.beginScope._wrap] || mode.beginScope._wrap);\n          modeBuffer = \"\";\n        } else if (mode.beginScope._multi) {\n          // at this point modeBuffer should just be the match\n          emitMultiClass(mode.beginScope, match);\n          modeBuffer = \"\";\n        }\n      }\n\n      top = Object.create(mode, { parent: { value: top } });\n      return top;\n    }\n\n    /**\n     * @param {CompiledMode } mode - the mode to potentially end\n     * @param {RegExpMatchArray} match - the latest match\n     * @param {string} matchPlusRemainder - match plus remainder of content\n     * @returns {CompiledMode | void} - the next mode, or if void continue on in current mode\n     */\n    function endOfMode(mode, match, matchPlusRemainder) {\n      let matched = startsWith(mode.endRe, matchPlusRemainder);\n\n      if (matched) {\n        if (mode[\"on:end\"]) {\n          const resp = new Response(mode);\n          mode[\"on:end\"](match, resp);\n          if (resp.isMatchIgnored) matched = false;\n        }\n\n        if (matched) {\n          while (mode.endsParent && mode.parent) {\n            mode = mode.parent;\n          }\n          return mode;\n        }\n      }\n      // even if on:end fires an `ignore` it's still possible\n      // that we might trigger the end node because of a parent mode\n      if (mode.endsWithParent) {\n        return endOfMode(mode.parent, match, matchPlusRemainder);\n      }\n    }\n\n    /**\n     * Handle matching but then ignoring a sequence of text\n     *\n     * @param {string} lexeme - string containing full match text\n     */\n    function doIgnore(lexeme) {\n      if (top.matcher.regexIndex === 0) {\n        // no more regexes to potentially match here, so we move the cursor forward one\n        // space\n        modeBuffer += lexeme[0];\n        return 1;\n      } else {\n        // no need to move the cursor, we still have additional regexes to try and\n        // match at this very spot\n        resumeScanAtSamePosition = true;\n        return 0;\n      }\n    }\n\n    /**\n     * Handle the start of a new potential mode match\n     *\n     * @param {EnhancedMatch} match - the current match\n     * @returns {number} how far to advance the parse cursor\n     */\n    function doBeginMatch(match) {\n      const lexeme = match[0];\n      const newMode = match.rule;\n\n      const resp = new Response(newMode);\n      // first internal before callbacks, then the public ones\n      const beforeCallbacks = [newMode.__beforeBegin, newMode[\"on:begin\"]];\n      for (const cb of beforeCallbacks) {\n        if (!cb) continue;\n        cb(match, resp);\n        if (resp.isMatchIgnored) return doIgnore(lexeme);\n      }\n\n      if (newMode.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (newMode.excludeBegin) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (!newMode.returnBegin && !newMode.excludeBegin) {\n          modeBuffer = lexeme;\n        }\n      }\n      startNewMode(newMode, match);\n      return newMode.returnBegin ? 0 : lexeme.length;\n    }\n\n    /**\n     * Handle the potential end of mode\n     *\n     * @param {RegExpMatchArray} match - the current match\n     */\n    function doEndMatch(match) {\n      const lexeme = match[0];\n      const matchPlusRemainder = codeToHighlight.substring(match.index);\n\n      const endMode = endOfMode(top, match, matchPlusRemainder);\n      if (!endMode) { return NO_MATCH; }\n\n      const origin = top;\n      if (top.endScope && top.endScope._wrap) {\n        processBuffer();\n        emitKeyword(lexeme, top.endScope._wrap);\n      } else if (top.endScope && top.endScope._multi) {\n        processBuffer();\n        emitMultiClass(top.endScope, match);\n      } else if (origin.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (!(origin.returnEnd || origin.excludeEnd)) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (origin.excludeEnd) {\n          modeBuffer = lexeme;\n        }\n      }\n      do {\n        if (top.scope) {\n          emitter.closeNode();\n        }\n        if (!top.skip && !top.subLanguage) {\n          relevance += top.relevance;\n        }\n        top = top.parent;\n      } while (top !== endMode.parent);\n      if (endMode.starts) {\n        startNewMode(endMode.starts, match);\n      }\n      return origin.returnEnd ? 0 : lexeme.length;\n    }\n\n    function processContinuations() {\n      const list = [];\n      for (let current = top; current !== language; current = current.parent) {\n        if (current.scope) {\n          list.unshift(current.scope);\n        }\n      }\n      list.forEach(item => emitter.openNode(item));\n    }\n\n    /** @type {{type?: MatchType, index?: number, rule?: Mode}}} */\n    let lastMatch = {};\n\n    /**\n     *  Process an individual match\n     *\n     * @param {string} textBeforeMatch - text preceding the match (since the last match)\n     * @param {EnhancedMatch} [match] - the match itself\n     */\n    function processLexeme(textBeforeMatch, match) {\n      const lexeme = match && match[0];\n\n      // add non-matched text to the current mode buffer\n      modeBuffer += textBeforeMatch;\n\n      if (lexeme == null) {\n        processBuffer();\n        return 0;\n      }\n\n      // we've found a 0 width match and we're stuck, so we need to advance\n      // this happens when we have badly behaved rules that have optional matchers to the degree that\n      // sometimes they can end up matching nothing at all\n      // Ref: https://github.com/highlightjs/highlight.js/issues/2140\n      if (lastMatch.type === \"begin\" && match.type === \"end\" && lastMatch.index === match.index && lexeme === \"\") {\n        // spit the \"skipped\" character that our regex choked on back into the output sequence\n        modeBuffer += codeToHighlight.slice(match.index, match.index + 1);\n        if (!SAFE_MODE) {\n          /** @type {AnnotatedError} */\n          const err = new Error(`0 width match regex (${languageName})`);\n          err.languageName = languageName;\n          err.badRule = lastMatch.rule;\n          throw err;\n        }\n        return 1;\n      }\n      lastMatch = match;\n\n      if (match.type === \"begin\") {\n        return doBeginMatch(match);\n      } else if (match.type === \"illegal\" && !ignoreIllegals) {\n        // illegal match, we do not continue processing\n        /** @type {AnnotatedError} */\n        const err = new Error('Illegal lexeme \"' + lexeme + '\" for mode \"' + (top.scope || '<unnamed>') + '\"');\n        err.mode = top;\n        throw err;\n      } else if (match.type === \"end\") {\n        const processed = doEndMatch(match);\n        if (processed !== NO_MATCH) {\n          return processed;\n        }\n      }\n\n      // edge case for when illegal matches $ (end of line) which is technically\n      // a 0 width match but not a begin/end match so it's not caught by the\n      // first handler (when ignoreIllegals is true)\n      if (match.type === \"illegal\" && lexeme === \"\") {\n        // advance so we aren't stuck in an infinite loop\n        return 1;\n      }\n\n      // infinite loops are BAD, this is a last ditch catch all. if we have a\n      // decent number of iterations yet our index (cursor position in our\n      // parsing) still 3x behind our index then something is very wrong\n      // so we bail\n      if (iterations > 100000 && iterations > match.index * 3) {\n        const err = new Error('potential infinite loop, way more iterations than matches');\n        throw err;\n      }\n\n      /*\n      Why might be find ourselves here?  An potential end match that was\n      triggered but could not be completed.  IE, `doEndMatch` returned NO_MATCH.\n      (this could be because a callback requests the match be ignored, etc)\n\n      This causes no real harm other than stopping a few times too many.\n      */\n\n      modeBuffer += lexeme;\n      return lexeme.length;\n    }\n\n    const language = getLanguage(languageName);\n    if (!language) {\n      error(LANGUAGE_NOT_FOUND.replace(\"{}\", languageName));\n      throw new Error('Unknown language: \"' + languageName + '\"');\n    }\n\n    const md = compileLanguage(language);\n    let result = '';\n    /** @type {CompiledMode} */\n    let top = continuation || md;\n    /** @type Record<string,CompiledMode> */\n    const continuations = {}; // keep continuations for sub-languages\n    const emitter = new options.__emitter(options);\n    processContinuations();\n    let modeBuffer = '';\n    let relevance = 0;\n    let index = 0;\n    let iterations = 0;\n    let resumeScanAtSamePosition = false;\n\n    try {\n      if (!language.__emitTokens) {\n        top.matcher.considerAll();\n\n        for (;;) {\n          iterations++;\n          if (resumeScanAtSamePosition) {\n            // only regexes not matched previously will now be\n            // considered for a potential match\n            resumeScanAtSamePosition = false;\n          } else {\n            top.matcher.considerAll();\n          }\n          top.matcher.lastIndex = index;\n\n          const match = top.matcher.exec(codeToHighlight);\n          // console.log(\"match\", match[0], match.rule && match.rule.begin)\n\n          if (!match) break;\n\n          const beforeMatch = codeToHighlight.substring(index, match.index);\n          const processedCount = processLexeme(beforeMatch, match);\n          index = match.index + processedCount;\n        }\n        processLexeme(codeToHighlight.substring(index));\n      } else {\n        language.__emitTokens(codeToHighlight, emitter);\n      }\n\n      emitter.finalize();\n      result = emitter.toHTML();\n\n      return {\n        language: languageName,\n        value: result,\n        relevance,\n        illegal: false,\n        _emitter: emitter,\n        _top: top\n      };\n    } catch (err) {\n      if (err.message && err.message.includes('Illegal')) {\n        return {\n          language: languageName,\n          value: escape(codeToHighlight),\n          illegal: true,\n          relevance: 0,\n          _illegalBy: {\n            message: err.message,\n            index,\n            context: codeToHighlight.slice(index - 100, index + 100),\n            mode: err.mode,\n            resultSoFar: result\n          },\n          _emitter: emitter\n        };\n      } else if (SAFE_MODE) {\n        return {\n          language: languageName,\n          value: escape(codeToHighlight),\n          illegal: false,\n          relevance: 0,\n          errorRaised: err,\n          _emitter: emitter,\n          _top: top\n        };\n      } else {\n        throw err;\n      }\n    }\n  }\n\n  /**\n   * returns a valid highlight result, without actually doing any actual work,\n   * auto highlight starts with this and it's possible for small snippets that\n   * auto-detection may not find a better match\n   * @param {string} code\n   * @returns {HighlightResult}\n   */\n  function justTextHighlightResult(code) {\n    const result = {\n      value: escape(code),\n      illegal: false,\n      relevance: 0,\n      _top: PLAINTEXT_LANGUAGE,\n      _emitter: new options.__emitter(options)\n    };\n    result._emitter.addText(code);\n    return result;\n  }\n\n  /**\n  Highlighting with language detection. Accepts a string with the code to\n  highlight. Returns an object with the following properties:\n\n  - language (detected language)\n  - relevance (int)\n  - value (an HTML string with highlighting markup)\n  - secondBest (object with the same structure for second-best heuristically\n    detected language, may be absent)\n\n    @param {string} code\n    @param {Array<string>} [languageSubset]\n    @returns {AutoHighlightResult}\n  */\n  function highlightAuto(code, languageSubset) {\n    languageSubset = languageSubset || options.languages || Object.keys(languages);\n    const plaintext = justTextHighlightResult(code);\n\n    const results = languageSubset.filter(getLanguage).filter(autoDetection).map(name =>\n      _highlight(name, code, false)\n    );\n    results.unshift(plaintext); // plaintext is always an option\n\n    const sorted = results.sort((a, b) => {\n      // sort base on relevance\n      if (a.relevance !== b.relevance) return b.relevance - a.relevance;\n\n      // always award the tie to the base language\n      // ie if C++ and Arduino are tied, it's more likely to be C++\n      if (a.language && b.language) {\n        if (getLanguage(a.language).supersetOf === b.language) {\n          return 1;\n        } else if (getLanguage(b.language).supersetOf === a.language) {\n          return -1;\n        }\n      }\n\n      // otherwise say they are equal, which has the effect of sorting on\n      // relevance while preserving the original ordering - which is how ties\n      // have historically been settled, ie the language that comes first always\n      // wins in the case of a tie\n      return 0;\n    });\n\n    const [best, secondBest] = sorted;\n\n    /** @type {AutoHighlightResult} */\n    const result = best;\n    result.secondBest = secondBest;\n\n    return result;\n  }\n\n  /**\n   * Builds new class name for block given the language name\n   *\n   * @param {HTMLElement} element\n   * @param {string} [currentLang]\n   * @param {string} [resultLang]\n   */\n  function updateClassName(element, currentLang, resultLang) {\n    const language = (currentLang && aliases[currentLang]) || resultLang;\n\n    element.classList.add(\"hljs\");\n    element.classList.add(`language-${language}`);\n  }\n\n  /**\n   * Applies highlighting to a DOM node containing code.\n   *\n   * @param {HighlightedHTMLElement} element - the HTML element to highlight\n  */\n  function highlightElement(element) {\n    /** @type HTMLElement */\n    let node = null;\n    const language = blockLanguage(element);\n\n    if (shouldNotHighlight(language)) return;\n\n    fire(\"before:highlightElement\",\n      { el: element, language });\n\n    if (element.dataset.highlighted) {\n      console.log(\"Element previously highlighted. To highlight again, first unset `dataset.highlighted`.\", element);\n      return;\n    }\n\n    // we should be all text, no child nodes (unescaped HTML) - this is possibly\n    // an HTML injection attack - it's likely too late if this is already in\n    // production (the code has likely already done its damage by the time\n    // we're seeing it)... but we yell loudly about this so that hopefully it's\n    // more likely to be caught in development before making it to production\n    if (element.children.length > 0) {\n      if (!options.ignoreUnescapedHTML) {\n        console.warn(\"One of your code blocks includes unescaped HTML. This is a potentially serious security risk.\");\n        console.warn(\"https://github.com/highlightjs/highlight.js/wiki/security\");\n        console.warn(\"The element with unescaped HTML:\");\n        console.warn(element);\n      }\n      if (options.throwUnescapedHTML) {\n        const err = new HTMLInjectionError(\n          \"One of your code blocks includes unescaped HTML.\",\n          element.innerHTML\n        );\n        throw err;\n      }\n    }\n\n    node = element;\n    const text = node.textContent;\n    const result = language ? highlight(text, { language, ignoreIllegals: true }) : highlightAuto(text);\n\n    element.innerHTML = result.value;\n    element.dataset.highlighted = \"yes\";\n    updateClassName(element, language, result.language);\n    element.result = {\n      language: result.language,\n      // TODO: remove with version 11.0\n      re: result.relevance,\n      relevance: result.relevance\n    };\n    if (result.secondBest) {\n      element.secondBest = {\n        language: result.secondBest.language,\n        relevance: result.secondBest.relevance\n      };\n    }\n\n    fire(\"after:highlightElement\", { el: element, result, text });\n  }\n\n  /**\n   * Updates highlight.js global options with the passed options\n   *\n   * @param {Partial<HLJSOptions>} userOptions\n   */\n  function configure(userOptions) {\n    options = inherit(options, userOptions);\n  }\n\n  // TODO: remove v12, deprecated\n  const initHighlighting = () => {\n    highlightAll();\n    deprecated(\"10.6.0\", \"initHighlighting() deprecated.  Use highlightAll() now.\");\n  };\n\n  // TODO: remove v12, deprecated\n  function initHighlightingOnLoad() {\n    highlightAll();\n    deprecated(\"10.6.0\", \"initHighlightingOnLoad() deprecated.  Use highlightAll() now.\");\n  }\n\n  let wantsHighlight = false;\n\n  /**\n   * auto-highlights all pre>code elements on the page\n   */\n  function highlightAll() {\n    // if we are called too early in the loading process\n    if (document.readyState === \"loading\") {\n      wantsHighlight = true;\n      return;\n    }\n\n    const blocks = document.querySelectorAll(options.cssSelector);\n    blocks.forEach(highlightElement);\n  }\n\n  function boot() {\n    // if a highlight was requested before DOM was loaded, do now\n    if (wantsHighlight) highlightAll();\n  }\n\n  // make sure we are in the browser environment\n  if (typeof window !== 'undefined' && window.addEventListener) {\n    window.addEventListener('DOMContentLoaded', boot, false);\n  }\n\n  /**\n   * Register a language grammar module\n   *\n   * @param {string} languageName\n   * @param {LanguageFn} languageDefinition\n   */\n  function registerLanguage(languageName, languageDefinition) {\n    let lang = null;\n    try {\n      lang = languageDefinition(hljs);\n    } catch (error$1) {\n      error(\"Language definition for '{}' could not be registered.\".replace(\"{}\", languageName));\n      // hard or soft error\n      if (!SAFE_MODE) { throw error$1; } else { error(error$1); }\n      // languages that have serious errors are replaced with essentially a\n      // \"plaintext\" stand-in so that the code blocks will still get normal\n      // css classes applied to them - and one bad language won't break the\n      // entire highlighter\n      lang = PLAINTEXT_LANGUAGE;\n    }\n    // give it a temporary name if it doesn't have one in the meta-data\n    if (!lang.name) lang.name = languageName;\n    languages[languageName] = lang;\n    lang.rawDefinition = languageDefinition.bind(null, hljs);\n\n    if (lang.aliases) {\n      registerAliases(lang.aliases, { languageName });\n    }\n  }\n\n  /**\n   * Remove a language grammar module\n   *\n   * @param {string} languageName\n   */\n  function unregisterLanguage(languageName) {\n    delete languages[languageName];\n    for (const alias of Object.keys(aliases)) {\n      if (aliases[alias] === languageName) {\n        delete aliases[alias];\n      }\n    }\n  }\n\n  /**\n   * @returns {string[]} List of language internal names\n   */\n  function listLanguages() {\n    return Object.keys(languages);\n  }\n\n  /**\n   * @param {string} name - name of the language to retrieve\n   * @returns {Language | undefined}\n   */\n  function getLanguage(name) {\n    name = (name || '').toLowerCase();\n    return languages[name] || languages[aliases[name]];\n  }\n\n  /**\n   *\n   * @param {string|string[]} aliasList - single alias or list of aliases\n   * @param {{languageName: string}} opts\n   */\n  function registerAliases(aliasList, { languageName }) {\n    if (typeof aliasList === 'string') {\n      aliasList = [aliasList];\n    }\n    aliasList.forEach(alias => { aliases[alias.toLowerCase()] = languageName; });\n  }\n\n  /**\n   * Determines if a given language has auto-detection enabled\n   * @param {string} name - name of the language\n   */\n  function autoDetection(name) {\n    const lang = getLanguage(name);\n    return lang && !lang.disableAutodetect;\n  }\n\n  /**\n   * Upgrades the old highlightBlock plugins to the new\n   * highlightElement API\n   * @param {HLJSPlugin} plugin\n   */\n  function upgradePluginAPI(plugin) {\n    // TODO: remove with v12\n    if (plugin[\"before:highlightBlock\"] && !plugin[\"before:highlightElement\"]) {\n      plugin[\"before:highlightElement\"] = (data) => {\n        plugin[\"before:highlightBlock\"](\n          Object.assign({ block: data.el }, data)\n        );\n      };\n    }\n    if (plugin[\"after:highlightBlock\"] && !plugin[\"after:highlightElement\"]) {\n      plugin[\"after:highlightElement\"] = (data) => {\n        plugin[\"after:highlightBlock\"](\n          Object.assign({ block: data.el }, data)\n        );\n      };\n    }\n  }\n\n  /**\n   * @param {HLJSPlugin} plugin\n   */\n  function addPlugin(plugin) {\n    upgradePluginAPI(plugin);\n    plugins.push(plugin);\n  }\n\n  /**\n   * @param {HLJSPlugin} plugin\n   */\n  function removePlugin(plugin) {\n    const index = plugins.indexOf(plugin);\n    if (index !== -1) {\n      plugins.splice(index, 1);\n    }\n  }\n\n  /**\n   *\n   * @param {PluginEvent} event\n   * @param {any} args\n   */\n  function fire(event, args) {\n    const cb = event;\n    plugins.forEach(function(plugin) {\n      if (plugin[cb]) {\n        plugin[cb](args);\n      }\n    });\n  }\n\n  /**\n   * DEPRECATED\n   * @param {HighlightedHTMLElement} el\n   */\n  function deprecateHighlightBlock(el) {\n    deprecated(\"10.7.0\", \"highlightBlock will be removed entirely in v12.0\");\n    deprecated(\"10.7.0\", \"Please use highlightElement now.\");\n\n    return highlightElement(el);\n  }\n\n  /* Interface definition */\n  Object.assign(hljs, {\n    highlight,\n    highlightAuto,\n    highlightAll,\n    highlightElement,\n    // TODO: Remove with v12 API\n    highlightBlock: deprecateHighlightBlock,\n    configure,\n    initHighlighting,\n    initHighlightingOnLoad,\n    registerLanguage,\n    unregisterLanguage,\n    listLanguages,\n    getLanguage,\n    registerAliases,\n    autoDetection,\n    inherit,\n    addPlugin,\n    removePlugin\n  });\n\n  hljs.debugMode = function() { SAFE_MODE = false; };\n  hljs.safeMode = function() { SAFE_MODE = true; };\n  hljs.versionString = version;\n\n  hljs.regex = {\n    concat: concat,\n    lookahead: lookahead,\n    either: either,\n    optional: optional,\n    anyNumberOfTimes: anyNumberOfTimes\n  };\n\n  for (const key in MODES) {\n    // @ts-ignore\n    if (typeof MODES[key] === \"object\") {\n      // @ts-ignore\n      deepFreeze(MODES[key]);\n    }\n  }\n\n  // merge all the modes/regexes into our main object\n  Object.assign(hljs, MODES);\n\n  return hljs;\n};\n\n// Other names for the variable may break build script\nconst highlight = HLJS({});\n\n// returns a new instance of the highlighter to be used for extensions\n// check https://github.com/wooorm/lowlight/issues/47\nhighlight.newInstance = () => HLJS({});\n\nmodule.exports = highlight;\nhighlight.HighlightJS = highlight;\nhighlight.default = highlight;\n", "// Imports\nimport ___CSS_LOADER_API_SOURCEMAP_IMPORT___ from \"../../css-loader/dist/runtime/sourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"pre code.hljs {\\n  display: block;\\n  overflow-x: auto;\\n  padding: 1em\\n}\\ncode.hljs {\\n  padding: 3px 5px\\n}\\n/*!\\n  Theme: GitHub\\n  Description: Light theme as seen on github.com\\n  Author: github.com\\n  Maintainer: @Hirse\\n  Updated: 2021-05-15\\n\\n  Outdated base version: https://github.com/primer/github-syntax-light\\n  Current colors taken from GitHub's CSS\\n*/\\n.hljs {\\n  color: #24292e;\\n  background: #ffffff\\n}\\n.hljs-doctag,\\n.hljs-keyword,\\n.hljs-meta .hljs-keyword,\\n.hljs-template-tag,\\n.hljs-template-variable,\\n.hljs-type,\\n.hljs-variable.language_ {\\n  /* prettylights-syntax-keyword */\\n  color: #d73a49\\n}\\n.hljs-title,\\n.hljs-title.class_,\\n.hljs-title.class_.inherited__,\\n.hljs-title.function_ {\\n  /* prettylights-syntax-entity */\\n  color: #6f42c1\\n}\\n.hljs-attr,\\n.hljs-attribute,\\n.hljs-literal,\\n.hljs-meta,\\n.hljs-number,\\n.hljs-operator,\\n.hljs-variable,\\n.hljs-selector-attr,\\n.hljs-selector-class,\\n.hljs-selector-id {\\n  /* prettylights-syntax-constant */\\n  color: #005cc5\\n}\\n.hljs-regexp,\\n.hljs-string,\\n.hljs-meta .hljs-string {\\n  /* prettylights-syntax-string */\\n  color: #032f62\\n}\\n.hljs-built_in,\\n.hljs-symbol {\\n  /* prettylights-syntax-variable */\\n  color: #e36209\\n}\\n.hljs-comment,\\n.hljs-code,\\n.hljs-formula {\\n  /* prettylights-syntax-comment */\\n  color: #6a737d\\n}\\n.hljs-name,\\n.hljs-quote,\\n.hljs-selector-tag,\\n.hljs-selector-pseudo {\\n  /* prettylights-syntax-entity-tag */\\n  color: #22863a\\n}\\n.hljs-subst {\\n  /* prettylights-syntax-storage-modifier-import */\\n  color: #24292e\\n}\\n.hljs-section {\\n  /* prettylights-syntax-markup-heading */\\n  color: #005cc5;\\n  font-weight: bold\\n}\\n.hljs-bullet {\\n  /* prettylights-syntax-markup-list */\\n  color: #735c0f\\n}\\n.hljs-emphasis {\\n  /* prettylights-syntax-markup-italic */\\n  color: #24292e;\\n  font-style: italic\\n}\\n.hljs-strong {\\n  /* prettylights-syntax-markup-bold */\\n  color: #24292e;\\n  font-weight: bold\\n}\\n.hljs-addition {\\n  /* prettylights-syntax-markup-inserted */\\n  color: #22863a;\\n  background-color: #f0fff4\\n}\\n.hljs-deletion {\\n  /* prettylights-syntax-markup-deleted */\\n  color: #b31d28;\\n  background-color: #ffeef0\\n}\\n.hljs-char.escape_,\\n.hljs-link,\\n.hljs-params,\\n.hljs-property,\\n.hljs-punctuation,\\n.hljs-tag {\\n  /* purposely ignored */\\n  \\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://./node_modules/highlight.js/styles/github.css\"],\"names\":[],\"mappings\":\"AAAA;EACE,cAAc;EACd,gBAAgB;EAChB;AACF;AACA;EACE;AACF;AACA;;;;;;;;;CASC;AACD;EACE,cAAc;EACd;AACF;AACA;;;;;;;EAOE,gCAAgC;EAChC;AACF;AACA;;;;EAIE,+BAA+B;EAC/B;AACF;AACA;;;;;;;;;;EAUE,iCAAiC;EACjC;AACF;AACA;;;EAGE,+BAA+B;EAC/B;AACF;AACA;;EAEE,iCAAiC;EACjC;AACF;AACA;;;EAGE,gCAAgC;EAChC;AACF;AACA;;;;EAIE,mCAAmC;EACnC;AACF;AACA;EACE,gDAAgD;EAChD;AACF;AACA;EACE,uCAAuC;EACvC,cAAc;EACd;AACF;AACA;EACE,oCAAoC;EACpC;AACF;AACA;EACE,sCAAsC;EACtC,cAAc;EACd;AACF;AACA;EACE,oCAAoC;EACpC,cAAc;EACd;AACF;AACA;EACE,wCAAwC;EACxC,cAAc;EACd;AACF;AACA;EACE,uCAAuC;EACvC,cAAc;EACd;AACF;AACA;;;;;;EAME,sBAAsB;;AAExB\",\"sourcesContent\":[\"pre code.hljs {\\n  display: block;\\n  overflow-x: auto;\\n  padding: 1em\\n}\\ncode.hljs {\\n  padding: 3px 5px\\n}\\n/*!\\n  Theme: GitHub\\n  Description: Light theme as seen on github.com\\n  Author: github.com\\n  Maintainer: @Hirse\\n  Updated: 2021-05-15\\n\\n  Outdated base version: https://github.com/primer/github-syntax-light\\n  Current colors taken from GitHub's CSS\\n*/\\n.hljs {\\n  color: #24292e;\\n  background: #ffffff\\n}\\n.hljs-doctag,\\n.hljs-keyword,\\n.hljs-meta .hljs-keyword,\\n.hljs-template-tag,\\n.hljs-template-variable,\\n.hljs-type,\\n.hljs-variable.language_ {\\n  /* prettylights-syntax-keyword */\\n  color: #d73a49\\n}\\n.hljs-title,\\n.hljs-title.class_,\\n.hljs-title.class_.inherited__,\\n.hljs-title.function_ {\\n  /* prettylights-syntax-entity */\\n  color: #6f42c1\\n}\\n.hljs-attr,\\n.hljs-attribute,\\n.hljs-literal,\\n.hljs-meta,\\n.hljs-number,\\n.hljs-operator,\\n.hljs-variable,\\n.hljs-selector-attr,\\n.hljs-selector-class,\\n.hljs-selector-id {\\n  /* prettylights-syntax-constant */\\n  color: #005cc5\\n}\\n.hljs-regexp,\\n.hljs-string,\\n.hljs-meta .hljs-string {\\n  /* prettylights-syntax-string */\\n  color: #032f62\\n}\\n.hljs-built_in,\\n.hljs-symbol {\\n  /* prettylights-syntax-variable */\\n  color: #e36209\\n}\\n.hljs-comment,\\n.hljs-code,\\n.hljs-formula {\\n  /* prettylights-syntax-comment */\\n  color: #6a737d\\n}\\n.hljs-name,\\n.hljs-quote,\\n.hljs-selector-tag,\\n.hljs-selector-pseudo {\\n  /* prettylights-syntax-entity-tag */\\n  color: #22863a\\n}\\n.hljs-subst {\\n  /* prettylights-syntax-storage-modifier-import */\\n  color: #24292e\\n}\\n.hljs-section {\\n  /* prettylights-syntax-markup-heading */\\n  color: #005cc5;\\n  font-weight: bold\\n}\\n.hljs-bullet {\\n  /* prettylights-syntax-markup-list */\\n  color: #735c0f\\n}\\n.hljs-emphasis {\\n  /* prettylights-syntax-markup-italic */\\n  color: #24292e;\\n  font-style: italic\\n}\\n.hljs-strong {\\n  /* prettylights-syntax-markup-bold */\\n  color: #24292e;\\n  font-weight: bold\\n}\\n.hljs-addition {\\n  /* prettylights-syntax-markup-inserted */\\n  color: #22863a;\\n  background-color: #f0fff4\\n}\\n.hljs-deletion {\\n  /* prettylights-syntax-markup-deleted */\\n  color: #b31d28;\\n  background-color: #ffeef0\\n}\\n.hljs-char.escape_,\\n.hljs-link,\\n.hljs-params,\\n.hljs-property,\\n.hljs-punctuation,\\n.hljs-tag {\\n  /* purposely ignored */\\n  \\n}\"],\"sourceRoot\":\"\"}]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// https://nodejs.org/api/packages.html#packages_writing_dual_packages_while_avoiding_or_minimizing_hazards\nimport HighlightJS from '../lib/core.js';\nexport { HighlightJS };\nexport default HighlightJS;\n", "\n      import API from \"!../../style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../style-loader/dist/runtime/styleDomAPI.js\";\n      \n      import setAttributes from \"!../../style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../css-loader/dist/cjs.js!./github.css\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\noptions.insert = function insertAtTop(element) {\n                                    var parent = document.querySelector('head');\n                                    // eslint-disable-next-line no-underscore-dangle\n                                    var lastInsertedElement =\n                                        window._lastElementInsertedByStyleLoader;\n\n                                    if (!lastInsertedElement) {\n                                        parent.insertBefore(element, parent.firstChild);\n                                    } else if (lastInsertedElement.nextSibling) {\n                                        parent.insertBefore(element, lastInsertedElement.nextSibling);\n                                    } else {\n                                        parent.appendChild(element);\n                                    }\n\n                                    // eslint-disable-next-line no-underscore-dangle\n                                    window._lastElementInsertedByStyleLoader = element;\n                                };\noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../css-loader/dist/cjs.js!./github.css\";\n       export default content && content.locals ? content.locals : undefined;\n", "const MODES = (hljs) => {\n  return {\n    IMPORTANT: {\n      scope: 'meta',\n      begin: '!important'\n    },\n    BLOCK_COMMENT: hljs.C_BLOCK_COMMENT_MODE,\n    HEXCOLOR: {\n      scope: 'number',\n      begin: /#(([0-9a-fA-F]{3,4})|(([0-9a-fA-F]{2}){3,4}))\\b/\n    },\n    FUNCTION_DISPATCH: {\n      className: \"built_in\",\n      begin: /[\\w-]+(?=\\()/\n    },\n    ATTRIBUTE_SELECTOR_MODE: {\n      scope: 'selector-attr',\n      begin: /\\[/,\n      end: /\\]/,\n      illegal: '$',\n      contains: [\n        hljs.APOS_STRING_MODE,\n        hljs.QUOTE_STRING_MODE\n      ]\n    },\n    CSS_NUMBER_MODE: {\n      scope: 'number',\n      begin: hljs.NUMBER_RE + '(' +\n        '%|em|ex|ch|rem' +\n        '|vw|vh|vmin|vmax' +\n        '|cm|mm|in|pt|pc|px' +\n        '|deg|grad|rad|turn' +\n        '|s|ms' +\n        '|Hz|kHz' +\n        '|dpi|dpcm|dppx' +\n        ')?',\n      relevance: 0\n    },\n    CSS_VARIABLE: {\n      className: \"attr\",\n      begin: /--[A-Za-z_][A-Za-z0-9_-]*/\n    }\n  };\n};\n\nconst TAGS = [\n  'a',\n  'abbr',\n  'address',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'blockquote',\n  'body',\n  'button',\n  'canvas',\n  'caption',\n  'cite',\n  'code',\n  'dd',\n  'del',\n  'details',\n  'dfn',\n  'div',\n  'dl',\n  'dt',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'header',\n  'hgroup',\n  'html',\n  'i',\n  'iframe',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'mark',\n  'menu',\n  'nav',\n  'object',\n  'ol',\n  'p',\n  'q',\n  'quote',\n  'samp',\n  'section',\n  'span',\n  'strong',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'ul',\n  'var',\n  'video'\n];\n\nconst MEDIA_FEATURES = [\n  'any-hover',\n  'any-pointer',\n  'aspect-ratio',\n  'color',\n  'color-gamut',\n  'color-index',\n  'device-aspect-ratio',\n  'device-height',\n  'device-width',\n  'display-mode',\n  'forced-colors',\n  'grid',\n  'height',\n  'hover',\n  'inverted-colors',\n  'monochrome',\n  'orientation',\n  'overflow-block',\n  'overflow-inline',\n  'pointer',\n  'prefers-color-scheme',\n  'prefers-contrast',\n  'prefers-reduced-motion',\n  'prefers-reduced-transparency',\n  'resolution',\n  'scan',\n  'scripting',\n  'update',\n  'width',\n  // TODO: find a better solution?\n  'min-width',\n  'max-width',\n  'min-height',\n  'max-height'\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-classes\nconst PSEUDO_CLASSES = [\n  'active',\n  'any-link',\n  'blank',\n  'checked',\n  'current',\n  'default',\n  'defined',\n  'dir', // dir()\n  'disabled',\n  'drop',\n  'empty',\n  'enabled',\n  'first',\n  'first-child',\n  'first-of-type',\n  'fullscreen',\n  'future',\n  'focus',\n  'focus-visible',\n  'focus-within',\n  'has', // has()\n  'host', // host or host()\n  'host-context', // host-context()\n  'hover',\n  'indeterminate',\n  'in-range',\n  'invalid',\n  'is', // is()\n  'lang', // lang()\n  'last-child',\n  'last-of-type',\n  'left',\n  'link',\n  'local-link',\n  'not', // not()\n  'nth-child', // nth-child()\n  'nth-col', // nth-col()\n  'nth-last-child', // nth-last-child()\n  'nth-last-col', // nth-last-col()\n  'nth-last-of-type', //nth-last-of-type()\n  'nth-of-type', //nth-of-type()\n  'only-child',\n  'only-of-type',\n  'optional',\n  'out-of-range',\n  'past',\n  'placeholder-shown',\n  'read-only',\n  'read-write',\n  'required',\n  'right',\n  'root',\n  'scope',\n  'target',\n  'target-within',\n  'user-invalid',\n  'valid',\n  'visited',\n  'where' // where()\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-elements\nconst PSEUDO_ELEMENTS = [\n  'after',\n  'backdrop',\n  'before',\n  'cue',\n  'cue-region',\n  'first-letter',\n  'first-line',\n  'grammar-error',\n  'marker',\n  'part',\n  'placeholder',\n  'selection',\n  'slotted',\n  'spelling-error'\n];\n\nconst ATTRIBUTES = [\n  'align-content',\n  'align-items',\n  'align-self',\n  'all',\n  'animation',\n  'animation-delay',\n  'animation-direction',\n  'animation-duration',\n  'animation-fill-mode',\n  'animation-iteration-count',\n  'animation-name',\n  'animation-play-state',\n  'animation-timing-function',\n  'backface-visibility',\n  'background',\n  'background-attachment',\n  'background-blend-mode',\n  'background-clip',\n  'background-color',\n  'background-image',\n  'background-origin',\n  'background-position',\n  'background-repeat',\n  'background-size',\n  'block-size',\n  'border',\n  'border-block',\n  'border-block-color',\n  'border-block-end',\n  'border-block-end-color',\n  'border-block-end-style',\n  'border-block-end-width',\n  'border-block-start',\n  'border-block-start-color',\n  'border-block-start-style',\n  'border-block-start-width',\n  'border-block-style',\n  'border-block-width',\n  'border-bottom',\n  'border-bottom-color',\n  'border-bottom-left-radius',\n  'border-bottom-right-radius',\n  'border-bottom-style',\n  'border-bottom-width',\n  'border-collapse',\n  'border-color',\n  'border-image',\n  'border-image-outset',\n  'border-image-repeat',\n  'border-image-slice',\n  'border-image-source',\n  'border-image-width',\n  'border-inline',\n  'border-inline-color',\n  'border-inline-end',\n  'border-inline-end-color',\n  'border-inline-end-style',\n  'border-inline-end-width',\n  'border-inline-start',\n  'border-inline-start-color',\n  'border-inline-start-style',\n  'border-inline-start-width',\n  'border-inline-style',\n  'border-inline-width',\n  'border-left',\n  'border-left-color',\n  'border-left-style',\n  'border-left-width',\n  'border-radius',\n  'border-right',\n  'border-right-color',\n  'border-right-style',\n  'border-right-width',\n  'border-spacing',\n  'border-style',\n  'border-top',\n  'border-top-color',\n  'border-top-left-radius',\n  'border-top-right-radius',\n  'border-top-style',\n  'border-top-width',\n  'border-width',\n  'bottom',\n  'box-decoration-break',\n  'box-shadow',\n  'box-sizing',\n  'break-after',\n  'break-before',\n  'break-inside',\n  'caption-side',\n  'caret-color',\n  'clear',\n  'clip',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'column-count',\n  'column-fill',\n  'column-gap',\n  'column-rule',\n  'column-rule-color',\n  'column-rule-style',\n  'column-rule-width',\n  'column-span',\n  'column-width',\n  'columns',\n  'contain',\n  'content',\n  'content-visibility',\n  'counter-increment',\n  'counter-reset',\n  'cue',\n  'cue-after',\n  'cue-before',\n  'cursor',\n  'direction',\n  'display',\n  'empty-cells',\n  'filter',\n  'flex',\n  'flex-basis',\n  'flex-direction',\n  'flex-flow',\n  'flex-grow',\n  'flex-shrink',\n  'flex-wrap',\n  'float',\n  'flow',\n  'font',\n  'font-display',\n  'font-family',\n  'font-feature-settings',\n  'font-kerning',\n  'font-language-override',\n  'font-size',\n  'font-size-adjust',\n  'font-smoothing',\n  'font-stretch',\n  'font-style',\n  'font-synthesis',\n  'font-variant',\n  'font-variant-caps',\n  'font-variant-east-asian',\n  'font-variant-ligatures',\n  'font-variant-numeric',\n  'font-variant-position',\n  'font-variation-settings',\n  'font-weight',\n  'gap',\n  'glyph-orientation-vertical',\n  'grid',\n  'grid-area',\n  'grid-auto-columns',\n  'grid-auto-flow',\n  'grid-auto-rows',\n  'grid-column',\n  'grid-column-end',\n  'grid-column-start',\n  'grid-gap',\n  'grid-row',\n  'grid-row-end',\n  'grid-row-start',\n  'grid-template',\n  'grid-template-areas',\n  'grid-template-columns',\n  'grid-template-rows',\n  'hanging-punctuation',\n  'height',\n  'hyphens',\n  'icon',\n  'image-orientation',\n  'image-rendering',\n  'image-resolution',\n  'ime-mode',\n  'inline-size',\n  'isolation',\n  'justify-content',\n  'left',\n  'letter-spacing',\n  'line-break',\n  'line-height',\n  'list-style',\n  'list-style-image',\n  'list-style-position',\n  'list-style-type',\n  'margin',\n  'margin-block',\n  'margin-block-end',\n  'margin-block-start',\n  'margin-bottom',\n  'margin-inline',\n  'margin-inline-end',\n  'margin-inline-start',\n  'margin-left',\n  'margin-right',\n  'margin-top',\n  'marks',\n  'mask',\n  'mask-border',\n  'mask-border-mode',\n  'mask-border-outset',\n  'mask-border-repeat',\n  'mask-border-slice',\n  'mask-border-source',\n  'mask-border-width',\n  'mask-clip',\n  'mask-composite',\n  'mask-image',\n  'mask-mode',\n  'mask-origin',\n  'mask-position',\n  'mask-repeat',\n  'mask-size',\n  'mask-type',\n  'max-block-size',\n  'max-height',\n  'max-inline-size',\n  'max-width',\n  'min-block-size',\n  'min-height',\n  'min-inline-size',\n  'min-width',\n  'mix-blend-mode',\n  'nav-down',\n  'nav-index',\n  'nav-left',\n  'nav-right',\n  'nav-up',\n  'none',\n  'normal',\n  'object-fit',\n  'object-position',\n  'opacity',\n  'order',\n  'orphans',\n  'outline',\n  'outline-color',\n  'outline-offset',\n  'outline-style',\n  'outline-width',\n  'overflow',\n  'overflow-wrap',\n  'overflow-x',\n  'overflow-y',\n  'padding',\n  'padding-block',\n  'padding-block-end',\n  'padding-block-start',\n  'padding-bottom',\n  'padding-inline',\n  'padding-inline-end',\n  'padding-inline-start',\n  'padding-left',\n  'padding-right',\n  'padding-top',\n  'page-break-after',\n  'page-break-before',\n  'page-break-inside',\n  'pause',\n  'pause-after',\n  'pause-before',\n  'perspective',\n  'perspective-origin',\n  'pointer-events',\n  'position',\n  'quotes',\n  'resize',\n  'rest',\n  'rest-after',\n  'rest-before',\n  'right',\n  'row-gap',\n  'scroll-margin',\n  'scroll-margin-block',\n  'scroll-margin-block-end',\n  'scroll-margin-block-start',\n  'scroll-margin-bottom',\n  'scroll-margin-inline',\n  'scroll-margin-inline-end',\n  'scroll-margin-inline-start',\n  'scroll-margin-left',\n  'scroll-margin-right',\n  'scroll-margin-top',\n  'scroll-padding',\n  'scroll-padding-block',\n  'scroll-padding-block-end',\n  'scroll-padding-block-start',\n  'scroll-padding-bottom',\n  'scroll-padding-inline',\n  'scroll-padding-inline-end',\n  'scroll-padding-inline-start',\n  'scroll-padding-left',\n  'scroll-padding-right',\n  'scroll-padding-top',\n  'scroll-snap-align',\n  'scroll-snap-stop',\n  'scroll-snap-type',\n  'scrollbar-color',\n  'scrollbar-gutter',\n  'scrollbar-width',\n  'shape-image-threshold',\n  'shape-margin',\n  'shape-outside',\n  'speak',\n  'speak-as',\n  'src', // @font-face\n  'tab-size',\n  'table-layout',\n  'text-align',\n  'text-align-all',\n  'text-align-last',\n  'text-combine-upright',\n  'text-decoration',\n  'text-decoration-color',\n  'text-decoration-line',\n  'text-decoration-style',\n  'text-emphasis',\n  'text-emphasis-color',\n  'text-emphasis-position',\n  'text-emphasis-style',\n  'text-indent',\n  'text-justify',\n  'text-orientation',\n  'text-overflow',\n  'text-rendering',\n  'text-shadow',\n  'text-transform',\n  'text-underline-position',\n  'top',\n  'transform',\n  'transform-box',\n  'transform-origin',\n  'transform-style',\n  'transition',\n  'transition-delay',\n  'transition-duration',\n  'transition-property',\n  'transition-timing-function',\n  'unicode-bidi',\n  'vertical-align',\n  'visibility',\n  'voice-balance',\n  'voice-duration',\n  'voice-family',\n  'voice-pitch',\n  'voice-range',\n  'voice-rate',\n  'voice-stress',\n  'voice-volume',\n  'white-space',\n  'widows',\n  'width',\n  'will-change',\n  'word-break',\n  'word-spacing',\n  'word-wrap',\n  'writing-mode',\n  'z-index'\n  // reverse makes sure longer attributes `font-weight` are matched fully\n  // instead of getting false positives on say `font`\n].reverse();\n\n/*\nLanguage: CSS\nCategory: common, css, web\nWebsite: https://developer.mozilla.org/en-US/docs/Web/CSS\n*/\n\n\n/** @type LanguageFn */\nfunction css(hljs) {\n  const regex = hljs.regex;\n  const modes = MODES(hljs);\n  const VENDOR_PREFIX = { begin: /-(webkit|moz|ms|o)-(?=[a-z])/ };\n  const AT_MODIFIERS = \"and or not only\";\n  const AT_PROPERTY_RE = /@-?\\w[\\w]*(-\\w+)*/; // @-webkit-keyframes\n  const IDENT_RE = '[a-zA-Z-][a-zA-Z0-9_-]*';\n  const STRINGS = [\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE\n  ];\n\n  return {\n    name: 'CSS',\n    case_insensitive: true,\n    illegal: /[=|'\\$]/,\n    keywords: { keyframePosition: \"from to\" },\n    classNameAliases: {\n      // for visual continuity with `tag {}` and because we\n      // don't have a great class for this?\n      keyframePosition: \"selector-tag\" },\n    contains: [\n      modes.BLOCK_COMMENT,\n      VENDOR_PREFIX,\n      // to recognize keyframe 40% etc which are outside the scope of our\n      // attribute value mode\n      modes.CSS_NUMBER_MODE,\n      {\n        className: 'selector-id',\n        begin: /#[A-Za-z0-9_-]+/,\n        relevance: 0\n      },\n      {\n        className: 'selector-class',\n        begin: '\\\\.' + IDENT_RE,\n        relevance: 0\n      },\n      modes.ATTRIBUTE_SELECTOR_MODE,\n      {\n        className: 'selector-pseudo',\n        variants: [\n          { begin: ':(' + PSEUDO_CLASSES.join('|') + ')' },\n          { begin: ':(:)?(' + PSEUDO_ELEMENTS.join('|') + ')' }\n        ]\n      },\n      // we may actually need this (12/2020)\n      // { // pseudo-selector params\n      //   begin: /\\(/,\n      //   end: /\\)/,\n      //   contains: [ hljs.CSS_NUMBER_MODE ]\n      // },\n      modes.CSS_VARIABLE,\n      {\n        className: 'attribute',\n        begin: '\\\\b(' + ATTRIBUTES.join('|') + ')\\\\b'\n      },\n      // attribute values\n      {\n        begin: /:/,\n        end: /[;}{]/,\n        contains: [\n          modes.BLOCK_COMMENT,\n          modes.HEXCOLOR,\n          modes.IMPORTANT,\n          modes.CSS_NUMBER_MODE,\n          ...STRINGS,\n          // needed to highlight these as strings and to avoid issues with\n          // illegal characters that might be inside urls that would tigger the\n          // languages illegal stack\n          {\n            begin: /(url|data-uri)\\(/,\n            end: /\\)/,\n            relevance: 0, // from keywords\n            keywords: { built_in: \"url data-uri\" },\n            contains: [\n              ...STRINGS,\n              {\n                className: \"string\",\n                // any character other than `)` as in `url()` will be the start\n                // of a string, which ends with `)` (from the parent mode)\n                begin: /[^)]/,\n                endsWithParent: true,\n                excludeEnd: true\n              }\n            ]\n          },\n          modes.FUNCTION_DISPATCH\n        ]\n      },\n      {\n        begin: regex.lookahead(/@/),\n        end: '[{;]',\n        relevance: 0,\n        illegal: /:/, // break on Less variables @var: ...\n        contains: [\n          {\n            className: 'keyword',\n            begin: AT_PROPERTY_RE\n          },\n          {\n            begin: /\\s/,\n            endsWithParent: true,\n            excludeEnd: true,\n            relevance: 0,\n            keywords: {\n              $pattern: /[a-z-]+/,\n              keyword: AT_MODIFIERS,\n              attribute: MEDIA_FEATURES.join(\" \")\n            },\n            contains: [\n              {\n                begin: /[a-z-]+(?=:)/,\n                className: \"attribute\"\n              },\n              ...STRINGS,\n              modes.CSS_NUMBER_MODE\n            ]\n          }\n        ]\n      },\n      {\n        className: 'selector-tag',\n        begin: '\\\\b(' + TAGS.join('|') + ')\\\\b'\n      }\n    ]\n  };\n}\n\nexport { css as default };\n", "/**\n * @param {string} value\n * @returns {RegExp}\n * */\nfunction escape(value) {\n  return new RegExp(value.replace(/[-/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&'), 'm');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * @param { Array<string | RegExp | Object> } args\n * @returns {object}\n */\nfunction stripOptionsFromArgs(args) {\n  const opts = args[args.length - 1];\n\n  if (typeof opts === 'object' && opts.constructor === Object) {\n    args.splice(args.length - 1, 1);\n    return opts;\n  } else {\n    return {};\n  }\n}\n\n/** @typedef { {capture?: boolean} } RegexEitherOptions */\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] | [...(RegExp | string)[], RegexEitherOptions]} args\n * @returns {string}\n */\nfunction either(...args) {\n  /** @type { object & {capture?: boolean} }  */\n  const opts = stripOptionsFromArgs(args);\n  const joined = '('\n    + (opts.capture ? \"\" : \"?:\")\n    + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: F#\nAuthor: Jonas Follesø <<EMAIL>>\nContributors: Troy Kershaw <<EMAIL>>, Henrik Feldt <<EMAIL>>, Melvyn Laïly <<EMAIL>>\nWebsite: https://docs.microsoft.com/en-us/dotnet/fsharp/\nCategory: functional\n*/\n\n\n/** @type LanguageFn */\nfunction fsharp(hljs) {\n  const KEYWORDS = [\n    \"abstract\",\n    \"and\",\n    \"as\",\n    \"assert\",\n    \"base\",\n    \"begin\",\n    \"class\",\n    \"default\",\n    \"delegate\",\n    \"do\",\n    \"done\",\n    \"downcast\",\n    \"downto\",\n    \"elif\",\n    \"else\",\n    \"end\",\n    \"exception\",\n    \"extern\",\n    // \"false\", // literal\n    \"finally\",\n    \"fixed\",\n    \"for\",\n    \"fun\",\n    \"function\",\n    \"global\",\n    \"if\",\n    \"in\",\n    \"inherit\",\n    \"inline\",\n    \"interface\",\n    \"internal\",\n    \"lazy\",\n    \"let\",\n    \"match\",\n    \"member\",\n    \"module\",\n    \"mutable\",\n    \"namespace\",\n    \"new\",\n    // \"not\", // built_in\n    // \"null\", // literal\n    \"of\",\n    \"open\",\n    \"or\",\n    \"override\",\n    \"private\",\n    \"public\",\n    \"rec\",\n    \"return\",\n    \"static\",\n    \"struct\",\n    \"then\",\n    \"to\",\n    // \"true\", // literal\n    \"try\",\n    \"type\",\n    \"upcast\",\n    \"use\",\n    \"val\",\n    \"void\",\n    \"when\",\n    \"while\",\n    \"with\",\n    \"yield\"\n  ];\n\n  const BANG_KEYWORD_MODE = {\n    // monad builder keywords (matches before non-bang keywords)\n    scope: 'keyword',\n    match: /\\b(yield|return|let|do|match|use)!/\n  };\n\n  const PREPROCESSOR_KEYWORDS = [\n    \"if\",\n    \"else\",\n    \"endif\",\n    \"line\",\n    \"nowarn\",\n    \"light\",\n    \"r\",\n    \"i\",\n    \"I\",\n    \"load\",\n    \"time\",\n    \"help\",\n    \"quit\"\n  ];\n\n  const LITERALS = [\n    \"true\",\n    \"false\",\n    \"null\",\n    \"Some\",\n    \"None\",\n    \"Ok\",\n    \"Error\",\n    \"infinity\",\n    \"infinityf\",\n    \"nan\",\n    \"nanf\"\n  ];\n\n  const SPECIAL_IDENTIFIERS = [\n    \"__LINE__\",\n    \"__SOURCE_DIRECTORY__\",\n    \"__SOURCE_FILE__\"\n  ];\n\n  // Since it's possible to re-bind/shadow names (e.g. let char = 'c'),\n  // these builtin types should only be matched when a type name is expected.\n  const KNOWN_TYPES = [\n    // basic types\n    \"bool\",\n    \"byte\",\n    \"sbyte\",\n    \"int8\",\n    \"int16\",\n    \"int32\",\n    \"uint8\",\n    \"uint16\",\n    \"uint32\",\n    \"int\",\n    \"uint\",\n    \"int64\",\n    \"uint64\",\n    \"nativeint\",\n    \"unativeint\",\n    \"decimal\",\n    \"float\",\n    \"double\",\n    \"float32\",\n    \"single\",\n    \"char\",\n    \"string\",\n    \"unit\",\n    \"bigint\",\n    // other native types or lowercase aliases\n    \"option\",\n    \"voption\",\n    \"list\",\n    \"array\",\n    \"seq\",\n    \"byref\",\n    \"exn\",\n    \"inref\",\n    \"nativeptr\",\n    \"obj\",\n    \"outref\",\n    \"voidptr\",\n    // other important FSharp types\n    \"Result\"\n  ];\n\n  const BUILTINS = [\n    // Somewhat arbitrary list of builtin functions and values.\n    // Most of them are declared in Microsoft.FSharp.Core\n    // I tried to stay relevant by adding only the most idiomatic\n    // and most used symbols that are not already declared as types.\n    \"not\",\n    \"ref\",\n    \"raise\",\n    \"reraise\",\n    \"dict\",\n    \"readOnlyDict\",\n    \"set\",\n    \"get\",\n    \"enum\",\n    \"sizeof\",\n    \"typeof\",\n    \"typedefof\",\n    \"nameof\",\n    \"nullArg\",\n    \"invalidArg\",\n    \"invalidOp\",\n    \"id\",\n    \"fst\",\n    \"snd\",\n    \"ignore\",\n    \"lock\",\n    \"using\",\n    \"box\",\n    \"unbox\",\n    \"tryUnbox\",\n    \"printf\",\n    \"printfn\",\n    \"sprintf\",\n    \"eprintf\",\n    \"eprintfn\",\n    \"fprintf\",\n    \"fprintfn\",\n    \"failwith\",\n    \"failwithf\"\n  ];\n\n  const ALL_KEYWORDS = {\n    keyword: KEYWORDS,\n    literal: LITERALS,\n    built_in: BUILTINS,\n    'variable.constant': SPECIAL_IDENTIFIERS\n  };\n\n  // (* potentially multi-line Meta Language style comment *)\n  const ML_COMMENT =\n    hljs.COMMENT(/\\(\\*(?!\\))/, /\\*\\)/, {\n      contains: [\"self\"]\n    });\n  // Either a multi-line (* Meta Language style comment *) or a single line // C style comment.\n  const COMMENT = {\n    variants: [\n      ML_COMMENT,\n      hljs.C_LINE_COMMENT_MODE,\n    ]\n  };\n\n  // Most identifiers can contain apostrophes\n  const IDENTIFIER_RE = /[a-zA-Z_](\\w|')*/;\n\n  const QUOTED_IDENTIFIER = {\n    scope: 'variable',\n    begin: /``/,\n    end: /``/\n  };\n\n  // 'a or ^a where a can be a ``quoted identifier``\n  const BEGIN_GENERIC_TYPE_SYMBOL_RE = /\\B('|\\^)/;\n  const GENERIC_TYPE_SYMBOL = {\n    scope: 'symbol',\n    variants: [\n      // the type name is a quoted identifier:\n      { match: concat(BEGIN_GENERIC_TYPE_SYMBOL_RE, /``.*?``/) },\n      // the type name is a normal identifier (we don't use IDENTIFIER_RE because there cannot be another apostrophe here):\n      { match: concat(BEGIN_GENERIC_TYPE_SYMBOL_RE, hljs.UNDERSCORE_IDENT_RE) }\n    ],\n    relevance: 0\n  };\n\n  const makeOperatorMode = function({ includeEqual }) {\n    // List or symbolic operator characters from the FSharp Spec 4.1, minus the dot, and with `?` added, used for nullable operators.\n    let allOperatorChars;\n    if (includeEqual)\n      allOperatorChars = \"!%&*+-/<=>@^|~?\";\n    else\n      allOperatorChars = \"!%&*+-/<>@^|~?\";\n    const OPERATOR_CHARS = Array.from(allOperatorChars);\n    const OPERATOR_CHAR_RE = concat('[', ...OPERATOR_CHARS.map(escape), ']');\n    // The lone dot operator is special. It cannot be redefined, and we don't want to highlight it. It can be used as part of a multi-chars operator though.\n    const OPERATOR_CHAR_OR_DOT_RE = either(OPERATOR_CHAR_RE, /\\./);\n    // When a dot is present, it must be followed by another operator char:\n    const OPERATOR_FIRST_CHAR_OF_MULTIPLE_RE = concat(OPERATOR_CHAR_OR_DOT_RE, lookahead(OPERATOR_CHAR_OR_DOT_RE));\n    const SYMBOLIC_OPERATOR_RE = either(\n      concat(OPERATOR_FIRST_CHAR_OF_MULTIPLE_RE, OPERATOR_CHAR_OR_DOT_RE, '*'), // Matches at least 2 chars operators\n      concat(OPERATOR_CHAR_RE, '+'), // Matches at least one char operators\n    );\n    return {\n      scope: 'operator',\n      match: either(\n        // symbolic operators:\n        SYMBOLIC_OPERATOR_RE,\n        // other symbolic keywords:\n        // Type casting and conversion operators:\n        /:\\?>/,\n        /:\\?/,\n        /:>/,\n        /:=/, // Reference cell assignment\n        /::?/, // : or ::\n        /\\$/), // A single $ can be used as an operator\n      relevance: 0\n    };\n  };\n\n  const OPERATOR = makeOperatorMode({ includeEqual: true });\n  // This variant is used when matching '=' should end a parent mode:\n  const OPERATOR_WITHOUT_EQUAL = makeOperatorMode({ includeEqual: false });\n\n  const makeTypeAnnotationMode = function(prefix, prefixScope) {\n    return {\n      begin: concat( // a type annotation is a\n        prefix,            // should be a colon or the 'of' keyword\n        lookahead(   // that has to be followed by\n          concat(\n            /\\s*/,         // optional space\n            either(  // then either of:\n              /\\w/,        // word\n              /'/,         // generic type name\n              /\\^/,        // generic type name\n              /#/,         // flexible type name\n              /``/,        // quoted type name\n              /\\(/,        // parens type expression\n              /{\\|/,       // anonymous type annotation\n      )))),\n      beginScope: prefixScope,\n      // BUG: because ending with \\n is necessary for some cases, multi-line type annotations are not properly supported.\n      // Examples where \\n is required at the end:\n      // - abstract member definitions in classes: abstract Property : int * string\n      // - return type annotations: let f f' = f' () : returnTypeAnnotation\n      // - record fields definitions: { A : int \\n B : string }\n      end: lookahead(\n        either(\n          /\\n/,\n          /=/)),\n      relevance: 0,\n      // we need the known types, and we need the type constraint keywords and literals. e.g.: when 'a : null\n      keywords: hljs.inherit(ALL_KEYWORDS, { type: KNOWN_TYPES }),\n      contains: [\n        COMMENT,\n        GENERIC_TYPE_SYMBOL,\n        hljs.inherit(QUOTED_IDENTIFIER, { scope: null }), // match to avoid strange patterns inside that may break the parsing\n        OPERATOR_WITHOUT_EQUAL\n      ]\n    };\n  };\n\n  const TYPE_ANNOTATION = makeTypeAnnotationMode(/:/, 'operator');\n  const DISCRIMINATED_UNION_TYPE_ANNOTATION = makeTypeAnnotationMode(/\\bof\\b/, 'keyword');\n\n  // type MyType<'a> = ...\n  const TYPE_DECLARATION = {\n    begin: [\n      /(^|\\s+)/, // prevents matching the following: `match s.stype with`\n      /type/,\n      /\\s+/,\n      IDENTIFIER_RE\n    ],\n    beginScope: {\n      2: 'keyword',\n      4: 'title.class'\n    },\n    end: lookahead(/\\(|=|$/),\n    keywords: ALL_KEYWORDS, // match keywords in type constraints. e.g.: when 'a : null\n    contains: [\n      COMMENT,\n      hljs.inherit(QUOTED_IDENTIFIER, { scope: null }), // match to avoid strange patterns inside that may break the parsing\n      GENERIC_TYPE_SYMBOL,\n      {\n        // For visual consistency, highlight type brackets as operators.\n        scope: 'operator',\n        match: /<|>/\n      },\n      TYPE_ANNOTATION // generic types can have constraints, which are type annotations. e.g. type MyType<'T when 'T : delegate<obj * string>> =\n    ]\n  };\n\n  const COMPUTATION_EXPRESSION = {\n    // computation expressions:\n    scope: 'computation-expression',\n    // BUG: might conflict with record deconstruction. e.g. let f { Name = name } = name // will highlight f\n    match: /\\b[_a-z]\\w*(?=\\s*\\{)/\n  };\n\n  const PREPROCESSOR = {\n    // preprocessor directives and fsi commands:\n    begin: [\n      /^\\s*/,\n      concat(/#/, either(...PREPROCESSOR_KEYWORDS)),\n      /\\b/\n    ],\n    beginScope: { 2: 'meta' },\n    end: lookahead(/\\s|$/)\n  };\n\n  // TODO: this definition is missing support for type suffixes and octal notation.\n  // BUG: range operator without any space is wrongly interpreted as a single number (e.g. 1..10 )\n  const NUMBER = {\n    variants: [\n      hljs.BINARY_NUMBER_MODE,\n      hljs.C_NUMBER_MODE\n    ]\n  };\n\n  // All the following string definitions are potentially multi-line.\n  // BUG: these definitions are missing support for byte strings (suffixed with B)\n\n  // \"...\"\n  const QUOTED_STRING = {\n    scope: 'string',\n    begin: /\"/,\n    end: /\"/,\n    contains: [\n      hljs.BACKSLASH_ESCAPE\n    ]\n  };\n  // @\"...\"\n  const VERBATIM_STRING = {\n    scope: 'string',\n    begin: /@\"/,\n    end: /\"/,\n    contains: [\n      {\n        match: /\"\"/ // escaped \"\n      },\n      hljs.BACKSLASH_ESCAPE\n    ]\n  };\n  // \"\"\"...\"\"\"\n  const TRIPLE_QUOTED_STRING = {\n    scope: 'string',\n    begin: /\"\"\"/,\n    end: /\"\"\"/,\n    relevance: 2\n  };\n  const SUBST = {\n    scope: 'subst',\n    begin: /\\{/,\n    end: /\\}/,\n    keywords: ALL_KEYWORDS\n  };\n  // $\"...{1+1}...\"\n  const INTERPOLATED_STRING = {\n    scope: 'string',\n    begin: /\\$\"/,\n    end: /\"/,\n    contains: [\n      {\n        match: /\\{\\{/ // escaped {\n      },\n      {\n        match: /\\}\\}/ // escaped }\n      },\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ]\n  };\n  // $@\"...{1+1}...\"\n  const INTERPOLATED_VERBATIM_STRING = {\n    scope: 'string',\n    begin: /(\\$@|@\\$)\"/,\n    end: /\"/,\n    contains: [\n      {\n        match: /\\{\\{/ // escaped {\n      },\n      {\n        match: /\\}\\}/ // escaped }\n      },\n      {\n        match: /\"\"/\n      },\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ]\n  };\n  // $\"\"\"...{1+1}...\"\"\"\n  const INTERPOLATED_TRIPLE_QUOTED_STRING = {\n    scope: 'string',\n    begin: /\\$\"\"\"/,\n    end: /\"\"\"/,\n    contains: [\n      {\n        match: /\\{\\{/ // escaped {\n      },\n      {\n        match: /\\}\\}/ // escaped }\n      },\n      SUBST\n    ],\n    relevance: 2\n  };\n  // '.'\n  const CHAR_LITERAL = {\n    scope: 'string',\n    match: concat(\n      /'/,\n      either(\n        /[^\\\\']/, // either a single non escaped char...\n        /\\\\(?:.|\\d{3}|x[a-fA-F\\d]{2}|u[a-fA-F\\d]{4}|U[a-fA-F\\d]{8})/ // ...or an escape sequence\n      ),\n      /'/\n    )\n  };\n  // F# allows a lot of things inside string placeholders.\n  // Things that don't currently seem allowed by the compiler: types definition, attributes usage.\n  // (Strictly speaking, some of the followings are only allowed inside triple quoted interpolated strings...)\n  SUBST.contains = [\n    INTERPOLATED_VERBATIM_STRING,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING,\n    QUOTED_STRING,\n    CHAR_LITERAL,\n    BANG_KEYWORD_MODE,\n    COMMENT,\n    QUOTED_IDENTIFIER,\n    TYPE_ANNOTATION,\n    COMPUTATION_EXPRESSION,\n    PREPROCESSOR,\n    NUMBER,\n    GENERIC_TYPE_SYMBOL,\n    OPERATOR\n  ];\n  const STRING = {\n    variants: [\n      INTERPOLATED_TRIPLE_QUOTED_STRING,\n      INTERPOLATED_VERBATIM_STRING,\n      INTERPOLATED_STRING,\n      TRIPLE_QUOTED_STRING,\n      VERBATIM_STRING,\n      QUOTED_STRING,\n      CHAR_LITERAL\n    ]\n  };\n\n  return {\n    name: 'F#',\n    aliases: [\n      'fs',\n      'f#'\n    ],\n    keywords: ALL_KEYWORDS,\n    illegal: /\\/\\*/,\n    classNameAliases: {\n      'computation-expression': 'keyword'\n    },\n    contains: [\n      BANG_KEYWORD_MODE,\n      STRING,\n      COMMENT,\n      QUOTED_IDENTIFIER,\n      TYPE_DECLARATION,\n      {\n        // e.g. [<Attributes(\"\")>] or [<``module``: MyCustomAttributeThatWorksOnModules>]\n        // or [<Sealed; NoEquality; NoComparison; CompiledName(\"FSharpAsync`1\")>]\n        scope: 'meta',\n        begin: /\\[</,\n        end: />\\]/,\n        relevance: 2,\n        contains: [\n          QUOTED_IDENTIFIER,\n          // can contain any constant value\n          TRIPLE_QUOTED_STRING,\n          VERBATIM_STRING,\n          QUOTED_STRING,\n          CHAR_LITERAL,\n          NUMBER\n        ]\n      },\n      DISCRIMINATED_UNION_TYPE_ANNOTATION,\n      TYPE_ANNOTATION,\n      COMPUTATION_EXPRESSION,\n      PREPROCESSOR,\n      NUMBER,\n      GENERIC_TYPE_SYMBOL,\n      OPERATOR\n    ]\n  };\n}\n\nexport { fsharp as default };\n", "const IDENT_RE = '[A-Za-z$_][0-9A-Za-z$_]*';\nconst KEYWORDS = [\n  \"as\", // for exports\n  \"in\",\n  \"of\",\n  \"if\",\n  \"for\",\n  \"while\",\n  \"finally\",\n  \"var\",\n  \"new\",\n  \"function\",\n  \"do\",\n  \"return\",\n  \"void\",\n  \"else\",\n  \"break\",\n  \"catch\",\n  \"instanceof\",\n  \"with\",\n  \"throw\",\n  \"case\",\n  \"default\",\n  \"try\",\n  \"switch\",\n  \"continue\",\n  \"typeof\",\n  \"delete\",\n  \"let\",\n  \"yield\",\n  \"const\",\n  \"class\",\n  // J<PERSON> handles these with a special rule\n  // \"get\",\n  // \"set\",\n  \"debugger\",\n  \"async\",\n  \"await\",\n  \"static\",\n  \"import\",\n  \"from\",\n  \"export\",\n  \"extends\"\n];\nconst LITERALS = [\n  \"true\",\n  \"false\",\n  \"null\",\n  \"undefined\",\n  \"NaN\",\n  \"Infinity\"\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects\nconst TYPES = [\n  // Fundamental objects\n  \"Object\",\n  \"Function\",\n  \"Boolean\",\n  \"Symbol\",\n  // numbers and dates\n  \"Math\",\n  \"Date\",\n  \"Number\",\n  \"BigInt\",\n  // text\n  \"String\",\n  \"RegExp\",\n  // Indexed collections\n  \"Array\",\n  \"Float32Array\",\n  \"Float64Array\",\n  \"Int8Array\",\n  \"Uint8Array\",\n  \"Uint8ClampedArray\",\n  \"Int16Array\",\n  \"Int32Array\",\n  \"Uint16Array\",\n  \"Uint32Array\",\n  \"BigInt64Array\",\n  \"BigUint64Array\",\n  // Keyed collections\n  \"Set\",\n  \"Map\",\n  \"WeakSet\",\n  \"WeakMap\",\n  // Structured data\n  \"ArrayBuffer\",\n  \"SharedArrayBuffer\",\n  \"Atomics\",\n  \"DataView\",\n  \"JSON\",\n  // Control abstraction objects\n  \"Promise\",\n  \"Generator\",\n  \"GeneratorFunction\",\n  \"AsyncFunction\",\n  // Reflection\n  \"Reflect\",\n  \"Proxy\",\n  // Internationalization\n  \"Intl\",\n  // WebAssembly\n  \"WebAssembly\"\n];\n\nconst ERROR_TYPES = [\n  \"Error\",\n  \"EvalError\",\n  \"InternalError\",\n  \"RangeError\",\n  \"ReferenceError\",\n  \"SyntaxError\",\n  \"TypeError\",\n  \"URIError\"\n];\n\nconst BUILT_IN_GLOBALS = [\n  \"setInterval\",\n  \"setTimeout\",\n  \"clearInterval\",\n  \"clearTimeout\",\n\n  \"require\",\n  \"exports\",\n\n  \"eval\",\n  \"isFinite\",\n  \"isNaN\",\n  \"parseFloat\",\n  \"parseInt\",\n  \"decodeURI\",\n  \"decodeURIComponent\",\n  \"encodeURI\",\n  \"encodeURIComponent\",\n  \"escape\",\n  \"unescape\"\n];\n\nconst BUILT_IN_VARIABLES = [\n  \"arguments\",\n  \"this\",\n  \"super\",\n  \"console\",\n  \"window\",\n  \"document\",\n  \"localStorage\",\n  \"sessionStorage\",\n  \"module\",\n  \"global\" // Node.js\n];\n\nconst BUILT_INS = [].concat(\n  BUILT_IN_GLOBALS,\n  TYPES,\n  ERROR_TYPES\n);\n\n/*\nLanguage: JavaScript\nDescription: JavaScript (JS) is a lightweight, interpreted, or just-in-time compiled programming language with first-class functions.\nCategory: common, scripting, web\nWebsite: https://developer.mozilla.org/en-US/docs/Web/JavaScript\n*/\n\n\n/** @type LanguageFn */\nfunction javascript(hljs) {\n  const regex = hljs.regex;\n  /**\n   * Takes a string like \"<Booger\" and checks to see\n   * if we can find a matching \"</Booger\" later in the\n   * content.\n   * @param {RegExpMatchArray} match\n   * @param {{after:number}} param1\n   */\n  const hasClosingTag = (match, { after }) => {\n    const tag = \"</\" + match[0].slice(1);\n    const pos = match.input.indexOf(tag, after);\n    return pos !== -1;\n  };\n\n  const IDENT_RE$1 = IDENT_RE;\n  const FRAGMENT = {\n    begin: '<>',\n    end: '</>'\n  };\n  // to avoid some special cases inside isTrulyOpeningTag\n  const XML_SELF_CLOSING = /<[A-Za-z0-9\\\\._:-]+\\s*\\/>/;\n  const XML_TAG = {\n    begin: /<[A-Za-z0-9\\\\._:-]+/,\n    end: /\\/[A-Za-z0-9\\\\._:-]+>|\\/>/,\n    /**\n     * @param {RegExpMatchArray} match\n     * @param {CallbackResponse} response\n     */\n    isTrulyOpeningTag: (match, response) => {\n      const afterMatchIndex = match[0].length + match.index;\n      const nextChar = match.input[afterMatchIndex];\n      if (\n        // HTML should not include another raw `<` inside a tag\n        // nested type?\n        // `<Array<Array<number>>`, etc.\n        nextChar === \"<\" ||\n        // the , gives away that this is not HTML\n        // `<T, A extends keyof T, V>`\n        nextChar === \",\"\n        ) {\n        response.ignoreMatch();\n        return;\n      }\n\n      // `<something>`\n      // Quite possibly a tag, lets look for a matching closing tag...\n      if (nextChar === \">\") {\n        // if we cannot find a matching closing tag, then we\n        // will ignore it\n        if (!hasClosingTag(match, { after: afterMatchIndex })) {\n          response.ignoreMatch();\n        }\n      }\n\n      // `<blah />` (self-closing)\n      // handled by simpleSelfClosing rule\n\n      let m;\n      const afterMatch = match.input.substring(afterMatchIndex);\n\n      // some more template typing stuff\n      //  <T = any>(key?: string) => Modify<\n      if ((m = afterMatch.match(/^\\s*=/))) {\n        response.ignoreMatch();\n        return;\n      }\n\n      // `<From extends string>`\n      // technically this could be HTML, but it smells like a type\n      // NOTE: This is ugh, but added specifically for https://github.com/highlightjs/highlight.js/issues/3276\n      if ((m = afterMatch.match(/^\\s+extends\\s+/))) {\n        if (m.index === 0) {\n          response.ignoreMatch();\n          // eslint-disable-next-line no-useless-return\n          return;\n        }\n      }\n    }\n  };\n  const KEYWORDS$1 = {\n    $pattern: IDENT_RE,\n    keyword: KEYWORDS,\n    literal: LITERALS,\n    built_in: BUILT_INS,\n    \"variable.language\": BUILT_IN_VARIABLES\n  };\n\n  // https://tc39.es/ecma262/#sec-literals-numeric-literals\n  const decimalDigits = '[0-9](_?[0-9])*';\n  const frac = `\\\\.(${decimalDigits})`;\n  // DecimalIntegerLiteral, including Annex B NonOctalDecimalIntegerLiteral\n  // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n  const decimalInteger = `0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*`;\n  const NUMBER = {\n    className: 'number',\n    variants: [\n      // DecimalLiteral\n      { begin: `(\\\\b(${decimalInteger})((${frac})|\\\\.)?|(${frac}))` +\n        `[eE][+-]?(${decimalDigits})\\\\b` },\n      { begin: `\\\\b(${decimalInteger})\\\\b((${frac})\\\\b|\\\\.)?|(${frac})\\\\b` },\n\n      // DecimalBigIntegerLiteral\n      { begin: `\\\\b(0|[1-9](_?[0-9])*)n\\\\b` },\n\n      // NonDecimalIntegerLiteral\n      { begin: \"\\\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\\\b\" },\n      { begin: \"\\\\b0[bB][0-1](_?[0-1])*n?\\\\b\" },\n      { begin: \"\\\\b0[oO][0-7](_?[0-7])*n?\\\\b\" },\n\n      // LegacyOctalIntegerLiteral (does not include underscore separators)\n      // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n      { begin: \"\\\\b0[0-7]+n?\\\\b\" },\n    ],\n    relevance: 0\n  };\n\n  const SUBST = {\n    className: 'subst',\n    begin: '\\\\$\\\\{',\n    end: '\\\\}',\n    keywords: KEYWORDS$1,\n    contains: [] // defined later\n  };\n  const HTML_TEMPLATE = {\n    begin: 'html`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'xml'\n    }\n  };\n  const CSS_TEMPLATE = {\n    begin: 'css`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'css'\n    }\n  };\n  const GRAPHQL_TEMPLATE = {\n    begin: 'gql`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'graphql'\n    }\n  };\n  const TEMPLATE_STRING = {\n    className: 'string',\n    begin: '`',\n    end: '`',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ]\n  };\n  const JSDOC_COMMENT = hljs.COMMENT(\n    /\\/\\*\\*(?!\\/)/,\n    '\\\\*/',\n    {\n      relevance: 0,\n      contains: [\n        {\n          begin: '(?=@[A-Za-z]+)',\n          relevance: 0,\n          contains: [\n            {\n              className: 'doctag',\n              begin: '@[A-Za-z]+'\n            },\n            {\n              className: 'type',\n              begin: '\\\\{',\n              end: '\\\\}',\n              excludeEnd: true,\n              excludeBegin: true,\n              relevance: 0\n            },\n            {\n              className: 'variable',\n              begin: IDENT_RE$1 + '(?=\\\\s*(-)|$)',\n              endsParent: true,\n              relevance: 0\n            },\n            // eat spaces (not newlines) so we can find\n            // types or variables\n            {\n              begin: /(?=[^\\n])\\s/,\n              relevance: 0\n            }\n          ]\n        }\n      ]\n    }\n  );\n  const COMMENT = {\n    className: \"comment\",\n    variants: [\n      JSDOC_COMMENT,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.C_LINE_COMMENT_MODE\n    ]\n  };\n  const SUBST_INTERNALS = [\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    HTML_TEMPLATE,\n    CSS_TEMPLATE,\n    GRAPHQL_TEMPLATE,\n    TEMPLATE_STRING,\n    // Skip numbers when they are part of a variable name\n    { match: /\\$\\d+/ },\n    NUMBER,\n    // This is intentional:\n    // See https://github.com/highlightjs/highlight.js/issues/3288\n    // hljs.REGEXP_MODE\n  ];\n  SUBST.contains = SUBST_INTERNALS\n    .concat({\n      // we need to pair up {} inside our subst to prevent\n      // it from ending too early by matching another }\n      begin: /\\{/,\n      end: /\\}/,\n      keywords: KEYWORDS$1,\n      contains: [\n        \"self\"\n      ].concat(SUBST_INTERNALS)\n    });\n  const SUBST_AND_COMMENTS = [].concat(COMMENT, SUBST.contains);\n  const PARAMS_CONTAINS = SUBST_AND_COMMENTS.concat([\n    // eat recursive parens in sub expressions\n    {\n      begin: /\\(/,\n      end: /\\)/,\n      keywords: KEYWORDS$1,\n      contains: [\"self\"].concat(SUBST_AND_COMMENTS)\n    }\n  ]);\n  const PARAMS = {\n    className: 'params',\n    begin: /\\(/,\n    end: /\\)/,\n    excludeBegin: true,\n    excludeEnd: true,\n    keywords: KEYWORDS$1,\n    contains: PARAMS_CONTAINS\n  };\n\n  // ES6 classes\n  const CLASS_OR_EXTENDS = {\n    variants: [\n      // class Car extends vehicle\n      {\n        match: [\n          /class/,\n          /\\s+/,\n          IDENT_RE$1,\n          /\\s+/,\n          /extends/,\n          /\\s+/,\n          regex.concat(IDENT_RE$1, \"(\", regex.concat(/\\./, IDENT_RE$1), \")*\")\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.class\",\n          5: \"keyword\",\n          7: \"title.class.inherited\"\n        }\n      },\n      // class Car\n      {\n        match: [\n          /class/,\n          /\\s+/,\n          IDENT_RE$1\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.class\"\n        }\n      },\n\n    ]\n  };\n\n  const CLASS_REFERENCE = {\n    relevance: 0,\n    match:\n    regex.either(\n      // Hard coded exceptions\n      /\\bJSON/,\n      // Float32Array, OutT\n      /\\b[A-Z][a-z]+([A-Z][a-z]*|\\d)*/,\n      // CSSFactory, CSSFactoryT\n      /\\b[A-Z]{2,}([A-Z][a-z]+|\\d)+([A-Z][a-z]*)*/,\n      // FPs, FPsT\n      /\\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\\d)*([A-Z][a-z]*)*/,\n      // P\n      // single letters are not highlighted\n      // BLAH\n      // this will be flagged as a UPPER_CASE_CONSTANT instead\n    ),\n    className: \"title.class\",\n    keywords: {\n      _: [\n        // se we still get relevance credit for JS library classes\n        ...TYPES,\n        ...ERROR_TYPES\n      ]\n    }\n  };\n\n  const USE_STRICT = {\n    label: \"use_strict\",\n    className: 'meta',\n    relevance: 10,\n    begin: /^\\s*['\"]use (strict|asm)['\"]/\n  };\n\n  const FUNCTION_DEFINITION = {\n    variants: [\n      {\n        match: [\n          /function/,\n          /\\s+/,\n          IDENT_RE$1,\n          /(?=\\s*\\()/\n        ]\n      },\n      // anonymous function\n      {\n        match: [\n          /function/,\n          /\\s*(?=\\()/\n        ]\n      }\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    label: \"func.def\",\n    contains: [ PARAMS ],\n    illegal: /%/\n  };\n\n  const UPPER_CASE_CONSTANT = {\n    relevance: 0,\n    match: /\\b[A-Z][A-Z_0-9]+\\b/,\n    className: \"variable.constant\"\n  };\n\n  function noneOf(list) {\n    return regex.concat(\"(?!\", list.join(\"|\"), \")\");\n  }\n\n  const FUNCTION_CALL = {\n    match: regex.concat(\n      /\\b/,\n      noneOf([\n        ...BUILT_IN_GLOBALS,\n        \"super\",\n        \"import\"\n      ]),\n      IDENT_RE$1, regex.lookahead(/\\(/)),\n    className: \"title.function\",\n    relevance: 0\n  };\n\n  const PROPERTY_ACCESS = {\n    begin: regex.concat(/\\./, regex.lookahead(\n      regex.concat(IDENT_RE$1, /(?![0-9A-Za-z$_(])/)\n    )),\n    end: IDENT_RE$1,\n    excludeBegin: true,\n    keywords: \"prototype\",\n    className: \"property\",\n    relevance: 0\n  };\n\n  const GETTER_OR_SETTER = {\n    match: [\n      /get|set/,\n      /\\s+/,\n      IDENT_RE$1,\n      /(?=\\()/\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [\n      { // eat to avoid empty params\n        begin: /\\(\\)/\n      },\n      PARAMS\n    ]\n  };\n\n  const FUNC_LEAD_IN_RE = '(\\\\(' +\n    '[^()]*(\\\\(' +\n    '[^()]*(\\\\(' +\n    '[^()]*' +\n    '\\\\)[^()]*)*' +\n    '\\\\)[^()]*)*' +\n    '\\\\)|' + hljs.UNDERSCORE_IDENT_RE + ')\\\\s*=>';\n\n  const FUNCTION_VARIABLE = {\n    match: [\n      /const|var|let/, /\\s+/,\n      IDENT_RE$1, /\\s*/,\n      /=\\s*/,\n      /(async\\s*)?/, // async is optional\n      regex.lookahead(FUNC_LEAD_IN_RE)\n    ],\n    keywords: \"async\",\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [\n      PARAMS\n    ]\n  };\n\n  return {\n    name: 'JavaScript',\n    aliases: ['js', 'jsx', 'mjs', 'cjs'],\n    keywords: KEYWORDS$1,\n    // this will be extended by TypeScript\n    exports: { PARAMS_CONTAINS, CLASS_REFERENCE },\n    illegal: /#(?![$_A-z])/,\n    contains: [\n      hljs.SHEBANG({\n        label: \"shebang\",\n        binary: \"node\",\n        relevance: 5\n      }),\n      USE_STRICT,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      HTML_TEMPLATE,\n      CSS_TEMPLATE,\n      GRAPHQL_TEMPLATE,\n      TEMPLATE_STRING,\n      COMMENT,\n      // Skip numbers when they are part of a variable name\n      { match: /\\$\\d+/ },\n      NUMBER,\n      CLASS_REFERENCE,\n      {\n        className: 'attr',\n        begin: IDENT_RE$1 + regex.lookahead(':'),\n        relevance: 0\n      },\n      FUNCTION_VARIABLE,\n      { // \"value\" container\n        begin: '(' + hljs.RE_STARTERS_RE + '|\\\\b(case|return|throw)\\\\b)\\\\s*',\n        keywords: 'return throw case',\n        relevance: 0,\n        contains: [\n          COMMENT,\n          hljs.REGEXP_MODE,\n          {\n            className: 'function',\n            // we have to count the parens to make sure we actually have the\n            // correct bounding ( ) before the =>.  There could be any number of\n            // sub-expressions inside also surrounded by parens.\n            begin: FUNC_LEAD_IN_RE,\n            returnBegin: true,\n            end: '\\\\s*=>',\n            contains: [\n              {\n                className: 'params',\n                variants: [\n                  {\n                    begin: hljs.UNDERSCORE_IDENT_RE,\n                    relevance: 0\n                  },\n                  {\n                    className: null,\n                    begin: /\\(\\s*\\)/,\n                    skip: true\n                  },\n                  {\n                    begin: /\\(/,\n                    end: /\\)/,\n                    excludeBegin: true,\n                    excludeEnd: true,\n                    keywords: KEYWORDS$1,\n                    contains: PARAMS_CONTAINS\n                  }\n                ]\n              }\n            ]\n          },\n          { // could be a comma delimited list of params to a function call\n            begin: /,/,\n            relevance: 0\n          },\n          {\n            match: /\\s+/,\n            relevance: 0\n          },\n          { // JSX\n            variants: [\n              { begin: FRAGMENT.begin, end: FRAGMENT.end },\n              { match: XML_SELF_CLOSING },\n              {\n                begin: XML_TAG.begin,\n                // we carefully check the opening tag to see if it truly\n                // is a tag and not a false positive\n                'on:begin': XML_TAG.isTrulyOpeningTag,\n                end: XML_TAG.end\n              }\n            ],\n            subLanguage: 'xml',\n            contains: [\n              {\n                begin: XML_TAG.begin,\n                end: XML_TAG.end,\n                skip: true,\n                contains: ['self']\n              }\n            ]\n          }\n        ],\n      },\n      FUNCTION_DEFINITION,\n      {\n        // prevent this from getting swallowed up by function\n        // since they appear \"function like\"\n        beginKeywords: \"while if switch catch for\"\n      },\n      {\n        // we have to count the parens to make sure we actually have the correct\n        // bounding ( ).  There could be any number of sub-expressions inside\n        // also surrounded by parens.\n        begin: '\\\\b(?!function)' + hljs.UNDERSCORE_IDENT_RE +\n          '\\\\(' + // first parens\n          '[^()]*(\\\\(' +\n            '[^()]*(\\\\(' +\n              '[^()]*' +\n            '\\\\)[^()]*)*' +\n          '\\\\)[^()]*)*' +\n          '\\\\)\\\\s*\\\\{', // end parens\n        returnBegin:true,\n        label: \"func.def\",\n        contains: [\n          PARAMS,\n          hljs.inherit(hljs.TITLE_MODE, { begin: IDENT_RE$1, className: \"title.function\" })\n        ]\n      },\n      // catch ... so it won't trigger the property rule below\n      {\n        match: /\\.\\.\\./,\n        relevance: 0\n      },\n      PROPERTY_ACCESS,\n      // hack: prevents detection of keywords in some circumstances\n      // .keyword()\n      // $keyword = x\n      {\n        match: '\\\\$' + IDENT_RE$1,\n        relevance: 0\n      },\n      {\n        match: [ /\\bconstructor(?=\\s*\\()/ ],\n        className: { 1: \"title.function\" },\n        contains: [ PARAMS ]\n      },\n      FUNCTION_CALL,\n      UPPER_CASE_CONSTANT,\n      CLASS_OR_EXTENDS,\n      GETTER_OR_SETTER,\n      {\n        match: /\\$[(.]/ // relevance booster for a pattern common to JS libs: `$(something)` and `$.something`\n      }\n    ]\n  };\n}\n\nexport { javascript as default };\n", "import highlightjs from 'highlight.js/lib/core';\nimport 'highlight.js/styles/github.css';\n\nimport bash from 'highlight.js/lib/languages/bash';\nimport csharp from 'highlight.js/lib/languages/csharp';\nimport css from 'highlight.js/lib/languages/css';\nimport fsharp from 'highlight.js/lib/languages/fsharp';\nimport http from 'highlight.js/lib/languages/http';\nimport javascript from 'highlight.js/lib/languages/javascript';\nimport json from 'highlight.js/lib/languages/json';\nimport julia from 'highlight.js/lib/languages/julia';\nimport markdown from 'highlight.js/lib/languages/markdown';\nimport matlab from 'highlight.js/lib/languages/matlab';\nimport plaintext from 'highlight.js/lib/languages/plaintext';\nimport python from 'highlight.js/lib/languages/python';\nimport r from 'highlight.js/lib/languages/r';\nimport ruby from 'highlight.js/lib/languages/ruby';\nimport shell from 'highlight.js/lib/languages/shell';\nimport sql from 'highlight.js/lib/languages/sql';\nimport xml from 'highlight.js/lib/languages/xml';\nimport yaml from 'highlight.js/lib/languages/yaml';\n\nhighlightjs.registerLanguage('bash', bash);\nhighlightjs.registerLanguage('csharp', csharp);\nhighlightjs.registerLanguage('css', css);\nhighlightjs.registerLanguage('fsharp', fsharp);\nhighlightjs.registerLanguage('http', http);\nhighlightjs.registerLanguage('javascript', javascript);\nhighlightjs.registerLanguage('json', json);\nhighlightjs.registerLanguage('julia', julia);\nhighlightjs.registerLanguage('markdown', markdown);\nhighlightjs.registerLanguage('matlab', matlab);\nhighlightjs.registerLanguage('plaintext', plaintext);\nhighlightjs.registerLanguage('python', python);\nhighlightjs.registerLanguage('r', r);\nhighlightjs.registerLanguage('ruby', ruby);\nhighlightjs.registerLanguage('shell', shell);\nhighlightjs.registerLanguage('sql', sql);\nhighlightjs.registerLanguage('xml', xml);\nhighlightjs.registerLanguage('yaml', yaml);\n\nexport default highlightjs;\n", "/*\nLanguage: Bash\nAuthor: vah <vah<PERSON><EMAIL>>\nContributrors: <PERSON> <<EMAIL>>\nWebsite: https://www.gnu.org/software/bash/\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction bash(hljs) {\n  const regex = hljs.regex;\n  const VAR = {};\n  const BRACED_VAR = {\n    begin: /\\$\\{/,\n    end: /\\}/,\n    contains: [\n      \"self\",\n      {\n        begin: /:-/,\n        contains: [ VAR ]\n      } // default values\n    ]\n  };\n  Object.assign(VAR, {\n    className: 'variable',\n    variants: [\n      { begin: regex.concat(/\\$[\\w\\d#@][\\w\\d_]*/,\n        // negative look-ahead tries to avoid matching patterns that are not\n        // Perl at all like $ident$, @ident@, etc.\n        `(?![\\\\w\\\\d])(?![$])`) },\n      BRACED_VAR\n    ]\n  });\n\n  const SUBST = {\n    className: 'subst',\n    begin: /\\$\\(/,\n    end: /\\)/,\n    contains: [ hljs.BACKSLASH_ESCAPE ]\n  };\n  const HERE_DOC = {\n    begin: /<<-?\\s*(?=\\w+)/,\n    starts: { contains: [\n      hljs.END_SAME_AS_BEGIN({\n        begin: /(\\w+)/,\n        end: /(\\w+)/,\n        className: 'string'\n      })\n    ] }\n  };\n  const QUOTE_STRING = {\n    className: 'string',\n    begin: /\"/,\n    end: /\"/,\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      VAR,\n      SUBST\n    ]\n  };\n  SUBST.contains.push(QUOTE_STRING);\n  const ESCAPED_QUOTE = {\n    match: /\\\\\"/\n  };\n  const APOS_STRING = {\n    className: 'string',\n    begin: /'/,\n    end: /'/\n  };\n  const ESCAPED_APOS = {\n    match: /\\\\'/\n  };\n  const ARITHMETIC = {\n    begin: /\\$?\\(\\(/,\n    end: /\\)\\)/,\n    contains: [\n      {\n        begin: /\\d+#[0-9a-f]+/,\n        className: \"number\"\n      },\n      hljs.NUMBER_MODE,\n      VAR\n    ]\n  };\n  const SH_LIKE_SHELLS = [\n    \"fish\",\n    \"bash\",\n    \"zsh\",\n    \"sh\",\n    \"csh\",\n    \"ksh\",\n    \"tcsh\",\n    \"dash\",\n    \"scsh\",\n  ];\n  const KNOWN_SHEBANG = hljs.SHEBANG({\n    binary: `(${SH_LIKE_SHELLS.join(\"|\")})`,\n    relevance: 10\n  });\n  const FUNCTION = {\n    className: 'function',\n    begin: /\\w[\\w\\d_]*\\s*\\(\\s*\\)\\s*\\{/,\n    returnBegin: true,\n    contains: [ hljs.inherit(hljs.TITLE_MODE, { begin: /\\w[\\w\\d_]*/ }) ],\n    relevance: 0\n  };\n\n  const KEYWORDS = [\n    \"if\",\n    \"then\",\n    \"else\",\n    \"elif\",\n    \"fi\",\n    \"for\",\n    \"while\",\n    \"until\",\n    \"in\",\n    \"do\",\n    \"done\",\n    \"case\",\n    \"esac\",\n    \"function\",\n    \"select\"\n  ];\n\n  const LITERALS = [\n    \"true\",\n    \"false\"\n  ];\n\n  // to consume paths to prevent keyword matches inside them\n  const PATH_MODE = { match: /(\\/[a-z._-]+)+/ };\n\n  // http://www.gnu.org/software/bash/manual/html_node/Shell-Builtin-Commands.html\n  const SHELL_BUILT_INS = [\n    \"break\",\n    \"cd\",\n    \"continue\",\n    \"eval\",\n    \"exec\",\n    \"exit\",\n    \"export\",\n    \"getopts\",\n    \"hash\",\n    \"pwd\",\n    \"readonly\",\n    \"return\",\n    \"shift\",\n    \"test\",\n    \"times\",\n    \"trap\",\n    \"umask\",\n    \"unset\"\n  ];\n\n  const BASH_BUILT_INS = [\n    \"alias\",\n    \"bind\",\n    \"builtin\",\n    \"caller\",\n    \"command\",\n    \"declare\",\n    \"echo\",\n    \"enable\",\n    \"help\",\n    \"let\",\n    \"local\",\n    \"logout\",\n    \"mapfile\",\n    \"printf\",\n    \"read\",\n    \"readarray\",\n    \"source\",\n    \"type\",\n    \"typeset\",\n    \"ulimit\",\n    \"unalias\"\n  ];\n\n  const ZSH_BUILT_INS = [\n    \"autoload\",\n    \"bg\",\n    \"bindkey\",\n    \"bye\",\n    \"cap\",\n    \"chdir\",\n    \"clone\",\n    \"comparguments\",\n    \"compcall\",\n    \"compctl\",\n    \"compdescribe\",\n    \"compfiles\",\n    \"compgroups\",\n    \"compquote\",\n    \"comptags\",\n    \"comptry\",\n    \"compvalues\",\n    \"dirs\",\n    \"disable\",\n    \"disown\",\n    \"echotc\",\n    \"echoti\",\n    \"emulate\",\n    \"fc\",\n    \"fg\",\n    \"float\",\n    \"functions\",\n    \"getcap\",\n    \"getln\",\n    \"history\",\n    \"integer\",\n    \"jobs\",\n    \"kill\",\n    \"limit\",\n    \"log\",\n    \"noglob\",\n    \"popd\",\n    \"print\",\n    \"pushd\",\n    \"pushln\",\n    \"rehash\",\n    \"sched\",\n    \"setcap\",\n    \"setopt\",\n    \"stat\",\n    \"suspend\",\n    \"ttyctl\",\n    \"unfunction\",\n    \"unhash\",\n    \"unlimit\",\n    \"unsetopt\",\n    \"vared\",\n    \"wait\",\n    \"whence\",\n    \"where\",\n    \"which\",\n    \"zcompile\",\n    \"zformat\",\n    \"zftp\",\n    \"zle\",\n    \"zmodload\",\n    \"zparseopts\",\n    \"zprof\",\n    \"zpty\",\n    \"zregexparse\",\n    \"zsocket\",\n    \"zstyle\",\n    \"ztcp\"\n  ];\n\n  const GNU_CORE_UTILS = [\n    \"chcon\",\n    \"chgrp\",\n    \"chown\",\n    \"chmod\",\n    \"cp\",\n    \"dd\",\n    \"df\",\n    \"dir\",\n    \"dircolors\",\n    \"ln\",\n    \"ls\",\n    \"mkdir\",\n    \"mkfifo\",\n    \"mknod\",\n    \"mktemp\",\n    \"mv\",\n    \"realpath\",\n    \"rm\",\n    \"rmdir\",\n    \"shred\",\n    \"sync\",\n    \"touch\",\n    \"truncate\",\n    \"vdir\",\n    \"b2sum\",\n    \"base32\",\n    \"base64\",\n    \"cat\",\n    \"cksum\",\n    \"comm\",\n    \"csplit\",\n    \"cut\",\n    \"expand\",\n    \"fmt\",\n    \"fold\",\n    \"head\",\n    \"join\",\n    \"md5sum\",\n    \"nl\",\n    \"numfmt\",\n    \"od\",\n    \"paste\",\n    \"ptx\",\n    \"pr\",\n    \"sha1sum\",\n    \"sha224sum\",\n    \"sha256sum\",\n    \"sha384sum\",\n    \"sha512sum\",\n    \"shuf\",\n    \"sort\",\n    \"split\",\n    \"sum\",\n    \"tac\",\n    \"tail\",\n    \"tr\",\n    \"tsort\",\n    \"unexpand\",\n    \"uniq\",\n    \"wc\",\n    \"arch\",\n    \"basename\",\n    \"chroot\",\n    \"date\",\n    \"dirname\",\n    \"du\",\n    \"echo\",\n    \"env\",\n    \"expr\",\n    \"factor\",\n    // \"false\", // keyword literal already\n    \"groups\",\n    \"hostid\",\n    \"id\",\n    \"link\",\n    \"logname\",\n    \"nice\",\n    \"nohup\",\n    \"nproc\",\n    \"pathchk\",\n    \"pinky\",\n    \"printenv\",\n    \"printf\",\n    \"pwd\",\n    \"readlink\",\n    \"runcon\",\n    \"seq\",\n    \"sleep\",\n    \"stat\",\n    \"stdbuf\",\n    \"stty\",\n    \"tee\",\n    \"test\",\n    \"timeout\",\n    // \"true\", // keyword literal already\n    \"tty\",\n    \"uname\",\n    \"unlink\",\n    \"uptime\",\n    \"users\",\n    \"who\",\n    \"whoami\",\n    \"yes\"\n  ];\n\n  return {\n    name: 'Bash',\n    aliases: [ 'sh' ],\n    keywords: {\n      $pattern: /\\b[a-z][a-z0-9._-]+\\b/,\n      keyword: KEYWORDS,\n      literal: LITERALS,\n      built_in: [\n        ...SHELL_BUILT_INS,\n        ...BASH_BUILT_INS,\n        // Shell modifiers\n        \"set\",\n        \"shopt\",\n        ...ZSH_BUILT_INS,\n        ...GNU_CORE_UTILS\n      ]\n    },\n    contains: [\n      KNOWN_SHEBANG, // to catch known shells and boost relevancy\n      hljs.SHEBANG(), // to catch unknown shells but still highlight the shebang\n      FUNCTION,\n      ARITHMETIC,\n      hljs.HASH_COMMENT_MODE,\n      HERE_DOC,\n      PATH_MODE,\n      QUOTE_STRING,\n      ESCAPED_QUOTE,\n      APOS_STRING,\n      ESCAPED_APOS,\n      VAR\n    ]\n  };\n}\n\nexport { bash as default };\n", "/*\nLanguage: C#\nAuthor: <PERSON> <<EMAIL>>\nContributor: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://docs.microsoft.com/dotnet/csharp/\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction csharp(hljs) {\n  const BUILT_IN_KEYWORDS = [\n    'bool',\n    'byte',\n    'char',\n    'decimal',\n    'delegate',\n    'double',\n    'dynamic',\n    'enum',\n    'float',\n    'int',\n    'long',\n    'nint',\n    'nuint',\n    'object',\n    'sbyte',\n    'short',\n    'string',\n    'ulong',\n    'uint',\n    'ushort'\n  ];\n  const FUNCTION_MODIFIERS = [\n    'public',\n    'private',\n    'protected',\n    'static',\n    'internal',\n    'protected',\n    'abstract',\n    'async',\n    'extern',\n    'override',\n    'unsafe',\n    'virtual',\n    'new',\n    'sealed',\n    'partial'\n  ];\n  const LITERAL_KEYWORDS = [\n    'default',\n    'false',\n    'null',\n    'true'\n  ];\n  const NORMAL_KEYWORDS = [\n    'abstract',\n    'as',\n    'base',\n    'break',\n    'case',\n    'catch',\n    'class',\n    'const',\n    'continue',\n    'do',\n    'else',\n    'event',\n    'explicit',\n    'extern',\n    'finally',\n    'fixed',\n    'for',\n    'foreach',\n    'goto',\n    'if',\n    'implicit',\n    'in',\n    'interface',\n    'internal',\n    'is',\n    'lock',\n    'namespace',\n    'new',\n    'operator',\n    'out',\n    'override',\n    'params',\n    'private',\n    'protected',\n    'public',\n    'readonly',\n    'record',\n    'ref',\n    'return',\n    'scoped',\n    'sealed',\n    'sizeof',\n    'stackalloc',\n    'static',\n    'struct',\n    'switch',\n    'this',\n    'throw',\n    'try',\n    'typeof',\n    'unchecked',\n    'unsafe',\n    'using',\n    'virtual',\n    'void',\n    'volatile',\n    'while'\n  ];\n  const CONTEXTUAL_KEYWORDS = [\n    'add',\n    'alias',\n    'and',\n    'ascending',\n    'async',\n    'await',\n    'by',\n    'descending',\n    'equals',\n    'from',\n    'get',\n    'global',\n    'group',\n    'init',\n    'into',\n    'join',\n    'let',\n    'nameof',\n    'not',\n    'notnull',\n    'on',\n    'or',\n    'orderby',\n    'partial',\n    'remove',\n    'select',\n    'set',\n    'unmanaged',\n    'value|0',\n    'var',\n    'when',\n    'where',\n    'with',\n    'yield'\n  ];\n\n  const KEYWORDS = {\n    keyword: NORMAL_KEYWORDS.concat(CONTEXTUAL_KEYWORDS),\n    built_in: BUILT_IN_KEYWORDS,\n    literal: LITERAL_KEYWORDS\n  };\n  const TITLE_MODE = hljs.inherit(hljs.TITLE_MODE, { begin: '[a-zA-Z](\\\\.?\\\\w)*' });\n  const NUMBERS = {\n    className: 'number',\n    variants: [\n      { begin: '\\\\b(0b[01\\']+)' },\n      { begin: '(-?)\\\\b([\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)(u|U|l|L|ul|UL|f|F|b|B)' },\n      { begin: '(-?)(\\\\b0[xX][a-fA-F0-9\\']+|(\\\\b[\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)([eE][-+]?[\\\\d\\']+)?)' }\n    ],\n    relevance: 0\n  };\n  const VERBATIM_STRING = {\n    className: 'string',\n    begin: '@\"',\n    end: '\"',\n    contains: [ { begin: '\"\"' } ]\n  };\n  const VERBATIM_STRING_NO_LF = hljs.inherit(VERBATIM_STRING, { illegal: /\\n/ });\n  const SUBST = {\n    className: 'subst',\n    begin: /\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS\n  };\n  const SUBST_NO_LF = hljs.inherit(SUBST, { illegal: /\\n/ });\n  const INTERPOLATED_STRING = {\n    className: 'string',\n    begin: /\\$\"/,\n    end: '\"',\n    illegal: /\\n/,\n    contains: [\n      { begin: /\\{\\{/ },\n      { begin: /\\}\\}/ },\n      hljs.BACKSLASH_ESCAPE,\n      SUBST_NO_LF\n    ]\n  };\n  const INTERPOLATED_VERBATIM_STRING = {\n    className: 'string',\n    begin: /\\$@\"/,\n    end: '\"',\n    contains: [\n      { begin: /\\{\\{/ },\n      { begin: /\\}\\}/ },\n      { begin: '\"\"' },\n      SUBST\n    ]\n  };\n  const INTERPOLATED_VERBATIM_STRING_NO_LF = hljs.inherit(INTERPOLATED_VERBATIM_STRING, {\n    illegal: /\\n/,\n    contains: [\n      { begin: /\\{\\{/ },\n      { begin: /\\}\\}/ },\n      { begin: '\"\"' },\n      SUBST_NO_LF\n    ]\n  });\n  SUBST.contains = [\n    INTERPOLATED_VERBATIM_STRING,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING,\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    NUMBERS,\n    hljs.C_BLOCK_COMMENT_MODE\n  ];\n  SUBST_NO_LF.contains = [\n    INTERPOLATED_VERBATIM_STRING_NO_LF,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING_NO_LF,\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    NUMBERS,\n    hljs.inherit(hljs.C_BLOCK_COMMENT_MODE, { illegal: /\\n/ })\n  ];\n  const STRING = { variants: [\n    INTERPOLATED_VERBATIM_STRING,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING,\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE\n  ] };\n\n  const GENERIC_MODIFIER = {\n    begin: \"<\",\n    end: \">\",\n    contains: [\n      { beginKeywords: \"in out\" },\n      TITLE_MODE\n    ]\n  };\n  const TYPE_IDENT_RE = hljs.IDENT_RE + '(<' + hljs.IDENT_RE + '(\\\\s*,\\\\s*' + hljs.IDENT_RE + ')*>)?(\\\\[\\\\])?';\n  const AT_IDENTIFIER = {\n    // prevents expressions like `@class` from incorrect flagging\n    // `class` as a keyword\n    begin: \"@\" + hljs.IDENT_RE,\n    relevance: 0\n  };\n\n  return {\n    name: 'C#',\n    aliases: [\n      'cs',\n      'c#'\n    ],\n    keywords: KEYWORDS,\n    illegal: /::/,\n    contains: [\n      hljs.COMMENT(\n        '///',\n        '$',\n        {\n          returnBegin: true,\n          contains: [\n            {\n              className: 'doctag',\n              variants: [\n                {\n                  begin: '///',\n                  relevance: 0\n                },\n                { begin: '<!--|-->' },\n                {\n                  begin: '</?',\n                  end: '>'\n                }\n              ]\n            }\n          ]\n        }\n      ),\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'meta',\n        begin: '#',\n        end: '$',\n        keywords: { keyword: 'if else elif endif define undef warning error line region endregion pragma checksum' }\n      },\n      STRING,\n      NUMBERS,\n      {\n        beginKeywords: 'class interface',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:,]/,\n        contains: [\n          { beginKeywords: \"where class\" },\n          TITLE_MODE,\n          GENERIC_MODIFIER,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        beginKeywords: 'namespace',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:]/,\n        contains: [\n          TITLE_MODE,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        beginKeywords: 'record',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:]/,\n        contains: [\n          TITLE_MODE,\n          GENERIC_MODIFIER,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        // [Attributes(\"\")]\n        className: 'meta',\n        begin: '^\\\\s*\\\\[(?=[\\\\w])',\n        excludeBegin: true,\n        end: '\\\\]',\n        excludeEnd: true,\n        contains: [\n          {\n            className: 'string',\n            begin: /\"/,\n            end: /\"/\n          }\n        ]\n      },\n      {\n        // Expression keywords prevent 'keyword Name(...)' from being\n        // recognized as a function definition\n        beginKeywords: 'new return throw await else',\n        relevance: 0\n      },\n      {\n        className: 'function',\n        begin: '(' + TYPE_IDENT_RE + '\\\\s+)+' + hljs.IDENT_RE + '\\\\s*(<[^=]+>\\\\s*)?\\\\(',\n        returnBegin: true,\n        end: /\\s*[{;=]/,\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        contains: [\n          // prevents these from being highlighted `title`\n          {\n            beginKeywords: FUNCTION_MODIFIERS.join(\" \"),\n            relevance: 0\n          },\n          {\n            begin: hljs.IDENT_RE + '\\\\s*(<[^=]+>\\\\s*)?\\\\(',\n            returnBegin: true,\n            contains: [\n              hljs.TITLE_MODE,\n              GENERIC_MODIFIER\n            ],\n            relevance: 0\n          },\n          { match: /\\(\\)/ },\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            excludeBegin: true,\n            excludeEnd: true,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              STRING,\n              NUMBERS,\n              hljs.C_BLOCK_COMMENT_MODE\n            ]\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      AT_IDENTIFIER\n    ]\n  };\n}\n\nexport { csharp as default };\n", "/*\nLanguage: HTTP\nDescription: HTTP request and response headers with automatic body highlighting\nAuthor: <PERSON> <<EMAIL>>\nCategory: protocols, web\nWebsite: https://developer.mozilla.org/en-US/docs/Web/HTTP/Overview\n*/\n\nfunction http(hljs) {\n  const regex = hljs.regex;\n  const VERSION = 'HTTP/([32]|1\\\\.[01])';\n  const HEADER_NAME = /[A-Za-z][A-Za-z0-9-]*/;\n  const HEADER = {\n    className: 'attribute',\n    begin: regex.concat('^', HEADER_NAME, '(?=\\\\:\\\\s)'),\n    starts: { contains: [\n      {\n        className: \"punctuation\",\n        begin: /: /,\n        relevance: 0,\n        starts: {\n          end: '$',\n          relevance: 0\n        }\n      }\n    ] }\n  };\n  const HEADERS_AND_BODY = [\n    HEADER,\n    {\n      begin: '\\\\n\\\\n',\n      starts: {\n        subLanguage: [],\n        endsWithParent: true\n      }\n    }\n  ];\n\n  return {\n    name: 'HTTP',\n    aliases: [ 'https' ],\n    illegal: /\\S/,\n    contains: [\n      // response\n      {\n        begin: '^(?=' + VERSION + \" \\\\d{3})\",\n        end: /$/,\n        contains: [\n          {\n            className: \"meta\",\n            begin: VERSION\n          },\n          {\n            className: 'number',\n            begin: '\\\\b\\\\d{3}\\\\b'\n          }\n        ],\n        starts: {\n          end: /\\b\\B/,\n          illegal: /\\S/,\n          contains: HEADERS_AND_BODY\n        }\n      },\n      // request\n      {\n        begin: '(?=^[A-Z]+ (.*?) ' + VERSION + '$)',\n        end: /$/,\n        contains: [\n          {\n            className: 'string',\n            begin: ' ',\n            end: ' ',\n            excludeBegin: true,\n            excludeEnd: true\n          },\n          {\n            className: \"meta\",\n            begin: VERSION\n          },\n          {\n            className: 'keyword',\n            begin: '[A-Z]+'\n          }\n        ],\n        starts: {\n          end: /\\b\\B/,\n          illegal: /\\S/,\n          contains: HEADERS_AND_BODY\n        }\n      },\n      // to allow headers to work even without a preamble\n      hljs.inherit(HEADER, { relevance: 0 })\n    ]\n  };\n}\n\nexport { http as default };\n", "/*\nLanguage: JSON\nDescription: J<PERSON><PERSON> (JavaScript Object Notation) is a lightweight data-interchange format.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://www.json.org\nCategory: common, protocols, web\n*/\n\nfunction json(hljs) {\n  const ATTRIBUTE = {\n    className: 'attr',\n    begin: /\"(\\\\.|[^\\\\\"\\r\\n])*\"(?=\\s*:)/,\n    relevance: 1.01\n  };\n  const PUNCTUATION = {\n    match: /[{}[\\],:]/,\n    className: \"punctuation\",\n    relevance: 0\n  };\n  const LITERALS = [\n    \"true\",\n    \"false\",\n    \"null\"\n  ];\n  // NOTE: normally we would rely on `keywords` for this but using a mode here allows us\n  // - to use the very tight `illegal: \\S` rule later to flag any other character\n  // - as illegal indicating that despite looking like JSON we do not truly have\n  // - JSON and thus improve false-positively greatly since J<PERSON><PERSON> will try and claim\n  // - all sorts of JSON looking stuff\n  const LITERALS_MODE = {\n    scope: \"literal\",\n    beginKeywords: LITERALS.join(\" \"),\n  };\n\n  return {\n    name: '<PERSON><PERSON><PERSON>',\n    keywords:{\n      literal: LITERALS,\n    },\n    contains: [\n      ATTRIBUTE,\n      PUNCTUATION,\n      hljs.QUOTE_STRING_MODE,\n      LITERALS_MODE,\n      hljs.C_NUMBER_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE\n    ],\n    illegal: '\\\\S'\n  };\n}\n\nexport { json as default };\n", "/*\nLanguage: Julia\nDescription: Julia is a high-level, high-performance, dynamic programming language.\nAuthor: <PERSON><PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>, <PERSON><PERSON> <ekrefred<PERSON>@gmail.com>\nWebsite: https://julialang.org\n*/\n\nfunction julia(hljs) {\n  // Since there are numerous special names in <PERSON>, it is too much trouble\n  // to maintain them by hand. Hence these names (i.e. keywords, literals and\n  // built-ins) are automatically generated from Julia 1.5.2 itself through\n  // the following scripts for each.\n\n  // ref: https://docs.julialang.org/en/v1/manual/variables/#Allowed-Variable-Names\n  const VARIABLE_NAME_RE = '[A-Za-z_\\\\u00A1-\\\\uFFFF][A-Za-z_0-9\\\\u00A1-\\\\uFFFF]*';\n\n  // # keyword generator, multi-word keywords handled manually below (Julia 1.5.2)\n  // import REPL.REPLCompletions\n  // res = String[\"in\", \"isa\", \"where\"]\n  // for kw in collect(x.keyword for x in REPLCompletions.complete_keyword(\"\"))\n  //     if !(contains(kw, \" \") || kw == \"struct\")\n  //         push!(res, kw)\n  //     end\n  // end\n  // sort!(unique!(res))\n  // foreach(x -> println(\"\\'\", x, \"\\',\"), res)\n  const KEYWORD_LIST = [\n    'baremodule',\n    'begin',\n    'break',\n    'catch',\n    'ccall',\n    'const',\n    'continue',\n    'do',\n    'else',\n    'elseif',\n    'end',\n    'export',\n    'false',\n    'finally',\n    'for',\n    'function',\n    'global',\n    'if',\n    'import',\n    'in',\n    'isa',\n    'let',\n    'local',\n    'macro',\n    'module',\n    'quote',\n    'return',\n    'true',\n    'try',\n    'using',\n    'where',\n    'while',\n  ];\n\n  // # literal generator (Julia 1.5.2)\n  // import REPL.REPLCompletions\n  // res = String[\"true\", \"false\"]\n  // for compl in filter!(x -> isa(x, REPLCompletions.ModuleCompletion) && (x.parent === Base || x.parent === Core),\n  //                     REPLCompletions.completions(\"\", 0)[1])\n  //     try\n  //         v = eval(Symbol(compl.mod))\n  //         if !(v isa Function || v isa Type || v isa TypeVar || v isa Module || v isa Colon)\n  //             push!(res, compl.mod)\n  //         end\n  //     catch e\n  //     end\n  // end\n  // sort!(unique!(res))\n  // foreach(x -> println(\"\\'\", x, \"\\',\"), res)\n  const LITERAL_LIST = [\n    'ARGS',\n    'C_NULL',\n    'DEPOT_PATH',\n    'ENDIAN_BOM',\n    'ENV',\n    'Inf',\n    'Inf16',\n    'Inf32',\n    'Inf64',\n    'InsertionSort',\n    'LOAD_PATH',\n    'MergeSort',\n    'NaN',\n    'NaN16',\n    'NaN32',\n    'NaN64',\n    'PROGRAM_FILE',\n    'QuickSort',\n    'RoundDown',\n    'RoundFromZero',\n    'RoundNearest',\n    'RoundNearestTiesAway',\n    'RoundNearestTiesUp',\n    'RoundToZero',\n    'RoundUp',\n    'VERSION|0',\n    'devnull',\n    'false',\n    'im',\n    'missing',\n    'nothing',\n    'pi',\n    'stderr',\n    'stdin',\n    'stdout',\n    'true',\n    'undef',\n    'π',\n    'ℯ',\n  ];\n\n  // # built_in generator (Julia 1.5.2)\n  // import REPL.REPLCompletions\n  // res = String[]\n  // for compl in filter!(x -> isa(x, REPLCompletions.ModuleCompletion) && (x.parent === Base || x.parent === Core),\n  //                     REPLCompletions.completions(\"\", 0)[1])\n  //     try\n  //         v = eval(Symbol(compl.mod))\n  //         if (v isa Type || v isa TypeVar) && (compl.mod != \"=>\")\n  //             push!(res, compl.mod)\n  //         end\n  //     catch e\n  //     end\n  // end\n  // sort!(unique!(res))\n  // foreach(x -> println(\"\\'\", x, \"\\',\"), res)\n  const BUILT_IN_LIST = [\n    'AbstractArray',\n    'AbstractChannel',\n    'AbstractChar',\n    'AbstractDict',\n    'AbstractDisplay',\n    'AbstractFloat',\n    'AbstractIrrational',\n    'AbstractMatrix',\n    'AbstractRange',\n    'AbstractSet',\n    'AbstractString',\n    'AbstractUnitRange',\n    'AbstractVecOrMat',\n    'AbstractVector',\n    'Any',\n    'ArgumentError',\n    'Array',\n    'AssertionError',\n    'BigFloat',\n    'BigInt',\n    'BitArray',\n    'BitMatrix',\n    'BitSet',\n    'BitVector',\n    'Bool',\n    'BoundsError',\n    'CapturedException',\n    'CartesianIndex',\n    'CartesianIndices',\n    'Cchar',\n    'Cdouble',\n    'Cfloat',\n    'Channel',\n    'Char',\n    'Cint',\n    'Cintmax_t',\n    'Clong',\n    'Clonglong',\n    'Cmd',\n    'Colon',\n    'Complex',\n    'ComplexF16',\n    'ComplexF32',\n    'ComplexF64',\n    'CompositeException',\n    'Condition',\n    'Cptrdiff_t',\n    'Cshort',\n    'Csize_t',\n    'Cssize_t',\n    'Cstring',\n    'Cuchar',\n    'Cuint',\n    'Cuintmax_t',\n    'Culong',\n    'Culonglong',\n    'Cushort',\n    'Cvoid',\n    'Cwchar_t',\n    'Cwstring',\n    'DataType',\n    'DenseArray',\n    'DenseMatrix',\n    'DenseVecOrMat',\n    'DenseVector',\n    'Dict',\n    'DimensionMismatch',\n    'Dims',\n    'DivideError',\n    'DomainError',\n    'EOFError',\n    'Enum',\n    'ErrorException',\n    'Exception',\n    'ExponentialBackOff',\n    'Expr',\n    'Float16',\n    'Float32',\n    'Float64',\n    'Function',\n    'GlobalRef',\n    'HTML',\n    'IO',\n    'IOBuffer',\n    'IOContext',\n    'IOStream',\n    'IdDict',\n    'IndexCartesian',\n    'IndexLinear',\n    'IndexStyle',\n    'InexactError',\n    'InitError',\n    'Int',\n    'Int128',\n    'Int16',\n    'Int32',\n    'Int64',\n    'Int8',\n    'Integer',\n    'InterruptException',\n    'InvalidStateException',\n    'Irrational',\n    'KeyError',\n    'LinRange',\n    'LineNumberNode',\n    'LinearIndices',\n    'LoadError',\n    'MIME',\n    'Matrix',\n    'Method',\n    'MethodError',\n    'Missing',\n    'MissingException',\n    'Module',\n    'NTuple',\n    'NamedTuple',\n    'Nothing',\n    'Number',\n    'OrdinalRange',\n    'OutOfMemoryError',\n    'OverflowError',\n    'Pair',\n    'PartialQuickSort',\n    'PermutedDimsArray',\n    'Pipe',\n    'ProcessFailedException',\n    'Ptr',\n    'QuoteNode',\n    'Rational',\n    'RawFD',\n    'ReadOnlyMemoryError',\n    'Real',\n    'ReentrantLock',\n    'Ref',\n    'Regex',\n    'RegexMatch',\n    'RoundingMode',\n    'SegmentationFault',\n    'Set',\n    'Signed',\n    'Some',\n    'StackOverflowError',\n    'StepRange',\n    'StepRangeLen',\n    'StridedArray',\n    'StridedMatrix',\n    'StridedVecOrMat',\n    'StridedVector',\n    'String',\n    'StringIndexError',\n    'SubArray',\n    'SubString',\n    'SubstitutionString',\n    'Symbol',\n    'SystemError',\n    'Task',\n    'TaskFailedException',\n    'Text',\n    'TextDisplay',\n    'Timer',\n    'Tuple',\n    'Type',\n    'TypeError',\n    'TypeVar',\n    'UInt',\n    'UInt128',\n    'UInt16',\n    'UInt32',\n    'UInt64',\n    'UInt8',\n    'UndefInitializer',\n    'UndefKeywordError',\n    'UndefRefError',\n    'UndefVarError',\n    'Union',\n    'UnionAll',\n    'UnitRange',\n    'Unsigned',\n    'Val',\n    'Vararg',\n    'VecElement',\n    'VecOrMat',\n    'Vector',\n    'VersionNumber',\n    'WeakKeyDict',\n    'WeakRef',\n  ];\n\n  const KEYWORDS = {\n    $pattern: VARIABLE_NAME_RE,\n    keyword: KEYWORD_LIST,\n    literal: LITERAL_LIST,\n    built_in: BUILT_IN_LIST,\n  };\n\n  // placeholder for recursive self-reference\n  const DEFAULT = {\n    keywords: KEYWORDS,\n    illegal: /<\\//\n  };\n\n  // ref: https://docs.julialang.org/en/v1/manual/integers-and-floating-point-numbers/\n  const NUMBER = {\n    className: 'number',\n    // supported numeric literals:\n    //  * binary literal (e.g. 0x10)\n    //  * octal literal (e.g. 0o76543210)\n    //  * hexadecimal literal (e.g. 0xfedcba876543210)\n    //  * hexadecimal floating point literal (e.g. 0x1p0, 0x1.2p2)\n    //  * decimal literal (e.g. 9876543210, 100_000_000)\n    //  * floating pointe literal (e.g. 1.2, 1.2f, .2, 1., 1.2e10, 1.2e-10)\n    begin: /(\\b0x[\\d_]*(\\.[\\d_]*)?|0x\\.\\d[\\d_]*)p[-+]?\\d+|\\b0[box][a-fA-F0-9][a-fA-F0-9_]*|(\\b\\d[\\d_]*(\\.[\\d_]*)?|\\.\\d[\\d_]*)([eEfF][-+]?\\d+)?/,\n    relevance: 0\n  };\n\n  const CHAR = {\n    className: 'string',\n    begin: /'(.|\\\\[xXuU][a-zA-Z0-9]+)'/\n  };\n\n  const INTERPOLATION = {\n    className: 'subst',\n    begin: /\\$\\(/,\n    end: /\\)/,\n    keywords: KEYWORDS\n  };\n\n  const INTERPOLATED_VARIABLE = {\n    className: 'variable',\n    begin: '\\\\$' + VARIABLE_NAME_RE\n  };\n\n  // TODO: neatly escape normal code in string literal\n  const STRING = {\n    className: 'string',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      INTERPOLATION,\n      INTERPOLATED_VARIABLE\n    ],\n    variants: [\n      {\n        begin: /\\w*\"\"\"/,\n        end: /\"\"\"\\w*/,\n        relevance: 10\n      },\n      {\n        begin: /\\w*\"/,\n        end: /\"\\w*/\n      }\n    ]\n  };\n\n  const COMMAND = {\n    className: 'string',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      INTERPOLATION,\n      INTERPOLATED_VARIABLE\n    ],\n    begin: '`',\n    end: '`'\n  };\n\n  const MACROCALL = {\n    className: 'meta',\n    begin: '@' + VARIABLE_NAME_RE\n  };\n\n  const COMMENT = {\n    className: 'comment',\n    variants: [\n      {\n        begin: '#=',\n        end: '=#',\n        relevance: 10\n      },\n      {\n        begin: '#',\n        end: '$'\n      }\n    ]\n  };\n\n  DEFAULT.name = 'Julia';\n  DEFAULT.contains = [\n    NUMBER,\n    CHAR,\n    STRING,\n    COMMAND,\n    MACROCALL,\n    COMMENT,\n    hljs.HASH_COMMENT_MODE,\n    {\n      className: 'keyword',\n      begin:\n        '\\\\b(((abstract|primitive)\\\\s+)type|(mutable\\\\s+)?struct)\\\\b'\n    },\n    { begin: /<:/ } // relevance booster\n  ];\n  INTERPOLATION.contains = DEFAULT.contains;\n\n  return DEFAULT;\n}\n\nexport { julia as default };\n", "/*\nLanguage: Markdown\nRequires: xml.js\nAuthor: <PERSON> <john.cre<PERSON><PERSON>@gmail.com>\nWebsite: https://daringfireball.net/projects/markdown/\nCategory: common, markup\n*/\n\nfunction markdown(hljs) {\n  const regex = hljs.regex;\n  const INLINE_HTML = {\n    begin: /<\\/?[A-Za-z_]/,\n    end: '>',\n    subLanguage: 'xml',\n    relevance: 0\n  };\n  const HORIZONTAL_RULE = {\n    begin: '^[-\\\\*]{3,}',\n    end: '$'\n  };\n  const CODE = {\n    className: 'code',\n    variants: [\n      // TODO: fix to allow these to work with sublanguage also\n      { begin: '(`{3,})[^`](.|\\\\n)*?\\\\1`*[ ]*' },\n      { begin: '(~{3,})[^~](.|\\\\n)*?\\\\1~*[ ]*' },\n      // needed to allow markdown as a sublanguage to work\n      {\n        begin: '```',\n        end: '```+[ ]*$'\n      },\n      {\n        begin: '~~~',\n        end: '~~~+[ ]*$'\n      },\n      { begin: '`.+?`' },\n      {\n        begin: '(?=^( {4}|\\\\t))',\n        // use contains to gobble up multiple lines to allow the block to be whatever size\n        // but only have a single open/close tag vs one per line\n        contains: [\n          {\n            begin: '^( {4}|\\\\t)',\n            end: '(\\\\n)$'\n          }\n        ],\n        relevance: 0\n      }\n    ]\n  };\n  const LIST = {\n    className: 'bullet',\n    begin: '^[ \\t]*([*+-]|(\\\\d+\\\\.))(?=\\\\s+)',\n    end: '\\\\s+',\n    excludeEnd: true\n  };\n  const LINK_REFERENCE = {\n    begin: /^\\[[^\\n]+\\]:/,\n    returnBegin: true,\n    contains: [\n      {\n        className: 'symbol',\n        begin: /\\[/,\n        end: /\\]/,\n        excludeBegin: true,\n        excludeEnd: true\n      },\n      {\n        className: 'link',\n        begin: /:\\s*/,\n        end: /$/,\n        excludeBegin: true\n      }\n    ]\n  };\n  const URL_SCHEME = /[A-Za-z][A-Za-z0-9+.-]*/;\n  const LINK = {\n    variants: [\n      // too much like nested array access in so many languages\n      // to have any real relevance\n      {\n        begin: /\\[.+?\\]\\[.*?\\]/,\n        relevance: 0\n      },\n      // popular internet URLs\n      {\n        begin: /\\[.+?\\]\\(((data|javascript|mailto):|(?:http|ftp)s?:\\/\\/).*?\\)/,\n        relevance: 2\n      },\n      {\n        begin: regex.concat(/\\[.+?\\]\\(/, URL_SCHEME, /:\\/\\/.*?\\)/),\n        relevance: 2\n      },\n      // relative urls\n      {\n        begin: /\\[.+?\\]\\([./?&#].*?\\)/,\n        relevance: 1\n      },\n      // whatever else, lower relevance (might not be a link at all)\n      {\n        begin: /\\[.*?\\]\\(.*?\\)/,\n        relevance: 0\n      }\n    ],\n    returnBegin: true,\n    contains: [\n      {\n        // empty strings for alt or link text\n        match: /\\[(?=\\])/ },\n      {\n        className: 'string',\n        relevance: 0,\n        begin: '\\\\[',\n        end: '\\\\]',\n        excludeBegin: true,\n        returnEnd: true\n      },\n      {\n        className: 'link',\n        relevance: 0,\n        begin: '\\\\]\\\\(',\n        end: '\\\\)',\n        excludeBegin: true,\n        excludeEnd: true\n      },\n      {\n        className: 'symbol',\n        relevance: 0,\n        begin: '\\\\]\\\\[',\n        end: '\\\\]',\n        excludeBegin: true,\n        excludeEnd: true\n      }\n    ]\n  };\n  const BOLD = {\n    className: 'strong',\n    contains: [], // defined later\n    variants: [\n      {\n        begin: /_{2}(?!\\s)/,\n        end: /_{2}/\n      },\n      {\n        begin: /\\*{2}(?!\\s)/,\n        end: /\\*{2}/\n      }\n    ]\n  };\n  const ITALIC = {\n    className: 'emphasis',\n    contains: [], // defined later\n    variants: [\n      {\n        begin: /\\*(?![*\\s])/,\n        end: /\\*/\n      },\n      {\n        begin: /_(?![_\\s])/,\n        end: /_/,\n        relevance: 0\n      }\n    ]\n  };\n\n  // 3 level deep nesting is not allowed because it would create confusion\n  // in cases like `***testing***` because where we don't know if the last\n  // `***` is starting a new bold/italic or finishing the last one\n  const BOLD_WITHOUT_ITALIC = hljs.inherit(BOLD, { contains: [] });\n  const ITALIC_WITHOUT_BOLD = hljs.inherit(ITALIC, { contains: [] });\n  BOLD.contains.push(ITALIC_WITHOUT_BOLD);\n  ITALIC.contains.push(BOLD_WITHOUT_ITALIC);\n\n  let CONTAINABLE = [\n    INLINE_HTML,\n    LINK\n  ];\n\n  [\n    BOLD,\n    ITALIC,\n    BOLD_WITHOUT_ITALIC,\n    ITALIC_WITHOUT_BOLD\n  ].forEach(m => {\n    m.contains = m.contains.concat(CONTAINABLE);\n  });\n\n  CONTAINABLE = CONTAINABLE.concat(BOLD, ITALIC);\n\n  const HEADER = {\n    className: 'section',\n    variants: [\n      {\n        begin: '^#{1,6}',\n        end: '$',\n        contains: CONTAINABLE\n      },\n      {\n        begin: '(?=^.+?\\\\n[=-]{2,}$)',\n        contains: [\n          { begin: '^[=-]*$' },\n          {\n            begin: '^',\n            end: \"\\\\n\",\n            contains: CONTAINABLE\n          }\n        ]\n      }\n    ]\n  };\n\n  const BLOCKQUOTE = {\n    className: 'quote',\n    begin: '^>\\\\s+',\n    contains: CONTAINABLE,\n    end: '$'\n  };\n\n  return {\n    name: 'Markdown',\n    aliases: [\n      'md',\n      'mkdown',\n      'mkd'\n    ],\n    contains: [\n      HEADER,\n      INLINE_HTML,\n      LIST,\n      BOLD,\n      ITALIC,\n      BLOCKQUOTE,\n      CODE,\n      HORIZONTAL_RULE,\n      LINK,\n      LINK_REFERENCE\n    ]\n  };\n}\n\nexport { markdown as default };\n", "/*\nLanguage: Matlab\nAuthor: <PERSON> <bar<PERSON><PERSON><PERSON><PERSON>@gmail.com>\nContributors: <PERSON> <ni<PERSON><PERSON><PERSON>@ya.ru>, <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.mathworks.com/products/matlab.html\nCategory: scientific\n*/\n\n/*\n  Formal syntax is not published, helpful link:\n  https://github.com/kornilova-l/matlab-IntelliJ-plugin/blob/master/src/main/grammar/Matlab.bnf\n*/\nfunction matlab(hljs) {\n  const TRANSPOSE_RE = '(\\'|\\\\.\\')+';\n  const TRANSPOSE = {\n    relevance: 0,\n    contains: [ { begin: TRANSPOSE_RE } ]\n  };\n\n  return {\n    name: 'Matlab',\n    keywords: {\n      keyword:\n        'arguments break case catch classdef continue else elseif end enumeration events for function '\n        + 'global if methods otherwise parfor persistent properties return spmd switch try while',\n      built_in:\n        'sin sind sinh asin asind asinh cos cosd cosh acos acosd acosh tan tand tanh atan '\n        + 'atand atan2 atanh sec secd sech asec asecd asech csc cscd csch acsc acscd acsch cot '\n        + 'cotd coth acot acotd acoth hypot exp expm1 log log1p log10 log2 pow2 realpow reallog '\n        + 'realsqrt sqrt nthroot nextpow2 abs angle complex conj imag real unwrap isreal '\n        + 'cplxpair fix floor ceil round mod rem sign airy besselj bessely besselh besseli '\n        + 'besselk beta betainc betaln ellipj ellipke erf erfc erfcx erfinv expint gamma '\n        + 'gammainc gammaln psi legendre cross dot factor isprime primes gcd lcm rat rats perms '\n        + 'nchoosek factorial cart2sph cart2pol pol2cart sph2cart hsv2rgb rgb2hsv zeros ones '\n        + 'eye repmat rand randn linspace logspace freqspace meshgrid accumarray size length '\n        + 'ndims numel disp isempty isequal isequalwithequalnans cat reshape diag blkdiag tril '\n        + 'triu fliplr flipud flipdim rot90 find sub2ind ind2sub bsxfun ndgrid permute ipermute '\n        + 'shiftdim circshift squeeze isscalar isvector ans eps realmax realmin pi i|0 inf nan '\n        + 'isnan isinf isfinite j|0 why compan gallery hadamard hankel hilb invhilb magic pascal '\n        + 'rosser toeplitz vander wilkinson max min nanmax nanmin mean nanmean type table '\n        + 'readtable writetable sortrows sort figure plot plot3 scatter scatter3 cellfun '\n        + 'legend intersect ismember procrustes hold num2cell '\n    },\n    illegal: '(//|\"|#|/\\\\*|\\\\s+/\\\\w+)',\n    contains: [\n      {\n        className: 'function',\n        beginKeywords: 'function',\n        end: '$',\n        contains: [\n          hljs.UNDERSCORE_TITLE_MODE,\n          {\n            className: 'params',\n            variants: [\n              {\n                begin: '\\\\(',\n                end: '\\\\)'\n              },\n              {\n                begin: '\\\\[',\n                end: '\\\\]'\n              }\n            ]\n          }\n        ]\n      },\n      {\n        className: 'built_in',\n        begin: /true|false/,\n        relevance: 0,\n        starts: TRANSPOSE\n      },\n      {\n        begin: '[a-zA-Z][a-zA-Z_0-9]*' + TRANSPOSE_RE,\n        relevance: 0\n      },\n      {\n        className: 'number',\n        begin: hljs.C_NUMBER_RE,\n        relevance: 0,\n        starts: TRANSPOSE\n      },\n      {\n        className: 'string',\n        begin: '\\'',\n        end: '\\'',\n        contains: [ { begin: '\\'\\'' } ]\n      },\n      {\n        begin: /\\]|\\}|\\)/,\n        relevance: 0,\n        starts: TRANSPOSE\n      },\n      {\n        className: 'string',\n        begin: '\"',\n        end: '\"',\n        contains: [ { begin: '\"\"' } ],\n        starts: TRANSPOSE\n      },\n      hljs.COMMENT('^\\\\s*%\\\\{\\\\s*$', '^\\\\s*%\\\\}\\\\s*$'),\n      hljs.COMMENT('%', '$')\n    ]\n  };\n}\n\nexport { matlab as default };\n", "/*\nLanguage: Plain text\nAuthor: <PERSON><PERSON> (<EMAIL>)\nDescription: Plain text without any highlighting.\nCategory: common\n*/\n\nfunction plaintext(hljs) {\n  return {\n    name: 'Plain text',\n    aliases: [\n      'text',\n      'txt'\n    ],\n    disableAutodetect: true\n  };\n}\n\nexport { plaintext as default };\n", "/*\nLanguage: Python\nDescription: Python is an interpreted, object-oriented, high-level programming language with dynamic semantics.\nWebsite: https://www.python.org\nCategory: common\n*/\n\nfunction python(hljs) {\n  const regex = hljs.regex;\n  const IDENT_RE = /[\\p{XID_Start}_]\\p{XID_Continue}*/u;\n  const RESERVED_WORDS = [\n    'and',\n    'as',\n    'assert',\n    'async',\n    'await',\n    'break',\n    'case',\n    'class',\n    'continue',\n    'def',\n    'del',\n    'elif',\n    'else',\n    'except',\n    'finally',\n    'for',\n    'from',\n    'global',\n    'if',\n    'import',\n    'in',\n    'is',\n    'lambda',\n    'match',\n    'nonlocal|10',\n    'not',\n    'or',\n    'pass',\n    'raise',\n    'return',\n    'try',\n    'while',\n    'with',\n    'yield'\n  ];\n\n  const BUILT_INS = [\n    '__import__',\n    'abs',\n    'all',\n    'any',\n    'ascii',\n    'bin',\n    'bool',\n    'breakpoint',\n    'bytearray',\n    'bytes',\n    'callable',\n    'chr',\n    'classmethod',\n    'compile',\n    'complex',\n    'delattr',\n    'dict',\n    'dir',\n    'divmod',\n    'enumerate',\n    'eval',\n    'exec',\n    'filter',\n    'float',\n    'format',\n    'frozenset',\n    'getattr',\n    'globals',\n    'hasattr',\n    'hash',\n    'help',\n    'hex',\n    'id',\n    'input',\n    'int',\n    'isinstance',\n    'issubclass',\n    'iter',\n    'len',\n    'list',\n    'locals',\n    'map',\n    'max',\n    'memoryview',\n    'min',\n    'next',\n    'object',\n    'oct',\n    'open',\n    'ord',\n    'pow',\n    'print',\n    'property',\n    'range',\n    'repr',\n    'reversed',\n    'round',\n    'set',\n    'setattr',\n    'slice',\n    'sorted',\n    'staticmethod',\n    'str',\n    'sum',\n    'super',\n    'tuple',\n    'type',\n    'vars',\n    'zip'\n  ];\n\n  const LITERALS = [\n    '__debug__',\n    'Ellipsis',\n    'False',\n    'None',\n    'NotImplemented',\n    'True'\n  ];\n\n  // https://docs.python.org/3/library/typing.html\n  // TODO: Could these be supplemented by a CamelCase matcher in certain\n  // contexts, leaving these remaining only for relevance hinting?\n  const TYPES = [\n    \"Any\",\n    \"Callable\",\n    \"Coroutine\",\n    \"Dict\",\n    \"List\",\n    \"Literal\",\n    \"Generic\",\n    \"Optional\",\n    \"Sequence\",\n    \"Set\",\n    \"Tuple\",\n    \"Type\",\n    \"Union\"\n  ];\n\n  const KEYWORDS = {\n    $pattern: /[A-Za-z]\\w+|__\\w+__/,\n    keyword: RESERVED_WORDS,\n    built_in: BUILT_INS,\n    literal: LITERALS,\n    type: TYPES\n  };\n\n  const PROMPT = {\n    className: 'meta',\n    begin: /^(>>>|\\.\\.\\.) /\n  };\n\n  const SUBST = {\n    className: 'subst',\n    begin: /\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS,\n    illegal: /#/\n  };\n\n  const LITERAL_BRACKET = {\n    begin: /\\{\\{/,\n    relevance: 0\n  };\n\n  const STRING = {\n    className: 'string',\n    contains: [ hljs.BACKSLASH_ESCAPE ],\n    variants: [\n      {\n        begin: /([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?'''/,\n        end: /'''/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          PROMPT\n        ],\n        relevance: 10\n      },\n      {\n        begin: /([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?\"\"\"/,\n        end: /\"\"\"/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          PROMPT\n        ],\n        relevance: 10\n      },\n      {\n        begin: /([fF][rR]|[rR][fF]|[fF])'''/,\n        end: /'''/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          PROMPT,\n          LITERAL_BRACKET,\n          SUBST\n        ]\n      },\n      {\n        begin: /([fF][rR]|[rR][fF]|[fF])\"\"\"/,\n        end: /\"\"\"/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          PROMPT,\n          LITERAL_BRACKET,\n          SUBST\n        ]\n      },\n      {\n        begin: /([uU]|[rR])'/,\n        end: /'/,\n        relevance: 10\n      },\n      {\n        begin: /([uU]|[rR])\"/,\n        end: /\"/,\n        relevance: 10\n      },\n      {\n        begin: /([bB]|[bB][rR]|[rR][bB])'/,\n        end: /'/\n      },\n      {\n        begin: /([bB]|[bB][rR]|[rR][bB])\"/,\n        end: /\"/\n      },\n      {\n        begin: /([fF][rR]|[rR][fF]|[fF])'/,\n        end: /'/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          LITERAL_BRACKET,\n          SUBST\n        ]\n      },\n      {\n        begin: /([fF][rR]|[rR][fF]|[fF])\"/,\n        end: /\"/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          LITERAL_BRACKET,\n          SUBST\n        ]\n      },\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE\n    ]\n  };\n\n  // https://docs.python.org/3.9/reference/lexical_analysis.html#numeric-literals\n  const digitpart = '[0-9](_?[0-9])*';\n  const pointfloat = `(\\\\b(${digitpart}))?\\\\.(${digitpart})|\\\\b(${digitpart})\\\\.`;\n  // Whitespace after a number (or any lexical token) is needed only if its absence\n  // would change the tokenization\n  // https://docs.python.org/3.9/reference/lexical_analysis.html#whitespace-between-tokens\n  // We deviate slightly, requiring a word boundary or a keyword\n  // to avoid accidentally recognizing *prefixes* (e.g., `0` in `0x41` or `08` or `0__1`)\n  const lookahead = `\\\\b|${RESERVED_WORDS.join('|')}`;\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      // exponentfloat, pointfloat\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#floating-point-literals\n      // optionally imaginary\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#imaginary-literals\n      // Note: no leading \\b because floats can start with a decimal point\n      // and we don't want to mishandle e.g. `fn(.5)`,\n      // no trailing \\b for pointfloat because it can end with a decimal point\n      // and we don't want to mishandle e.g. `0..hex()`; this should be safe\n      // because both MUST contain a decimal point and so cannot be confused with\n      // the interior part of an identifier\n      {\n        begin: `(\\\\b(${digitpart})|(${pointfloat}))[eE][+-]?(${digitpart})[jJ]?(?=${lookahead})`\n      },\n      {\n        begin: `(${pointfloat})[jJ]?`\n      },\n\n      // decinteger, bininteger, octinteger, hexinteger\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#integer-literals\n      // optionally \"long\" in Python 2\n      // https://docs.python.org/2.7/reference/lexical_analysis.html#integer-and-long-integer-literals\n      // decinteger is optionally imaginary\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#imaginary-literals\n      {\n        begin: `\\\\b([1-9](_?[0-9])*|0+(_?0)*)[lLjJ]?(?=${lookahead})`\n      },\n      {\n        begin: `\\\\b0[bB](_?[01])+[lL]?(?=${lookahead})`\n      },\n      {\n        begin: `\\\\b0[oO](_?[0-7])+[lL]?(?=${lookahead})`\n      },\n      {\n        begin: `\\\\b0[xX](_?[0-9a-fA-F])+[lL]?(?=${lookahead})`\n      },\n\n      // imagnumber (digitpart-based)\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#imaginary-literals\n      {\n        begin: `\\\\b(${digitpart})[jJ](?=${lookahead})`\n      }\n    ]\n  };\n  const COMMENT_TYPE = {\n    className: \"comment\",\n    begin: regex.lookahead(/# type:/),\n    end: /$/,\n    keywords: KEYWORDS,\n    contains: [\n      { // prevent keywords from coloring `type`\n        begin: /# type:/\n      },\n      // comment within a datatype comment includes no keywords\n      {\n        begin: /#/,\n        end: /\\b\\B/,\n        endsWithParent: true\n      }\n    ]\n  };\n  const PARAMS = {\n    className: 'params',\n    variants: [\n      // Exclude params in functions without params\n      {\n        className: \"\",\n        begin: /\\(\\s*\\)/,\n        skip: true\n      },\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        excludeBegin: true,\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        contains: [\n          'self',\n          PROMPT,\n          NUMBER,\n          STRING,\n          hljs.HASH_COMMENT_MODE\n        ]\n      }\n    ]\n  };\n  SUBST.contains = [\n    STRING,\n    NUMBER,\n    PROMPT\n  ];\n\n  return {\n    name: 'Python',\n    aliases: [\n      'py',\n      'gyp',\n      'ipython'\n    ],\n    unicodeRegex: true,\n    keywords: KEYWORDS,\n    illegal: /(<\\/|\\?)|=>/,\n    contains: [\n      PROMPT,\n      NUMBER,\n      {\n        // very common convention\n        begin: /\\bself\\b/\n      },\n      {\n        // eat \"if\" prior to string so that it won't accidentally be\n        // labeled as an f-string\n        beginKeywords: \"if\",\n        relevance: 0\n      },\n      STRING,\n      COMMENT_TYPE,\n      hljs.HASH_COMMENT_MODE,\n      {\n        match: [\n          /\\bdef/, /\\s+/,\n          IDENT_RE,\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.function\"\n        },\n        contains: [ PARAMS ]\n      },\n      {\n        variants: [\n          {\n            match: [\n              /\\bclass/, /\\s+/,\n              IDENT_RE, /\\s*/,\n              /\\(\\s*/, IDENT_RE,/\\s*\\)/\n            ],\n          },\n          {\n            match: [\n              /\\bclass/, /\\s+/,\n              IDENT_RE\n            ],\n          }\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.class\",\n          6: \"title.class.inherited\",\n        }\n      },\n      {\n        className: 'meta',\n        begin: /^[\\t ]*@/,\n        end: /(?=#)|$/,\n        contains: [\n          NUMBER,\n          PARAMS,\n          STRING\n        ]\n      }\n    ]\n  };\n}\n\nexport { python as default };\n", "/*\nLanguage: R\nDescription: R is a free software environment for statistical computing and graphics.\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nWebsite: https://www.r-project.org\nCategory: common,scientific\n*/\n\n/** @type LanguageFn */\nfunction r(hljs) {\n  const regex = hljs.regex;\n  // Identifiers in R cannot start with `_`, but they can start with `.` if it\n  // is not immediately followed by a digit.\n  // R also supports quoted identifiers, which are near-arbitrary sequences\n  // delimited by backticks (`…`), which may contain escape sequences. These are\n  // handled in a separate mode. See `test/markup/r/names.txt` for examples.\n  // FIXME: Support Unicode identifiers.\n  const IDENT_RE = /(?:(?:[a-zA-Z]|\\.[._a-zA-Z])[._a-zA-Z0-9]*)|\\.(?!\\d)/;\n  const NUMBER_TYPES_RE = regex.either(\n    // Special case: only hexadecimal binary powers can contain fractions\n    /0[xX][0-9a-fA-F]+\\.[0-9a-fA-F]*[pP][+-]?\\d+i?/,\n    // Hexadecimal numbers without fraction and optional binary power\n    /0[xX][0-9a-fA-F]+(?:[pP][+-]?\\d+)?[Li]?/,\n    // Decimal numbers\n    /(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:[eE][+-]?\\d+)?[Li]?/\n  );\n  const OPERATORS_RE = /[=!<>:]=|\\|\\||&&|:::?|<-|<<-|->>|->|\\|>|[-+*\\/?!$&|:<=>@^~]|\\*\\*/;\n  const PUNCTUATION_RE = regex.either(\n    /[()]/,\n    /[{}]/,\n    /\\[\\[/,\n    /[[\\]]/,\n    /\\\\/,\n    /,/\n  );\n\n  return {\n    name: 'R',\n\n    keywords: {\n      $pattern: IDENT_RE,\n      keyword:\n        'function if in break next repeat else for while',\n      literal:\n        'NULL NA TRUE FALSE Inf NaN NA_integer_|10 NA_real_|10 '\n        + 'NA_character_|10 NA_complex_|10',\n      built_in:\n        // Builtin constants\n        'LETTERS letters month.abb month.name pi T F '\n        // Primitive functions\n        // These are all the functions in `base` that are implemented as a\n        // `.Primitive`, minus those functions that are also keywords.\n        + 'abs acos acosh all any anyNA Arg as.call as.character '\n        + 'as.complex as.double as.environment as.integer as.logical '\n        + 'as.null.default as.numeric as.raw asin asinh atan atanh attr '\n        + 'attributes baseenv browser c call ceiling class Conj cos cosh '\n        + 'cospi cummax cummin cumprod cumsum digamma dim dimnames '\n        + 'emptyenv exp expression floor forceAndCall gamma gc.time '\n        + 'globalenv Im interactive invisible is.array is.atomic is.call '\n        + 'is.character is.complex is.double is.environment is.expression '\n        + 'is.finite is.function is.infinite is.integer is.language '\n        + 'is.list is.logical is.matrix is.na is.name is.nan is.null '\n        + 'is.numeric is.object is.pairlist is.raw is.recursive is.single '\n        + 'is.symbol lazyLoadDBfetch length lgamma list log max min '\n        + 'missing Mod names nargs nzchar oldClass on.exit pos.to.env '\n        + 'proc.time prod quote range Re rep retracemem return round '\n        + 'seq_along seq_len seq.int sign signif sin sinh sinpi sqrt '\n        + 'standardGeneric substitute sum switch tan tanh tanpi tracemem '\n        + 'trigamma trunc unclass untracemem UseMethod xtfrm',\n    },\n\n    contains: [\n      // Roxygen comments\n      hljs.COMMENT(\n        /#'/,\n        /$/,\n        { contains: [\n          {\n            // Handle `@examples` separately to cause all subsequent code\n            // until the next `@`-tag on its own line to be kept as-is,\n            // preventing highlighting. This code is example R code, so nested\n            // doctags shouldn’t be treated as such. See\n            // `test/markup/r/roxygen.txt` for an example.\n            scope: 'doctag',\n            match: /@examples/,\n            starts: {\n              end: regex.lookahead(regex.either(\n                // end if another doc comment\n                /\\n^#'\\s*(?=@[a-zA-Z]+)/,\n                // or a line with no comment\n                /\\n^(?!#')/\n              )),\n              endsParent: true\n            }\n          },\n          {\n            // Handle `@param` to highlight the parameter name following\n            // after.\n            scope: 'doctag',\n            begin: '@param',\n            end: /$/,\n            contains: [\n              {\n                scope: 'variable',\n                variants: [\n                  { match: IDENT_RE },\n                  { match: /`(?:\\\\.|[^`\\\\])+`/ }\n                ],\n                endsParent: true\n              }\n            ]\n          },\n          {\n            scope: 'doctag',\n            match: /@[a-zA-Z]+/\n          },\n          {\n            scope: 'keyword',\n            match: /\\\\[a-zA-Z]+/\n          }\n        ] }\n      ),\n\n      hljs.HASH_COMMENT_MODE,\n\n      {\n        scope: 'string',\n        contains: [ hljs.BACKSLASH_ESCAPE ],\n        variants: [\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]\"(-*)\\(/,\n            end: /\\)(-*)\"/\n          }),\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]\"(-*)\\{/,\n            end: /\\}(-*)\"/\n          }),\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]\"(-*)\\[/,\n            end: /\\](-*)\"/\n          }),\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]'(-*)\\(/,\n            end: /\\)(-*)'/\n          }),\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]'(-*)\\{/,\n            end: /\\}(-*)'/\n          }),\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]'(-*)\\[/,\n            end: /\\](-*)'/\n          }),\n          {\n            begin: '\"',\n            end: '\"',\n            relevance: 0\n          },\n          {\n            begin: \"'\",\n            end: \"'\",\n            relevance: 0\n          }\n        ],\n      },\n\n      // Matching numbers immediately following punctuation and operators is\n      // tricky since we need to look at the character ahead of a number to\n      // ensure the number is not part of an identifier, and we cannot use\n      // negative look-behind assertions. So instead we explicitly handle all\n      // possible combinations of (operator|punctuation), number.\n      // TODO: replace with negative look-behind when available\n      // { begin: /(?<![a-zA-Z0-9._])0[xX][0-9a-fA-F]+\\.[0-9a-fA-F]*[pP][+-]?\\d+i?/ },\n      // { begin: /(?<![a-zA-Z0-9._])0[xX][0-9a-fA-F]+([pP][+-]?\\d+)?[Li]?/ },\n      // { begin: /(?<![a-zA-Z0-9._])(\\d+(\\.\\d*)?|\\.\\d+)([eE][+-]?\\d+)?[Li]?/ }\n      {\n        relevance: 0,\n        variants: [\n          {\n            scope: {\n              1: 'operator',\n              2: 'number'\n            },\n            match: [\n              OPERATORS_RE,\n              NUMBER_TYPES_RE\n            ]\n          },\n          {\n            scope: {\n              1: 'operator',\n              2: 'number'\n            },\n            match: [\n              /%[^%]*%/,\n              NUMBER_TYPES_RE\n            ]\n          },\n          {\n            scope: {\n              1: 'punctuation',\n              2: 'number'\n            },\n            match: [\n              PUNCTUATION_RE,\n              NUMBER_TYPES_RE\n            ]\n          },\n          {\n            scope: { 2: 'number' },\n            match: [\n              /[^a-zA-Z0-9._]|^/, // not part of an identifier, or start of document\n              NUMBER_TYPES_RE\n            ]\n          }\n        ]\n      },\n\n      // Operators/punctuation when they're not directly followed by numbers\n      {\n        // Relevance boost for the most common assignment form.\n        scope: { 3: 'operator' },\n        match: [\n          IDENT_RE,\n          /\\s+/,\n          /<-/,\n          /\\s+/\n        ]\n      },\n\n      {\n        scope: 'operator',\n        relevance: 0,\n        variants: [\n          { match: OPERATORS_RE },\n          { match: /%[^%]*%/ }\n        ]\n      },\n\n      {\n        scope: 'punctuation',\n        relevance: 0,\n        match: PUNCTUATION_RE\n      },\n\n      {\n        // Escaped identifier\n        begin: '`',\n        end: '`',\n        contains: [ { begin: /\\\\./ } ]\n      }\n    ]\n  };\n}\n\nexport { r as default };\n", "/*\nLanguage: Ruby\nDescription: Ruby is a dynamic, open source programming language with a focus on simplicity and productivity.\nWebsite: https://www.ruby-lang.org/\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>\nCategory: common\n*/\n\nfunction ruby(hljs) {\n  const regex = hljs.regex;\n  const RUBY_METHOD_RE = '([a-zA-Z_]\\\\w*[!?=]?|[-+~]@|<<|>>|=~|===?|<=>|[<>]=?|\\\\*\\\\*|[-/+%^&*~`|]|\\\\[\\\\]=?)';\n  // TODO: move concepts like CAMEL_CASE into `modes.js`\n  const CLASS_NAME_RE = regex.either(\n    /\\b([A-Z]+[a-z0-9]+)+/,\n    // ends in caps\n    /\\b([A-Z]+[a-z0-9]+)+[A-Z]+/,\n  )\n  ;\n  const CLASS_NAME_WITH_NAMESPACE_RE = regex.concat(CLASS_NAME_RE, /(::\\w+)*/);\n  // very popular ruby built-ins that one might even assume\n  // are actual keywords (despite that not being the case)\n  const PSEUDO_KWS = [\n    \"include\",\n    \"extend\",\n    \"prepend\",\n    \"public\",\n    \"private\",\n    \"protected\",\n    \"raise\",\n    \"throw\"\n  ];\n  const RUBY_KEYWORDS = {\n    \"variable.constant\": [\n      \"__FILE__\",\n      \"__LINE__\",\n      \"__ENCODING__\"\n    ],\n    \"variable.language\": [\n      \"self\",\n      \"super\",\n    ],\n    keyword: [\n      \"alias\",\n      \"and\",\n      \"begin\",\n      \"BEGIN\",\n      \"break\",\n      \"case\",\n      \"class\",\n      \"defined\",\n      \"do\",\n      \"else\",\n      \"elsif\",\n      \"end\",\n      \"END\",\n      \"ensure\",\n      \"for\",\n      \"if\",\n      \"in\",\n      \"module\",\n      \"next\",\n      \"not\",\n      \"or\",\n      \"redo\",\n      \"require\",\n      \"rescue\",\n      \"retry\",\n      \"return\",\n      \"then\",\n      \"undef\",\n      \"unless\",\n      \"until\",\n      \"when\",\n      \"while\",\n      \"yield\",\n      ...PSEUDO_KWS\n    ],\n    built_in: [\n      \"proc\",\n      \"lambda\",\n      \"attr_accessor\",\n      \"attr_reader\",\n      \"attr_writer\",\n      \"define_method\",\n      \"private_constant\",\n      \"module_function\"\n    ],\n    literal: [\n      \"true\",\n      \"false\",\n      \"nil\"\n    ]\n  };\n  const YARDOCTAG = {\n    className: 'doctag',\n    begin: '@[A-Za-z]+'\n  };\n  const IRB_OBJECT = {\n    begin: '#<',\n    end: '>'\n  };\n  const COMMENT_MODES = [\n    hljs.COMMENT(\n      '#',\n      '$',\n      { contains: [ YARDOCTAG ] }\n    ),\n    hljs.COMMENT(\n      '^=begin',\n      '^=end',\n      {\n        contains: [ YARDOCTAG ],\n        relevance: 10\n      }\n    ),\n    hljs.COMMENT('^__END__', hljs.MATCH_NOTHING_RE)\n  ];\n  const SUBST = {\n    className: 'subst',\n    begin: /#\\{/,\n    end: /\\}/,\n    keywords: RUBY_KEYWORDS\n  };\n  const STRING = {\n    className: 'string',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ],\n    variants: [\n      {\n        begin: /'/,\n        end: /'/\n      },\n      {\n        begin: /\"/,\n        end: /\"/\n      },\n      {\n        begin: /`/,\n        end: /`/\n      },\n      {\n        begin: /%[qQwWx]?\\(/,\n        end: /\\)/\n      },\n      {\n        begin: /%[qQwWx]?\\[/,\n        end: /\\]/\n      },\n      {\n        begin: /%[qQwWx]?\\{/,\n        end: /\\}/\n      },\n      {\n        begin: /%[qQwWx]?</,\n        end: />/\n      },\n      {\n        begin: /%[qQwWx]?\\//,\n        end: /\\//\n      },\n      {\n        begin: /%[qQwWx]?%/,\n        end: /%/\n      },\n      {\n        begin: /%[qQwWx]?-/,\n        end: /-/\n      },\n      {\n        begin: /%[qQwWx]?\\|/,\n        end: /\\|/\n      },\n      // in the following expressions, \\B in the beginning suppresses recognition of ?-sequences\n      // where ? is the last character of a preceding identifier, as in: `func?4`\n      { begin: /\\B\\?(\\\\\\d{1,3})/ },\n      { begin: /\\B\\?(\\\\x[A-Fa-f0-9]{1,2})/ },\n      { begin: /\\B\\?(\\\\u\\{?[A-Fa-f0-9]{1,6}\\}?)/ },\n      { begin: /\\B\\?(\\\\M-\\\\C-|\\\\M-\\\\c|\\\\c\\\\M-|\\\\M-|\\\\C-\\\\M-)[\\x20-\\x7e]/ },\n      { begin: /\\B\\?\\\\(c|C-)[\\x20-\\x7e]/ },\n      { begin: /\\B\\?\\\\?\\S/ },\n      // heredocs\n      {\n        // this guard makes sure that we have an entire heredoc and not a false\n        // positive (auto-detect, etc.)\n        begin: regex.concat(\n          /<<[-~]?'?/,\n          regex.lookahead(/(\\w+)(?=\\W)[^\\n]*\\n(?:[^\\n]*\\n)*?\\s*\\1\\b/)\n        ),\n        contains: [\n          hljs.END_SAME_AS_BEGIN({\n            begin: /(\\w+)/,\n            end: /(\\w+)/,\n            contains: [\n              hljs.BACKSLASH_ESCAPE,\n              SUBST\n            ]\n          })\n        ]\n      }\n    ]\n  };\n\n  // Ruby syntax is underdocumented, but this grammar seems to be accurate\n  // as of version 2.7.2 (confirmed with (irb and `Ripper.sexp(...)`)\n  // https://docs.ruby-lang.org/en/2.7.0/doc/syntax/literals_rdoc.html#label-Numbers\n  const decimal = '[1-9](_?[0-9])*|0';\n  const digits = '[0-9](_?[0-9])*';\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      // decimal integer/float, optionally exponential or rational, optionally imaginary\n      { begin: `\\\\b(${decimal})(\\\\.(${digits}))?([eE][+-]?(${digits})|r)?i?\\\\b` },\n\n      // explicit decimal/binary/octal/hexadecimal integer,\n      // optionally rational and/or imaginary\n      { begin: \"\\\\b0[dD][0-9](_?[0-9])*r?i?\\\\b\" },\n      { begin: \"\\\\b0[bB][0-1](_?[0-1])*r?i?\\\\b\" },\n      { begin: \"\\\\b0[oO][0-7](_?[0-7])*r?i?\\\\b\" },\n      { begin: \"\\\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*r?i?\\\\b\" },\n\n      // 0-prefixed implicit octal integer, optionally rational and/or imaginary\n      { begin: \"\\\\b0(_?[0-7])+r?i?\\\\b\" }\n    ]\n  };\n\n  const PARAMS = {\n    variants: [\n      {\n        match: /\\(\\)/,\n      },\n      {\n        className: 'params',\n        begin: /\\(/,\n        end: /(?=\\))/,\n        excludeBegin: true,\n        endsParent: true,\n        keywords: RUBY_KEYWORDS,\n      }\n    ]\n  };\n\n  const INCLUDE_EXTEND = {\n    match: [\n      /(include|extend)\\s+/,\n      CLASS_NAME_WITH_NAMESPACE_RE\n    ],\n    scope: {\n      2: \"title.class\"\n    },\n    keywords: RUBY_KEYWORDS\n  };\n\n  const CLASS_DEFINITION = {\n    variants: [\n      {\n        match: [\n          /class\\s+/,\n          CLASS_NAME_WITH_NAMESPACE_RE,\n          /\\s+<\\s+/,\n          CLASS_NAME_WITH_NAMESPACE_RE\n        ]\n      },\n      {\n        match: [\n          /\\b(class|module)\\s+/,\n          CLASS_NAME_WITH_NAMESPACE_RE\n        ]\n      }\n    ],\n    scope: {\n      2: \"title.class\",\n      4: \"title.class.inherited\"\n    },\n    keywords: RUBY_KEYWORDS\n  };\n\n  const UPPER_CASE_CONSTANT = {\n    relevance: 0,\n    match: /\\b[A-Z][A-Z_0-9]+\\b/,\n    className: \"variable.constant\"\n  };\n\n  const METHOD_DEFINITION = {\n    match: [\n      /def/, /\\s+/,\n      RUBY_METHOD_RE\n    ],\n    scope: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [\n      PARAMS\n    ]\n  };\n\n  const OBJECT_CREATION = {\n    relevance: 0,\n    match: [\n      CLASS_NAME_WITH_NAMESPACE_RE,\n      /\\.new[. (]/\n    ],\n    scope: {\n      1: \"title.class\"\n    }\n  };\n\n  // CamelCase\n  const CLASS_REFERENCE = {\n    relevance: 0,\n    match: CLASS_NAME_RE,\n    scope: \"title.class\"\n  };\n\n  const RUBY_DEFAULT_CONTAINS = [\n    STRING,\n    CLASS_DEFINITION,\n    INCLUDE_EXTEND,\n    OBJECT_CREATION,\n    UPPER_CASE_CONSTANT,\n    CLASS_REFERENCE,\n    METHOD_DEFINITION,\n    {\n      // swallow namespace qualifiers before symbols\n      begin: hljs.IDENT_RE + '::' },\n    {\n      className: 'symbol',\n      begin: hljs.UNDERSCORE_IDENT_RE + '(!|\\\\?)?:',\n      relevance: 0\n    },\n    {\n      className: 'symbol',\n      begin: ':(?!\\\\s)',\n      contains: [\n        STRING,\n        { begin: RUBY_METHOD_RE }\n      ],\n      relevance: 0\n    },\n    NUMBER,\n    {\n      // negative-look forward attempts to prevent false matches like:\n      // @ident@ or $ident$ that might indicate this is not ruby at all\n      className: \"variable\",\n      begin: '(\\\\$\\\\W)|((\\\\$|@@?)(\\\\w+))(?=[^@$?])' + `(?![A-Za-z])(?![@$?'])`\n    },\n    {\n      className: 'params',\n      begin: /\\|/,\n      end: /\\|/,\n      excludeBegin: true,\n      excludeEnd: true,\n      relevance: 0, // this could be a lot of things (in other languages) other than params\n      keywords: RUBY_KEYWORDS\n    },\n    { // regexp container\n      begin: '(' + hljs.RE_STARTERS_RE + '|unless)\\\\s*',\n      keywords: 'unless',\n      contains: [\n        {\n          className: 'regexp',\n          contains: [\n            hljs.BACKSLASH_ESCAPE,\n            SUBST\n          ],\n          illegal: /\\n/,\n          variants: [\n            {\n              begin: '/',\n              end: '/[a-z]*'\n            },\n            {\n              begin: /%r\\{/,\n              end: /\\}[a-z]*/\n            },\n            {\n              begin: '%r\\\\(',\n              end: '\\\\)[a-z]*'\n            },\n            {\n              begin: '%r!',\n              end: '![a-z]*'\n            },\n            {\n              begin: '%r\\\\[',\n              end: '\\\\][a-z]*'\n            }\n          ]\n        }\n      ].concat(IRB_OBJECT, COMMENT_MODES),\n      relevance: 0\n    }\n  ].concat(IRB_OBJECT, COMMENT_MODES);\n\n  SUBST.contains = RUBY_DEFAULT_CONTAINS;\n  PARAMS.contains = RUBY_DEFAULT_CONTAINS;\n\n  // >>\n  // ?>\n  const SIMPLE_PROMPT = \"[>?]>\";\n  // irb(main):001:0>\n  const DEFAULT_PROMPT = \"[\\\\w#]+\\\\(\\\\w+\\\\):\\\\d+:\\\\d+[>*]\";\n  const RVM_PROMPT = \"(\\\\w+-)?\\\\d+\\\\.\\\\d+\\\\.\\\\d+(p\\\\d+)?[^\\\\d][^>]+>\";\n\n  const IRB_DEFAULT = [\n    {\n      begin: /^\\s*=>/,\n      starts: {\n        end: '$',\n        contains: RUBY_DEFAULT_CONTAINS\n      }\n    },\n    {\n      className: 'meta.prompt',\n      begin: '^(' + SIMPLE_PROMPT + \"|\" + DEFAULT_PROMPT + '|' + RVM_PROMPT + ')(?=[ ])',\n      starts: {\n        end: '$',\n        keywords: RUBY_KEYWORDS,\n        contains: RUBY_DEFAULT_CONTAINS\n      }\n    }\n  ];\n\n  COMMENT_MODES.unshift(IRB_OBJECT);\n\n  return {\n    name: 'Ruby',\n    aliases: [\n      'rb',\n      'gemspec',\n      'podspec',\n      'thor',\n      'irb'\n    ],\n    keywords: RUBY_KEYWORDS,\n    illegal: /\\/\\*/,\n    contains: [ hljs.SHEBANG({ binary: \"ruby\" }) ]\n      .concat(IRB_DEFAULT)\n      .concat(COMMENT_MODES)\n      .concat(RUBY_DEFAULT_CONTAINS)\n  };\n}\n\nexport { ruby as default };\n", "/*\nLanguage: Shell Session\nRequires: bash.js\nAuthor: T<PERSON><PERSON><PERSON><PERSON><PERSON>e <<EMAIL>>\nCategory: common\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction shell(hljs) {\n  return {\n    name: 'Shell Session',\n    aliases: [\n      'console',\n      'shellsession'\n    ],\n    contains: [\n      {\n        className: 'meta.prompt',\n        // We cannot add \\s (spaces) in the regular expression otherwise it will be too broad and produce unexpected result.\n        // For instance, in the following example, it would match \"echo /path/to/home >\" as a prompt:\n        // echo /path/to/home > t.exe\n        begin: /^\\s{0,3}[/~\\w\\d[\\]()@-]*[>%$#][ ]?/,\n        starts: {\n          end: /[^\\\\](?=\\s*$)/,\n          subLanguage: 'bash'\n        }\n      }\n    ]\n  };\n}\n\nexport { shell as default };\n", "/*\n Language: SQL\n Website: https://en.wikipedia.org/wiki/SQL\n Category: common, database\n */\n\n/*\n\nGoals:\n\nSQL is intended to highlight basic/common SQL keywords and expressions\n\n- If pretty much every single SQL server includes supports, then it's a canidate.\n- It is NOT intended to include tons of vendor specific keywords (Oracle, MySQL,\n  PostgreSQL) although the list of data types is purposely a bit more expansive.\n- For more specific SQL grammars please see:\n  - PostgreSQL and PL/pgSQL - core\n  - T-SQL - https://github.com/highlightjs/highlightjs-tsql\n  - sql_more (core)\n\n */\n\nfunction sql(hljs) {\n  const regex = hljs.regex;\n  const COMMENT_MODE = hljs.COMMENT('--', '$');\n  const STRING = {\n    className: 'string',\n    variants: [\n      {\n        begin: /'/,\n        end: /'/,\n        contains: [ { begin: /''/ } ]\n      }\n    ]\n  };\n  const QUOTED_IDENTIFIER = {\n    begin: /\"/,\n    end: /\"/,\n    contains: [ { begin: /\"\"/ } ]\n  };\n\n  const LITERALS = [\n    \"true\",\n    \"false\",\n    // Not sure it's correct to call NULL literal, and clauses like IS [NOT] NULL look strange that way.\n    // \"null\",\n    \"unknown\"\n  ];\n\n  const MULTI_WORD_TYPES = [\n    \"double precision\",\n    \"large object\",\n    \"with timezone\",\n    \"without timezone\"\n  ];\n\n  const TYPES = [\n    'bigint',\n    'binary',\n    'blob',\n    'boolean',\n    'char',\n    'character',\n    'clob',\n    'date',\n    'dec',\n    'decfloat',\n    'decimal',\n    'float',\n    'int',\n    'integer',\n    'interval',\n    'nchar',\n    'nclob',\n    'national',\n    'numeric',\n    'real',\n    'row',\n    'smallint',\n    'time',\n    'timestamp',\n    'varchar',\n    'varying', // modifier (character varying)\n    'varbinary'\n  ];\n\n  const NON_RESERVED_WORDS = [\n    \"add\",\n    \"asc\",\n    \"collation\",\n    \"desc\",\n    \"final\",\n    \"first\",\n    \"last\",\n    \"view\"\n  ];\n\n  // https://jakewheat.github.io/sql-overview/sql-2016-foundation-grammar.html#reserved-word\n  const RESERVED_WORDS = [\n    \"abs\",\n    \"acos\",\n    \"all\",\n    \"allocate\",\n    \"alter\",\n    \"and\",\n    \"any\",\n    \"are\",\n    \"array\",\n    \"array_agg\",\n    \"array_max_cardinality\",\n    \"as\",\n    \"asensitive\",\n    \"asin\",\n    \"asymmetric\",\n    \"at\",\n    \"atan\",\n    \"atomic\",\n    \"authorization\",\n    \"avg\",\n    \"begin\",\n    \"begin_frame\",\n    \"begin_partition\",\n    \"between\",\n    \"bigint\",\n    \"binary\",\n    \"blob\",\n    \"boolean\",\n    \"both\",\n    \"by\",\n    \"call\",\n    \"called\",\n    \"cardinality\",\n    \"cascaded\",\n    \"case\",\n    \"cast\",\n    \"ceil\",\n    \"ceiling\",\n    \"char\",\n    \"char_length\",\n    \"character\",\n    \"character_length\",\n    \"check\",\n    \"classifier\",\n    \"clob\",\n    \"close\",\n    \"coalesce\",\n    \"collate\",\n    \"collect\",\n    \"column\",\n    \"commit\",\n    \"condition\",\n    \"connect\",\n    \"constraint\",\n    \"contains\",\n    \"convert\",\n    \"copy\",\n    \"corr\",\n    \"corresponding\",\n    \"cos\",\n    \"cosh\",\n    \"count\",\n    \"covar_pop\",\n    \"covar_samp\",\n    \"create\",\n    \"cross\",\n    \"cube\",\n    \"cume_dist\",\n    \"current\",\n    \"current_catalog\",\n    \"current_date\",\n    \"current_default_transform_group\",\n    \"current_path\",\n    \"current_role\",\n    \"current_row\",\n    \"current_schema\",\n    \"current_time\",\n    \"current_timestamp\",\n    \"current_path\",\n    \"current_role\",\n    \"current_transform_group_for_type\",\n    \"current_user\",\n    \"cursor\",\n    \"cycle\",\n    \"date\",\n    \"day\",\n    \"deallocate\",\n    \"dec\",\n    \"decimal\",\n    \"decfloat\",\n    \"declare\",\n    \"default\",\n    \"define\",\n    \"delete\",\n    \"dense_rank\",\n    \"deref\",\n    \"describe\",\n    \"deterministic\",\n    \"disconnect\",\n    \"distinct\",\n    \"double\",\n    \"drop\",\n    \"dynamic\",\n    \"each\",\n    \"element\",\n    \"else\",\n    \"empty\",\n    \"end\",\n    \"end_frame\",\n    \"end_partition\",\n    \"end-exec\",\n    \"equals\",\n    \"escape\",\n    \"every\",\n    \"except\",\n    \"exec\",\n    \"execute\",\n    \"exists\",\n    \"exp\",\n    \"external\",\n    \"extract\",\n    \"false\",\n    \"fetch\",\n    \"filter\",\n    \"first_value\",\n    \"float\",\n    \"floor\",\n    \"for\",\n    \"foreign\",\n    \"frame_row\",\n    \"free\",\n    \"from\",\n    \"full\",\n    \"function\",\n    \"fusion\",\n    \"get\",\n    \"global\",\n    \"grant\",\n    \"group\",\n    \"grouping\",\n    \"groups\",\n    \"having\",\n    \"hold\",\n    \"hour\",\n    \"identity\",\n    \"in\",\n    \"indicator\",\n    \"initial\",\n    \"inner\",\n    \"inout\",\n    \"insensitive\",\n    \"insert\",\n    \"int\",\n    \"integer\",\n    \"intersect\",\n    \"intersection\",\n    \"interval\",\n    \"into\",\n    \"is\",\n    \"join\",\n    \"json_array\",\n    \"json_arrayagg\",\n    \"json_exists\",\n    \"json_object\",\n    \"json_objectagg\",\n    \"json_query\",\n    \"json_table\",\n    \"json_table_primitive\",\n    \"json_value\",\n    \"lag\",\n    \"language\",\n    \"large\",\n    \"last_value\",\n    \"lateral\",\n    \"lead\",\n    \"leading\",\n    \"left\",\n    \"like\",\n    \"like_regex\",\n    \"listagg\",\n    \"ln\",\n    \"local\",\n    \"localtime\",\n    \"localtimestamp\",\n    \"log\",\n    \"log10\",\n    \"lower\",\n    \"match\",\n    \"match_number\",\n    \"match_recognize\",\n    \"matches\",\n    \"max\",\n    \"member\",\n    \"merge\",\n    \"method\",\n    \"min\",\n    \"minute\",\n    \"mod\",\n    \"modifies\",\n    \"module\",\n    \"month\",\n    \"multiset\",\n    \"national\",\n    \"natural\",\n    \"nchar\",\n    \"nclob\",\n    \"new\",\n    \"no\",\n    \"none\",\n    \"normalize\",\n    \"not\",\n    \"nth_value\",\n    \"ntile\",\n    \"null\",\n    \"nullif\",\n    \"numeric\",\n    \"octet_length\",\n    \"occurrences_regex\",\n    \"of\",\n    \"offset\",\n    \"old\",\n    \"omit\",\n    \"on\",\n    \"one\",\n    \"only\",\n    \"open\",\n    \"or\",\n    \"order\",\n    \"out\",\n    \"outer\",\n    \"over\",\n    \"overlaps\",\n    \"overlay\",\n    \"parameter\",\n    \"partition\",\n    \"pattern\",\n    \"per\",\n    \"percent\",\n    \"percent_rank\",\n    \"percentile_cont\",\n    \"percentile_disc\",\n    \"period\",\n    \"portion\",\n    \"position\",\n    \"position_regex\",\n    \"power\",\n    \"precedes\",\n    \"precision\",\n    \"prepare\",\n    \"primary\",\n    \"procedure\",\n    \"ptf\",\n    \"range\",\n    \"rank\",\n    \"reads\",\n    \"real\",\n    \"recursive\",\n    \"ref\",\n    \"references\",\n    \"referencing\",\n    \"regr_avgx\",\n    \"regr_avgy\",\n    \"regr_count\",\n    \"regr_intercept\",\n    \"regr_r2\",\n    \"regr_slope\",\n    \"regr_sxx\",\n    \"regr_sxy\",\n    \"regr_syy\",\n    \"release\",\n    \"result\",\n    \"return\",\n    \"returns\",\n    \"revoke\",\n    \"right\",\n    \"rollback\",\n    \"rollup\",\n    \"row\",\n    \"row_number\",\n    \"rows\",\n    \"running\",\n    \"savepoint\",\n    \"scope\",\n    \"scroll\",\n    \"search\",\n    \"second\",\n    \"seek\",\n    \"select\",\n    \"sensitive\",\n    \"session_user\",\n    \"set\",\n    \"show\",\n    \"similar\",\n    \"sin\",\n    \"sinh\",\n    \"skip\",\n    \"smallint\",\n    \"some\",\n    \"specific\",\n    \"specifictype\",\n    \"sql\",\n    \"sqlexception\",\n    \"sqlstate\",\n    \"sqlwarning\",\n    \"sqrt\",\n    \"start\",\n    \"static\",\n    \"stddev_pop\",\n    \"stddev_samp\",\n    \"submultiset\",\n    \"subset\",\n    \"substring\",\n    \"substring_regex\",\n    \"succeeds\",\n    \"sum\",\n    \"symmetric\",\n    \"system\",\n    \"system_time\",\n    \"system_user\",\n    \"table\",\n    \"tablesample\",\n    \"tan\",\n    \"tanh\",\n    \"then\",\n    \"time\",\n    \"timestamp\",\n    \"timezone_hour\",\n    \"timezone_minute\",\n    \"to\",\n    \"trailing\",\n    \"translate\",\n    \"translate_regex\",\n    \"translation\",\n    \"treat\",\n    \"trigger\",\n    \"trim\",\n    \"trim_array\",\n    \"true\",\n    \"truncate\",\n    \"uescape\",\n    \"union\",\n    \"unique\",\n    \"unknown\",\n    \"unnest\",\n    \"update\",\n    \"upper\",\n    \"user\",\n    \"using\",\n    \"value\",\n    \"values\",\n    \"value_of\",\n    \"var_pop\",\n    \"var_samp\",\n    \"varbinary\",\n    \"varchar\",\n    \"varying\",\n    \"versioning\",\n    \"when\",\n    \"whenever\",\n    \"where\",\n    \"width_bucket\",\n    \"window\",\n    \"with\",\n    \"within\",\n    \"without\",\n    \"year\",\n  ];\n\n  // these are reserved words we have identified to be functions\n  // and should only be highlighted in a dispatch-like context\n  // ie, array_agg(...), etc.\n  const RESERVED_FUNCTIONS = [\n    \"abs\",\n    \"acos\",\n    \"array_agg\",\n    \"asin\",\n    \"atan\",\n    \"avg\",\n    \"cast\",\n    \"ceil\",\n    \"ceiling\",\n    \"coalesce\",\n    \"corr\",\n    \"cos\",\n    \"cosh\",\n    \"count\",\n    \"covar_pop\",\n    \"covar_samp\",\n    \"cume_dist\",\n    \"dense_rank\",\n    \"deref\",\n    \"element\",\n    \"exp\",\n    \"extract\",\n    \"first_value\",\n    \"floor\",\n    \"json_array\",\n    \"json_arrayagg\",\n    \"json_exists\",\n    \"json_object\",\n    \"json_objectagg\",\n    \"json_query\",\n    \"json_table\",\n    \"json_table_primitive\",\n    \"json_value\",\n    \"lag\",\n    \"last_value\",\n    \"lead\",\n    \"listagg\",\n    \"ln\",\n    \"log\",\n    \"log10\",\n    \"lower\",\n    \"max\",\n    \"min\",\n    \"mod\",\n    \"nth_value\",\n    \"ntile\",\n    \"nullif\",\n    \"percent_rank\",\n    \"percentile_cont\",\n    \"percentile_disc\",\n    \"position\",\n    \"position_regex\",\n    \"power\",\n    \"rank\",\n    \"regr_avgx\",\n    \"regr_avgy\",\n    \"regr_count\",\n    \"regr_intercept\",\n    \"regr_r2\",\n    \"regr_slope\",\n    \"regr_sxx\",\n    \"regr_sxy\",\n    \"regr_syy\",\n    \"row_number\",\n    \"sin\",\n    \"sinh\",\n    \"sqrt\",\n    \"stddev_pop\",\n    \"stddev_samp\",\n    \"substring\",\n    \"substring_regex\",\n    \"sum\",\n    \"tan\",\n    \"tanh\",\n    \"translate\",\n    \"translate_regex\",\n    \"treat\",\n    \"trim\",\n    \"trim_array\",\n    \"unnest\",\n    \"upper\",\n    \"value_of\",\n    \"var_pop\",\n    \"var_samp\",\n    \"width_bucket\",\n  ];\n\n  // these functions can\n  const POSSIBLE_WITHOUT_PARENS = [\n    \"current_catalog\",\n    \"current_date\",\n    \"current_default_transform_group\",\n    \"current_path\",\n    \"current_role\",\n    \"current_schema\",\n    \"current_transform_group_for_type\",\n    \"current_user\",\n    \"session_user\",\n    \"system_time\",\n    \"system_user\",\n    \"current_time\",\n    \"localtime\",\n    \"current_timestamp\",\n    \"localtimestamp\"\n  ];\n\n  // those exist to boost relevance making these very\n  // \"SQL like\" keyword combos worth +1 extra relevance\n  const COMBOS = [\n    \"create table\",\n    \"insert into\",\n    \"primary key\",\n    \"foreign key\",\n    \"not null\",\n    \"alter table\",\n    \"add constraint\",\n    \"grouping sets\",\n    \"on overflow\",\n    \"character set\",\n    \"respect nulls\",\n    \"ignore nulls\",\n    \"nulls first\",\n    \"nulls last\",\n    \"depth first\",\n    \"breadth first\"\n  ];\n\n  const FUNCTIONS = RESERVED_FUNCTIONS;\n\n  const KEYWORDS = [\n    ...RESERVED_WORDS,\n    ...NON_RESERVED_WORDS\n  ].filter((keyword) => {\n    return !RESERVED_FUNCTIONS.includes(keyword);\n  });\n\n  const VARIABLE = {\n    className: \"variable\",\n    begin: /@[a-z0-9][a-z0-9_]*/,\n  };\n\n  const OPERATOR = {\n    className: \"operator\",\n    begin: /[-+*/=%^~]|&&?|\\|\\|?|!=?|<(?:=>?|<|>)?|>[>=]?/,\n    relevance: 0,\n  };\n\n  const FUNCTION_CALL = {\n    begin: regex.concat(/\\b/, regex.either(...FUNCTIONS), /\\s*\\(/),\n    relevance: 0,\n    keywords: { built_in: FUNCTIONS }\n  };\n\n  // keywords with less than 3 letters are reduced in relevancy\n  function reduceRelevancy(list, {\n    exceptions, when\n  } = {}) {\n    const qualifyFn = when;\n    exceptions = exceptions || [];\n    return list.map((item) => {\n      if (item.match(/\\|\\d+$/) || exceptions.includes(item)) {\n        return item;\n      } else if (qualifyFn(item)) {\n        return `${item}|0`;\n      } else {\n        return item;\n      }\n    });\n  }\n\n  return {\n    name: 'SQL',\n    case_insensitive: true,\n    // does not include {} or HTML tags `</`\n    illegal: /[{}]|<\\//,\n    keywords: {\n      $pattern: /\\b[\\w\\.]+/,\n      keyword:\n        reduceRelevancy(KEYWORDS, { when: (x) => x.length < 3 }),\n      literal: LITERALS,\n      type: TYPES,\n      built_in: POSSIBLE_WITHOUT_PARENS\n    },\n    contains: [\n      {\n        begin: regex.either(...COMBOS),\n        relevance: 0,\n        keywords: {\n          $pattern: /[\\w\\.]+/,\n          keyword: KEYWORDS.concat(COMBOS),\n          literal: LITERALS,\n          type: TYPES\n        },\n      },\n      {\n        className: \"type\",\n        begin: regex.either(...MULTI_WORD_TYPES)\n      },\n      FUNCTION_CALL,\n      VARIABLE,\n      STRING,\n      QUOTED_IDENTIFIER,\n      hljs.C_NUMBER_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      COMMENT_MODE,\n      OPERATOR\n    ]\n  };\n}\n\nexport { sql as default };\n", "/*\nLanguage: HTML, XML\nWebsite: https://www.w3.org/XML/\nCategory: common, web\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction xml(hljs) {\n  const regex = hljs.regex;\n  // XML names can have the following additional letters: https://www.w3.org/TR/xml/#NT-NameChar\n  // OTHER_NAME_CHARS = /[:\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]/;\n  // Element names start with NAME_START_CHAR followed by optional other Unicode letters, ASCII digits, hyphens, underscores, and periods\n  // const TAG_NAME_RE = regex.concat(/[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/, regex.optional(/[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*:/), /[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*/);;\n  // const XML_IDENT_RE = /[A-Z_a-z:\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]+/;\n  // const TAG_NAME_RE = regex.concat(/[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/, regex.optional(/[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*:/), /[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*/);\n  // however, to cater for performance and more Unicode support rely simply on the Unicode letter class\n  const TAG_NAME_RE = regex.concat(/[\\p{L}_]/u, regex.optional(/[\\p{L}0-9_.-]*:/u), /[\\p{L}0-9_.-]*/u);\n  const XML_IDENT_RE = /[\\p{L}0-9._:-]+/u;\n  const XML_ENTITIES = {\n    className: 'symbol',\n    begin: /&[a-z]+;|&#[0-9]+;|&#x[a-f0-9]+;/\n  };\n  const XML_META_KEYWORDS = {\n    begin: /\\s/,\n    contains: [\n      {\n        className: 'keyword',\n        begin: /#?[a-z_][a-z1-9_-]+/,\n        illegal: /\\n/\n      }\n    ]\n  };\n  const XML_META_PAR_KEYWORDS = hljs.inherit(XML_META_KEYWORDS, {\n    begin: /\\(/,\n    end: /\\)/\n  });\n  const APOS_META_STRING_MODE = hljs.inherit(hljs.APOS_STRING_MODE, { className: 'string' });\n  const QUOTE_META_STRING_MODE = hljs.inherit(hljs.QUOTE_STRING_MODE, { className: 'string' });\n  const TAG_INTERNALS = {\n    endsWithParent: true,\n    illegal: /</,\n    relevance: 0,\n    contains: [\n      {\n        className: 'attr',\n        begin: XML_IDENT_RE,\n        relevance: 0\n      },\n      {\n        begin: /=\\s*/,\n        relevance: 0,\n        contains: [\n          {\n            className: 'string',\n            endsParent: true,\n            variants: [\n              {\n                begin: /\"/,\n                end: /\"/,\n                contains: [ XML_ENTITIES ]\n              },\n              {\n                begin: /'/,\n                end: /'/,\n                contains: [ XML_ENTITIES ]\n              },\n              { begin: /[^\\s\"'=<>`]+/ }\n            ]\n          }\n        ]\n      }\n    ]\n  };\n  return {\n    name: 'HTML, XML',\n    aliases: [\n      'html',\n      'xhtml',\n      'rss',\n      'atom',\n      'xjb',\n      'xsd',\n      'xsl',\n      'plist',\n      'wsf',\n      'svg'\n    ],\n    case_insensitive: true,\n    unicodeRegex: true,\n    contains: [\n      {\n        className: 'meta',\n        begin: /<![a-z]/,\n        end: />/,\n        relevance: 10,\n        contains: [\n          XML_META_KEYWORDS,\n          QUOTE_META_STRING_MODE,\n          APOS_META_STRING_MODE,\n          XML_META_PAR_KEYWORDS,\n          {\n            begin: /\\[/,\n            end: /\\]/,\n            contains: [\n              {\n                className: 'meta',\n                begin: /<![a-z]/,\n                end: />/,\n                contains: [\n                  XML_META_KEYWORDS,\n                  XML_META_PAR_KEYWORDS,\n                  QUOTE_META_STRING_MODE,\n                  APOS_META_STRING_MODE\n                ]\n              }\n            ]\n          }\n        ]\n      },\n      hljs.COMMENT(\n        /<!--/,\n        /-->/,\n        { relevance: 10 }\n      ),\n      {\n        begin: /<!\\[CDATA\\[/,\n        end: /\\]\\]>/,\n        relevance: 10\n      },\n      XML_ENTITIES,\n      // xml processing instructions\n      {\n        className: 'meta',\n        end: /\\?>/,\n        variants: [\n          {\n            begin: /<\\?xml/,\n            relevance: 10,\n            contains: [\n              QUOTE_META_STRING_MODE\n            ]\n          },\n          {\n            begin: /<\\?[a-z][a-z0-9]+/,\n          }\n        ]\n\n      },\n      {\n        className: 'tag',\n        /*\n        The lookahead pattern (?=...) ensures that 'begin' only matches\n        '<style' as a single word, followed by a whitespace or an\n        ending bracket.\n        */\n        begin: /<style(?=\\s|>)/,\n        end: />/,\n        keywords: { name: 'style' },\n        contains: [ TAG_INTERNALS ],\n        starts: {\n          end: /<\\/style>/,\n          returnEnd: true,\n          subLanguage: [\n            'css',\n            'xml'\n          ]\n        }\n      },\n      {\n        className: 'tag',\n        // See the comment in the <style tag about the lookahead pattern\n        begin: /<script(?=\\s|>)/,\n        end: />/,\n        keywords: { name: 'script' },\n        contains: [ TAG_INTERNALS ],\n        starts: {\n          end: /<\\/script>/,\n          returnEnd: true,\n          subLanguage: [\n            'javascript',\n            'handlebars',\n            'xml'\n          ]\n        }\n      },\n      // we need this for now for jSX\n      {\n        className: 'tag',\n        begin: /<>|<\\/>/\n      },\n      // open tag\n      {\n        className: 'tag',\n        begin: regex.concat(\n          /</,\n          regex.lookahead(regex.concat(\n            TAG_NAME_RE,\n            // <tag/>\n            // <tag>\n            // <tag ...\n            regex.either(/\\/>/, />/, /\\s/)\n          ))\n        ),\n        end: /\\/?>/,\n        contains: [\n          {\n            className: 'name',\n            begin: TAG_NAME_RE,\n            relevance: 0,\n            starts: TAG_INTERNALS\n          }\n        ]\n      },\n      // close tag\n      {\n        className: 'tag',\n        begin: regex.concat(\n          /<\\//,\n          regex.lookahead(regex.concat(\n            TAG_NAME_RE, />/\n          ))\n        ),\n        contains: [\n          {\n            className: 'name',\n            begin: TAG_NAME_RE,\n            relevance: 0\n          },\n          {\n            begin: />/,\n            relevance: 0,\n            endsParent: true\n          }\n        ]\n      }\n    ]\n  };\n}\n\nexport { xml as default };\n", "/*\nLanguage: YAML\nDescription: Yet Another Markdown Language\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nRequires: ruby.js\nWebsite: https://yaml.org\nCategory: common, config\n*/\nfunction yaml(hljs) {\n  const LITERALS = 'true false yes no null';\n\n  // YAML spec allows non-reserved URI characters in tags.\n  const URI_CHARACTERS = '[\\\\w#;/?:@&=+$,.~*\\'()[\\\\]]+';\n\n  // Define keys as starting with a word character\n  // ...containing word chars, spaces, colons, forward-slashes, hyphens and periods\n  // ...and ending with a colon followed immediately by a space, tab or newline.\n  // The YAML spec allows for much more than this, but this covers most use-cases.\n  const KEY = {\n    className: 'attr',\n    variants: [\n      { begin: '\\\\w[\\\\w :\\\\/.-]*:(?=[ \\t]|$)' },\n      { // double quoted keys\n        begin: '\"\\\\w[\\\\w :\\\\/.-]*\":(?=[ \\t]|$)' },\n      { // single quoted keys\n        begin: '\\'\\\\w[\\\\w :\\\\/.-]*\\':(?=[ \\t]|$)' }\n    ]\n  };\n\n  const TEMPLATE_VARIABLES = {\n    className: 'template-variable',\n    variants: [\n      { // jinja templates Ansible\n        begin: /\\{\\{/,\n        end: /\\}\\}/\n      },\n      { // Ruby i18n\n        begin: /%\\{/,\n        end: /\\}/\n      }\n    ]\n  };\n  const STRING = {\n    className: 'string',\n    relevance: 0,\n    variants: [\n      {\n        begin: /'/,\n        end: /'/\n      },\n      {\n        begin: /\"/,\n        end: /\"/\n      },\n      { begin: /\\S+/ }\n    ],\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      TEMPLATE_VARIABLES\n    ]\n  };\n\n  // Strings inside of value containers (objects) can't contain braces,\n  // brackets, or commas\n  const CONTAINER_STRING = hljs.inherit(STRING, { variants: [\n    {\n      begin: /'/,\n      end: /'/\n    },\n    {\n      begin: /\"/,\n      end: /\"/\n    },\n    { begin: /[^\\s,{}[\\]]+/ }\n  ] });\n\n  const DATE_RE = '[0-9]{4}(-[0-9][0-9]){0,2}';\n  const TIME_RE = '([Tt \\\\t][0-9][0-9]?(:[0-9][0-9]){2})?';\n  const FRACTION_RE = '(\\\\.[0-9]*)?';\n  const ZONE_RE = '([ \\\\t])*(Z|[-+][0-9][0-9]?(:[0-9][0-9])?)?';\n  const TIMESTAMP = {\n    className: 'number',\n    begin: '\\\\b' + DATE_RE + TIME_RE + FRACTION_RE + ZONE_RE + '\\\\b'\n  };\n\n  const VALUE_CONTAINER = {\n    end: ',',\n    endsWithParent: true,\n    excludeEnd: true,\n    keywords: LITERALS,\n    relevance: 0\n  };\n  const OBJECT = {\n    begin: /\\{/,\n    end: /\\}/,\n    contains: [ VALUE_CONTAINER ],\n    illegal: '\\\\n',\n    relevance: 0\n  };\n  const ARRAY = {\n    begin: '\\\\[',\n    end: '\\\\]',\n    contains: [ VALUE_CONTAINER ],\n    illegal: '\\\\n',\n    relevance: 0\n  };\n\n  const MODES = [\n    KEY,\n    {\n      className: 'meta',\n      begin: '^---\\\\s*$',\n      relevance: 10\n    },\n    { // multi line string\n      // Blocks start with a | or > followed by a newline\n      //\n      // Indentation of subsequent lines must be the same to\n      // be considered part of the block\n      className: 'string',\n      begin: '[\\\\|>]([1-9]?[+-])?[ ]*\\\\n( +)[^ ][^\\\\n]*\\\\n(\\\\2[^\\\\n]+\\\\n?)*'\n    },\n    { // Ruby/Rails erb\n      begin: '<%[%=-]?',\n      end: '[%-]?%>',\n      subLanguage: 'ruby',\n      excludeBegin: true,\n      excludeEnd: true,\n      relevance: 0\n    },\n    { // named tags\n      className: 'type',\n      begin: '!\\\\w+!' + URI_CHARACTERS\n    },\n    // https://yaml.org/spec/1.2/spec.html#id2784064\n    { // verbatim tags\n      className: 'type',\n      begin: '!<' + URI_CHARACTERS + \">\"\n    },\n    { // primary tags\n      className: 'type',\n      begin: '!' + URI_CHARACTERS\n    },\n    { // secondary tags\n      className: 'type',\n      begin: '!!' + URI_CHARACTERS\n    },\n    { // fragment id &ref\n      className: 'meta',\n      begin: '&' + hljs.UNDERSCORE_IDENT_RE + '$'\n    },\n    { // fragment reference *ref\n      className: 'meta',\n      begin: '\\\\*' + hljs.UNDERSCORE_IDENT_RE + '$'\n    },\n    { // array listing\n      className: 'bullet',\n      // TODO: remove |$ hack when we have proper look-ahead support\n      begin: '-(?=[ ]|$)',\n      relevance: 0\n    },\n    hljs.HASH_COMMENT_MODE,\n    {\n      beginKeywords: LITERALS,\n      keywords: { literal: LITERALS }\n    },\n    TIMESTAMP,\n    // numbers are any valid C-style number that\n    // sit isolated from other words\n    {\n      className: 'number',\n      begin: hljs.C_NUMBER_RE + '\\\\b',\n      relevance: 0\n    },\n    OBJECT,\n    ARRAY,\n    STRING\n  ];\n\n  const VALUE_MODES = [ ...MODES ];\n  VALUE_MODES.pop();\n  VALUE_MODES.push(CONTAINER_STRING);\n  VALUE_CONTAINER.contains = VALUE_MODES;\n\n  return {\n    name: 'YAML',\n    case_insensitive: true,\n    aliases: [ 'yml' ],\n    contains: MODES\n  };\n}\n\nexport { yaml as default };\n"], "names": ["deepFreeze", "obj", "Map", "clear", "delete", "set", "Error", "Set", "add", "Object", "freeze", "getOwnPropertyNames", "for<PERSON>ach", "name", "prop", "type", "isFrozen", "Response", "constructor", "mode", "undefined", "data", "this", "isMatchIgnored", "ignoreMatch", "escapeHTML", "value", "replace", "inherit$1", "original", "result", "create", "key", "_len", "arguments", "length", "objects", "Array", "_key", "emitsWrappingTags", "node", "scope", "HTMLR<PERSON><PERSON>", "parseTree", "options", "buffer", "classPrefix", "walk", "addText", "text", "openNode", "className", "scopeToCSSClass", "prefix", "startsWith", "includes", "pieces", "split", "concat", "shift", "map", "x", "i", "repeat", "join", "span", "closeNode", "newNode", "opts", "children", "assign", "TokenTree", "rootNode", "stack", "top", "root", "push", "pop", "closeAllNodes", "toJSON", "JSON", "stringify", "builder", "_walk", "child", "_collapse", "every", "el", "TokenTreeEmitter", "super", "startScope", "endScope", "__addSublanguage", "emitter", "toHTML", "finalize", "source", "re", "<PERSON><PERSON><PERSON>", "anyNumberOfTimes", "optional", "_len2", "args", "_key3", "either", "_len3", "_key4", "splice", "stripOptionsFromArgs", "capture", "countMatchGroups", "RegExp", "toString", "exec", "BACKREF_RE", "_rewriteBackreferences", "regexps", "_ref2", "joinWith", "numCaptures", "regex", "offset", "out", "match", "substring", "index", "String", "Number", "IDENT_RE", "UNDERSCORE_IDENT_RE", "NUMBER_RE", "C_NUMBER_RE", "BINARY_NUMBER_RE", "BACKSLASH_ESCAPE", "begin", "relevance", "APOS_STRING_MODE", "end", "illegal", "contains", "QUOTE_STRING_MODE", "COMMENT", "excludeBegin", "ENGLISH_WORD", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "HASH_COMMENT_MODE", "NUMBER_MODE", "C_NUMBER_MODE", "BINARY_NUMBER_MODE", "REGEXP_MODE", "TITLE_MODE", "UNDERSCORE_TITLE_MODE", "METHOD_GUARD", "MODES", "__proto__", "END_SAME_AS_BEGIN", "on:begin", "m", "resp", "_beginMatch", "on:end", "MATCH_NOTHING_RE", "PHRASAL_WORDS_MODE", "RE_STARTERS_RE", "SHEBANG", "beginShebang", "binary", "skipIfHasPrecedingDot", "response", "input", "scopeClassName", "_parent", "beginKeywords", "parent", "__beforeBegin", "keywords", "compileIllegal", "isArray", "compileMatch", "compileRelevance", "beforeMatchExt", "beforeMatch", "starts", "originalMode", "keys", "endsParent", "COMMON_KEYWORDS", "compileKeywords", "rawKeywords", "caseInsensitive", "scopeName", "compiledKeywords", "compileList", "keywordList", "toLowerCase", "keyword", "pair", "scoreForKeyword", "providedScore", "commonKeyword", "seenDeprecations", "error", "message", "console", "warn", "_len4", "_key5", "log", "deprecated", "version", "MultiClassError", "remapScopeNames", "regexes", "_ref3", "scopeNames", "emit", "positions", "_emit", "_multi", "MultiClass", "beginScope", "scopeSugar", "_wrap", "skip", "returnBegin", "beginMultiClass", "excludeEnd", "returnEnd", "endMultiClass", "compileLanguage", "language", "langRe", "global", "case_insensitive", "unicodeRegex", "MultiRegex", "matchIndexes", "matchAt", "position", "addRule", "compile", "terminators", "matcherRe", "lastIndex", "s", "findIndex", "matchData", "ResumableMultiRegex", "rules", "multiRegexes", "count", "regexIndex", "getMatcher", "matcher", "slice", "_ref4", "_ref5", "_slicedToArray", "resumingScanAtSamePosition", "considerAll", "m2", "compilerExtensions", "classNameAliases", "compileMode", "cmode", "isCompiled", "ext", "keywordPattern", "$pattern", "keywordPatternRe", "beginRe", "endsWithParent", "endRe", "terminatorEnd", "illegalRe", "c", "variants", "cachedVariants", "variant", "dependencyOnParent", "expandOrCloneMode", "mm", "term", "rule", "buildModeRegex", "HTMLInjectionError", "reason", "html", "escape", "inherit", "NO_MATCH", "Symbol", "HLJS", "hljs", "languages", "aliases", "plugins", "SAFE_MODE", "LANGUAGE_NOT_FOUND", "PLAINTEXT_LANGUAGE", "disableAutodetect", "ignoreUnescapedHTML", "throwUnescapedHTML", "noHighlightRe", "languageDetectRe", "cssSelector", "__emitter", "shouldNotHighlight", "languageName", "test", "highlight", "codeOrLanguageName", "optionsOrCode", "ignoreIllegals", "code", "context", "fire", "_highlight", "codeToHighlight", "continuation", "keywordHits", "processKeywords", "matchText", "modeBuffer", "buf", "word", "_data", "kind", "keywordRelevance", "cssClass", "emitKey<PERSON>", "processBuffer", "subLanguage", "continuations", "_top", "highlightAuto", "_emitter", "processSubLanguage", "emitMultiClass", "max", "klass", "startNewMode", "endOfMode", "matchPlusRemainder", "matched", "lexeme", "doIgnore", "resumeScanAtSamePosition", "doEndMatch", "endMode", "origin", "lastMatch", "processLexeme", "textBeforeMatch", "err", "badRule", "newMode", "_i", "_beforeCallbacks", "cb", "doBeginMatch", "processed", "iterations", "getLanguage", "md", "list", "current", "unshift", "item", "processContinuations", "__emitTokens", "processedCount", "_illegalBy", "resultSoFar", "errorRaised", "languageSubset", "plaintext", "justTextHighlightResult", "results", "filter", "autoDetection", "_sorted", "sort", "a", "b", "supersetOf", "best", "secondBest", "highlightElement", "element", "block", "classes", "parentNode", "find", "_class", "blockLanguage", "dataset", "highlighted", "innerHTML", "textContent", "currentLang", "resultLang", "classList", "updateClassName", "wantsHighlight", "highlightAll", "document", "readyState", "querySelectorAll", "registerAliases", "aliasList", "_ref6", "alias", "lang", "event", "plugin", "window", "addEventListener", "highlightBlock", "configure", "userOptions", "initHighlighting", "initHighlightingOnLoad", "registerLanguage", "languageDefinition", "error$1", "rawDefinition", "bind", "unregisterLanguage", "_i2", "_Object$keys", "listLanguages", "addPlugin", "upgradePluginAPI", "removePlugin", "indexOf", "debugMode", "safeMode", "versionString", "newInstance", "module", "exports", "HighlightJS", "default", "___CSS_LOADER_EXPORT___", "id", "styleTagTransform", "setAttributes", "insert", "querySelector", "lastInsertedElement", "_lastElementInsertedByStyleLoader", "nextS<PERSON>ling", "insertBefore", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "domAPI", "insertStyleElement", "locals", "TAGS", "MEDIA_FEATURES", "PSEUDO_CLASSES", "PSEUDO_ELEMENTS", "ATTRIBUTES", "reverse", "_key2", "KEYWORDS", "LITERALS", "TYPES", "ERROR_TYPES", "BUILT_IN_GLOBALS", "BUILT_IN_VARIABLES", "BUILT_INS", "highlightjs", "VAR", "BRACED_VAR", "SUBST", "HERE_DOC", "QUOTE_STRING", "ARITHMETIC", "KNOWN_SHEBANG", "FUNCTION", "literal", "built_in", "NUMBERS", "VERBATIM_STRING", "VERBATIM_STRING_NO_LF", "SUBST_NO_LF", "INTERPOLATED_STRING", "INTERPOLATED_VERBATIM_STRING", "INTERPOLATED_VERBATIM_STRING_NO_LF", "STRING", "GENERIC_MODIFIER", "TYPE_IDENT_RE", "AT_IDENTIFIER", "modes", "IMPORTANT", "BLOCK_COMMENT", "HEXCOLOR", "FUNCTION_DISPATCH", "ATTRIBUTE_SELECTOR_MODE", "CSS_NUMBER_MODE", "CSS_VARIABLE", "STRINGS", "keyframePosition", "attribute", "BANG_KEYWORD_MODE", "KNOWN_TYPES", "ALL_KEYWORDS", "QUOTED_IDENTIFIER", "BEGIN_GENERIC_TYPE_SYMBOL_RE", "GENERIC_TYPE_SYMBOL", "makeOperatorMode", "_ref", "allOperatorChars", "includeEqual", "OPERATOR_CHAR_RE", "from", "OPERATOR_CHAR_OR_DOT_RE", "OPERATOR_FIRST_CHAR_OF_MULTIPLE_RE", "SYMBOLIC_OPERATOR_RE", "OPERATOR", "OPERATOR_WITHOUT_EQUAL", "makeTypeAnnotationMode", "prefixScope", "TYPE_ANNOTATION", "DISCRIMINATED_UNION_TYPE_ANNOTATION", "TYPE_DECLARATION", "COMPUTATION_EXPRESSION", "PREPROCESSOR", "NUMBER", "QUOTED_STRING", "TRIPLE_QUOTED_STRING", "INTERPOLATED_TRIPLE_QUOTED_STRING", "CHAR_LITERAL", "VERSION", "HEADER", "HEADERS_AND_BODY", "IDENT_RE$1", "XML_TAG", "isTrulyOpeningTag", "afterMatchIndex", "nextChar", "hasClosingTag", "after", "tag", "afterMatch", "KEYWORDS$1", "decimalDigits", "frac", "decimalInteger", "HTML_TEMPLATE", "CSS_TEMPLATE", "GRAPHQL_TEMPLATE", "TEMPLATE_STRING", "SUBST_INTERNALS", "SUBST_AND_COMMENTS", "PARAMS_CONTAINS", "PARAMS", "CLASS_OR_EXTENDS", "CLASS_REFERENCE", "_", "FUNCTION_DEFINITION", "label", "FUNCTION_CALL", "PROPERTY_ACCESS", "GETTER_OR_SETTER", "FUNC_LEAD_IN_RE", "FUNCTION_VARIABLE", "LITERALS_MODE", "VARIABLE_NAME_RE", "DEFAULT", "INTERPOLATION", "INTERPOLATED_VARIABLE", "COMMAND", "MACROCALL", "INLINE_HTML", "LINK", "BOLD", "ITALIC", "BOLD_WITHOUT_ITALIC", "ITALIC_WITHOUT_BOLD", "CONTAINABLE", "TRANSPOSE_RE", "TRANSPOSE", "RESERVED_WORDS", "PROMPT", "LITERAL_BRACKET", "digitpart", "pointfloat", "COMMENT_TYPE", "NUMBER_TYPES_RE", "OPERATORS_RE", "PUNCTUATION_RE", "RUBY_METHOD_RE", "CLASS_NAME_RE", "CLASS_NAME_WITH_NAMESPACE_RE", "RUBY_KEYWORDS", "YARDOCTAG", "IRB_OBJECT", "COMMENT_MODES", "digits", "RUBY_DEFAULT_CONTAINS", "IRB_DEFAULT", "COMMENT_MODE", "RESERVED_FUNCTIONS", "COMBOS", "FUNCTIONS", "exceptions", "qualifyFn", "when", "reduceRelevancy", "TAG_NAME_RE", "XML_ENTITIES", "XML_META_KEYWORDS", "XML_META_PAR_KEYWORDS", "APOS_META_STRING_MODE", "QUOTE_META_STRING_MODE", "TAG_INTERNALS", "URI_CHARACTERS", "CONTAINER_STRING", "VALUE_CONTAINER", "OBJECT", "ARRAY", "VALUE_MODES"], "sourceRoot": ""}