from unittest import TestCase

import simplejson as json
from operator import itemgetter

class TestItemSortKey(TestCase):
    def test_simple_first(self):
        a = {'a': 1, 'c': 5, 'jack': 'jill', 'pick': 'axe', 'array': [1, 5, 6, 9], 'tuple': (83, 12, 3), 'crate': 'dog', 'zeak': 'oh'}
        self.assertEqual(
            '{"a": 1, "c": 5, "crate": "dog", "jack": "jill", "pick": "axe", "zeak": "oh", "array": [1, 5, 6, 9], "tuple": [83, 12, 3]}',
            json.dumps(a, item_sort_key=json.simple_first))

    def test_case(self):
        a = {'a': 1, 'c': 5, 'Jack': 'jill', 'pick': 'axe', 'Array': [1, 5, 6, 9], 'tuple': (83, 12, 3), 'crate': 'dog', 'zeak': 'oh'}
        self.assertEqual(
            '{"Array": [1, 5, 6, 9], "Jack": "jill", "a": 1, "c": 5, "crate": "dog", "pick": "axe", "tuple": [83, 12, 3], "zeak": "oh"}',
            json.dumps(a, item_sort_key=itemgetter(0)))
        self.assertEqual(
            '{"a": 1, "Array": [1, 5, 6, 9], "c": 5, "crate": "dog", "Jack": "jill", "pick": "axe", "tuple": [83, 12, 3], "zeak": "oh"}',
            json.dumps(a, item_sort_key=lambda kv: kv[0].lower()))

    def test_item_sort_key_value(self):
        # https://github.com/simplejson/simplejson/issues/173
        a = {'a': 1, 'b': 0}
        self.assertEqual(
            '{"b": 0, "a": 1}',
            json.dumps(a, item_sort_key=lambda kv: kv[1]))
