2025-07-31 17:31:30,907 - error_handler - CRITICAL - 未捕获的异常
Traceback (most recent call last):
  File "/mnt/Data2/auto_task/web_server/app_new.py", line 13, in <module>
    import callbacks  # 导入回调函数模块
    ^^^^^^^^^^^^^^^^
  File "/mnt/Data2/auto_task/web_server/callbacks.py", line 161
    try:
    ^^^
SyntaxError: expected 'except' or 'finally' block
2025-07-31 17:33:39,171 - numexpr.utils - INFO - Note: detected 256 virtual cores but NumExpr set to maximum of 64, check "NUMEXPR_MAX_THREADS" environment variable.
2025-07-31 17:33:39,171 - numexpr.utils - INFO - Note: NumExpr detected 256 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-07-31 17:33:39,171 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-07-31 17:33:39,370 - dash.dash - INFO - Dash is running on http://0.0.0.0:8051/

2025-07-31 17:34:30,817 - numexpr.utils - INFO - Note: detected 256 virtual cores but NumExpr set to maximum of 64, check "NUMEXPR_MAX_THREADS" environment variable.
2025-07-31 17:34:30,817 - numexpr.utils - INFO - Note: NumExpr detected 256 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-07-31 17:34:30,817 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-07-31 17:34:31,061 - dash.dash - INFO - Dash is running on http://0.0.0.0:8051/

2025-07-31 17:34:31,064 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8051
 * Running on http://*************:8051
2025-07-31 17:34:31,064 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 17:34:34,855 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:34] "GET /?user=admin&path=/mnt/Data2/Jupyter/2025/行业SUE/bt_data/中性100_70%现货_SUE_L2增强 HTTP/1.1" 200 -
2025-07-31 17:34:35,073 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:35] "GET /_dash-layout HTTP/1.1" 200 -
2025-07-31 17:34:35,113 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:35] "GET /_dash-dependencies HTTP/1.1" 200 -
2025-07-31 17:34:35,745 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:35] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:35,748 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:35] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:35,752 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:35] "[36mGET /_dash-component-suites/dash/dcc/async-dropdown.js HTTP/1.1[0m" 304 -
2025-07-31 17:34:35,789 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:35] "[36mGET /_dash-component-suites/dash/dash_table/async-highlight.js HTTP/1.1[0m" 304 -
2025-07-31 17:34:35,790 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:35] "[36mGET /_dash-component-suites/dash/dash_table/async-table.js HTTP/1.1[0m" 304 -
2025-07-31 17:34:36,023 - error_handler - ERROR - 应用错误在 update_charts: 策略名称不能为空
2025-07-31 17:34:36,024 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:36] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:36,323 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:36] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:36,364 - error_handler - ERROR - 应用错误在 update_account_options: 策略名称不能为空
2025-07-31 17:34:36,364 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:36] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:36,366 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:36] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:36,407 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:36] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:36,603 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:36] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:36,916 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:36] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:36,919 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:36] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:36,923 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:36] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:36,924 - error_handler - ERROR - 应用错误在 update_charts: 策略名称不能为空
2025-07-31 17:34:36,925 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:36] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:36,957 - error_handler - ERROR - 应用错误在 update_charts: 策略名称不能为空
2025-07-31 17:34:36,958 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:36] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:37,208 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:37] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:38,043 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:38] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:06,053 - error_handler - ERROR - 应用错误在 update_charts: 策略名称不能为空
2025-07-31 17:35:06,054 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:06] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:06,056 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:06] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:06,452 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:06] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:06,455 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:06] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:06,458 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:06] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:06,462 - error_handler - ERROR - 应用错误在 update_account_options: 策略名称不能为空
2025-07-31 17:35:06,462 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:06] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:08,780 - error_handler - ERROR - 应用错误在 update_account_options: 策略名称不能为空
2025-07-31 17:35:08,781 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:08] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:08,812 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:08] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:08,817 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:08] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:08,821 - error_handler - ERROR - 应用错误在 update_charts: 策略名称不能为空
2025-07-31 17:35:08,822 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:08] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:08,824 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:08] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:09,318 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:09] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:02:55,246 - error_handler - ERROR - 未处理的错误在 test_function: 测试错误
Traceback (most recent call last):
  File "/mnt/Data2/auto_task/web_server/error_handler.py", line 67, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/Data2/auto_task/web_server/test_simple.py", line 169, in test_function
    raise ValueError("测试错误")
ValueError: 测试错误
2025-08-01 09:07:06,003 - error_handler - ERROR - 未处理的错误在 test_function: 测试错误
Traceback (most recent call last):
  File "/mnt/Data2/auto_task/web_server/error_handler.py", line 67, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/Data2/auto_task/web_server/test_simple.py", line 169, in test_function
    raise ValueError("测试错误")
ValueError: 测试错误
2025-08-01 09:09:31,079 - error_handler - CRITICAL - 未捕获的异常
AttributeError: _ARRAY_API not found
2025-08-01 09:09:31,085 - error_handler - CRITICAL - 未捕获的异常
AttributeError: _ARRAY_API not found
2025-08-01 09:09:31,286 - matplotlib - DEBUG - matplotlib data path: /root/anaconda3/lib/python3.11/site-packages/matplotlib/mpl-data
2025-08-01 09:09:31,290 - matplotlib - DEBUG - CONFIGDIR=/root/.config/matplotlib
2025-08-01 09:09:31,291 - matplotlib - DEBUG - interactive is False
2025-08-01 09:09:31,291 - matplotlib - DEBUG - platform is linux
2025-08-01 09:13:48,004 - matplotlib - DEBUG - matplotlib data path: /mnt/Data2/auto_task/web_server/.conda/lib/python3.13/site-packages/matplotlib/mpl-data
2025-08-01 09:13:48,008 - matplotlib - DEBUG - CONFIGDIR=/root/.config/matplotlib
2025-08-01 09:13:48,008 - matplotlib - DEBUG - interactive is False
2025-08-01 09:13:48,009 - matplotlib - DEBUG - platform is linux
2025-08-01 09:14:15,228 - matplotlib - DEBUG - matplotlib data path: /mnt/Data2/auto_task/web_server/.conda/lib/python3.13/site-packages/matplotlib/mpl-data
2025-08-01 09:14:15,234 - matplotlib - DEBUG - CONFIGDIR=/root/.config/matplotlib
2025-08-01 09:14:15,236 - matplotlib - DEBUG - interactive is False
2025-08-01 09:14:15,236 - matplotlib - DEBUG - platform is linux
2025-08-01 09:16:04,315 - error_handler - CRITICAL - 未捕获的异常
AttributeError: _ARRAY_API not found
2025-08-01 09:16:04,321 - error_handler - CRITICAL - 未捕获的异常
AttributeError: _ARRAY_API not found
2025-08-01 09:16:04,533 - matplotlib - DEBUG - matplotlib data path: /root/anaconda3/lib/python3.11/site-packages/matplotlib/mpl-data
2025-08-01 09:16:04,537 - matplotlib - DEBUG - CONFIGDIR=/root/.config/matplotlib
2025-08-01 09:16:04,538 - matplotlib - DEBUG - interactive is False
2025-08-01 09:16:04,538 - matplotlib - DEBUG - platform is linux
2025-08-01 09:16:40,437 - error_handler - CRITICAL - 未捕获的异常
AttributeError: _ARRAY_API not found
2025-08-01 09:16:40,444 - error_handler - CRITICAL - 未捕获的异常
AttributeError: _ARRAY_API not found
2025-08-01 09:16:40,681 - dash.dash - INFO - Dash is running on http://0.0.0.0:8051/

2025-08-01 09:16:40,694 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8051
 * Running on http://*************:8051
2025-08-01 09:16:40,694 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-01 09:22:31,006 - error_handler - ERROR - 未处理的错误在 test_function: 测试错误
Traceback (most recent call last):
  File "/mnt/Data2/auto_task/web_server/error_handler.py", line 67, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/Data2/auto_task/web_server/test_simple.py", line 169, in test_function
    raise ValueError("测试错误")
ValueError: 测试错误
2025-08-01 09:23:00,228 - error_handler - ERROR - 未处理的错误在 test_function: 测试错误
Traceback (most recent call last):
  File "/mnt/Data2/auto_task/web_server/error_handler.py", line 67, in wrapper
    return func(*args, **kwargs)
  File "/mnt/Data2/auto_task/web_server/test_simple.py", line 169, in test_function
    raise ValueError("测试错误")
ValueError: 测试错误
2025-08-01 09:23:27,603 - error_handler - CRITICAL - 未捕获的异常
Traceback (most recent call last):
  File "/mnt/Data2/auto_task/web_server/app_new.py", line 78, in <module>
    main()
    ~~~~^^
  File "/mnt/Data2/auto_task/web_server/app_new.py", line 67, in main
    app.run_server(
    ^^^^^^^^^^^^^^
  File "/mnt/Data2/auto_task/web_server/.conda/lib/python3.13/site-packages/dash/_obsolete.py", line 22, in __getattr__
    raise err.exc(err.message)
dash.exceptions.ObsoleteAttributeException: app.run_server has been replaced by app.run
2025-08-01 09:25:16,359 - dash.dash - INFO - Dash is running on http://0.0.0.0:8051/

2025-08-01 09:25:16,361 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8051
 * Running on http://*************:8051
2025-08-01 09:25:16,361 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-01 09:26:10,154 - dash.dash - INFO - Dash is running on http://0.0.0.0:8051/

2025-08-01 09:26:10,156 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8051
 * Running on http://*************:8051
2025-08-01 09:26:10,156 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-01 09:26:37,652 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:37] "GET / HTTP/1.1" 200 -
2025-08-01 09:26:37,814 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:37] "GET /_dash-component-suites/dash/deps/polyfill@7.v3_2_0m1754010816.12.1.min.js HTTP/1.1" 200 -
2025-08-01 09:26:37,959 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:37] "GET /_dash-component-suites/dash/deps/react@18.v3_2_0m1754010816.3.1.min.js HTTP/1.1" 200 -
2025-08-01 09:26:37,960 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:37] "GET /_dash-component-suites/dash/deps/react-dom@18.v3_2_0m1754010816.3.1.min.js HTTP/1.1" 200 -
2025-08-01 09:26:37,962 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:37] "GET /_dash-component-suites/dash/deps/prop-types@15.v3_2_0m1754010816.8.1.min.js HTTP/1.1" 200 -
2025-08-01 09:26:37,996 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:37] "GET /_dash-component-suites/dash_bootstrap_components/_components/dash_bootstrap_components.v2_0_3m1754010816.min.js HTTP/1.1" 200 -
2025-08-01 09:26:38,008 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:38] "GET /_dash-component-suites/dash/dash-renderer/build/dash_renderer.v3_2_0m1754010816.min.js HTTP/1.1" 200 -
2025-08-01 09:26:38,095 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:38] "GET /_dash-component-suites/dash/dcc/dash_core_components.v3_2_0m1754010816.js HTTP/1.1" 200 -
2025-08-01 09:26:38,270 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:38] "GET /_dash-component-suites/dash/html/dash_html_components.v3_0_4m1754010816.min.js HTTP/1.1" 200 -
2025-08-01 09:26:38,272 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:38] "GET /_dash-component-suites/dash/dcc/dash_core_components-shared.v3_2_0m1754010816.js HTTP/1.1" 200 -
2025-08-01 09:26:38,316 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:38] "GET /_dash-component-suites/dash/dash_table/bundle.v6_0_4m1754010816.js HTTP/1.1" 200 -
2025-08-01 09:26:39,955 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:39] "GET /_dash-dependencies HTTP/1.1" 200 -
2025-08-01 09:26:39,956 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:39] "GET /_favicon.ico?v=3.2.0 HTTP/1.1" 200 -
2025-08-01 09:26:39,996 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:39] "GET /_dash-layout HTTP/1.1" 200 -
2025-08-01 09:26:40,712 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:40] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:40,712 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:40] "GET /_dash-component-suites/dash/dcc/async-dropdown.js HTTP/1.1" 200 -
2025-08-01 09:26:40,713 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:40] "GET /_dash-component-suites/dash/dash_table/async-table.js HTTP/1.1" 200 -
2025-08-01 09:26:40,713 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:40] "GET /_dash-component-suites/dash/dash_table/async-highlight.js HTTP/1.1" 200 -
2025-08-01 09:26:40,752 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:40] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:41,675 - error_handler - ERROR - 应用错误在 update_charts: 数据路径不能为空
2025-08-01 09:26:41,677 - error_handler - ERROR - 应用错误在 update_account_options: 数据路径不能为空
2025-08-01 09:26:41,678 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:41] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:41,678 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:41] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:41,719 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:41] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:41,721 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:41] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:41,723 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:41] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:41,917 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:41] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:41,923 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:41] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:41,947 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:41] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:41,949 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:41] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:41,958 - error_handler - ERROR - 应用错误在 update_charts: 数据路径不能为空
2025-08-01 09:26:41,959 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:41] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:42,266 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:42] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:42,268 - error_handler - ERROR - 应用错误在 update_charts: 数据路径不能为空
2025-08-01 09:26:42,269 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:42] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:42,467 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:42] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:50,227 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:50] "GET /?user=admin&path=/mnt/Data2/Jupyter/2025/行业SUE/bt_data/中性100_70%现货_SUE_L2增强 HTTP/1.1" 200 -
2025-08-01 09:26:50,480 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:50] "GET /_dash-layout HTTP/1.1" 200 -
2025-08-01 09:26:50,627 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:50] "GET /_dash-dependencies HTTP/1.1" 200 -
2025-08-01 09:26:50,877 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:50] "[36mGET /_dash-component-suites/dash/dcc/async-dropdown.js HTTP/1.1[0m" 304 -
2025-08-01 09:26:50,892 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:50] "[36mGET /_dash-component-suites/dash/dash_table/async-table.js HTTP/1.1[0m" 304 -
2025-08-01 09:26:50,892 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:50] "[36mGET /_dash-component-suites/dash/dash_table/async-highlight.js HTTP/1.1[0m" 304 -
2025-08-01 09:26:50,902 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:50] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:50,920 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:50] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:51,182 - error_handler - ERROR - 应用错误在 update_charts: 策略名称不能为空
2025-08-01 09:26:51,183 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:51] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:51,194 - error_handler - ERROR - 应用错误在 update_account_options: 策略名称不能为空
2025-08-01 09:26:51,196 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:51] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:51,196 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:51] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:51,210 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:51] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:51,223 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:51] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:51,737 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:51] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:51,779 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:51] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:51,782 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:51] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:51,949 - error_handler - ERROR - 应用错误在 update_charts: 策略名称不能为空
2025-08-01 09:26:51,950 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:51] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:51,951 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:51] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:52,256 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:52] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:57,672 - error_handler - ERROR - 应用错误在 update_charts: 策略名称不能为空
2025-08-01 09:26:57,672 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:57] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:57,711 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:57] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:57,713 - error_handler - ERROR - 应用错误在 update_account_options: 策略名称不能为空
2025-08-01 09:26:57,713 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:57] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:57,714 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:57] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:57,717 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:57] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:57,893 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:57] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:59,586 - error_handler - ERROR - 应用错误在 update_charts: 策略名称不能为空
2025-08-01 09:26:59,587 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:59] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:59,621 - error_handler - ERROR - 应用错误在 update_account_options: 策略名称不能为空
2025-08-01 09:26:59,622 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:59] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:59,624 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:59] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:59,627 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:59] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:59,629 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:59] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:26:59,816 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:26:59] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:27:02,599 - error_handler - ERROR - 应用错误在 update_account_options: 策略名称不能为空
2025-08-01 09:27:02,603 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:27:02] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:27:02,603 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:27:02] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:27:02,628 - error_handler - ERROR - 应用错误在 update_charts: 策略名称不能为空
2025-08-01 09:27:02,628 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:27:02] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:27:02,644 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:27:02] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:27:02,645 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:27:02] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:27:02,849 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:27:02] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:27:03,366 - error_handler - ERROR - 应用错误在 update_account_options: 策略名称不能为空
2025-08-01 09:27:03,366 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:27:03] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:27:03,486 - error_handler - ERROR - 应用错误在 update_charts: 策略名称不能为空
2025-08-01 09:27:03,487 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:27:03] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:27:03,488 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:27:03] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:27:03,489 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:27:03] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:27:03,508 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:27:03] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:27:03,693 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:27:03] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:30:33,008 - error_handler - CRITICAL - 未捕获的异常
AttributeError: _ARRAY_API not found
2025-08-01 09:30:33,015 - error_handler - CRITICAL - 未捕获的异常
AttributeError: _ARRAY_API not found
2025-08-01 09:30:33,258 - dash.dash - INFO - Dash is running on http://0.0.0.0:8051/

2025-08-01 09:30:54,384 - dash.dash - INFO - Dash is running on http://0.0.0.0:8051/

2025-08-01 09:30:54,386 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8051
 * Running on http://*************:8051
2025-08-01 09:30:54,386 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-01 09:30:58,233 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:30:58] "GET /?user=admin&path=/mnt/Data2/Jupyter/2025/行业SUE/bt_data/中性100_70%现货_SUE_L2增强 HTTP/1.1" 200 -
2025-08-01 09:30:58,640 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:30:58] "GET /_dash-layout HTTP/1.1" 200 -
2025-08-01 09:30:59,186 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:30:59] "GET /_dash-dependencies HTTP/1.1" 200 -
2025-08-01 09:31:00,369 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:00] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:00,370 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:00] "[36mGET /_dash-component-suites/dash/dash_table/async-highlight.js HTTP/1.1[0m" 304 -
2025-08-01 09:31:00,409 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:00] "[36mGET /_dash-component-suites/dash/dcc/async-dropdown.js HTTP/1.1[0m" 304 -
2025-08-01 09:31:00,409 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:00] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:00,410 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:00] "[36mGET /_dash-component-suites/dash/dash_table/async-table.js HTTP/1.1[0m" 304 -
2025-08-01 09:31:00,684 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:00] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:00,712 - error_handler - ERROR - 应用错误在 update_account_options: 策略名称不能为空
2025-08-01 09:31:00,714 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:00] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:00,714 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:00] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:00,717 - error_handler - INFO - 用户操作: 更新图表
2025-08-01 09:31:00,726 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:00] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:00,900 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:00] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:01,292 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:01] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:01,327 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:01] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:01,330 - error_handler - INFO - 用户操作: 更新图表
2025-08-01 09:31:01,333 - error_handler - ERROR - 应用错误在 update_charts: 账户ID不能为空
2025-08-01 09:31:01,334 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:01] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:01,336 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:01] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:01,344 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:01] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:01,401 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:01] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:01,643 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:01] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:30,597 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:30] "GET /?user=admin&path=/mnt/Data2/Jupyter/2025/行业SUE/bt_data/ HTTP/1.1" 200 -
2025-08-01 09:31:30,812 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:30] "GET /_dash-layout HTTP/1.1" 200 -
2025-08-01 09:31:30,980 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:30] "GET /_dash-dependencies HTTP/1.1" 200 -
2025-08-01 09:31:31,190 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:31] "[36mGET /_dash-component-suites/dash/dcc/async-dropdown.js HTTP/1.1[0m" 304 -
2025-08-01 09:31:31,208 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:31] "[36mGET /_dash-component-suites/dash/dash_table/async-table.js HTTP/1.1[0m" 304 -
2025-08-01 09:31:31,211 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:31] "[36mGET /_dash-component-suites/dash/dash_table/async-highlight.js HTTP/1.1[0m" 304 -
2025-08-01 09:31:31,221 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:31] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:31,231 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:31] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:31,586 - error_handler - INFO - 用户操作: 更新图表
2025-08-01 09:31:31,586 - error_handler - ERROR - 应用错误在 update_charts: 无法加载日收益数据
2025-08-01 09:31:31,587 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:31] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:31,615 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:31] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:31,616 - error_handler - ERROR - 应用错误在 update_account_options: 策略名称不能为空
2025-08-01 09:31:31,616 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:31] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:31,626 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:31] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:31,627 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:31] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:31,823 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:31] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:31,826 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:31] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:31,829 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:31] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:31,843 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:31] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:31,860 - error_handler - ERROR - 应用错误在 update_charts: 账户ID不能为空
2025-08-01 09:31:31,860 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:31] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:32,074 - error_handler - ERROR - 应用错误在 update_charts: 账户ID不能为空
2025-08-01 09:31:32,074 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:32] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:32,116 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:32] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:32,345 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:32] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:34,468 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:34] "GET /?user=admin&path=/mnt/Data2/Jupyter/2025/行业SUE/bt_data HTTP/1.1" 200 -
2025-08-01 09:31:34,677 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:34] "GET /_dash-layout HTTP/1.1" 200 -
2025-08-01 09:31:34,813 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:34] "GET /_dash-dependencies HTTP/1.1" 200 -
2025-08-01 09:31:35,041 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:35] "[36mGET /_dash-component-suites/dash/dcc/async-dropdown.js HTTP/1.1[0m" 304 -
2025-08-01 09:31:35,063 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:35] "[36mGET /_dash-component-suites/dash/dash_table/async-table.js HTTP/1.1[0m" 304 -
2025-08-01 09:31:35,073 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:35] "[36mGET /_dash-component-suites/dash/dash_table/async-highlight.js HTTP/1.1[0m" 304 -
2025-08-01 09:31:35,083 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:35] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:35,086 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:35] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:35,453 - error_handler - ERROR - 应用错误在 update_account_options: 策略名称不能为空
2025-08-01 09:31:35,454 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:35] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:35,456 - error_handler - INFO - 用户操作: 更新图表
2025-08-01 09:31:35,458 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:35] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:35,458 - error_handler - ERROR - 应用错误在 update_charts: 无法加载日收益数据
2025-08-01 09:31:35,459 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:35] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:35,464 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:35] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:35,496 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:35] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:35,674 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:35] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:35,692 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:35] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:35,715 - error_handler - ERROR - 应用错误在 update_charts: 账户ID不能为空
2025-08-01 09:31:35,716 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:35] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:35,717 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:35] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:35,983 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:35] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:38,934 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:38] "GET /?user=admin&path=/mnt/Data2/Jupyter/2025/行业SUE HTTP/1.1" 200 -
2025-08-01 09:31:39,143 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:39] "GET /_dash-layout HTTP/1.1" 200 -
2025-08-01 09:31:39,280 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:39] "GET /_dash-dependencies HTTP/1.1" 200 -
2025-08-01 09:31:39,534 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:39] "[36mGET /_dash-component-suites/dash/dcc/async-dropdown.js HTTP/1.1[0m" 304 -
2025-08-01 09:31:39,547 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:39] "[36mGET /_dash-component-suites/dash/dash_table/async-highlight.js HTTP/1.1[0m" 304 -
2025-08-01 09:31:39,548 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:39] "[36mGET /_dash-component-suites/dash/dash_table/async-table.js HTTP/1.1[0m" 304 -
2025-08-01 09:31:39,576 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:39] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:39,579 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:39] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:39,919 - app_new - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "/mnt/Data2/auto_task/web_server/.conda/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/mnt/Data2/auto_task/web_server/.conda/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/mnt/Data2/auto_task/web_server/.conda/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/mnt/Data2/auto_task/web_server/.conda/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/Data2/auto_task/web_server/.conda/lib/python3.13/site-packages/dash/dash.py", line 1494, in dispatch
    response_data = ctx.run(partial_func)
  File "/mnt/Data2/auto_task/web_server/.conda/lib/python3.13/site-packages/dash/_callback.py", line 688, in add_context
    raise err
  File "/mnt/Data2/auto_task/web_server/.conda/lib/python3.13/site-packages/dash/_callback.py", line 679, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)  # type: ignore[reportArgumentType]
  File "/mnt/Data2/auto_task/web_server/.conda/lib/python3.13/site-packages/dash/_callback.py", line 59, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
  File "/mnt/Data2/auto_task/web_server/callbacks.py", line 108, in update_period_options
    periods = data_handler.get_period_options(user, data_path, strategy, account_id)
  File "/mnt/Data2/auto_task/web_server/data_handler.py", line 100, in get_period_options
    if cache_key in self.cache:
                    ^^^^^^^^^^
AttributeError: 'DataHandler' object has no attribute 'cache'
2025-08-01 09:31:39,923 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:39] "[35m[1mPOST /_dash-update-component HTTP/1.1[0m" 500 -
2025-08-01 09:31:39,929 - error_handler - INFO - 用户操作: 更新账户选项
2025-08-01 09:31:39,929 - error_handler - ERROR - 应用错误在 update_account_options: 获取账户选项失败: 'DataHandler' object has no attribute 'cache'
2025-08-01 09:31:39,930 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:39] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:39,962 - error_handler - INFO - 用户操作: 更新图表
2025-08-01 09:31:39,963 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:39] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:39,978 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:39] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:40,022 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:40] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:40,231 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:40] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:40,234 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:40] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:40,253 - error_handler - ERROR - 应用错误在 update_charts: 账户ID不能为空
2025-08-01 09:31:40,254 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:40] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:40,272 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:40] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:40,446 - error_handler - ERROR - 应用错误在 update_charts: 账户ID不能为空
2025-08-01 09:31:40,447 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:40] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:40,486 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:40] "POST /_dash-update-component HTTP/1.1" 200 -
2025-08-01 09:31:40,708 - werkzeug - INFO - 127.0.0.1 - - [01/Aug/2025 09:31:40] "POST /_dash-update-component HTTP/1.1" 200 -
