"""
数据处理模块
负责所有数据加载、处理和缓存操作
"""

import os
import json
import pandas as pd
import numpy as np
from dateutil.relativedelta import relativedelta
from typing import Dict, List, Optional, Tuple
from config import PATH_MAPPINGS, FILE_CONFIG
from cache_manager import cached, cache_manager


class DataHandler:
    """数据处理类"""

    def __init__(self):
        # 使用全局缓存管理器，但保留本地缓存用于某些方法
        self.cache = {}
    
    def get_real_path(self, user: str, path: str, strategy: str) -> str:
        """获取真实路径"""
        if user == 'admin':
            real_path = path
        else:
            path = path.replace('\\', '/').split('upyter/')[-1]
            real_path = f'/jupyter/jupyter_docker/{user}/jupyter/{path}'
        
        if strategy and strategy != "":
            if strategy.endswith('/'):
                strategy = strategy[:-1]
            real_path = f"{real_path}/bt_data/{strategy}"
        
        return real_path.replace('//', '/')
    
    @cached(timeout=600, key_prefix='strategy_options')  # 缓存10分钟
    def get_strategy_options(self, user: str, path: str) -> List[Dict]:
        """获取策略选项列表"""
        try:
            if user == 'admin':
                real_path = path
            else:
                real_path = path.replace('\\', '/').split('upyter/')[-1]
                real_path = f'/jupyter/jupyter_docker/{user}/jupyter/{real_path}'

            directory_path = f'{real_path}/bt_data'.replace('//', '/')

            if not os.path.exists(directory_path):
                return []

            folders = [f for f in os.listdir(directory_path)
                      if os.path.isdir(os.path.join(directory_path, f)) and not f.startswith('.')]

            # 按修改时间排序
            sorted_folders = sorted(folders,
                                  key=lambda x: os.path.getmtime(os.path.join(directory_path, x)),
                                  reverse=True)

            return [{'label': strategy, 'value': strategy} for strategy in sorted_folders]

        except Exception as e:
            print(f"Error getting strategy options: {e}")
            return []
    
    def get_account_options(self, user: str, path: str, strategy: str) -> List[Dict]:
        """获取账户选项列表"""
        cache_key = f"accounts_{user}_{path}_{strategy}"
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        try:
            real_path = self.get_real_path(user, path, strategy)
            performance_dir = os.path.join(real_path, 'show_data')
            
            if not os.path.exists(performance_dir):
                return []
            
            files = os.listdir(performance_dir)
            account_ids = []
            
            for file in files:
                if file.endswith('.fea'):
                    account_id = file.split('.fea')[0]
                    account_ids.append(account_id)
            
            account_ids.sort()
            options = [{'label': account_id, 'value': account_id} for account_id in account_ids]
            self.cache[cache_key] = options
            return options
            
        except Exception as e:
            print(f"Error getting account options: {e}")
            return []
    
    def get_period_options(self, user: str, path: str, strategy: str, account_id: str) -> List[str]:
        """获取周期选项列表"""
        cache_key = f"periods_{user}_{path}_{strategy}_{account_id}"
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        try:
            real_path = self.get_real_path(user, path, strategy)
            
            if account_id == '全部账户汇总':
                fn = f"{real_path}/performance.xlsx"
            elif '汇总' in account_id:
                fn = f"{real_path}/performance/{account_id}.xlsx"
            else:
                account_id_clean = account_id.split('_')[-1]
                fn = f"{real_path}/performance/account_{account_id_clean}.xlsx"
            
            if not os.path.exists(fn):
                return []
            
            excel_file = pd.ExcelFile(fn)
            sheet_names = excel_file.sheet_names
            self.cache[cache_key] = sheet_names
            return sheet_names
            
        except Exception as e:
            print(f"Error getting period options: {e}")
            return []
    
    @cached(timeout=300, key_prefix='daily_return_data')  # 缓存5分钟
    def load_daily_return_data(self, user: str, path: str, strategy: str, account_id: str) -> Optional[pd.DataFrame]:
        """加载日收益数据"""
        try:
            real_path = self.get_real_path(user, path, strategy)
            file_path = f"{real_path}/show_data/{account_id}.fea"

            if os.path.exists(file_path):
                df = pd.read_feather(file_path)
                return df
            else:
                # 尝试加载balance文件
                balance_file = f"{real_path}/data/{account_id}_asset.fea"
                if os.path.exists(balance_file):
                    df = pd.read_feather(balance_file)
                    return df

            return None

        except Exception as e:
            print(f"Error loading daily return data: {e}")
            return None
    
    @cached(timeout=300, key_prefix='performance_data')  # 缓存5分钟
    def load_performance_data(self, user: str, path: str, strategy: str, account_id: str, sheet_name: str = '全部') -> Optional[pd.DataFrame]:
        """加载业绩数据"""
        try:
            real_path = self.get_real_path(user, path, strategy)

            if account_id in ['全部账户汇总', '总账户']:
                fn = f"{real_path}/performance.xlsx"
            elif '汇总' in account_id:
                fn = f"{real_path}/performance/{account_id}.xlsx"
            else:
                account_id_clean = account_id.split('期货账户_')[-1].split('股票账户_')[-1]
                fn = f"{real_path}/performance/account_{account_id_clean}.xlsx"

            if not os.path.exists(fn):
                return None

            df = pd.read_excel(fn, sheet_name=sheet_name).rename({'Unnamed: 0': ''}, axis=1)
            return df

        except Exception as e:
            print(f"Error loading performance data: {e}")
            return None
    
    def load_config_data(self, user: str, path: str, strategy: str) -> Dict:
        """加载配置数据"""
        cache_key = f"config_{user}_{path}_{strategy}"
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        try:
            real_path = self.get_real_path(user, path, strategy)
            config_file = f"{real_path}/run_info/config.json"
            
            if not os.path.exists(config_file):
                return {}
            
            with open(config_file, 'r') as file:
                config_data = json.load(file)
            
            self.cache[cache_key] = config_data
            return config_data
            
        except Exception as e:
            print(f"Error loading config data: {e}")
            return {}
    
    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()
    
    def period_filter(self, data: pd.DataFrame, period: str, date_field: str = 'date') -> pd.DataFrame:
        """根据周期过滤数据"""
        if period == '全部' or data.empty:
            return data

        last_date = data.iloc[-1][date_field]
        last_1year = (pd.to_datetime(last_date) - relativedelta(years=1)).strftime("%Y-%m-%d")
        last_6m = (pd.to_datetime(last_date) - relativedelta(months=6)).strftime("%Y-%m-%d")
        last_3m = (pd.to_datetime(last_date) - relativedelta(months=3)).strftime("%Y-%m-%d")
        last_1m = (pd.to_datetime(last_date) - relativedelta(months=1)).strftime("%Y-%m-%d")

        near_1y = last_date[:4]
        near_2y = str(int(last_date[:4]) - 1)
        near_3y = str(int(last_date[:4]) - 2)

        if '近' in period:
            if '1月' in period:
                return data[data[date_field] >= last_1m]
            elif '3月' in period:
                return data[data[date_field] >= last_3m]
            elif '6月' in period:
                return data[data[date_field] >= last_6m]
            elif '1年' in period:
                return data[data[date_field] >= last_1year]
        else:
            if near_1y in period:
                return data[(data[date_field] >= f"{near_1y}-01-01") &
                           (data[date_field] <= f"{near_1y}-12-31")]
            elif near_2y in period:
                return data[(data[date_field] >= f"{near_2y}-01-01") &
                           (data[date_field] <= f"{near_2y}-12-31")]
            elif near_3y in period:
                return data[(data[date_field] >= f"{near_3y}-01-01") &
                           (data[date_field] <= f"{near_3y}-12-31")]

        return data

    def enhance_data_with_metrics(self, data: pd.DataFrame, strategy_path: str, strategy: str,
                                 account_id: str, selected_metrics: List[str]) -> pd.DataFrame:
        """增强数据，添加额外的指标计算"""
        if not selected_metrics:
            return data

        # 检查是否需要计算额外指标
        need_calculation = any(metric in selected_metrics
                             for metric in ['仓位占比', '交易次数', '累计换手率'])

        if not need_calculation:
            return data

        try:
            real_data_path = f"{strategy_path}/data".replace('//', '/')

            if account_id == '全部账户汇总':
                return self._calculate_all_account_metrics(data, real_data_path, selected_metrics)
            elif '汇总' in account_id:
                return self._calculate_summary_metrics(data, real_data_path, account_id, selected_metrics)
            else:
                return self._calculate_single_account_metrics(data, real_data_path, account_id, selected_metrics)

        except Exception as e:
            print(f"Error enhancing data with metrics: {e}")
            return data

    def _calculate_all_account_metrics(self, data: pd.DataFrame, real_data_path: str,
                                     selected_metrics: List[str]) -> pd.DataFrame:
        """计算全部账户汇总指标"""
        try:
            all_fns = os.listdir(real_data_path)
            all_ids = [x.split('_')[0] for x in all_fns if '_order' in x]

            count_asset_df = pd.DataFrame()
            count_order_list = []

            for real_id in all_ids:
                asset_file = f'{real_data_path}/{real_id}_asset.fea'
                if os.path.exists(asset_file):
                    asset_df = pd.read_feather(asset_file).set_index('date')
                    if count_asset_df.empty:
                        count_asset_df['balance'] = asset_df['balance']
                        count_asset_df['available'] = asset_df['available']
                    else:
                        count_asset_df['balance'] += asset_df['balance']
                        count_asset_df['available'] += asset_df['available']

                order_file = f'{real_data_path}/{real_id}_order.fea'
                if os.path.exists(order_file):
                    order_df = pd.read_feather(order_file)
                    count_order_list.append(order_df)

            data = data.set_index('date')

            # 计算仓位占比
            if '仓位占比' in selected_metrics and not count_asset_df.empty:
                data['仓位占比'] = round((1 - count_asset_df['available'] / count_asset_df['balance']) * 100, 2)

            # 计算交易次数
            if '交易次数' in selected_metrics:
                if count_order_list:
                    count_order_df = pd.concat(count_order_list)
                    count_order_df['交易次数'] = 1
                    daily_trades = count_order_df.groupby('date')['交易次数'].sum()
                    data['交易次数'] = daily_trades.reindex(data.index, fill_value=0).cumsum()
                else:
                    data['交易次数'] = 0

            return data.reset_index()

        except Exception as e:
            print(f"Error calculating all account metrics: {e}")
            return data

    def _calculate_summary_metrics(self, data: pd.DataFrame, real_data_path: str,
                                 account_id: str, selected_metrics: List[str]) -> pd.DataFrame:
        """计算汇总账户指标"""
        # 类似于全部账户汇总的逻辑，但需要根据账户类型过滤
        return self._calculate_all_account_metrics(data, real_data_path, selected_metrics)

    def _calculate_single_account_metrics(self, data: pd.DataFrame, real_data_path: str,
                                        account_id: str, selected_metrics: List[str]) -> pd.DataFrame:
        """计算单个账户指标"""
        try:
            real_id = account_id.split('_')[-1]
            asset_file = f'{real_data_path}/{real_id}_asset.fea'

            if not os.path.exists(asset_file):
                return data

            asset_df = pd.read_feather(asset_file).set_index('date')

            # 检查账户类型
            if hasattr(asset_df, 'account_type') and asset_df.iloc[0].account_type == 'bank':
                return data

            data = data.set_index('date')

            # 计算仓位占比
            if '仓位占比' in selected_metrics:
                data['仓位占比'] = round((1 - asset_df['available'] / asset_df['balance']) * 100, 2)

            # 计算交易次数和换手率
            order_file = f'{real_data_path}/{real_id}_order.fea'
            if os.path.exists(order_file) and ('交易次数' in selected_metrics or '累计换手率' in selected_metrics):
                order_df = pd.read_feather(order_file)

                if '交易次数' in selected_metrics:
                    order_df['交易次数'] = 1
                    daily_trades = order_df.groupby('date')['交易次数'].sum()
                    data['交易次数'] = daily_trades.reindex(data.index, fill_value=0).cumsum()

                if '累计换手率' in selected_metrics:
                    daily_turnover = order_df.groupby('date')['order_values'].sum() / asset_df['balance']
                    data['累计换手率'] = daily_turnover.reindex(data.index, fill_value=0).cumsum()

            return data.reset_index()

        except Exception as e:
            print(f"Error calculating single account metrics: {e}")
            return data


# 全局数据处理器实例
data_handler = DataHandler()
