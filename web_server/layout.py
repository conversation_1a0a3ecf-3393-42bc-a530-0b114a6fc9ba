"""
布局模块
定义应用的UI布局和组件
"""

from dash import dcc, html, dash_table
import dash_bootstrap_components as dbc
from config import UI_CONFIG, THEME_CONFIG
from error_handler import create_loading_component, create_empty_state


def create_header():
    """创建页面头部"""
    return dbc.Container([
        dbc.Row([
            dbc.Col([
                html.Div([
                    html.I(className="fas fa-chart-line me-3", style={'fontSize': '2rem', 'color': '#0d6efd'}),
                    html.H1(
                        UI_CONFIG['page_title'],
                        className="d-inline text-primary mb-0",
                        style={'fontWeight': 'bold', 'fontSize': '2.5rem'}
                    )
                ], className="text-center mb-4 d-flex align-items-center justify-content-center"),
                html.P(
                    "专业的量化投资策略回测分析平台",
                    className="text-center text-muted mb-4",
                    style={'fontSize': '1.1rem'}
                )
            ])
        ])
    ], fluid=True, className="mb-4 py-3 bg-gradient")


def create_control_panel():
    """创建控制面板"""
    return dbc.Container([
        dbc.Card([
            dbc.CardHeader([
                html.H5([
                    html.I(className="fas fa-cogs me-2"),
                    "参数配置"
                ], className="mb-0 text-primary")
            ]),
            dbc.CardBody([
                # 第一行：数据目录和策略选择
                dbc.Row([
                    dbc.Col([
                        dbc.Label([
                            html.I(className="fas fa-folder me-1"),
                            "数据目录:"
                        ], className="fw-bold text-secondary"),
                        dbc.InputGroup([
                            dbc.InputGroupText(html.I(className="fas fa-database")),
                            dbc.Input(
                                id='data-pth',
                                type='text',
                                placeholder='请输入回测数据所在位置...',
                                className="form-control"
                            )
                        ], className="mb-3")
                    ], width=6),
                    dbc.Col([
                        dbc.Label([
                            html.I(className="fas fa-strategy me-1"),
                            "策略:"
                        ], className="fw-bold text-secondary"),
                        dcc.Dropdown(
                            id='strage-dropdown',
                            placeholder="选择策略...",
                            className="mb-3",
                            style={'fontSize': '14px'}
                        )
                    ], width=6),
                ], className="mb-3"),

                # 第二行：账户和周期选择
                dbc.Row([
                    dbc.Col([
                        dbc.Label([
                            html.I(className="fas fa-user me-1"),
                            "账户ID:"
                        ], className="fw-bold text-secondary"),
                        dcc.Dropdown(
                            id='account-id-dropdown',
                            value='全部账户汇总',
                            placeholder="选择账户...",
                            className="mb-3",
                            style={'fontSize': '14px'}
                        )
                    ], width=4),
                    dbc.Col([
                        dbc.Label([
                            html.I(className="fas fa-calendar me-1"),
                            "业绩评价周期:"
                        ], className="fw-bold text-secondary"),
                        dcc.Dropdown(
                            id='period-dropdown',
                            value='全部',
                            placeholder="选择周期...",
                            className="mb-3",
                            style={'fontSize': '14px'}
                        )
                    ], width=4),
                    dbc.Col([
                        dbc.Label([
                            html.I(className="fas fa-sync-alt me-1"),
                            "快速操作:"
                        ], className="fw-bold text-secondary"),
                        dbc.ButtonGroup([
                            dbc.Button([
                                html.I(className="fas fa-refresh me-1"),
                                "刷新"
                            ], color="outline-primary", size="sm"),
                            dbc.Button([
                                html.I(className="fas fa-download me-1"),
                                "导出"
                            ], id="export-button", color="outline-success", size="sm")
                        ], className="d-grid")
                    ], width=4)
                ], className="mb-3"),

                # 第三行：指标选择
                dbc.Row([
                    dbc.Col([
                        dbc.Label([
                            html.I(className="fas fa-chart-bar me-1"),
                            "指标选择:"
                        ], className="fw-bold text-secondary mb-2"),
                        html.Div([
                            dcc.Checklist(
                                id='metrics-checklist',
                                className="metrics-checklist",
                                labelClassName="metric-label",
                                inputClassName="metric-input"
                            )
                        ], className="metrics-container")
                    ])
                ])
            ])
        ], className="shadow-sm border-0")
    ], fluid=True, className="mb-4")


def create_main_content():
    """创建主要内容区域"""
    return dbc.Container([
        # 动态标题区域
        dbc.Row([
            dbc.Col([
                dbc.Alert([
                    html.H4([
                        html.I(className="fas fa-chart-area me-2"),
                        html.Span(id='dynamic-title', className="strategy-title")
                    ], className="mb-0 text-white")
                ], color="primary", className="text-center mb-4 border-0 shadow-sm")
            ])
        ]),

        # 主要内容区域
        dbc.Row([
            # 左侧图表区域
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5([
                            html.I(className="fas fa-chart-line me-2"),
                            "策略分析图表"
                        ], className="mb-0 text-primary")
                    ]),
                    dbc.CardBody([
                        dcc.Loading(
                            id="loading-graphs",
                            type="cube",
                            color="#0d6efd",
                            children=[
                                html.Div(id='dynamic-graphs', className="chart-container")
                            ]
                        )
                    ], className="p-0")
                ], className="shadow-sm border-0 h-100")
            ], width=8),

            # 右侧信息面板
            dbc.Col([
                # 运行信息卡片
                dbc.Card([
                    dbc.CardHeader([
                        html.H5([
                            html.I(className="fas fa-info-circle me-2"),
                            "运行信息"
                        ], className="mb-0 text-primary")
                    ]),
                    dbc.CardBody([
                        dcc.Loading(
                            id="loading-run-info",
                            type="dot",
                            color="#0d6efd",
                            children=[
                                dash_table.DataTable(
                                    id='run-info',
                                    style_cell={
                                        'textAlign': 'left',
                                        'fontSize': '13px',
                                        'fontFamily': 'system-ui, -apple-system, sans-serif',
                                        'padding': '8px',
                                        'border': 'none'
                                    },
                                    style_header={
                                        'backgroundColor': '#f8f9fa',
                                        'fontWeight': '600',
                                        'color': '#495057',
                                        'border': 'none',
                                        'borderBottom': '2px solid #dee2e6'
                                    },
                                    style_data={
                                        'backgroundColor': 'white',
                                        'border': 'none',
                                        'borderBottom': '1px solid #f8f9fa'
                                    },
                                    style_data_conditional=[
                                        {
                                            'if': {'row_index': 'odd'},
                                            'backgroundColor': '#f8f9fa'
                                        }
                                    ]
                                )
                            ]
                        )
                    ])
                ], className="mb-3 shadow-sm border-0"),

                # 业绩指标卡片
                dbc.Card([
                    dbc.CardHeader([
                        html.H5([
                            html.I(className="fas fa-trophy me-2"),
                            "业绩指标"
                        ], className="mb-0 text-primary")
                    ]),
                    dbc.CardBody([
                        dcc.Loading(
                            id="loading-table",
                            type="dot",
                            color="#0d6efd",
                            children=[
                                dash_table.DataTable(
                                    id='table',
                                    style_cell={
                                        'textAlign': 'left',
                                        'fontSize': '12px',
                                        'fontFamily': 'system-ui, -apple-system, sans-serif',
                                        'padding': '8px',
                                        'maxWidth': '120px',
                                        'overflow': 'hidden',
                                        'textOverflow': 'ellipsis',
                                        'border': 'none'
                                    },
                                    style_header={
                                        'backgroundColor': '#f8f9fa',
                                        'fontWeight': '600',
                                        'color': '#495057',
                                        'border': 'none',
                                        'borderBottom': '2px solid #dee2e6'
                                    },
                                    style_data={
                                        'backgroundColor': 'white',
                                        'border': 'none',
                                        'borderBottom': '1px solid #f8f9fa'
                                    },
                                    style_data_conditional=[
                                        {
                                            'if': {'row_index': 'odd'},
                                            'backgroundColor': '#f8f9fa'
                                        },
                                        {
                                            'if': {'column_id': '策略'},
                                            'fontWeight': '600',
                                            'color': '#dc3545'
                                        },
                                        {
                                            'if': {'column_id': '基准'},
                                            'fontWeight': '600',
                                            'color': '#6c757d'
                                        },
                                        {
                                            'if': {'column_id': '超额'},
                                            'fontWeight': '600',
                                            'color': '#7046aa'
                                        }
                                    ],
                                    tooltip_data=[],
                                    tooltip_duration=None,
                                    style_table={'maxHeight': '500px', 'overflowY': 'auto'}
                                )
                            ]
                        )
                    ])
                ], className="shadow-sm border-0")
            ], width=4)
        ])
    ], fluid=True)


def create_footer():
    """创建页面底部"""
    return dbc.Container([
        html.Hr(),
        dbc.Row([
            dbc.Col([
                html.P(
                    "策略回测分析系统 © 2024",
                    className="text-center text-muted small"
                )
            ])
        ])
    ], fluid=True, className="mt-5")


def create_layout():
    """创建完整的应用布局"""
    return dbc.Container([
        # 存储组件
        dcc.Store(id="store"),
        dcc.Store(id="tempdata"),

        # 下载组件
        dcc.Download(id="download-component"),

        # 全局错误显示区域
        html.Div(id='global-error-display', className="mb-3"),
        dcc.Location(id='url', refresh=False),
        
        # 页面内容
        create_header(),
        create_control_panel(),
        create_main_content(),
        create_footer()
    ], fluid=True, className="px-4")


def get_external_stylesheets():
    """获取外部样式表"""
    return [
        dbc.themes.BOOTSTRAP,
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    ]


def get_custom_css():
    """获取自定义CSS样式"""
    return """
    <style>
    /* 全局样式 */
    body {
        font-family: 'system-ui', -apple-system, 'Segoe UI', 'Roboto', sans-serif;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }

    /* 渐变背景 */
    .bg-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 0 0 20px 20px;
    }

    /* 卡片样式 */
    .card {
        border: none;
        border-radius: 15px;
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    }

    .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 2px solid #dee2e6;
        border-radius: 15px 15px 0 0 !important;
        padding: 1rem 1.5rem;
    }

    /* 指标选择样式 */
    .metrics-container {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        border: 1px solid #e9ecef;
    }

    .metrics-checklist {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 10px;
    }

    .metric-label {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        background: white;
        border-radius: 8px;
        border: 1px solid #dee2e6;
        transition: all 0.2s ease;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
    }

    .metric-label:hover {
        background: #e3f2fd;
        border-color: #2196f3;
        transform: translateY(-1px);
    }

    .metric-input {
        margin-right: 8px !important;
        transform: scale(1.2);
    }

    /* 图表容器 */
    .chart-container {
        border-radius: 0 0 15px 15px;
        overflow: hidden;
    }

    /* 策略标题 */
    .strategy-title {
        font-weight: 600;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    /* 表格样式 */
    .dash-table-container {
        border-radius: 10px;
        overflow: hidden;
        border: 1px solid #e9ecef;
    }

    /* 输入组样式 */
    .input-group-text {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 1px solid #ced4da;
        color: #495057;
    }

    /* 按钮样式 */
    .btn {
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
    }

    /* 加载动画样式 */
    ._dash-loading {
        margin: 2rem auto;
    }

    /* 下拉框样式 */
    .Select-control {
        border-radius: 8px !important;
        border: 1px solid #ced4da !important;
        transition: all 0.2s ease !important;
    }

    .Select-control:hover {
        border-color: #80bdff !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .metrics-checklist {
            grid-template-columns: 1fr;
        }

        .card-body {
            padding: 1rem;
        }

        .container-fluid {
            padding-left: 10px;
            padding-right: 10px;
        }
    }

    /* 动画效果 */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .card {
        animation: fadeIn 0.5s ease-out;
    }

    /* 滚动条样式 */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* 工具提示样式 */
    .tooltip {
        font-size: 12px;
    }

    /* 状态指示器 */
    .status-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 8px;
    }

    .status-success { background-color: #28a745; }
    .status-warning { background-color: #ffc107; }
    .status-danger { background-color: #dc3545; }
    .status-info { background-color: #17a2b8; }
    </style>
    """
