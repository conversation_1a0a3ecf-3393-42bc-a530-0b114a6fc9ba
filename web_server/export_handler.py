"""
导出处理模块
负责策略目录的打包和下载功能
"""

import os
import zipfile
import tempfile
import shutil
from datetime import datetime
from typing import Optional, Tuple
from flask import send_file, Response
import logging

logger = logging.getLogger(__name__)


class ExportHandler:
    """导出处理类"""
    
    def __init__(self):
        self.temp_dir = tempfile.gettempdir()
    
    def create_strategy_archive(self, user: str, data_path: str, strategy: str) -> Optional[str]:
        """
        创建策略目录的压缩包
        
        Args:
            user: 用户名
            data_path: 数据路径
            strategy: 策略名称
            
        Returns:
            压缩包文件路径，如果失败返回None
        """
        try:
            # 获取真实路径
            real_path = self._get_real_path(user, data_path, strategy)
            
            if not os.path.exists(real_path):
                logger.error(f"策略目录不存在: {real_path}")
                return None
            
            # 创建临时压缩包文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            zip_filename = f"strategy_{strategy}_{timestamp}.zip"
            zip_path = os.path.join(self.temp_dir, zip_filename)
            
            # 创建压缩包
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                self._add_directory_to_zip(zipf, real_path, strategy)
            
            logger.info(f"成功创建策略压缩包: {zip_path}")
            return zip_path
            
        except Exception as e:
            logger.error(f"创建策略压缩包失败: {e}")
            return None
    
    def _get_real_path(self, user: str, path: str, strategy: str) -> str:
        """获取真实路径"""
        if user == 'admin':
            real_path = path
        else:
            path = path.replace('\\', '/').split('upyter/')[-1]
            real_path = f'/jupyter/jupyter_docker/{user}/jupyter/{path}'
        
        if strategy and strategy != "":
            if strategy.endswith('/'):
                strategy = strategy[:-1]
            real_path = f"{real_path}/bt_data/{strategy}"
        
        return real_path.replace('//', '/')
    
    def _add_directory_to_zip(self, zipf: zipfile.ZipFile, dir_path: str, base_name: str):
        """
        递归添加目录到压缩包
        
        Args:
            zipf: ZipFile对象
            dir_path: 要添加的目录路径
            base_name: 压缩包内的基础目录名
        """
        for root, dirs, files in os.walk(dir_path):
            # 计算相对路径
            rel_path = os.path.relpath(root, dir_path)
            if rel_path == '.':
                zip_dir = base_name
            else:
                zip_dir = os.path.join(base_name, rel_path).replace('\\', '/')
            
            # 添加文件
            for file in files:
                file_path = os.path.join(root, file)
                zip_file_path = os.path.join(zip_dir, file).replace('\\', '/')
                
                # 跳过一些不必要的文件
                if self._should_include_file(file):
                    zipf.write(file_path, zip_file_path)
    
    def _should_include_file(self, filename: str) -> bool:
        """
        判断是否应该包含该文件
        
        Args:
            filename: 文件名
            
        Returns:
            是否包含该文件
        """
        # 跳过的文件类型
        skip_extensions = {'.tmp', '.log', '.lock', '.cache'}
        skip_patterns = {'__pycache__', '.git', '.svn', 'Thumbs.db', '.DS_Store'}
        
        # 检查文件扩展名
        _, ext = os.path.splitext(filename)
        if ext.lower() in skip_extensions:
            return False
        
        # 检查文件名模式
        if filename in skip_patterns:
            return False
        
        # 检查隐藏文件（以.开头的文件，除了一些特殊情况）
        if filename.startswith('.') and filename not in {'.gitignore', '.env'}:
            return False
        
        return True
    
    def cleanup_temp_file(self, file_path: str):
        """
        清理临时文件
        
        Args:
            file_path: 要清理的文件路径
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"已清理临时文件: {file_path}")
        except Exception as e:
            logger.error(f"清理临时文件失败: {e}")
    
    def get_download_response(self, zip_path: str, strategy: str) -> Optional[Response]:
        """
        获取下载响应
        
        Args:
            zip_path: 压缩包路径
            strategy: 策略名称
            
        Returns:
            Flask响应对象
        """
        try:
            if not os.path.exists(zip_path):
                logger.error(f"压缩包文件不存在: {zip_path}")
                return None
            
            # 生成下载文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            download_filename = f"strategy_{strategy}_{timestamp}.zip"
            
            return send_file(
                zip_path,
                as_attachment=True,
                download_name=download_filename,
                mimetype='application/zip'
            )
            
        except Exception as e:
            logger.error(f"生成下载响应失败: {e}")
            return None


# 全局导出处理器实例
export_handler = ExportHandler()
